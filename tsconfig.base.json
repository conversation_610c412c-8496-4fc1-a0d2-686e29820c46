{
  "compileOnSave": false,
  "compilerOptions": {
    "rootDir": ".",
    "sourceMap": true,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "target": "ES2021",
    "module": "CommonJS", // Default for Node services, can be overridden
    "lib": ["es2021", "dom"],
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "baseUrl": ".", // Important for path aliases
    "paths": {
      "@libs/*": ["libs/*"],
      "@libs/prisma-utils": ["libs/prisma-utils/src"],
      "@libs/prisma-utils/*": ["libs/prisma-utils/src/*"]
    },
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "importHelpers": false, // Disable to avoid runtime tslib dependency
    "strict": true, // Enforce strict type checking
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true, 
    "noUnusedLocals": false, // Relaxed for now during setup
    "noUnusedParameters": false // Relaxed for now during setup
  },
  "exclude": ["node_modules", "tmp", "dist"]
}
