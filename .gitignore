# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/

# Build output
dist/
dist-webpack/



# TypeScript cache
*.tsbuildinfo

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build output
.nuxt
dist

# Remix build output
.cache/
build/
public/build/

# Gatsby files
.cache/
public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# IDE files
.idea/
*.iml
.vscode/

# OS generated files
.DS_Store
Thumbs.db

# Build outputs in services/frontends/libs
**/dist
**/node_modules
**/coverage
**/.env               # General ignore for .env files in subdirectories
**/.env.*             # General ignore for .env.* files in subdirectories
!**/.env.local        # Allow .env.local in subdirectories
!**/.env.docker       # Allow .env.docker in subdirectories
!**/.env.example      # Allow .env.example in subdirectories
!**/test/.env.test.*.example  # Allow all test example files

# Environment variables
.env*                 # General ignore for .env files at root
!.env.local           # Allow .env.local at root
!.env.docker          # Allow .env.docker at root
!.env.bundling
!.env.example         # Allow .env.example at root


.windsurf

__frontend
__webclient
__ui-components

notes_*.md

generated