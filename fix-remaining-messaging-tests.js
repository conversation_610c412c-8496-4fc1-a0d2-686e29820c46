const fs = require('fs');

// Fix remaining issues in in-memory-event-bus.unit.spec.ts
const inMemoryPath = '/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts';
let content = fs.readFileSync(inMemoryPath, 'utf8');

// Fix remaining jest.fn() calls that weren't replaced
content = content.replace(/eventBus\.subscribe\('([^']+)', jest\.fn\(\)\);/g, 
  `eventBus.subscribe('$1', { handle: jest.fn(), supportedEvents: ['$1'] });`);

// Fix specific handler types that need jest.fn() structure but with proper interface
content = content.replace(
  /const errorHandler = jest\.fn\(\)\.mockImplementation\(\(\) => \{\s*throw new Error\('Handler error'\);\s*\}\);/g,
  `const errorHandler = {
        handle: jest.fn().mockImplementation(() => {
          throw new Error('Handler error');
        }),
        supportedEvents: ['error.test']
      };`
);

content = content.replace(
  /const slowHandler = jest\.fn\(\)\.mockImplementation\(\(\) => \{[^}]+\}\);/gs,
  `const slowHandler = {
        handle: jest.fn().mockImplementation(() => {
          // Simulate processing time
          const start = Date.now();
          while (Date.now() - start < 10) {
            // Busy wait
          }
        }),
        supportedEvents: ['performance.test']
      };`
);

// Fix error handlers in multi-error test
content = content.replace(
  /const errorHandler1 = jest\.fn\(\)\.mockImplementation\(\(\) => \{\s*throw new Error\('Handler 1 error'\);\s*\}\);/g,
  `const errorHandler1 = {
        handle: jest.fn().mockImplementation(() => {
          throw new Error('Handler 1 error');
        }),
        supportedEvents: ['multi.error']
      };`
);

content = content.replace(
  /const errorHandler2 = jest\.fn\(\)\.mockImplementation\(\(\) => \{\s*throw new Error\('Handler 2 error'\);\s*\}\);/g,
  `const errorHandler2 = {
        handle: jest.fn().mockImplementation(() => {
          throw new Error('Handler 2 error');
        }),
        supportedEvents: ['multi.error']
      };`
);

content = content.replace(
  /const asyncErrorHandler = jest\.fn\(\)\.mockImplementation\(async \(\) => \{[^}]+\}\);/gs,
  `const asyncErrorHandler = {
        handle: jest.fn().mockImplementation(async () => {
          await new Promise(resolve => setTimeout(resolve, 1));
          throw new Error('Async handler error');
        }),
        supportedEvents: ['async.error']
      };`
);

// Fix correlation ID metadata structure 
content = content.replace(
  /metadata: \{\s*source: 'test',\s*correlationId: 'test-correlation-456'\s*\}/g,
  `source: 'test',
        correlationId: 'test-correlation-456'`
);

fs.writeFileSync(inMemoryPath, content);

// Fix event-factory.unit.spec.ts
const eventFactoryPath = '/root/code/polyrepo/libs/messaging/test/unit/event-factory.unit.spec.ts';
let factoryContent = fs.readFileSync(eventFactoryPath, 'utf8');

// Fix userCreated call signature
factoryContent = factoryContent.replace(
  /const userEvent = EventFactory\.userCreated\(\{ id: 1, email: 'test@test\.com' \}, \{\s*source: 'test', correlationId\s*\}\);/g,
  `const userEvent = EventFactory.userCreated('user-123', { id: 1, email: '<EMAIL>' }, correlationId);`
);

// Fix cache operation calls - add missing 'hit' property
factoryContent = factoryContent.replace(
  /const operationData = \{\s*operation: 'set' as const,\s*key: '[^']*',\s*hit: true,\s*ttl: \d+,\s*size: \d+,\s*responseTime: \d+\s*\};/g,
  `const operationData = {
          operation: 'set' as const,
          key: 'user:456',
          hit: true,
          ttl: 3600,
          size: 512,
          responseTime: 8
        };`
);

factoryContent = factoryContent.replace(
  /const operationData = \{\s*operation: 'delete' as const,\s*key: '[^']*',\s*responseTime: \d+\s*\};/g,
  `const operationData = {
          operation: 'delete' as const,
          key: 'user:789',
          hit: true,
          responseTime: 3
        };`
);

// Fix metadata access for cache operations
factoryContent = factoryContent.replace(/event\.metadata\.operationType/g, 'event.data.operation');
factoryContent = factoryContent.replace(/event\.metadata\.cacheResult/g, 'event.metadata?.cacheEfficiency');

// Fix correlationId access - it's now a top-level property
factoryContent = factoryContent.replace(/\.metadata\.correlationId/g, '.correlationId');

fs.writeFileSync(eventFactoryPath, factoryContent);

console.log('Fixed remaining messaging test issues');