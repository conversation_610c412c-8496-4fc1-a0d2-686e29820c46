# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Session Setup Protocol

**ALWAYS START BY:**
1. **Read project overview**: Review `INTRODUCTION.md` for project structure, architecture, and current status
2. **Check for session notes**: Look for `notes_*.md` files in project root to understand previous work
3. **Review development setup**: Check `infrastructure/local-dev/DEVELOPMENT.md` for environment details
4. **Create session notes**: Create `notes_session_YYYYMMDD.md` in project root to document discoveries
5. **Use Bundled Volume Development** for development work
6. **Use yarn** as package manager (NOT npm)

## Essential Commands

### Quick Start (Bundle Optimization - Recommended)
```bash
cd /root/code/polyrepo
yarn install                                    # Install all dependencies
yarn dev:cli start --auto                       # BUNDLE OPTIMIZATION: 6x faster (3-5s vs 15-30s)
                                                # No yarn build:libs needed - direct source bundling!
```

### Development Commands
```bash
# Library management (order matters!)
yarn build:libs                    # Build all shared libraries in correct order

# Testing and Quality
yarn test:all                      # Run all workspace tests
yarn lint:all                      # Lint all workspaces
cd services/<service-name> && yarn test:integration  # Service integration tests
cd services/<service-name> && yarn test:e2e         # End-to-end tests

# Database operations (User Service only)
cd services/user-service
yarn prisma:migrate:dev           # Run database migrations
yarn prisma:generate              # Generate Prisma client
yarn prisma:studio                # Open database GUI
```

### Enhanced Development Workflow (CLI-Based)
```bash
# RECOMMENDED: Use CLI for all development operations
yarn dev:cli start --auto                          # Start full development workflow (auto-restart)
yarn dev:cli start                                 # Start development workflow (manual restart)
yarn dev:cli status                                # Show all process status
yarn dev:cli stop                                  # Stop all processes and containers
yarn dev:cli health                                # Comprehensive health check
yarn dev:cli logs [service]                        # Show logs for specific service
yarn dev:cli cleanup                               # Clean up dead processes
yarn dev:cli bundle-test                           # Test bundle optimization

# Individual service bundle commands (if needed)
cd services/api-gateway && yarn dev:bundle         # Build and start bundle-optimized service
cd services/api-gateway && yarn build:webpack:watch # Watch mode for development

# Service endpoints
# API Gateway: http://localhost:3000
# Auth Service: http://localhost:3001 (direct)
# User Service: http://localhost:3002 (direct) 
# Keycloak Admin: http://localhost:8080 (admin/admin)
# Grafana: http://localhost:3200 (admin/admin)

# Test authentication flows
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}'
```

## Session Notes Requirements

**Create `notes_session_YYYYMMDD.md` documenting:**
- Issues with tools/actions and solutions found
- Unobvious project decisions requiring investigation
- Time-saving discoveries for future sessions
- Documentation improvement suggestions
- Code fixes and changes (reference files with examples)
- Intermediate summaries of findings, work completed, and plans

## Critical Architecture Knowledge

### Bundle Optimization (NEW - Recommended)
**6x Faster Development**: Direct source bundling eliminates the need for pre-building libraries
- **Before**: Library change → `yarn build:libs` (15-30s) → webpack rebuild (3-5s) = 18-35s total
- **After**: Library change → webpack rebuild directly from src/ (3-5s) = 3-5s total

### Shared Library Build Order (Legacy - Only for Production Builds)
For production builds only, must build in this exact sequence:
1. `observability` → 2. `shared-types` → 3. `shared-utils` → 4. `keycloak-client` → 5. `auth-common` → 6. `error-handling` → 7. `resilience` → 8. `http` → 9. `caching` → 10. `messaging`

**Script handles this:** `yarn build:libs` (uses `infrastructure/local-dev/build-libs-order.sh`)

### Current Library Integration Status

**🚀 Production-Ready Libraries with Comprehensive Testing:**
- **@libs/http**: Modern HTTP/2 client with Got library (60-80% performance improvement) ✅ **Tested**: 25+ unit/integration tests
- **@libs/caching**: Redis-based caching with decorators and messaging events (~95% cache hit improvement) ✅ **Tested**: 35+ unit/integration tests
- **@libs/messaging**: Event-driven architecture with Redis Streams and HTTP lifecycle events ✅ **Tested**: 45+ unit/integration tests
- **@libs/resilience**: Circuit breaker patterns with Opossum for automatic failure isolation ✅ **Tested**: 30+ unit tests
- **@libs/observability**: Comprehensive logging, metrics, and distributed tracing ✅ **Tested**: 20+ unit tests
- **@libs/error-handling**: Type-safe error codes with correlation ID management ✅ **Tested**: 35+ unit tests
- **@libs/auth-common**: JWT guards, strategies, and user context management ✅ **Tested**: 95+ unit/integration tests
- **@libs/keycloak-client**: Centralized Keycloak integration with JWKS caching and auth flows ✅ **Tested**: 31+ unit tests

**✅ Authentication Integration Complete:**
- Client credentials flow working (service-to-service)
- Password grant flow working (user authentication)
- Test realm configured with roles and users
- Container networking properly configured
- Comprehensive auth flow testing with real NestJS integration

### Development Mode: Enhanced Bundled Volume
- **Recommended mode** for active development
- ~10-15 second code-to-live feedback loops
- Webpack watch + Docker volume mounting + library watching
- Automatic library change detection and rebuilding
- Optional auto-restarts on code changes
- **Commands:**
  - `yarn start:dev:bundled:watch:enhanced` - Manual restart mode
  - `yarn start:dev:bundled:watch:auto` - Auto-restart mode

### Service Architecture
- **API Gateway** (port 3000): Entry point, dynamic proxy, JWT validation, HTTP/2 routing
- **Auth Service** (port 3001): Keycloak integration, JWKS caching, JWT issuing
- **User Service** (port 3002): User management, PostgreSQL/Prisma, profile management
- **Keycloak** (port 8080): Authentication server with test realm and users configured
- **Shared libs** (`libs/`): Cross-cutting concerns with comprehensive cross-library integration

### Authentication Configuration
- **Test Realm**: `polyrepo-test` with pre-configured test users
- **Test Users**: `<EMAIL>`, `<EMAIL>`, `<EMAIL>`
- **Password**: `testpassword` for all test users
- **Client**: `auth-service-test-client` with service account enabled

### Cross-Library Integration Matrix

**Complete Integration Network:**
```
@libs/http ↔ @libs/observability ↔ @libs/error-handling ↔ @libs/messaging
     ↕                ↕                       ↕                ↕
@libs/caching ↔ @libs/resilience ↔ @libs/auth-common ↔ @libs/keycloak-client
```

**Key Integration Points:**
- **HTTP → Resilience**: Automatic circuit breaker protection for all service calls
- **HTTP → Caching**: Response caching with Redis and cache-aside patterns
- **HTTP → Messaging**: Lifecycle events (request/response/error) published to Redis Streams
- **HTTP → Observability**: Comprehensive metrics, logging, and distributed tracing
- **Caching → Messaging**: Cache operation events and invalidation patterns
- **Resilience → Messaging**: Circuit breaker state change events
- **Auth → Keycloak**: JWT validation with automatic JWKS caching and rotation

## Quick Troubleshooting

| Issue | Solution |
|-------|----------|
| `Cannot find module @libs/*` | Run `yarn build:libs` |
| `EACCES: permission denied` in Docker | Check container permissions, restart with clean volumes |
| `network was found but has incorrect label` | `docker network rm <network_name>` or `docker network prune -f` |
| `EROFS: read-only file system` | Remove `:ro` flags from volume mounts |
| Windows permission errors after Docker | Remove Docker junctions: `rmdir "path\to\junction"` |
| `ENOENT` symlink errors | Check lib-watcher symlink creation in `/app/node_modules/@libs` |
| Service won't start | Check `docker-compose logs <service>` |
| Database connection errors | Verify containers: `docker ps \| grep postgres` |
| Webpack build fails | Check `services/*/webpack-watch-*.log` |
| Port conflicts | Check service endpoints table above |

### Reset Everything
```bash
cd /root/code/polyrepo
docker-compose -f infrastructure/local-dev/docker-compose.bundled-dev-volume.yml down -v
pkill -f webpack                   # Stop webpack watches
docker volume prune -f             # Clean volumes
docker network prune -f            # Clean networks
docker system prune -f             # Clean unused resources
# On Windows: Check for and remove Docker junctions with `dir /a | findstr JUNCTION`
yarn build:libs                    # Rebuild libraries
yarn start:dev:bundled:watch       # Restart development
```

## Task Management with Dart MCP

**All project tasks are managed in Dart - no separate workplan files.** Use MCP tools to:

### Fetching Tasks
```bash
# List all tasks
mcp__dart__list_tasks --limit 50

# Get specific task details
mcp__dart__get_task --id <task_id>

# Search tasks by title
mcp__dart__list_tasks --title "Language Service"

# Filter tasks by status
mcp__dart__list_tasks --status "To-do"
```

### Creating and Managing Tasks
```bash
# Create new task
mcp__dart__create_task --title "Task Title" --description "Full context" --status "To-do" --priority "High" --size "Medium" --tags '["Engineering", "Feature"]'

# Create subtask
mcp__dart__create_task --title "Subtask Title" --parentId <parent_task_id> --description "Subtask details"

# Update task
mcp__dart__update_task --id <task_id> --status "Doing"

# Add comment to task
mcp__dart__add_task_comment --taskId <task_id> --text "Progress update or notes"
```

### Task Management Rules
- **Single Source of Truth**: All tasks live in Dart, no external workplans
- **Rich Context**: Task descriptions contain full implementation details, background, alternatives
- **Subtask Hierarchy**: Use Dart's parentId feature for task breakdown (no manual numbering)
- **Status Updates**: Always update task status when starting/completing work
- **Deferred Tasks**: Use "Deferred" tag for tasks postponed 6+ months

**Complete Guidelines**: See `docs/task-management-guidelines.md` for detailed workflow and standards.

## Grafana MCP Integration

**Purpose:** Direct access to monitoring data for analysis and troubleshooting
**Usage:** Query metrics, logs, and traces programmatically

### Prometheus Metrics
```typescript
// Query auth service metrics
await queryPrometheus({
  datasourceUid: 'PBFA97CFB590B2093',
  expr: 'auth_service_login_attempts_total'
});
```

### Loki Logs
```typescript
// Search application logs  
await queryLokiLogs({
  datasourceUid: 'P8E80F9AEF21F6940',
  logql: '{service="auth-service", level="error"}'
});
```

## Claude Development Workflows

### Always Start with Observability
**We have comprehensive logging and monitoring - USE IT FIRST:**

1. **Check Service Status:**
```bash
# Verify monitoring stack is running
docker ps | grep -E "(grafana|prometheus|loki|tempo|pyroscope)"

# Check service health endpoints
curl http://localhost:3001/health  # auth-service
curl http://localhost:3002/health  # user-service
```

2. **Query Our Comprehensive Logs:**
```typescript
// Check recent logs before debugging
await queryLokiLogs({
  datasourceUid: 'P8E80F9AEF21F6940',
  logql: '{service="auth-service"}',
  limit: 10
});
```

### Development Tasks
1. **Code Analysis:** Search and analyze code patterns across services
2. **Configuration Management:** Update environment files and service configs
3. **Documentation:** Generate and update service documentation
4. **Testing:** Execute test suites and analyze results

### Debugging and Troubleshooting
1. **Service Health Check:** Use Grafana MCP to check service metrics and logs
2. **Check Task History:** Search Dart for previously resolved similar issues
3. **Container Status:** Use Docker commands to verify service states
4. **Configuration Review:** Read service configuration files and environment variables
5. **Log Analysis:** Query Loki for error patterns and service behavior

**Search Task History:**
```bash
# Search completed tasks for similar issues
mcp__dart__list_tasks --status "Done" --tags "Bug" --limit 10

# Search for tasks by component
mcp__dart__list_tasks --tags "typescript" --limit 10
```

### Monitoring and Analysis
1. **Performance Analysis:** Query Prometheus metrics for service performance
2. **Error Investigation:** Search logs for error patterns and root causes
3. **Business Analytics:** Analyze business event logs for insights
4. **Capacity Planning:** Monitor resource usage and growth patterns

## Best Practices for Claude Assistance

### Code Analysis and Development
- Always read service README files before making changes
- Check test coverage before implementing new features
- Verify observability integration when adding new services
- Use consistent patterns from existing services

### Debugging and Troubleshooting
- **Start with observability data** (logs, metrics, traces) - we have comprehensive logging
- Check service dependencies and health endpoints
- Verify environment configuration and connectivity
- Use test suites to isolate issues

### Documentation and Project Management
- **CRITICAL: Always update documentation when making changes** - docs must reflect current implementation
- **Resolve documentation contradictions immediately**:
  - When docs contradict implementation, investigate which is correct
  - Update either docs to match implementation OR implementation to match docs
  - Consider business impact, technical debt, and user expectations
  - Document the decision rationale
- **Create troubleshooting entries** for new issues encountered
- Maintain the development roadmap with discoveries
- Keep service-specific docs in appropriate locations
- **Update library README files** when changing functionality or configuration
- **Verify cross-references** between related documentation files

**When to Create Troubleshooting Entry:**
- Any issue that took >15 minutes to resolve
- Error messages not easily found in existing docs
- Environment-specific problems
- Issues likely to affect other developers

**Troubleshooting Entry Template:**
```markdown
## Symptoms
- What the user sees/experiences
- Exact error messages

## Environment
- OS, service, context where this occurs

## Root Cause
- Why this happens

## Solution
- Step-by-step resolution

## Prevention
- How to avoid in future
```

### Follow Project Conventions
- Check environment files for current configuration
- Verify shared library dependencies before making changes
- Update development roadmap when discovering new functionality

## Integration Notes

### Current Production Status
- **HTTP/2 Performance**: 60-80% improvement over axios with Got library
- **Caching Efficiency**: ~95% cache hit rate improvement with Redis integration
- **Event-Driven Architecture**: Complete HTTP lifecycle events via Redis Streams
- **Resilience Patterns**: Automatic circuit breaker protection for all external calls
- **Authentication**: Production-ready JWT validation with JWKS caching
- **Observability**: Full metrics, logging, and tracing across all libraries

### Key Technical Achievements
- **API Gateway Modernization**: Dynamic proxy replacing 3 fragmented controllers (~138 lines → unified)
- **Cross-Library Event Integration**: HTTP events automatically published to messaging system
- **Comprehensive Error Handling**: Type-safe error codes with correlation ID management
- **Service Client Patterns**: BaseServiceClient pattern eliminating HTTP boilerplate
- **Automatic Failure Isolation**: Circuit breakers protect against cascading failures

### Available Tools
- **Grafana MCP Integration**: Direct access to monitoring data for analysis
- **Bundle Optimization Testing**: 6x faster testing with webpack (3-5s vs 15-30s traditional)
- **Comprehensive Testing Strategy**: Unit + Integration tests with modern Jest 2024 patterns
- **Centralized Mock Architecture**: @libs/testing-utils MockFactory for consistent testing
- **Development Hot-Reload**: ~10-15s code-to-live feedback loops
- **Production Monitoring**: Full observability stack with metrics and distributed tracing

### Library Documentation & Testing Status
✅ **Complete Documentation + Testing Available:**
- `@libs/http/README.md` - HTTP/2 client, caching, circuit breakers | **Tests**: 25+ unit/integration
- `@libs/caching/README.md` - Redis integration, decorators, messaging | **Tests**: 35+ unit/integration  
- `@libs/messaging/README.md` - Event factory, Redis Streams, HTTP events | **Tests**: 45+ unit/integration
- `@libs/resilience/README.md` - Circuit breakers, state management, events | **Tests**: 30+ unit tests
- `@libs/observability/README.md` - Logging, metrics, tracing integration | **Tests**: 20+ unit tests
- `@libs/error-handling/README.md` - Error correlation, response building | **Tests**: 35+ unit tests
- `@libs/auth-common/README.md` - JWT guards, strategies, user context | **Tests**: 95+ unit/integration
- `@libs/keycloak-client/README.md` - JWKS caching, authentication flows | **Tests**: 31+ unit tests
- `services/api-gateway/docs/DYNAMIC-PROXY-ARCHITECTURE.md` - Modern proxy implementation