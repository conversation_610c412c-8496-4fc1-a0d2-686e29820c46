# Docker ignore file to improve build performance and reduce context size

# Dependencies - exclude all node_modules directories at any level
**/node_modules/
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Logs
logs/
*.log

# Build outputs - exclude all dist directories
**/dist/
dist/
build/
.next/

# Testing
coverage/
.nyc_output
.coverage

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
.dockerignore
Dockerfile*
docker-compose*.yml

# Temporary files
*.tmp
*.temp

# Documentation (unless needed for app)
README.md
docs/
*.md

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Prisma
.prisma/
