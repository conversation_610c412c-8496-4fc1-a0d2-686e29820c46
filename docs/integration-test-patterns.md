# Integration Test Patterns

## Overview

This document outlines standardized patterns for integration testing across the Polyrepo microservices architecture. Integration tests validate interactions between services and external dependencies using real infrastructure.

## Core Principles

### 1. Real Infrastructure Testing
- **Always use real services** (Keycloak, PostgreSQL, Redis)
- **No mocks for external dependencies** in integration tests
- **Validate actual service communication** and data flow
- **Test infrastructure health** before running tests

### 2. Service Boundary Validation
- **Test cross-service communication** (Auth ↔ User Service)
- **Validate data consistency** across service boundaries
- **Test failure scenarios** and error propagation
- **Verify circuit breaker** and resilience patterns

### 3. Observability Integration
- **Validate business events** are logged correctly
- **Check metrics collection** during operations
- **Verify distributed tracing** across services
- **Test log correlation** with unique identifiers

## Standard Integration Test Structure

### Basic Template

```typescript
// Example: auth-user-integration.spec.ts
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { TestEnvironment, TestDataGenerator, KeycloakTestUtils } from '@libs/testing-utils';
import request from 'supertest';

describe('Auth + User Service Integration', () => {
  let authApp: INestApplication;
  let userApp: INestApplication;
  let testId: string;

  beforeAll(async () => {
    // Environment setup
    TestEnvironment.setupEnvironment('integration', 'auth-user');
    testId = TestEnvironment.createTestId();

    // Setup auth service
    const authModule: TestingModule = await Test.createTestingModule({
      imports: [AuthModule], // Real modules, no mocks
    }).compile();

    authApp = authModule.createNestApplication();
    await authApp.init();

    // Setup user service  
    const userModule: TestingModule = await Test.createTestingModule({
      imports: [UserModule], // Real modules, no mocks
    }).compile();

    userApp = userModule.createNestApplication();
    await userApp.init();
  });

  afterAll(async () => {
    await authApp?.close();
    await userApp?.close();
  });

  describe('Cross-Service User Flow', () => {
    it('should create user in both auth and user services', async () => {
      const userData = TestDataGenerator.createRegistrationData({
        email: `test-${testId}@example.com`
      });

      // Register via auth service
      const authResponse = await request(authApp.getHttpServer())
        .post('/auth/register')
        .send(userData)
        .expect(201);

      expect(authResponse.body.userId).toBeDefined();
      
      // Verify user exists in user service
      const userResponse = await request(userApp.getHttpServer())
        .get(`/users/keycloak/${authResponse.body.keycloakId}`)
        .expect(200);

      expect(userResponse.body.email).toBe(userData.email);
    });

    it('should handle authentication across services', async () => {
      // Test actual JWT token flow
      const token = await KeycloakTestUtils.authenticateTestUser(
        `test-${testId}@example.com`,
        'testpassword123'
      );

      // Use token to access user service
      const profileResponse = await request(userApp.getHttpServer())
        .get('/users/me') // When implemented
        .set('Authorization', `Bearer ${token.access_token}`)
        .expect(200);

      expect(profileResponse.body.email).toBe(`test-${testId}@example.com`);
    });
  });
});
```

## Service-Specific Integration Patterns

### 1. Auth Service Integration Tests

**Focus Areas:**
- Keycloak integration (user creation, authentication)
- JWT token validation
- Role assignment and verification
- Password reset flows
- User status management

**Example Pattern:**

```typescript
describe('Auth Service Integration', () => {
  it('should create user in Keycloak and validate token', async () => {
    const userData = TestDataGenerator.createRegistrationData();
    
    // Register user
    const regResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send(userData)
      .expect(201);

    // Authenticate user
    const loginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: userData.email, password: userData.password })
      .expect(200);

    // Validate token structure
    expect(loginResponse.body.access_token).toBeDefined();
    expect(loginResponse.body.refresh_token).toBeDefined();
    
    // Verify token works for protected endpoints
    await request(app.getHttpServer())
      .get('/auth/admin-check')
      .set('Authorization', `Bearer ${loginResponse.body.access_token}`)
      .expect(403); // Regular user should not have admin access
  });

  it('should handle role assignment through Keycloak', async () => {
    // Test role-based access control with real roles
    const adminToken = await KeycloakTestUtils.authenticateTestUser(
      '<EMAIL>',
      'testpassword123'
    );

    await request(app.getHttpServer())
      .get('/auth/admin-check')
      .set('Authorization', `Bearer ${adminToken.access_token}`)
      .expect(200);
  });
});
```

### 2. User Service Integration Tests

**Focus Areas:**
- Database operations (Prisma)
- Data persistence and retrieval
- Soft delete functionality
- User profile management
- Search and filtering

**Example Pattern:**

```typescript
describe('User Service Integration', () => {
  it('should persist user data correctly', async () => {
    const userData = TestDataGenerator.createTestUser();
    
    // Create user
    const createResponse = await request(app.getHttpServer())
      .post('/users')
      .send(userData)
      .expect(201);

    const userId = createResponse.body.id;

    // Retrieve user
    const getResponse = await request(app.getHttpServer())
      .get(`/users/${userId}`)
      .expect(200);

    expect(getResponse.body.email).toBe(userData.email);
    expect(getResponse.body.isDeleted).toBe(false);

    // Test soft delete
    await request(app.getHttpServer())
      .delete(`/users/${userId}`)
      .expect(200);

    // Verify soft delete
    const deletedResponse = await request(app.getHttpServer())
      .get(`/users/${userId}`)
      .expect(200);

    expect(deletedResponse.body.isDeleted).toBe(true);
  });
});
```

### 3. API Gateway Integration Tests

**Focus Areas:**
- Service routing and proxying
- Authentication middleware
- Rate limiting and throttling
- Circuit breaker patterns
- Error handling and retries

**Example Pattern:**

```typescript
describe('API Gateway Integration', () => {
  it('should route requests to correct services', async () => {
    // Test auth service routing
    const authResponse = await request(app.getHttpServer())
      .get('/api/auth/health')
      .expect(200);

    expect(authResponse.body.service).toBe('auth-service');

    // Test user service routing
    const userResponse = await request(app.getHttpServer())
      .get('/api/users/health')
      .expect(200);

    expect(userResponse.body.service).toBe('user-service');
  });

  it('should handle authentication across all routes', async () => {
    const token = await KeycloakTestUtils.authenticateTestUser(
      '<EMAIL>',
      'password'
    );

    // Protected route should work with valid token
    await request(app.getHttpServer())
      .get('/api/users/me')
      .set('Authorization', `Bearer ${token.access_token}`)
      .expect(200);

    // Protected route should fail without token
    await request(app.getHttpServer())
      .get('/api/users/me')
      .expect(401);
  });
});
```

## Cross-Service Integration Patterns

### 1. Auth ↔ User Service Flow

```typescript
describe('Auth-User Cross-Service Integration', () => {
  it('should maintain data consistency across services', async () => {
    const testId = TestEnvironment.createTestId();
    const userData = TestDataGenerator.createRegistrationData({
      email: `test-${testId}@example.com`
    });

    // Register via auth service
    const authResponse = await request(authApp.getHttpServer())
      .post('/auth/register')
      .send(userData)
      .expect(201);

    // Verify user created in user service
    const userResponse = await request(userApp.getHttpServer())
      .get(`/users/keycloak/${authResponse.body.keycloakId}`)
      .expect(200);

    // Data should be consistent
    expect(userResponse.body.email).toBe(userData.email);
    expect(userResponse.body.firstName).toBe(userData.firstName);
    expect(userResponse.body.lastName).toBe(userData.lastName);

    // Test authentication with created user
    const loginResponse = await request(authApp.getHttpServer())
      .post('/auth/login')
      .send({ email: userData.email, password: userData.password })
      .expect(200);

    expect(loginResponse.body.access_token).toBeDefined();
  });
});
```

### 2. Service Failure Scenarios

```typescript
describe('Service Failure Handling', () => {
  it('should handle user service unavailability gracefully', async () => {
    // Simulate user service being down
    // (In real scenarios, this might involve stopping a container)
    
    const userData = TestDataGenerator.createRegistrationData();
    
    // Auth service should handle user service failure
    const response = await request(authApp.getHttpServer())
      .post('/auth/register')
      .send(userData);

    // Should either succeed with retry or fail gracefully
    expect([201, 503]).toContain(response.status);
    
    if (response.status === 503) {
      expect(response.body.message).toContain('User service unavailable');
    }
  });
});
```

## Observability Testing Patterns

### 1. Business Event Validation

```typescript
import { queryLokiLogs } from '@libs/testing-utils';

describe('Business Event Integration', () => {
  it('should log business events across services', async () => {
    const testId = TestEnvironment.createTestId();
    const userData = TestDataGenerator.createRegistrationData({
      email: `test-${testId}@example.com`
    });

    // Perform operation
    await request(app.getHttpServer())
      .post('/auth/register')
      .send(userData)
      .expect(201);

    // Wait for event processing
    await TestEnvironment.waitForProcessing(2000);

    // Validate business event logged
    const logs = await queryLokiLogs({
      datasourceUid: 'P8E80F9AEF21F6940',
      logql: `{service="auth-service"} |= "USER_REGISTERED" |= "${testId}"`,
      limit: 10
    });

    expect(logs.length).toBeGreaterThan(0);
    
    const event = JSON.parse(logs[0].line);
    expect(event.eventType).toBe('USER_REGISTERED');
    expect(event.userId).toBeDefined();
    expect(event.email).toBe(userData.email);
  });
});
```

### 2. Metrics Validation

```typescript
import { queryPrometheus } from '@libs/testing-utils';

describe('Metrics Integration', () => {
  it('should collect service metrics', async () => {
    // Perform operations that generate metrics
    await request(app.getHttpServer())
      .post('/auth/login')
      .send({ email: '<EMAIL>', password: 'password' })
      .expect(200);

    // Wait for metric collection
    await TestEnvironment.waitForProcessing(5000);

    // Query metrics
    const metrics = await queryPrometheus({
      datasourceUid: 'PBFA97CFB590B2093',
      expr: 'auth_service_login_attempts_total',
      queryType: 'instant',
      startTime: 'now-5m'
    });

    expect(metrics.length).toBeGreaterThan(0);
  });
});
```

## Test Environment Setup

### 1. Infrastructure Prerequisites

```typescript
// test-environment-validator.ts
export class IntegrationTestValidator {
  static async validateInfrastructure(): Promise<void> {
    const checks = [
      { name: 'Keycloak', url: 'http://localhost:8080/health' },
      { name: 'PostgreSQL', check: () => this.checkDatabase() },
      { name: 'Redis', check: () => this.checkRedis() },
      { name: 'User Service', url: 'http://localhost:3002/health' },
    ];

    for (const check of checks) {
      try {
        if (check.url) {
          await fetch(check.url);
        } else if (check.check) {
          await check.check();
        }
        console.log(`✅ ${check.name} is available`);
      } catch (error) {
        throw new Error(`❌ ${check.name} is not available: ${error.message}`);
      }
    }
  }

  private static async checkDatabase(): Promise<void> {
    // Database connectivity check
  }

  private static async checkRedis(): Promise<void> {
    // Redis connectivity check
  }
}
```

### 2. Test Data Management

```typescript
// integration-test-setup.ts
export class IntegrationTestSetup {
  static async createTestData(): Promise<TestData> {
    const testId = TestEnvironment.createTestId();
    
    return {
      testId,
      users: [
        TestDataGenerator.createTestUser({ email: `user1-${testId}@example.com` }),
        TestDataGenerator.createTestUser({ email: `user2-${testId}@example.com` }),
        TestDataGenerator.createTestAdmin({ email: `admin-${testId}@example.com` }),
      ],
      organizations: [
        TestDataGenerator.createTestOrganization({ name: `Org-${testId}` }),
      ],
    };
  }

  static async cleanupTestData(testId: string): Promise<void> {
    // Clean up users, organizations, and other test data
    // This ensures tests don't interfere with each other
  }
}
```

## Performance and Load Testing Patterns

### 1. Concurrent Request Testing

```typescript
describe('Concurrent Operations', () => {
  it('should handle concurrent user registrations', async () => {
    const testId = TestEnvironment.createTestId();
    const userPromises = Array.from({ length: 10 }, (_, i) =>
      request(app.getHttpServer())
        .post('/auth/register')
        .send(TestDataGenerator.createRegistrationData({
          email: `user${i}-${testId}@example.com`
        }))
    );

    const results = await Promise.allSettled(userPromises);
    
    // All requests should succeed
    const successful = results.filter(r => r.status === 'fulfilled').length;
    expect(successful).toBe(10);
  });
});
```

### 2. Circuit Breaker Testing

```typescript
describe('Circuit Breaker Integration', () => {
  it('should open circuit breaker on service failures', async () => {
    // Generate enough failures to trigger circuit breaker
    const failurePromises = Array.from({ length: 10 }, () =>
      request(app.getHttpServer())
        .post('/auth/invalid-endpoint')
        .expect(404)
    );

    await Promise.all(failurePromises);

    // Next request should be circuit-broken
    const response = await request(app.getHttpServer())
      .post('/auth/register')
      .send(TestDataGenerator.createRegistrationData());

    expect([503, 201]).toContain(response.status);
  });
});
```

## Best Practices

### 1. Test Isolation
- Use unique test identifiers for all test data
- Clean up resources after each test
- Avoid shared state between tests

### 2. Error Handling
- Test both success and failure scenarios
- Validate error responses and status codes
- Test network failures and timeouts

### 3. Performance Considerations
- Use realistic data volumes
- Test with concurrent operations
- Monitor resource usage during tests

### 4. Observability
- Always validate business events
- Check metrics collection
- Verify distributed tracing

### 5. Infrastructure Management
- Validate infrastructure health before tests
- Use test-specific configuration
- Implement proper cleanup procedures

## Common Pitfalls

### 1. Avoid These Anti-Patterns
❌ Using mocks for external services in integration tests
❌ Shared test data without unique identifiers
❌ Not cleaning up test resources
❌ Ignoring observability validation
❌ Testing only happy path scenarios

### 2. Follow These Guidelines
✅ Always use real infrastructure
✅ Generate unique test identifiers
✅ Clean up after each test
✅ Validate business events and metrics
✅ Test error scenarios and edge cases
✅ Use standardized testing utilities from `@libs/testing-utils`

## Integration with CI/CD

### Environment-Specific Configuration

```yaml
# .github/workflows/integration-tests.yml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: yarn install
      
      - name: Build shared libraries
        run: yarn build:libs
      
      - name: Start Keycloak
        run: docker-compose up -d keycloak
      
      - name: Wait for services
        run: ./scripts/wait-for-services.sh
      
      - name: Run integration tests
        run: yarn test:integration
```

This comprehensive integration testing pattern ensures robust validation of service interactions while maintaining clear boundaries and leveraging the existing infrastructure effectively.