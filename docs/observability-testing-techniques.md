# Observability Testing Techniques

This document describes advanced testing techniques for validating observability integration in the Polyrepo project, focusing on direct observability stack validation rather than mocked testing.

## Overview

Observability testing ensures that:
1. **Logs reach their destination** - Services correctly send structured logs to Loki
2. **Metrics are collected** - Prometheus receives and stores application metrics
3. **Traces are captured** - OpenTelemetry traces reach Tempo
4. **Business events are logged** - Domain-specific events are properly recorded
5. **Test results are observable** - Test execution data flows into the observability stack

## Testing Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Test Execution │    │  Application    │    │  Observability  │
│                 │    │  Under Test     │    │  Stack          │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Test Suite  │ │    │ │ Service     │ │    │ │    Loki     │ │
│ │             │ │────┼▶│ Code        │ │────┼▶│   (Logs)    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Validation  │ │    │ │ Observ.     │ │    │ │ Prometheus  │ │
│ │ Queries     │ │────┼─┼▶│ Client     │ │────┼▶│ (Metrics)   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│                 │    │                 │    │ ┌─────────────┐ │
│                 │    │                 │    │ │   Tempo     │ │
│                 │    │                 │    │ │  (Traces)   │ │
│                 │    │                 │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Technique 1: Direct Observability Validation

### Concept

After performing operations in tests, directly query the observability stack to verify that expected logs, metrics, and traces were generated. This validates the complete observability pipeline.

### Implementation Pattern

```typescript
// Example: Auth Service Login Test with Observability Validation
describe('AuthController Login with Observability', () => {
  let observabilityValidator: ObservabilityTestValidator;
  
  beforeEach(async () => {
    observabilityValidator = new ObservabilityTestValidator({
      lokiUrl: 'http://localhost:3100',
      prometheusUrl: 'http://localhost:9090',
      tempoUrl: 'http://localhost:3201'
    });
    
    // Wait for stack to be ready
    await observabilityValidator.waitForStackReady();
  });

  it('should log successful login events to Loki', async () => {
    const correlationId = randomUUID();
    const testUser = await createTestUser();
    
    // Perform the operation
    const loginResult = await request(app.getHttpServer())
      .post('/auth/login')
      .set('X-Correlation-ID', correlationId)
      .send({
        email: testUser.email,
        password: testUser.password
      })
      .expect(200);

    // Wait for logs to propagate (configurable delay)
    await observabilityValidator.waitForLogPropagation();

    // Validate logs were written to Loki
    const loginLogs = await observabilityValidator.queryLogs({
      query: `{service="auth-service", level="info"} |= "login" |= "${correlationId}"`,
      timeRange: { from: 'now-5m', to: 'now' }
    });

    expect(loginLogs).toHaveLength(1);
    expect(loginLogs[0]).toMatchObject({
      level: 'info',
      message: expect.stringContaining('User login successful'),
      correlationId,
      userId: expect.any(String),
      service: 'auth-service'
    });
  });

  it('should increment login metrics in Prometheus', async () => {
    const initialCount = await observabilityValidator.queryMetric(
      'auth_service_login_attempts_total{status="success"}'
    );

    // Perform login
    await performSuccessfulLogin();

    // Wait for metrics scraping
    await observabilityValidator.waitForMetricsScrape();

    const finalCount = await observabilityValidator.queryMetric(
      'auth_service_login_attempts_total{status="success"}'
    );

    expect(finalCount).toBe(initialCount + 1);
  });
});
```

### Observability Test Validator Implementation

```typescript
export class ObservabilityTestValidator {
  constructor(private config: ObservabilityConfig) {}

  async queryLogs(params: LogQueryParams): Promise<LogEntry[]> {
    const response = await fetch(`${this.config.lokiUrl}/loki/api/v1/query_range`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        query: params.query,
        start: params.timeRange.from,
        end: params.timeRange.to,
        limit: String(params.limit || 100)
      })
    });

    if (!response.ok) {
      throw new Error(`Loki query failed: ${response.statusText}`);
    }

    const data = await response.json();
    return this.parseLokiResponse(data);
  }

  async queryMetric(query: string): Promise<number> {
    const response = await fetch(`${this.config.prometheusUrl}/api/v1/query`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({ query })
    });

    if (!response.ok) {
      throw new Error(`Prometheus query failed: ${response.statusText}`);
    }

    const data = await response.json();
    return this.parsePrometheusResponse(data);
  }

  async waitForStackReady(timeout = 30000): Promise<void> {
    const checks = [
      { name: 'Loki', url: `${this.config.lokiUrl}/ready` },
      { name: 'Prometheus', url: `${this.config.prometheusUrl}/-/ready` },
      { name: 'Tempo', url: `${this.config.tempoUrl}/api/search` }
    ];

    await Promise.all(checks.map(check => this.waitForService(check, timeout)));
  }

  async waitForLogPropagation(delay = 2000): Promise<void> {
    // Configurable delay to allow logs to reach Loki
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  async waitForMetricsScrape(delay = 5000): Promise<void> {
    // Wait for Prometheus scraping interval
    await new Promise(resolve => setTimeout(resolve, delay));
  }
}
```

### Test Categories by Observability Type

**Log Validation Tests:**
- Structured log format verification
- Correlation ID propagation
- Business event logging
- Error log generation
- Log level filtering

**Metrics Validation Tests:**
- Counter increment verification
- Histogram bucket distribution
- Gauge value accuracy
- Custom business metrics
- Service health metrics

**Trace Validation Tests:**
- Span creation and completion
- Parent-child span relationships
- Cross-service trace propagation
- Trace sampling verification
- Custom span attributes

## Technique 2: Test Results Observability

### Concept

Integrate test execution data into the observability stack, allowing test results to be monitored, analyzed, and alerted on through Grafana dashboards.

### Implementation Pattern

```typescript
export class TestResultsObservabilityReporter {
  constructor(
    private logger: ObservabilityLogger,
    private metricsService: MetricsService
  ) {}

  async reportTestSuiteStart(suiteName: string, testCount: number): Promise<void> {
    this.logger.info({
      message: 'Test suite started',
      testSuite: suiteName,
      testCount,
      timestamp: new Date().toISOString(),
      type: 'test_suite_start'
    });

    this.metricsService.incrementCounter('test_suite_starts_total', {
      suite: suiteName
    });
  }

  async reportTestResult(result: TestResult): Promise<void> {
    this.logger.info({
      message: 'Test completed',
      testName: result.name,
      testSuite: result.suite,
      status: result.status,
      duration: result.duration,
      error: result.error,
      timestamp: new Date().toISOString(),
      type: 'test_result'
    });

    this.metricsService.incrementCounter('test_executions_total', {
      suite: result.suite,
      status: result.status
    });

    this.metricsService.recordHistogram('test_duration_seconds', result.duration, {
      suite: result.suite,
      test: result.name
    });
  }

  async reportTestSuiteComplete(suite: TestSuiteResult): Promise<void> {
    this.logger.info({
      message: 'Test suite completed',
      testSuite: suite.name,
      totalTests: suite.totalTests,
      passed: suite.passed,
      failed: suite.failed,
      skipped: suite.skipped,
      duration: suite.duration,
      coverage: suite.coverage,
      timestamp: new Date().toISOString(),
      type: 'test_suite_complete'
    });

    this.metricsService.recordGauge('test_coverage_percentage', suite.coverage, {
      suite: suite.name
    });
  }
}
```

### Jest Integration

```typescript
// jest-observability-reporter.ts
export class JestObservabilityReporter implements Reporter {
  private reporter: TestResultsObservabilityReporter;

  constructor() {
    this.reporter = new TestResultsObservabilityReporter(
      createLogger('test-reporter'),
      createMetricsService()
    );
  }

  onRunStart(results: AggregatedResult): void {
    this.reporter.reportTestSuiteStart('jest', results.numTotalTestSuites);
  }

  onTestResult(test: Test, testResult: TestResult): void {
    testResult.testResults.forEach(result => {
      this.reporter.reportTestResult({
        name: result.fullName,
        suite: test.path,
        status: result.status,
        duration: result.duration || 0,
        error: result.failureMessages?.[0]
      });
    });
  }

  onRunComplete(contexts: Set<Context>, results: AggregatedResult): void {
    this.reporter.reportTestSuiteComplete({
      name: 'jest',
      totalTests: results.numTotalTests,
      passed: results.numPassedTests,
      failed: results.numFailedTests,
      skipped: results.numPendingTests,
      duration: Date.now() - results.startTime,
      coverage: this.calculateCoverage(results.coverageMap)
    });
  }
}
```

## Test Environment Configuration

### Environment-Specific Settings

```typescript
// observability-test.config.ts
export const observabilityTestConfig = {
  unit: {
    enableObservability: false,
    mockObservabilityStack: true,
    reportResults: false
  },
  integration: {
    enableObservability: true,
    mockObservabilityStack: false,
    reportResults: true,
    stackUrls: {
      loki: 'http://localhost:3100',
      prometheus: 'http://localhost:9090',
      tempo: 'http://localhost:3201'
    }
  },
  e2e: {
    enableObservability: true,
    mockObservabilityStack: false,
    reportResults: true,
    stackUrls: {
      loki: 'http://localhost:3100',
      prometheus: 'http://localhost:9090',
      tempo: 'http://localhost:3201'
    }
  }
};
```

### Test Setup Utilities

```typescript
export class ObservabilityTestSetup {
  static async setupObservabilityTesting(testType: TestType): Promise<void> {
    const config = observabilityTestConfig[testType];
    
    if (!config.enableObservability) {
      // Mock observability services
      jest.mock('@libs/observability');
      return;
    }

    // Wait for observability stack
    const validator = new ObservabilityTestValidator(config.stackUrls);
    await validator.waitForStackReady();

    if (config.reportResults) {
      // Setup test results reporting
      setupTestResultsReporting();
    }
  }

  static async cleanupObservabilityTesting(): Promise<void> {
    // Cleanup test data from observability stack if needed
    await this.cleanupTestLogs();
    await this.cleanupTestMetrics();
  }

  private static async cleanupTestLogs(): Promise<void> {
    // Implementation depends on Loki retention policies
    // Could use test-specific labels for cleanup
  }
}
```

## Grafana Dashboard Integration

### Test Results Dashboard

Create Grafana dashboards to visualize test execution data:

```json
{
  "dashboard": {
    "title": "Test Execution Monitoring",
    "panels": [
      {
        "title": "Test Success Rate",
        "type": "stat",
        "targets": [{
          "expr": "rate(test_executions_total{status=\"passed\"}[5m]) / rate(test_executions_total[5m]) * 100"
        }]
      },
      {
        "title": "Test Duration Trends",
        "type": "graph",
        "targets": [{
          "expr": "histogram_quantile(0.95, test_duration_seconds)"
        }]
      },
      {
        "title": "Test Coverage by Suite",
        "type": "bargauge",
        "targets": [{
          "expr": "test_coverage_percentage"
        }]
      }
    ]
  }
}
```

### Alerting Rules

```yaml
# test-alerts.yml
groups:
  - name: test_execution_alerts
    rules:
      - alert: TestFailureRateHigh
        expr: rate(test_executions_total{status="failed"}[5m]) / rate(test_executions_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High test failure rate detected"
          description: "Test failure rate is {{ $value | humanizePercentage }} over the last 5 minutes"

      - alert: TestCoverageLow
        expr: test_coverage_percentage < 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Test coverage below threshold"
          description: "Test coverage is {{ $value }}% for suite {{ $labels.suite }}"
```

## Usage Guidelines

### When to Use Observability Testing

**Use for Integration/E2E Tests:**
- Validating real observability pipeline
- Testing cross-service trace propagation
- Verifying business event logging
- Monitoring test execution trends

**Avoid for Unit Tests:**
- Adds unnecessary complexity
- Increases test execution time
- Unit tests should focus on logic validation

### Test Execution Strategy

1. **Fast Unit Tests**: Run with mocked observability
2. **Integration Tests**: Enable observability validation
3. **E2E Tests**: Full observability pipeline testing
4. **CI/CD Pipeline**: Collect and analyze test observability data

### Performance Considerations

- **Async Validation**: Don't block test execution waiting for logs
- **Configurable Delays**: Tune propagation delays based on stack performance
- **Selective Testing**: Only validate observability for critical test cases
- **Cleanup Strategy**: Implement test data cleanup to prevent stack bloat

## Implementation Roadmap

### Phase 1: Foundation
- [ ] Create `ObservabilityTestValidator` utility
- [ ] Implement basic log/metric query capabilities
- [ ] Add observability test configuration

### Phase 2: Integration
- [ ] Integrate with Jest test runner
- [ ] Create test results observability reporter
- [ ] Setup Grafana dashboards for test monitoring

### Phase 3: Advanced Features
- [ ] Implement trace validation testing
- [ ] Add alerting for test execution anomalies
- [ ] Create automated test observability cleanup

### Phase 4: Documentation and Training
- [ ] Create observability testing best practices guide
- [ ] Add examples for each service
- [ ] Setup CI/CD integration guidelines

## Conclusion

Observability testing provides confidence that the entire observability pipeline works correctly in realistic conditions. By combining direct stack validation with test results monitoring, we ensure both application observability and testing process observability, creating a comprehensive testing strategy that scales with system complexity.

This approach moves beyond traditional mocking to validate real observability behavior while providing valuable insights into testing process health and trends.