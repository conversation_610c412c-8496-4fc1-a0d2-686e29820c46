# Keycloak Realm Accessibility Issue

## Issue Summary
Keycloak realms are created successfully but HTTP endpoints return 404 when accessed from other containers or host, preventing authentication flows.

## Symptoms
- Keycloak container is healthy and accessible on base URL
- Realm configuration shows realms exist when accessed via kcadm from inside container
- HTTP requests to realm endpoints return 404 from external containers
- Auth service times out when trying to authenticate users
- All realm endpoints fail: `/realms/{realm}/.well-known/openid_configuration`

## Investigation Findings

### ✅ Working Components
1. **Keycloak Container**: Healthy, running on port 8080
2. **Database**: PostgreSQL connection working
3. **Realm Creation**: Both `polyrepo-realm` and `polyrepo-test` created successfully
4. **User Creation**: Test users exist with correct credentials
5. **Client Configuration**: Clients configured with proper secrets and grants
6. **Internal Admin Access**: kcadm works from inside Keycloak container

### ❌ Failing Components
1. **Realm HTTP Endpoints**: All return 404 from external access
2. **Auth Service**: Times out connecting to Keycloak token endpoints
3. **Host Access**: Cannot access realm endpoints from host system

### Configuration Details
```yaml
# Keycloak Environment (docker-compose.bundled-dev-volume.yml)
KC_PROXY: edge
KC_HOSTNAME_STRICT: "false"
KC_HOSTNAME_STRICT_HTTPS: "false"
KC_HTTP_ENABLED: "true"
KC_HEALTH_ENABLED: "true"
```

### Test Results
```bash
# ✅ Base connectivity works
curl http://localhost:8080/health → 200 OK

# ❌ Realm endpoints fail
curl http://localhost:8080/realms/master/.well-known/openid_configuration → 404
curl http://localhost:8080/realms/polyrepo-realm/.well-known/openid_configuration → 404

# ✅ Admin access works
docker exec kcadm get realms → Shows all realms exist
```

### Error Patterns
- Keycloak logs show successful realm creation
- No error logs when accessing realm endpoints
- Network connectivity works for base endpoints
- Circuit breaker not triggering (no upstream connection)

## Potential Root Causes

### 1. Frontend URL Configuration
Keycloak might need explicit frontend URL configuration for realms to be accessible externally.

### 2. Proxy Configuration Issues
`KC_PROXY: edge` might be interfering with direct realm access patterns.

### 3. Development vs Production Mode
`start-dev` command might have different realm exposure behavior.

### 4. Network/Hostname Resolution
Docker internal vs external hostname resolution affecting realm URL generation.

## RESOLUTION ✅

### Root Cause Identified
**Architecture Mismatch**: Auth service was using outdated manual circuit breaker pattern instead of the reworked automatic HTTP lib integration.

### Issues Found and Fixed
1. **Manual Circuit Breaker Usage** (Fixed ✅)
   - Auth service manually creating circuit breakers with `circuitBreakerService.getCircuitBreaker()`
   - Should use automatic protection via HttpClientService with `serviceName` parameter
   - **Fix**: Removed manual circuit breaker code, let HTTP lib handle protection automatically

2. **Wrong Realm Usage** (Fixed ✅) 
   - Initial investigation revealed auth service hitting `/realms/master/` instead of configured realm
   - **Root cause**: Manual circuit breaker was bypassing proper HTTP flow
   - **Fix**: Removing manual circuit breaker restored proper realm configuration

3. **Connectivity Issues** (Fixed ✅)
   - Initial 404 errors on realm endpoints were side effect of configuration issues
   - **Fix**: Proper HTTP lib integration resolved connectivity

### Current Status
- ✅ **HTTP Connectivity**: Working (auth service reaches Keycloak successfully)
- ✅ **Realm Configuration**: Using correct `polyrepo-test` realm
- ✅ **Circuit Breaker**: Automatic protection via HTTP lib
- ✅ **Authentication**: RESOLVED - Password grant working fully

### Final Resolution: Keycloak 24.0 User Profile Requirements
**Root Cause**: Keycloak 24.0 introduced breaking changes requiring email, firstName, and lastName fields for token generation.

**Symptoms**:
- 400 Bad Request on password grant
- Keycloak logs showing `error="resolve_required_actions"`
- Users could be created but token generation failed

**Solution Applied**:
1. **Updated User Creation Script** (`configure-keycloak-test.sh:169`):
   ```bash
   # Before (missing required fields)
   $KCADM create users -r "$realm" -s username="$username" -s enabled=true -s emailVerified=true
   
   # After (includes required fields for Keycloak 24.0)
   $KCADM create users -r "$realm" -s username="$username" -s email="$username" -s firstName="Test" -s lastName="User" -s enabled=true -s emailVerified=true
   ```

2. **Rebuilt keycloak-setup container** with updated script
3. **Verified user fields**: Users now have email, firstName, lastName populated

**Test Results**:
- ✅ Direct password grant: `<EMAIL>` / `testpassword` → Valid JWT
- ✅ Auth service `/auth/login` → Valid JWT with user info and roles
- ✅ Both access_token and refresh_token provided
- ✅ Token includes user profile: name="Test User", email="<EMAIL>"

### Key Learnings
- **Keycloak 24.0 Breaking Change**: User profile fields now required for token generation
- **Setup Scripts Matter**: Container rebuild required for configuration changes
- **Field Requirements**: email, firstName, lastName are effectively required even if not marked as such
- **Web Search Value**: Community knowledge helped identify version-specific issues

### Additional Issues and Resolutions

#### Circuit Breaker Issue: Master Realm Hardcoding (June 2025)
**Root Cause**: KeycloakClientService had hardcoded `/realms/master/` in `getAdminAccessToken()` method, causing:
1. Admin token requests to fail (401 Unauthorized from master realm)
2. Circuit breaker protection to trigger due to repeated failures
3. Registration endpoint to hang/timeout

**Symptoms**:
- Registration endpoint hangs for 30+ seconds
- Observability logs show: `Starting POST http://keycloak:8080/realms/master/protocol/openid-connect/token`
- HTTP retries with 401 responses
- Circuit breaker triggers after multiple failures

**Resolution Applied**:
1. **Fixed KeycloakClientModule realm configuration**: Changed `polyrepo-test-realm` → `polyrepo-test`
2. **Fixed hardcoded master realm**: Updated `getAdminAccessToken()` to use `${this.realm}` instead of `master`
3. **Updated service account permissions**: Added `realm-admin` role to `auth-service-test-client`

**Files Modified**:
- `/libs/keycloak-client/src/keycloak-client.module.ts:87` - Fixed realm name
- `/libs/keycloak-client/src/keycloak-client.service.ts:382` - Fixed admin token endpoint
- `/infrastructure/local-dev/keycloak-setup/configure-keycloak-test.sh:14` - Added realm-admin role

**Prevention**:
- Always use configurable realm variables instead of hardcoded values
- Test admin operations in both production and test realms
- Monitor observability logs for hardcoded endpoint patterns

## Related Files
- `/infrastructure/local-dev/docker-compose.bundled-dev-volume.yml`
- `/services/auth-service/.env.bundling`
- `/libs/keycloak-client/src/keycloak-client.service.ts`
- `/services/auth-service/src/auth/services/keycloak.service.ts`