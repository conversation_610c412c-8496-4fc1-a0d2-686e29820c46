# Task Management Guidelines

## Overview
This document defines how we organize, track, and execute tasks in the polyrepo project using Dart as our single source of truth. All task planning, tracking, and documentation happens in Dart - no separate workplan files.

## Task Organization Principles

### 1. **Single Source of Truth**
- **All tasks live in Dart workspace** - no external workplan files
- **Rich descriptions contain full context** - implementation details, background, alternatives
- **Cross-references use Dart task links** - link related tasks directly
- **Historical decisions preserved** - capture why approaches were chosen

### 2. **Task Status Lifecycle**
```
To-do → Doing → Done
  ↓  
[Deferred] (via tag)
```

**Status Definitions:**
- `To-do`: Ready to start, all dependencies met, current priority
- `Doing`: Currently being worked on (limit: 1-2 tasks per person)  
- `Done`: Completed and verified against acceptance criteria

**Special Handling:**
- `Deferred`: Use "Deferred" tag for tasks that are good to do someday, but not important in near term (6+ months)

### 3. **Task Decomposition Rules**
- **Maximum Task Size**: No task should take more than 1-2 weeks
- **Subtask Granularity**: Each subtask completable in 1-3 days
- **Atomic Deliverables**: Clear, testable completion criteria
- **Implementation Context**: Include technical approach and alternatives considered

## Task Information Structure

### Core Fields (Required in Dart)
```
**Task Title**: Clear, actionable title following naming conventions
**Status**: Current status (To-do/Doing/Done)
**Priority**: High/Medium/Low based on impact and urgency  
**Size**: Effort estimate (Small/Medium/Large/Extra Large)
**Tags**: Domain tags (Engineering, Product, Testing, etc.)
**Parent Task**: Use parentId for subtask hierarchy (Dart's built-in feature)
**Assignee**: Person responsible (optional)
**Description**: Full context including:
  - Background and motivation
  - Technical approach and implementation details
  - Subtasks breakdown (use Dart subtasks or description checkboxes)
  - Acceptance criteria
  - Dependencies and blockers
  - Alternative approaches considered
  - Implementation notes and decisions
```

### Description Template
```markdown
## Context & Background
Why this task exists, business/technical motivation

## Technical Approach
High-level implementation strategy, architecture decisions

## Subtasks
1. [ ] Specific actionable item (1-3 days)
2. [ ] Another specific actionable item  
3. [ ] Testing and verification steps
4. [ ] Documentation updates

## Implementation Details
- Code examples where helpful
- Integration points with existing systems
- Performance/security considerations
- Alternative approaches considered and why rejected

## Dependencies
- **Hard**: Must complete Task X first because...
- **Soft**: Would benefit from Task Y being done first
- **External**: Waiting for team/system Z

## Acceptance Criteria
- [ ] Specific, testable requirement
- [ ] Performance benchmark (if applicable)
- [ ] Integration tests pass
- [ ] Documentation updated
- [ ] Code review completed

## Related Tasks
@mention other Notion tasks that are connected
```

## Task Organization Strategy

### Task Hierarchy with Dart's Subtask Feature
- **Main Tasks**: Top-level deliverables and features (use descriptive titles)
- **Subtasks**: Use Dart's built-in subtask feature via `parentId` for task breakdown
- **No Manual Numbering**: Dart automatically manages task IDs and relationships

### Task Breakdown Rules
- **Large Tasks**: Break into subtasks using Dart's subtask feature for better tracking
- **Subtask Creation**: Create subtasks when work takes 3+ days or needs independent tracking
- **Simple Subtasks**: Use description checkboxes for 1-2 day items that don't need separate tracking
- **Hierarchical Organization**: Leverage Dart's parent-child relationships for project structure

### Dart Subtask Benefits
- **Automatic ID Management**: No manual numbering conflicts or gaps
- **Built-in Relationships**: Parent tasks automatically track subtask progress
- **Flexible Organization**: Easy to reorganize task hierarchies as needs change
- **Progress Tracking**: Visual progress indicators for parent tasks

### Examples
```
Language Service Implementation (Main Task)
├── API Design and Data Modeling (Subtask)
├── Core Business Logic Implementation (Subtask)
├── Integration with Authentication (Subtask)
└── Testing and Documentation (Subtask)

Frontend Architecture & Applications (Main Task)
├── Technology Stack Decisions (Subtask)
├── Component Library Setup (Subtask)
└── Application Implementation (Subtask)
```

## Task Naming Conventions

### Format Pattern
`[Service/Component] - [Action] [Object/Feature]`

### Action Verbs (Start with these)
- **Implement**: Create new functionality
- **Enhance**: Improve existing functionality  
- **Integrate**: Connect with other systems
- **Design**: Plan architecture/approach
- **Setup**: Initial configuration/scaffolding
- **Add**: Include new capability
- **Fix**: Resolve bugs or issues
- **Refactor**: Improve code structure
- **Test**: Add testing coverage
- **Document**: Create/update documentation

### Good Examples
- `Language Service - Implement Core API`
- `Frontend - Setup Component Library`
- `API Gateway - Integrate Swagger Aggregation`
- `User Service - Add Rate Limiting`
- `Infrastructure - Fix Docker Bundling Issues`

### Bad Examples
- `Work on language stuff` (vague)
- `API improvements` (no specific action)
- `Frontend` (no action or scope)

## Subtask Management Strategy

### When to Create Dart Subtasks (using parentId)
- **Duration**: Subtask takes 3+ days
- **Independence**: Can be worked on by different people or in parallel
- **Complexity**: Requires its own planning and acceptance criteria
- **Tracking**: Needs independent progress monitoring and status updates
- **Assignment**: Different assignees than parent task

### When to Use Description Checkboxes
- **Duration**: Subtask takes 1-2 days
- **Sequential**: Must be done by same person in order
- **Simple**: Clear, straightforward work items
- **Integrated**: Part of larger technical implementation
- **Dependencies**: Tightly coupled to parent task completion

### Dart Subtask Creation Process
```typescript
// Example: Creating subtasks in Dart
await createTask({
  title: "API Design and Data Modeling",
  parentId: "VXsOfarSWZ2t", // Parent task ID
  description: "Design REST API endpoints and data models for language service",
  status: "To-do",
  priority: "High",
  size: "Medium",
  tags: ["Engineering", "Feature"]
});
```

### Example Decision Making
```
❌ Dart Subtask: "Add database migration script" (1 day, simple)
✅ Description Checkbox: "[ ] Add database migration script"

✅ Dart Subtask: "Frontend User Authentication Integration" (5 days, complex)
❌ Description Checkbox: Too complex for simple checkbox

✅ Dart Subtask: "API Gateway Swagger Integration" (4 days, can be worked independently)
✅ Dart Subtask: "Request Correlation ID Implementation" (3 days, different developer)
```

## Labeling System

### Standard Tags (Dart Compatible)
**Domain Tags:**
- `Engineering` - Backend services, APIs, infrastructure
- `Frontend` - UI/UX, client-side applications
- `Product` - Product features and business logic
- `Testing` - Unit tests, integration tests, E2E tests
- `Documentation` - READMEs, guides, API docs
- `Security` - Authentication, authorization, compliance
- `Performance` - Optimization, monitoring, scalability

**Type Tags:**
- `Feature` - New functionality
- `Enhancement` - Improvement to existing functionality
- `Bug` - Fix for defective behavior
- `Refactor` - Code improvement without functional changes
- `Research` - Investigation or planning work
- `Maintenance` - Keeping systems running/updated
- `Technical-Debt` - Technical debt resolution and cleanup

**Additional Tags:**
- `infrastructure` - DevOps, deployment, shared libraries
- `database` - Data modeling, migrations, queries
- `api-gateway` - Gateway routing and features
- `auth-service` - Authentication/authorization features
- `user-service` - User management functionality
- `Deferred` - Tasks postponed for future iterations (6+ months)

### Tagging Guidelines
- **Use 2-4 tags per task** (don't over-tag)
- **Always include domain tag** (Engineering/Frontend/Product)
- **Include service-specific tags if applicable**
- **Add type tag for clarity** (Feature/Bug/Enhancement)
- **Use "Deferred" tag instead of status** for postponed tasks

## Size Estimation

### Size Categories (Dart Compatible)
- **Small**: 1-2 days, well-understood, minimal complexity
- **Medium**: 3-5 days, some complexity or unknowns
- **Large**: 1-2 weeks, significant complexity or multiple components
- **Extra Large**: 2+ weeks - should be decomposed into subtasks using parentId

### Sizing Guidelines
- **Consider complexity**, not just time
- **Account for unknowns** and potential blockers
- **Include testing and documentation** time
- **If Extra Large size**: Break down into subtasks using Dart's parentId feature

## Task Categories and Treatment Patterns

### 1. **Infrastructure Enhancement Tasks**
**Pattern**: Analyze → Design → Implement → Integrate → Document
- Review existing libs/ implementations
- Design enhancement approach  
- Implement in shared libraries
- Integrate with services
- Update templates and docs

### 2. **New Service Implementation Tasks**  
**Pattern**: Plan → Core → Integrate → Polish → Deploy
- API design and data modeling
- Core business logic implementation
- Integration with other services  
- Testing, observability, documentation
- Deployment and monitoring setup

### 3. **Frontend Development Tasks**
**Pattern**: Architect → Foundation → Implement → Integrate
- Technology and structure decisions
- Shared component library setup
- Application-specific implementation
- Backend service integration

### 4. **Deferred Tasks**
Tasks marked as "Deferred" are:
- **Good ideas** but not critical for current goals
- **Future enhancements** that can wait 6+ months
- **Nice-to-have features** that don't block other work
- **Optimization tasks** that aren't performance bottlenecks

**Examples of Deferred tasks:**
- "Implement AI-powered content recommendations"
- "Add advanced analytics dashboard"
- "Create mobile app versions"
- "Optimize database queries for 10x scale"

## Migration from Workplans

### Process
1. **Extract all tasks** from workplan documents
2. **Decompose large tasks** into manageable subtasks
3. **Preserve all context** in rich Notion descriptions
4. **Set appropriate status**:
   - Current priorities → "To Do" 
   - Future nice-to-haves → "Deferred"
   - Already completed → "Done"
5. **Delete workplan files** once migration complete

### Context Preservation
- Include original workplan content in description
- Maintain technical implementation details
- Preserve alternative approaches that were considered
- Keep dependency relationships and reasoning

## Quality Standards

### Task Completeness Checklist
- [ ] Clear, actionable title
- [ ] Complete description with all context
- [ ] Properly decomposed subtasks
- [ ] Specific acceptance criteria
- [ ] Dependencies identified and justified
- [ ] Appropriate priority and status
- [ ] Related tasks cross-referenced

### Acceptance Criteria Guidelines
**Good Example:**
```
- [ ] API Gateway aggregates Swagger docs from auth-service, user-service, and 1+ other services
- [ ] Combined documentation accessible at GET /docs/swagger  
- [ ] Response time < 200ms for documentation generation
- [ ] Swagger UI renders correctly with service filtering dropdown
- [ ] Integration tests verify doc aggregation for all services
- [ ] Documentation updated with setup instructions
```

**Bad Example:**
```
- [ ] Implement Swagger aggregation
- [ ] Make it work properly
- [ ] Add some tests
```

## Daily Workflow

### Before Starting Work
1. Review task description and context fully
2. Verify all dependencies are met
3. Update status to "In Progress"
4. Break down approach if needed

### During Work
1. Update progress in task comments
2. Document decisions and blockers as they occur
3. Create new tasks for discovered work
4. Keep related tasks informed of changes

### After Completion
1. Verify all acceptance criteria met
2. Complete final testing and documentation
3. Update status to "Done"
4. Update any dependent tasks
5. Reflect on what was learned

## Tool Usage - Notion Database

### Database Structure

**Main Database: "Project Tasks" (Active Work)**
```
Project Tasks Database:
├── Task Number (Text - hierarchical numbering)
├── Task Name (Title - following naming conventions)
├── Status (Select: To Do, In Progress, Blocked, Deferred)
├── Priority (Select: High, Medium, Low)
├── Size (Select: S, M, L, XL)
├── Labels (Multi-select: frontend, backend, infrastructure, feature, etc.)
├── Parent Task (Relation - for subtask hierarchy)
├── Assignee (Person - optional)
├── Description (Rich Text - contains full context)
├── Due Date (Date - optional)
```

**Archive Database: "Completed Tasks" (Historical Record)**
```
Completed Tasks Database:
├── Task Number (Text - preserved from original)
├── Task Name (Title - original name)
├── Completed Date (Date - when marked done)
├── Size (Select - original estimate)
├── Labels (Multi-select - original labels)
├── Assignee (Person - who completed it)
├── Description (Rich Text - full context preserved)
├── Actual Effort (Select: S, M, L, XL - for learning)
├── Notes (Rich Text - completion notes, learnings)
```

### Task Completion Workflow
1. **Mark task "Done"** in main database
2. **Move to Completed Tasks database** within 1 week
3. **Add completion notes** and actual effort
4. **Remove from main database** to keep it clean

### Rich Description Best Practices
- **Use formatting**: Headers, bullets, code blocks, checkboxes
- **Include code samples**: Show implementation approaches
- **Cross-reference**: @mention related tasks and people
- **Keep it updated**: Add learnings and decisions as work progresses
- **Link externally**: Reference docs, issues, PRs when relevant

## Success Metrics

### Task Quality Indicators
- Time to complete vs original estimate
- Number of follow-up tasks needed (indicates completeness)
- Clarity of handoff (can someone else understand and continue?)
- Acceptance criteria hit rate (how often do we meet our own criteria?)

### Process Health Indicators  
- Percentage of tasks blocked by dependencies
- Average time tasks spend "In Progress"
- Number of "Deferred" tasks created vs completed
- Team satisfaction with task clarity and context

---

This approach ensures we have:
- **Single source of truth** in Notion
- **Rich context** preserved in task descriptions  
- **Flexible priority management** with deferred status
- **Clear execution patterns** for different task types
- **Quality standards** that ensure proper completion