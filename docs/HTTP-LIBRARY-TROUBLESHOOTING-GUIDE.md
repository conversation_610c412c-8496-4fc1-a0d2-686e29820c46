# HTTP Library Troubleshooting Guide

*Consolidated troubleshooting information for @libs/http*  
*Date: 2025-06-19*

## Quick Diagnosis Checklist

### 1. Service Health Check
```bash
# Check all services are running
curl http://localhost:3001/health  # Auth Service
curl http://localhost:3002/health  # User Service  
curl http://localhost:3000/health  # API Gateway

# Check service discovery
docker-compose logs api-gateway | grep "serviceClientsSize"
```

### 2. HTTP Client Functionality
```bash
# Test direct service communication
docker exec -it api-gateway wget http://auth-service:3000/health
docker exec -it api-gateway wget http://user-service:3002/health
```

### 3. Circuit Breaker Status
```bash
# Check circuit breaker logs
docker-compose logs | grep "circuit breaker"
docker-compose logs | grep "Circuit breaker"
```

## Common Issues and Solutions

### Issue 1: Got HTTP Client Returns Undefined

#### Symptoms
- API Gateway proxy returns 500 errors with "Internal proxy error"
- Console logs show "No response received from backend service"
- Circuit breaker shows 0ms response times
- Got HTTP calls return `undefined` instead of making network requests

#### Root Cause
**Metrics Configuration Conflict** - Multiple ObservabilityModule instances create conflicting metric definitions.

#### Investigation Steps
1. Check for Got client execution logs:
   ```bash
   docker-compose logs | grep "Got client execution"
   ```

2. Look for metrics errors:
   ```bash
   docker-compose logs | grep "Added label.*is not included in initial labelset"
   ```

3. Verify service connectivity:
   ```bash
   docker exec -it api-gateway wget http://auth-service:3000/health
   ```

#### Solution
1. **Fix metrics module isolation** - prevent duplicate ObservabilityModule imports
2. **Coordinate metric label definitions** across modules
3. **Improve error surfacing** - metrics errors should not crash HTTP requests

### Issue 2: ESM + Webpack + Got v14 Integration

#### Symptoms
- `Cannot find module './create.js'` errors
- `ERR_REQUIRE_ESM` runtime errors
- Webpack build failures with Got imports

#### Root Cause
Got v14 is a pure ESM package requiring Node.js 20+ and proper webpack ESM configuration.

#### Solution
1. **Upgrade Node.js to 22**:
   ```yaml
   # docker-compose.yml
   services:
     auth-service:
       image: node:22-alpine  # Upgraded from node:18-alpine
   ```

2. **Update webpack configuration**:
   ```javascript
   module.exports = {
     externalsType: 'import',  // Enable ESM imports
     resolve: {
       extensions: ['.ts', '.js', '.mjs', '.json'],  // Add .mjs support
     },
     module: {
       rules: [{
         test: /\.(js|mjs|ts)$/,
         include: [/node_modules\/got/],  // Transpile Got modules
       }]
     }
   };
   ```

### Issue 3: Dependency Injection Token Mismatch

#### Symptoms
- Bundle registration failures
- `ObservabilityLogger` vs `LOGGER_SERVICE` token errors
- Async module loading issues

#### Root Cause
Multiple logging tokens causing DI container conflicts.

#### Solution
1. **Create unified observability tokens**:
   ```typescript
   // @libs/observability/src/tokens.ts
   export const OBSERVABILITY_LOGGER = Symbol('ObservabilityLogger');
   export const METRICS_SERVICE = Symbol('MetricsService');
   ```

2. **Update HttpModule providers**:
   ```typescript
   providers: [
     {
       provide: OBSERVABILITY_LOGGER,
       useFactory: (factory: LoggerFactory) => factory.createLogger('HttpClient'),
       inject: [LOGGER_FACTORY],
     }
   ]
   ```

### Issue 4: Circuit Breaker Not Triggering

#### Symptoms
- Services continue failing without circuit breaker protection
- No circuit breaker state changes in logs
- Requests not being blocked when services are down

#### Diagnosis
1. Check circuit breaker configuration:
   ```bash
   docker-compose logs | grep "circuit breaker.*configured"
   ```

2. Verify error classification:
   ```bash
   docker-compose logs | grep "shouldTriggerCircuitBreaker"
   ```

#### Solution
1. **Verify circuit breaker integration**:
   ```typescript
   // Ensure CircuitBreakerModule is imported
   @Module({
     imports: [
       HttpModule.forRoot(),
       CircuitBreakerModule.register(),  // Required!
     ],
   })
   ```

2. **Check error classification logic**:
   ```typescript
   private shouldTriggerCircuitBreaker(error: any): boolean {
     if (error instanceof HttpResponseError) {
       return error.statusCode >= 500;  // Only 5xx errors
     }
     return error instanceof NetworkError || error instanceof TimeoutError;
   }
   ```

### Issue 5: Cache Not Working

#### Symptoms
- No cache hit logs
- Repeated requests to same endpoints
- Cache metrics showing 0 hits

#### Diagnosis
1. Check Redis connectivity:
   ```bash
   docker-compose logs redis
   docker exec -it redis redis-cli ping
   ```

2. Verify cache configuration:
   ```bash
   docker-compose logs | grep "cache.*enabled"
   ```

#### Solution
1. **Verify Redis configuration**:
   ```typescript
   HttpModule.forRoot({
     cache: {
       enabled: true,
       redisConfig: {
         host: 'redis',
         port: 6379,
       }
     }
   })
   ```

2. **Check cache adapter implementation**:
   ```typescript
   // Ensure RedisHttpCacheAdapter is properly configured
   const cacheAdapter = new RedisHttpCacheAdapter(redisConfig);
   ```

## Performance Issues

### Issue 1: Slow Response Times

#### Diagnosis
1. Check HTTP/2 enablement:
   ```bash
   docker-compose logs | grep "http2.*enabled"
   ```

2. Monitor connection reuse:
   ```bash
   docker-compose logs | grep "connection.*reuse"
   ```

#### Solution
1. **Enable HTTP/2**:
   ```typescript
   HttpModule.forRoot({
     http2: true,  // Enable HTTP/2 for performance
   })
   ```

2. **Optimize timeouts**:
   ```typescript
   {
     timeout: {
       response: 3000,  // 3s for user-facing operations
       connect: 1000,   // 1s for fast connection
       socket: 4000,    // 4s total socket timeout
     }
   }
   ```

### Issue 2: High Memory Usage

#### Diagnosis
1. Check connection pooling:
   ```bash
   docker-compose logs | grep "connection.*pool"
   ```

2. Monitor cache size:
   ```bash
   docker exec -it redis redis-cli info memory
   ```

#### Solution
1. **Configure connection limits**:
   ```typescript
   {
     agent: {
       maxSockets: 15,
       maxFreeSockets: 10,
     }
   }
   ```

2. **Set cache limits**:
   ```typescript
   {
     cache: {
       maxSize: 1000,
       defaultTtl: 300000,  // 5 minutes
     }
   }
   ```

## Development Issues

### Issue 1: Webpack Bundle Size Too Large

#### Symptoms
- Bundle sizes > 1MB
- Slow container startup times
- Memory issues in development

#### Solution
1. **Optimize externals configuration**:
   ```javascript
   externals: [
     nodeExternals({
       allowlist: [
         /^@libs\//,  // Only bundle internal libraries
         'got',       // Bundle Got for ESM compatibility
       ],
     }),
   ]
   ```

2. **Use production builds**:
   ```bash
   yarn build:libs
   yarn start:prod
   ```

### Issue 2: Hot Reload Not Working

#### Symptoms
- Changes not reflected in running containers
- Need to restart containers for updates

#### Solution
1. **Use volume development mode**:
   ```bash
   yarn start:dev:bundled:watch
   ```

2. **Verify volume mounts**:
   ```yaml
   volumes:
     - .:/app
     - /app/node_modules
   ```

## Monitoring and Debugging

### Enable Debug Logging
```bash
# Set debug environment variables
DEBUG=http:*,circuit-breaker:*
LOG_LEVEL=debug

# Or in docker-compose.yml
environment:
  - DEBUG=http:*
  - LOG_LEVEL=debug
```

### Check Metrics in Grafana
1. Navigate to http://localhost:3001/grafana
2. Check HTTP request metrics:
   - `http_requests_total`
   - `http_request_duration_seconds`
   - `http_circuit_breaker_state`

### Correlation ID Tracking
```bash
# Follow a request through the system
docker-compose logs | grep "correlation-id-12345"
```

## Container and Infrastructure Issues

### Permission Errors in Docker

**Symptoms:**
- `EACCES: permission denied` errors
- `EROFS: read-only file system` errors

**Solution:**
```bash
cd /root/code/polyrepo
docker-compose -f infrastructure/local-dev/docker-compose.bundled-dev-volume.yml down -v
docker volume prune -f
yarn dev:cli start
```

### Network Conflicts

**Symptoms:**
- `network was found but has incorrect label`
- Port already in use errors

**Solution:**
```bash
docker network prune -f
docker system prune -f
yarn dev:cli stop && yarn dev:cli start
```

### Health Check Failures

**Symptoms:**
- All services showing as "unhealthy" in Docker
- Services appear to be running but health checks fail

**Root Cause:**
Health checks using `localhost:3000` instead of `0.0.0.0:3000` inside containers.

**Solution:**
Health checks should use `0.0.0.0:3000` for container internal networking.

## Library and Build Issues

### Library Build Failures

**Symptoms:**
- `Cannot find module @libs/*` errors
- TypeScript compilation errors across services

**Solution:**
```bash
yarn build:libs
# Wait for completion, then restart services
yarn dev:cli restart
```

### Symlink Errors (Windows)

**Symptoms:**
- `ENOENT` symlink errors
- Junction-related errors on Windows

**Solution:**
```bash
# Remove Docker junctions (Windows only)
rmdir "path\to\junction"
# Or use: dir /a | findstr JUNCTION to find them

# Clean and restart
docker-compose down -v
yarn dev:cli start
```

## Authentication and Service Issues

### Keycloak Connection Issues

**Symptoms:**
- Auth service timing out during user authentication
- `KEYCLOAK_CONNECTION_ERROR` in logs

**Diagnosis:**
```bash
# Check Keycloak connectivity
docker exec polyrepo_auth_service_volume wget -qO- http://keycloak:8080/realms/polyrepo-test

# Check client configuration
docker exec polyrepo_keycloak_dev /opt/keycloak/bin/kcadm.sh get clients -r polyrepo-test
```

**Common Solutions:**
- Verify `KEYCLOAK_BASE_URL=http://keycloak:8080` in `.env.bundling`
- Ensure correct realm name (`polyrepo-test` for development)
- Check client credentials match between Keycloak and service configuration

### Database Connection Issues

**Symptoms:**
- User service cannot connect to PostgreSQL
- `ECONNREFUSED` errors

**Solution:**
```bash
# Check PostgreSQL containers
docker ps | grep postgres

# Verify connection from service
docker exec polyrepo_user_service_volume pg_isready -h postgres-user-service -p 5432
```

## Prisma Bundling Issues

### Deep Prisma Bundling Errors in Webpack

**Symptoms:**
- Error: `Invalid 'r=Object.create()' invocation` in webpack bundle
- Error: `Right-hand side of 'instanceof' is not an object`
- User Service fails to start with Prisma-related bundling errors
- Database operations fail with module resolution errors

**Root Cause:**
Prisma Client includes native binaries and engine files that cannot be properly bundled by webpack. When Prisma engines are bundled, they lose their binary nature and become invalid JavaScript objects, causing instanceof checks to fail.

**Solution:**

1. **Update webpack.config.js externals configuration:**
```javascript
externals: [
  // ... existing externals ...
  '@prisma/client',               // Prisma ORM client (large, has native binaries)
  'prisma',                       // Prisma CLI tools
  '@prisma/engines',              // Prisma engines (binaries)
  '@prisma/engines-version',      // Prisma engines version
  // External reference to generated Prisma client to avoid bundling conflicts
  ({request}, callback) => {
    if (request.includes('generated/prisma-client')) {
      return callback(null, 'commonjs ' + request);
    }
    // Externalize any Prisma engine requests
    if (request.includes('@prisma/engines') || request.includes('schema-engine') || request.includes('query-engine')) {
      return callback(null, 'commonjs ' + request);
    }
    callback();
  },
],
```

2. **Update Prisma service imports to use standard client:**
```typescript
// In prisma.service.ts - use standard @prisma/client
import { PrismaClient } from '@prisma/client';
// NOT: import { PrismaClient } from '../generated/prisma-client';
```

3. **Run database migrations using documented approach:**
```bash
# From user-service directory, using Docker environment credentials
cd services/user-service
DATABASE_URL="postgresql://devuser_user_service:devpassword_user_service@localhost:5433/polyrepo_user_db?schema=public" npx prisma migrate reset --force
```

## Complete Reset Procedure

When all else fails, use this complete reset:

```bash
cd /root/code/polyrepo

# Stop everything
yarn dev:cli stop
docker-compose -f infrastructure/local-dev/docker-compose.bundled-dev-volume.yml down -v

# Clean Docker resources
docker volume prune -f
docker network prune -f
docker system prune -f

# Kill any hanging processes
pkill -f webpack
pkill -f nodemon

# Rebuild libraries
yarn build:libs

# Restart development
yarn dev:cli start
```

## Emergency Procedures

### Rollback HTTP Library Changes
```bash
# Revert to previous version
git revert <http-library-commit>
yarn install && yarn build:libs
docker-compose restart
```

### Disable Circuit Breakers
```typescript
// Temporary disable for debugging
CircuitBreakerModule.register({
  enabled: false,  // Disable all circuit breakers
})
```

### Bypass Cache
```typescript
// Temporary disable for debugging
HttpModule.forRoot({
  cache: { enabled: false },
})
```

---

*For additional help, check the [HTTP Library Improvement Backlog](./HTTP-LIBRARY-IMPROVEMENT-BACKLOG.md) for known issues and planned fixes.*
