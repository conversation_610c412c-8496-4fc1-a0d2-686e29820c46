# Troubleshooting Documentation Guidelines

This document outlines the comprehensive strategy for managing troubleshooting knowledge across repository files and Notion database.

## Documentation Strategy Overview

### **Repository (Implementation Documentation)**
- **CLAUDE.md** - Quick troubleshooting table for immediate reference
- **Service READMEs** - Service-specific setup and configuration issues
- **Implementation patterns** - Technical patterns and architecture decisions

### **Notion (Operational Troubleshooting Knowledge)**
- **Troubleshooting Knowledge Base** - Comprehensive searchable database
- **Rich context** - Detailed root cause analysis and proven solutions
- **Component tagging** - Granular technical stack categorization

## Notion Troubleshooting Database Structure

### **Database Schema**
```
Issue Title: [Severity] [Components] - [Brief Description]
Components: Technical stack tags (docker, typescript, nestjs, etc.)
Severity: Critical, High, Medium, Low
Status: Active, Resolved, Deprecated
Frequency: Daily, Weekly, Monthly, Rare
Environment Tags: dev-only, production-only, windows-specific, etc.
Description: Comprehensive issue documentation
Last Updated: Maintenance tracking
```

### **Component Tagging System**
**Technical Stack**: `typescript`, `docker`, `webpack`, `nestjs`, `prisma`, `keycloak`, `redis`, `grafana`, `prometheus`, `loki`, `jaeger`

**Services**: `auth-service`, `user-service`, `api-gateway`

**Infrastructure**: `monorepo`, `shared-libraries`, `networking`, `environment`, `yarn`, `node-modules`, `volumes`, `ports`

## Issue Documentation Standards

### **When to Create Troubleshooting Entry**
- Issue resolution took >15 minutes
- Error messages not easily found in existing documentation
- Environment-specific problems with clear solutions
- Issues likely to affect other developers
- Complex integration problems with non-obvious root causes

### **Issue Documentation Template**
```markdown
## Symptoms
- What the user sees/experiences
- Exact error messages and codes
- System behavior and failure modes

## Environment
- Operating system, service context, trigger conditions
- When this occurs (development, production, specific configurations)

## Root Cause
- Technical explanation of why this happens
- Underlying system interactions causing the issue
- Dependencies and configuration factors

## Solution
- Step-by-step resolution with exact commands
- Code examples and configuration changes
- Multiple approaches if applicable

## Prevention
- Best practices to avoid future occurrences
- Configuration recommendations
- Development workflow improvements
```

## Duplicate Detection Criteria

### **Primary Criteria: Root Cause + Context**
Issues are duplicates when they share:
1. **Same root cause** (technical mechanism causing the problem)
2. **Same context** (environment, components, trigger conditions)  
3. **Same solution approach** (resolution strategy)

### **Decision Framework**
```
Same symptoms + Same root cause + Same environment = Duplicate
Same symptoms + Different root cause = Separate issue
Same root cause + Different trigger = Related but separate
```

### **Examples**
**Duplicate**: 
- "EACCES permission denied in auth-service Docker container"
- "EACCES permission denied in user-service Docker container"
- **Same root cause**: Docker container user permissions

**NOT Duplicate**:
- "EACCES permission denied in Docker container" 
- "EACCES permission denied on Windows host after Docker"
- **Different root causes**: Container permissions vs Windows junctions

## Severity Classification

### **Critical**: Service/Development Blocking
- Complete service startup failures
- Build process cannot complete
- Development environment unusable
- Data loss or corruption risks

### **High**: Significant Workflow Impact
- Major feature dysfunction
- Performance degradation >50%
- Complex configuration issues
- Security vulnerabilities

### **Medium**: Workflow Inconvenience
- Minor feature issues
- Performance impact <25%
- Configuration complexity
- Documentation gaps

### **Low**: Edge Cases and Optimization
- Rare occurrence issues
- Performance fine-tuning
- Enhancement opportunities
- Documentation improvements

## Frequency Guidelines

### **Daily**: Core Development Issues
- Basic build failures
- Common environment problems
- Frequently changed configurations

### **Weekly**: Regular Development Issues
- Integration problems
- Service configuration issues
- Development workflow problems

### **Monthly**: Occasional Technical Issues
- Environment-specific problems
- Complex integration scenarios
- Infrastructure changes

### **Rare**: Edge Cases and Specific Conditions
- Platform-specific issues
- Complex interaction scenarios
- Advanced configuration problems

## Migration Workflow

### **From Repository Troubleshooting Docs**
1. **Analyze existing files** for distinct technical issues
2. **Extract high-value problems** with clear solutions
3. **Check for duplicates** using root cause + context criteria
4. **Create Notion entries** with comprehensive documentation
5. **Update CLAUDE.md** quick reference table if needed
6. **Archive repository files** after migration

### **From Session Notes**
1. **Review notes regularly** for troubleshooting extraction opportunities
2. **Identify issues** that took >15 minutes to resolve
3. **Document symptoms and solutions** from investigation process
4. **Create Notion entries** for valuable issues
5. **Delete session notes** after extraction

## Maintenance and Quality

### **Regular Reviews**
- **Weekly**: Review new session notes for extraction opportunities
- **Monthly**: Update existing entries with new discoveries
- **Quarterly**: Review and consolidate related issues

### **Quality Standards**
- **Accurate symptoms**: Exact error messages and behavior
- **Proven solutions**: Tested and verified resolution steps
- **Clear context**: Environment and trigger conditions
- **Comprehensive prevention**: Best practices and recommendations

### **Search Optimization**
- **Component tags**: Use granular technical stack components
- **Keywords**: Include exact error messages and technical terms
- **Cross-references**: Link related issues and dependencies
- **Status maintenance**: Keep Active/Resolved status current

## Integration with Development Workflow

### **During Development**
- **Check Notion database** before investigating issues
- **Create entries** for new problems encountered
- **Update existing entries** with additional context or solutions

### **Code Review Process**
- **Review troubleshooting additions** for completeness and accuracy
- **Verify solutions** are tested and comprehensive
- **Check for duplicate issues** before creating new entries

### **Documentation Updates**
- **Update CLAUDE.md** quick reference for frequently encountered issues
- **Maintain service READMEs** with setup and configuration guidance
- **Reference Notion database** in service documentation

This comprehensive approach ensures troubleshooting knowledge is captured, organized, and easily discoverable while avoiding duplication and maintaining high quality standards.