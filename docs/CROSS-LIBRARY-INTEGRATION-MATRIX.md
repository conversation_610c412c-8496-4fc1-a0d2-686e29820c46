# Cross-Library Integration Matrix

This document provides a comprehensive view of how all libraries in the polyrepo integrate with each other to create a cohesive, event-driven microservices ecosystem.

## Overview

The polyrepo libraries form a tightly integrated network where each library both consumes and produces capabilities for others. This creates a powerful, observable, and resilient system with automatic cross-cutting concerns.

## Integration Matrix

### Visual Integration Network

```
@libs/http ↔ @libs/observability ↔ @libs/error-handling ↔ @libs/messaging
     ↕                ↕                       ↕                ↕
@libs/caching ↔ @libs/resilience ↔ @libs/auth-common ↔ @libs/keycloak-client
     ↕                ↕                       ↕                ↕
@libs/shared-types ← @libs/shared-utils ← @libs/observability ← All Libraries
```

### Dependency Levels

**Level 0 (Foundation)**
- `@libs/observability` - Core logging, metrics, tracing
- `@libs/shared-types` - Common DTOs and interfaces  
- `@libs/shared-utils` - Utility functions

**Level 1 (Core Infrastructure)**
- `@libs/keycloak-client` - Authentication provider
- `@libs/error-handling` - Error correlation and response building
- `@libs/resilience` - Circuit breaker patterns

**Level 2 (Service Integration)**
- `@libs/auth-common` - Authentication guards and strategies
- `@libs/http` - HTTP/2 client with full integration
- `@libs/caching` - Redis caching with events
- `@libs/messaging` - Event-driven architecture

## Detailed Integration Patterns

### HTTP ↔ All Libraries Integration

#### HTTP → Observability
```typescript
// Automatic integration
const response = await httpClient.get('/api/data', {
  serviceName: 'data-service',
  operationName: 'fetch-user-data',
});

// Automatically produces:
// - Metrics: http_requests_total, http_request_duration_seconds
// - Logs: Structured logs with correlation context
// - Traces: Distributed tracing spans with timing
```

#### HTTP → Resilience
```typescript
// Circuit breaker automatically wraps HTTP calls
const client = httpClient.createServiceClient('external-api', {
  circuitBreaker: { enabled: true }, // Default: true
});

// Automatic protection:
// - Monitors failure rates
// - Opens circuit on threshold breach
// - Publishes state change events
```

#### HTTP → Caching
```typescript
// Redis-backed HTTP caching
const response = await httpClient.get('/api/config', {
  cache: { enabled: true, ttl: 3600000 },
});

// Features:
// - Shared cache across service instances
// - Cache invalidation events published
// - ETag and Last-Modified support
// - Stale-while-revalidate patterns
```

#### HTTP → Messaging
```typescript
// HTTP lifecycle events automatically published
// Events: HttpRequestEvent, HttpResponseEvent, HttpErrorEvent

// Example consuming HTTP events:
@EventSubscriber()
export class HttpAnalyticsService {
  @OnEvent('http.request.completed')
  handleHttpRequest(event: HttpResponseEvent) {
    // Analyze performance, update dashboards, etc.
  }
}
```

#### HTTP → Error Handling
```typescript
try {
  const response = await httpClient.post('/api/process', data);
} catch (error) {
  // Automatically enhanced with:
  // - Correlation ID tracking
  // - Loki query links for investigation  
  // - Structured error format
  // - Circuit breaker state information
}
```

### Caching ↔ Messaging Integration

```typescript
@Injectable()
export class SmartCacheService {
  constructor(
    private cache: CacheService,
    private eventBus: EventBusService,
  ) {}
  
  async set(key: string, value: any, ttl?: number): Promise<void> {
    await this.cache.set(key, value, ttl);
    
    // Automatically publishes CacheOperationEvent
    this.eventBus.emit('cache.operation', {
      operation: 'set',
      key,
      success: true,
      timestamp: new Date(),
    });
  }
  
  // Cache invalidation via events
  @OnEvent('user.profile.updated')
  async invalidateUserCache(event: UserProfileUpdatedEvent) {
    await this.cache.del(`user:${event.userId}:*`);
  }
}
```

### Resilience ↔ Observability Integration

```typescript
@Injectable()
export class ObservableCircuitBreaker {
  @OnEvent('circuit.breaker.state.changed')
  handleStateChange(event: CircuitBreakerStateChangedEvent) {
    // Automatic metrics updates
    this.metrics.setGauge('circuit_breaker_state', {
      service: event.serviceName,
      state: event.newState,
    }, event.newState === 'open' ? 1 : 0);
    
    // Structured logging
    this.logger.warn('Circuit breaker state changed', {
      service: event.serviceName,
      previousState: event.previousState,
      newState: event.newState,
      failureCount: event.stats.failureCount,
      successCount: event.stats.successCount,
    });
    
    // Distributed tracing
    this.tracing.recordEvent('circuit.breaker.state.changed', {
      service: event.serviceName,
      state: event.newState,
    });
  }
}
```

### Auth ↔ Keycloak Integration

```typescript
@Injectable()
export class AuthService {
  constructor(private keycloakClient: KeycloakClientService) {}
  
  // JWT strategy automatically uses Keycloak JWKS
  @UseGuards(JwtAuthGuard)
  async validateToken(@Headers('authorization') auth: string) {
    // JwtStrategy automatically:
    // - Extracts token from header
    // - Validates against cached JWKS from Keycloak
    // - Caches validation results
    // - Publishes auth events
  }
}

// JWKS caching with automatic rotation
@Injectable() 
export class JwksService {
  @Cron('0 */4 * * *') // Every 4 hours
  async refreshJwks() {
    await this.keycloakClient.refreshJwks();
    // Publishes JwksRefreshedEvent
  }
}
```

### Error Handling ↔ Observability Integration

```typescript
@Injectable()
export class CorrelatedErrorHandler {
  async handleError(error: any, context: string) {
    const correlationId = this.correlation.getOrCreateId();
    
    // Enhanced error with observability data
    const enhancedError = {
      ...error,
      correlationId,
      traceId: this.tracing.getCurrentTraceId(),
      spanId: this.tracing.getCurrentSpanId(),
      context,
      timestamp: new Date(),
      lokiQueryUrl: this.buildLokiQuery(correlationId),
    };
    
    // Automatic logging with correlation
    this.logger.error('Request failed', enhancedError);
    
    // Metrics update
    this.metrics.increment('errors_total', {
      context,
      errorType: error.constructor.name,
    });
    
    return enhancedError;
  }
  
  private buildLokiQuery(correlationId: string): string {
    return `{correlation_id="${correlationId}"}`;
  }
}
```

## Event Flow Architecture

### HTTP Request Lifecycle Events

```typescript
// 1. Request Start Event
{
  type: 'http.request.started',
  correlationId: 'req_123_abc',
  traceId: 'trace_456_def', 
  serviceName: 'user-service',
  operation: 'get-user-profile',
  method: 'GET',
  url: '/users/123',
  timestamp: '2024-01-15T10:30:00.000Z'
}

// 2. Cache Check Event (if caching enabled)
{
  type: 'cache.operation',
  operation: 'get',
  key: 'http:GET:/users/123',
  hit: false,
  correlationId: 'req_123_abc'
}

// 3. Circuit Breaker Check Event
{
  type: 'circuit.breaker.check',
  serviceName: 'user-service',
  state: 'closed',
  correlationId: 'req_123_abc'
}

// 4. Response Event
{
  type: 'http.response.received',
  correlationId: 'req_123_abc',
  statusCode: 200,
  duration: 150,
  fromCache: false,
  retryAttempt: 0
}

// 5. Cache Store Event (if cacheable)
{
  type: 'cache.operation',
  operation: 'set',
  key: 'http:GET:/users/123',
  ttl: 300000,
  correlationId: 'req_123_abc'
}
```

### Error Scenario Event Flow

```typescript
// 1. Request Failure
{
  type: 'http.request.failed',
  correlationId: 'req_789_ghi',
  error: 'ECONNREFUSED',
  attempt: 1,
  willRetry: true
}

// 2. Circuit Breaker Failure Recording
{
  type: 'circuit.breaker.failure.recorded',
  serviceName: 'user-service',
  failureCount: 3,
  threshold: 5,
  correlationId: 'req_789_ghi'
}

// 3. Retry Attempt
{
  type: 'http.request.retry',
  correlationId: 'req_789_ghi',
  attempt: 2,
  delay: 1000,
  reason: 'connection_refused'
}

// 4. Circuit Opens (after threshold)
{
  type: 'circuit.breaker.opened',
  serviceName: 'user-service',
  failureCount: 5,
  correlationId: 'req_789_ghi'
}
```

## Performance Impact Analysis

### Integration Overhead

| Integration Point | Overhead | Benefits |
|------------------|----------|----------|
| HTTP → Observability | ~2ms per request | Full request visibility |
| HTTP → Resilience | ~0.5ms per request | Automatic failure protection |
| HTTP → Caching | ~1ms per request | 95% cache hit improvement |
| HTTP → Messaging | ~0.3ms per request | Event-driven analytics |
| Error → Correlation | ~0.1ms per error | Complete error tracking |

### Memory Usage

| Component | Memory Impact | Justification |
|-----------|---------------|---------------|
| Circuit Breaker State | ~1KB per service | Failure statistics tracking |
| Cache Metadata | ~100B per cache key | Cache metadata and TTL |
| Correlation Context | ~200B per request | Request tracking data |
| Event Buffers | ~10KB baseline | Event queuing and batching |

## Configuration Integration

### Centralized Configuration Pattern

```typescript
// libs/shared-config/src/integration.config.ts
export interface IntegratedLibraryConfig {
  http: {
    http2: boolean;
    defaultTimeout: number;
    observability: ObservabilityConfig;
    resilience: ResilienceConfig;
    caching: CachingConfig;
  };
  
  observability: {
    metrics: { enabled: boolean; port: number };
    logging: { level: string; lokiUrl: string };
    tracing: { enabled: boolean; jaegerUrl: string };
  };
  
  resilience: {
    circuitBreaker: {
      failureThreshold: number;
      timeout: number;
      resetTimeout: number;
    };
  };
  
  caching: {
    redis: { url: string; keyPrefix: string };
    defaultTtl: number;
    messaging: MessageIntegrationConfig;
  };
  
  messaging: {
    redis: RedisStreamsConfig;
    inMemory: InMemoryBusConfig;
    events: EventConfiguration;
  };
}
```

### Environment-Based Integration

```typescript
// Development: Full observability, loose circuit breakers
const devConfig: IntegratedLibraryConfig = {
  http: {
    observability: { detailed: true, sampling: 1.0 },
    resilience: { failureThreshold: 10, timeout: 30000 },
  },
  messaging: { batchSize: 1, flushInterval: 100 },
};

// Production: Optimized observability, strict circuit breakers  
const prodConfig: IntegratedLibraryConfig = {
  http: {
    observability: { detailed: false, sampling: 0.1 },
    resilience: { failureThreshold: 5, timeout: 5000 },
  },
  messaging: { batchSize: 100, flushInterval: 1000 },
};
```

## Testing Integration Patterns

### Integration Test Example

```typescript
describe('Cross-Library Integration', () => {
  let httpClient: HttpClientService;
  let cacheService: CacheService;
  let eventBus: EventBusService;
  let metrics: MetricsService;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        HttpModule.register({ cache: { enabled: true } }),
        CachingModule.register(),
        MessagingModule.register(),
        ObservabilityModule.register(),
        ResilienceModule.register(),
      ],
    }).compile();
    
    httpClient = module.get(HttpClientService);
    cacheService = module.get(CacheService);
    eventBus = module.get(EventBusService);
    metrics = module.get(MetricsService);
  });
  
  it('should integrate HTTP caching with messaging events', async () => {
    const eventSpy = jest.fn();
    eventBus.on('cache.operation', eventSpy);
    
    // First request - cache miss
    await httpClient.get('/test', { cache: { enabled: true } });
    
    // Second request - cache hit
    await httpClient.get('/test', { cache: { enabled: true } });
    
    // Verify events published
    expect(eventSpy).toHaveBeenCalledTimes(3); // miss, set, hit
    expect(eventSpy).toHaveBeenCalledWith(
      expect.objectContaining({
        operation: 'get',
        hit: false,
      })
    );
  });
  
  it('should integrate circuit breaker with observability', async () => {
    // Trigger circuit breaker failures
    for (let i = 0; i < 5; i++) {
      try {
        await httpClient.get('/failing-endpoint');
      } catch (error) {
        // Expected failures
      }
    }
    
    // Check metrics updated
    const circuitState = await metrics.getGauge('circuit_breaker_state');
    expect(circuitState).toBe(1); // Open state
    
    // Check event published
    expect(eventBus.events).toContainEqual(
      expect.objectContaining({
        type: 'circuit.breaker.opened',
      })
    );
  });
});
```

## Monitoring Integration Health

### Health Check Integration

```typescript
@Injectable()
export class IntegrationHealthService {
  constructor(
    private health: HealthCheckService,
    private cache: CacheService,
    private messaging: MessagingService,
    private metrics: MetricsService,
  ) {}
  
  @HealthIndicator()
  async checkIntegrationHealth(): Promise<HealthIndicatorResult> {
    const checks = await Promise.allSettled([
      this.checkCacheIntegration(),
      this.checkMessagingIntegration(), 
      this.checkMetricsIntegration(),
      this.checkCircuitBreakerIntegration(),
    ]);
    
    const results = checks.map((check, index) => ({
      name: ['cache', 'messaging', 'metrics', 'circuit-breaker'][index],
      status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      details: check.status === 'fulfilled' ? check.value : check.reason,
    }));
    
    const allHealthy = results.every(r => r.status === 'healthy');
    
    return this.health.isHealthy('cross-library-integration', {
      components: results,
      overall: allHealthy ? 'operational' : 'degraded',
    });
  }
}
```

## Future Integration Opportunities

### Planned Enhancements

1. **Machine Learning Integration**
   - Predictive circuit breaker thresholds
   - Intelligent cache warming
   - Anomaly detection in request patterns

2. **Advanced Event Sourcing**
   - Complete request/response event store
   - Event replay for debugging
   - Business event correlation

3. **Distributed Configuration**
   - Real-time configuration updates
   - A/B testing integration
   - Feature flag coordination

4. **Security Integration**
   - Automatic threat detection
   - Rate limiting coordination
   - Security event correlation

## Best Practices

### Integration Guidelines

1. **Event Naming Conventions**
   ```typescript
   // Format: {library}.{entity}.{action}
   'http.request.started'
   'cache.operation.completed'
   'circuit.breaker.state.changed'
   'auth.token.validated'
   ```

2. **Correlation Context**
   ```typescript
   // Always propagate correlation context
   interface CorrelationContext {
     correlationId: string;
     traceId?: string;
     spanId?: string;
     userId?: string;
     sessionId?: string;
   }
   ```

3. **Error Boundary Integration**
   ```typescript
   // Consistent error handling across libraries
   try {
     await operation();
   } catch (error) {
     const enhancedError = this.errorHandler.enhance(error, {
       context: 'library-integration',
       correlationId: this.correlation.getId(),
     });
     
     this.eventBus.emit('error.occurred', enhancedError);
     throw enhancedError;
   }
   ```

4. **Performance Monitoring**
   ```typescript
   // Monitor integration overhead
   const start = performance.now();
   await integratedOperation();
   const duration = performance.now() - start;
   
   this.metrics.histogram('integration_duration', duration, {
     operation: 'http_with_cache_and_circuit_breaker',
   });
   ```

This integration matrix enables the polyrepo to function as a cohesive, observable, and resilient microservices platform where all cross-cutting concerns are automatically handled through library integrations.