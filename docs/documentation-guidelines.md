# Documentation Guidelines

This document outlines the standards and best practices for maintaining documentation across the Polyrepo project.

## Documentation Structure

### Root Documentation
- **README.md** - Project overview, key features, quick start, and references to detailed documentation
- **INTRODUCTION.md** - Comprehensive project introduction for developers and AI assistants
- **CLAUDE.md** - Claude-specific tooling and behavior guidance (replaces claude-integration.md)

### General Project Documentation (`/docs/`)
- **README.md** - Master table of contents for all documentation with navigation and brief explanations
- **testing-standards.md** - Testing conventions and standards
- **observability-integration.md** - Monitoring and observability setup
- **task-management-guidelines.md** - Comprehensive task organization and workflow in Notion
- **notes-guidelines.md** - Session notes workflow and lifecycle
- **troubleshooting-docs-guidelines.md** - Strategy for troubleshooting knowledge management
- **documentation-guidelines.md** - This file - documentation standards and practices
- **troubleshooting/** - Legacy troubleshooting files (being migrated to Notion)

### Notion-Based Documentation
- **Task Management Database** - All project tasks, priorities, and planning (single source of truth)
- **Troubleshooting Knowledge Base** - Comprehensive searchable troubleshooting database
- **Session Notes Analysis** - Extracted insights from development sessions

### Legacy Documentation (`/docs/troubleshooting/`)
- **Individual troubleshooting files** - Being systematically migrated to Notion database
- **Historical reference** - Contains detailed technical investigations

### Service-Specific Documentation
- **Service README** - Located in each service root directory (`services/*/README.md`)
- **Testing Documentation** - Located in service test directory (`services/*/test/README.md`)
- **API Documentation** - Generated Swagger/OpenAPI docs accessible via service endpoints

### Infrastructure Documentation
- **Local Development** - `infrastructure/local-dev/LOCAL_DEVELOPMENT_SETUP.md`
- **Monitoring Setup** - `infrastructure/monitoring/README.md`
- **Deployment Guides** - `infrastructure/deployment/README.md` (when created)

## Documentation Standards

### Writing Style
- Use clear, concise language
- Prefer imperative mood for instructions ("Run the command" vs "You should run the command")
- Include practical examples and code snippets
- Maintain a professional yet approachable tone

### File Naming Conventions
- Use lowercase with hyphens: `service-name.md`, `integration-guide.md`
- Be descriptive but concise
- Use consistent naming patterns across similar documents

### Content Organization
- Start with a brief overview/purpose statement
- Use clear headings and subheadings
- Include a table of contents for longer documents
- End with references to related documentation

### Code Examples
- Always include complete, runnable examples
- Use proper syntax highlighting
- Include expected output when relevant
- Provide context for when to use each example

### Cross-References
- Always use relative paths for internal links
- Link to specific sections when relevant: `[Testing Standards](./testing-standards.md#unit-tests)`
- Maintain bidirectional links when documenting related concepts
- Update all references when moving or renaming files

### Documentation Organization Strategy

#### **Repository Documentation (Implementation Focus)**
- **CLAUDE.md** - Claude-specific tooling and behavior guidance
- **INTRODUCTION.md** - Project architecture and structure overview
- **Service READMEs** - Technical implementation details and patterns
- **Quick Reference** - Implementation patterns and setup instructions

#### **Notion Documentation (Operational Knowledge)**
- **Task Management** - All project work, priorities, and planning
- **Troubleshooting Database** - Comprehensive searchable issue resolution
- **Rich Context** - Detailed analysis with component tagging and cross-references
- **Guidelines**: See `troubleshooting-docs-guidelines.md` for complete strategy

#### **Session Notes Workflow**
- **Temporary Notes** - Create `notes_session_YYYYMMDD.md` in project root
- **Extract Insights** - Migrate valuable troubleshooting knowledge to Notion
- **Lifecycle Management** - Delete notes after extracting permanent knowledge
- **Guidelines**: See `notes-guidelines.md` for complete workflow

## Maintenance Responsibilities

### Service Owners
- Maintain service-specific README files
- Update API documentation when endpoints change
- Document configuration changes and new environment variables
- Keep test documentation current with testing practices

### Infrastructure Team
- Maintain infrastructure setup and deployment documentation
- Update monitoring and observability guides
- Document security and compliance procedures
- Keep troubleshooting guides current

### All Contributors
- Update relevant documentation when making code changes
- **Create troubleshooting entries** in Notion for issues taking >15 minutes to resolve
- **Maintain session notes** during development for knowledge extraction
- Review documentation during code reviews
- Suggest improvements to documentation standards

## Documentation Review Process

### For New Features
1. Include documentation requirements in feature planning
2. Draft documentation alongside implementation
3. Review documentation as part of code review process
4. Update cross-references and roadmap as needed

### For Updates
1. Identify all affected documentation when making changes
2. Update documentation in the same PR as code changes
3. Test all examples and code snippets
4. Verify all links and references work correctly

## Quality Standards

### Accuracy
- All code examples must be tested and functional
- Environment variables and configuration examples must be current
- Screenshots and visual examples should be updated regularly

### Completeness
- Include prerequisites and setup requirements
- Provide troubleshooting information for common issues
- Document both success and failure scenarios
- Include performance and resource considerations

### Accessibility
- Use clear headings for navigation
- Provide alternative text for images and diagrams
- Use consistent formatting and structure
- Include search-friendly keywords and tags

This documentation framework ensures consistent, maintainable, and helpful documentation across the entire project.
