# Session Notes Guidelines

Session notes are **temporary documentation** for preserving knowledge between Claude sessions. They should be kept minimal and focused on actionable insights.

## Notes Location and Lifecycle

### **Temporary Notes (Project Root)**
- Create `notes_session_YYYYMMDD.md` in project root for current session
- If filename occupied, use `notes_[topic]_YYYYMMDD.md`
- **Delete after extracting valuable insights** to Notion or formal docs

### **Permanent Knowledge (Notion)**
- **Troubleshooting Database** - For resolved technical issues
- **Task Management** - For project work and discoveries
- **Documentation Updates** - Suggestions for improving project docs

## What to Document in Session Notes

Based on CLAUDE.md requirements, document:

### **Tool and Process Issues**
- Problems using specific tools and how you solved them
- Workflow inefficiencies and better approaches discovered
- Development environment quirks and workarounds

### **Project Investigation Findings**
- Unobvious project decisions that required investigation
- Architecture insights that weren't immediately clear
- Time-consuming research that could be avoided next time

### **Practical Discoveries**
- Commands, configurations, or patterns that save time
- Environment-specific solutions
- Integration gotchas and solutions

### **Work Progress**
- Intermediate summaries: what found, what done, what planned
- Code changes applied (reference files, brief explanation)
- Next steps and incomplete work

## Session Notes Format

```markdown
# Session Notes - [Date] - [Brief Topic]

## Context
- What triggered this session
- Previous notes referenced

## Issues & Solutions
- **Problem:** [Brief description]
- **Solution:** [What worked]
- **Time saved:** [How this helps future sessions]

## Project Insights
- [Unobvious decisions discovered]
- [Architecture findings]
- [Development patterns learned]

## Changes Applied
- [File references with brief explanation]
- [Configuration updates]

## Completed / In Progress / Next Steps
- ✅ [Completed work]
- 🔄 [Current progress]
- 📋 [Next session tasks]

## Extract to Formal Docs
- [ ] Troubleshooting entry for [specific issue]
- [ ] Update [specific doc] with [insight]
- [ ] Create task for [follow-up work]
```

## Integration with Notion Workflow

### **After Each Session:**
1. **Review notes** for troubleshooting extraction opportunities
2. **Create Notion entries** for significant issues (>15 min resolution time)
3. **Update existing docs** with discoveries
4. **Archive/delete** session notes after extraction

### **Between Sessions:**
1. **Check project root** for existing `notes_*.md` files
2. **Review previous findings** to avoid repetition
3. **Continue incomplete work** documented in notes

### **Weekly Cleanup:**
- Extract all valuable insights to Notion/formal docs
- Delete processed session notes
- Update project documentation with accumulated discoveries

## Troubleshooting Extraction Criteria

Extract to **Notion Troubleshooting Database** when:
- Issue took >15 minutes to resolve
- Error messages not easily found in existing docs
- Environment-specific problems with clear solutions
- Issues likely to affect other developers

## Documentation Update Criteria

Update **formal project docs** when:
- Discovered missing setup steps
- Found better development workflows
- Identified common pitfalls
- Learned architectural insights worth preserving

This keeps notes lightweight and actionable while ensuring valuable knowledge flows into permanent documentation systems.