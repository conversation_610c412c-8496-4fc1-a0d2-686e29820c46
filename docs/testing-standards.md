# Testing Standards

This document outlines the testing standards for the project, including directory structure, naming conventions, and best practices.

## Test Types

We use the following types of tests:

1. **Unit Tests**: Test individual components in isolation with dependencies mocked
   - **Location**: `services/*/test/unit/`
   - **Naming**: `*.spec.ts`
   - **Environment**: `test/.env.test.unit`
   - **Command**: `yarn test`

2. **Integration Tests**: Test interactions between components and external services
   - **Location**: `services/*/test/integration/`
   - **Naming**: `*.integration.spec.ts`
   - **Environment**: `test/.env.test.integration`
   - **Command**: `yarn test:integration`

3. **End-to-End Tests**: Test complete user flows through the entire system
   - **Location**: `services/*/test/e2e/`
   - **Naming**: `*.e2e-spec.ts`
   - **Environment**: `test/.env.test.e2e`
   - **Command**: `yarn test:e2e`

## Directory Structure

### Standard Structure

```
services/<service-name>/
├── src/
│   ├── module1/
│   │   ├── module1.controller.ts
│   │   ├── module1.service.ts
│   │   └── ...
│   └── ...
├── test/
│   ├── unit/
│   │   ├── module1/
│   │   │   ├── module1.controller.spec.ts
│   │   │   ├── module1.service.spec.ts
│   │   │   └── ...
│   │   └── ...
│   ├── integration/
│   │   ├── module1.integration.spec.ts
│   │   ├── module2.integration.spec.ts
│   │   └── ...
│   ├── e2e/
│   │   ├── module1.e2e-spec.ts
│   │   ├── module2.e2e-spec.ts
│   │   └── ...
│   ├── mocks/
│   │   ├── module1.mock.ts
│   │   ├── module2.mock.ts
│   │   └── ...
│   └── fixtures/
│       ├── module1.fixture.ts
│       ├── module2.fixture.ts
│       └── ...
└── .env.test
```

### Test File Naming Conventions

- **Unit Tests**: `<file-name>.spec.ts`
- **Integration Tests**: `<feature-name>.integration.spec.ts`
- **End-to-End Tests**: `<feature-name>.e2e-spec.ts`
- **Mocks**: `<module-name>.mock.ts`
- **Fixtures**: `<module-name>.fixture.ts`

## Environment Files

Each test type has its own environment configuration file located in the `test` directory:

1. **Unit Tests**: `test/.env.test.unit`
   - All external services should be mocked
   - `USE_REAL_SERVICES=false`

2. **Integration Tests**: `test/.env.test.integration`
   - Real services should be used when available
   - `USE_REAL_SERVICES=true`

3. **E2E Tests**: `test/.env.test.e2e`
   - Real services must be used
   - `USE_REAL_SERVICES=true`

### Example Files

Each test type has an example environment file that should be committed to the repository:

- `test/.env.test.unit.example`
- `test/.env.test.integration.example`
- `test/.env.test.e2e.example`

Developers should copy these files and remove the `.example` suffix to create their local test environment files.

### Local Overrides

- `.env.test.local`: Local overrides for test environment (not committed to git)

## Testing Philosophy

We follow a clear separation between test types:

- **Unit Tests**: ALWAYS use mocks - test individual components in isolation
- **Integration Tests**: ALWAYS use real services - test component interactions
- **E2E Tests**: ALWAYS use real services - test complete user flows

The `USE_REAL_SERVICES` flag has been **REMOVED** to eliminate confusion. Test type is determined by file location:
- Files in `/test/unit/` → Use mocks (TestEnvironment returns false for shouldUseRealServices())
- Files in `/test/integration/` or `/test/e2e/` → Use real services (TestEnvironment returns true)

### Centralized Testing Utilities

All projects must use the centralized testing utilities from `@libs/testing-utils`:

- **MockFactory**: Standardized mocks for all services (unit tests only)
- **TestDataGenerator**: Consistent test data generation across services
- **KeycloakTestUtils**: Real Keycloak integration testing utilities
- **TestEnvironment**: Automatic test type detection and configuration
- **IntegrationTestEnvironment**: Multi-service integration setup

Service-specific test utilities (like observability testing) are permitted but should not duplicate centralized functionality.

## Test Implementation Guidelines

### Unit Tests

- Test each component in isolation
- **ALWAYS use mocks** from `@libs/testing-utils/MockFactory`
- Focus on testing business logic
- Aim for high code coverage (>80%)

Example:

```typescript
describe('UserService', () => {
  let service: UserService;
  let mockRepository: MockType<Repository<User>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getRepositoryToken(User),
          useFactory: mockRepositoryFactory,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    mockRepository = module.get(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find a user by id', async () => {
    const user = { id: 1, name: 'Test User' };
    mockRepository.findOne.mockReturnValue(user);

    expect(await service.findById(1)).toEqual(user);
    expect(mockRepository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
  });
});
```

### Integration Tests

- Test interactions between components and external services
- **ALWAYS use real services** (Keycloak, User Service, databases)
- Use `KeycloakTestUtils` for real Keycloak testing
- Focus on testing component integration and data consistency
- Tests automatically skip if real services unavailable

Example:

```typescript
describe('Auth Integration', () => {
  let app: INestApplication;
  let authService: AuthService;

  beforeAll(async () => {
    // Skip if not in integration mode
    if (!TestEnvironment.shouldUseRealServices()) {
      return;
    }
    
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
      // NO MOCKS - use real services only
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    userService = moduleFixture.get<UserService>(UserService);
    authService = moduleFixture.get<AuthService>(AuthService);
  });

  afterAll(async () => {
    await app.close();
  });

  it('should register and authenticate a user', async () => {
    // Test registration
    const user = await authService.register({
      email: '<EMAIL>',
      password: 'password',
      firstName: 'Test',
      lastName: 'User',
    });

    expect(user).toBeDefined();
    expect(user.email).toBe('<EMAIL>');

    // Test authentication
    const tokens = await authService.login({
      email: '<EMAIL>',
      password: 'password',
    });

    expect(tokens).toBeDefined();
    expect(tokens.access_token).toBeDefined();
    expect(tokens.refresh_token).toBeDefined();
  });
});
```

### End-to-End Tests

- Test the entire application flow
- Use real implementations for all components
- Focus on testing user flows

Example:

```typescript
describe('Auth Controller (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should register a new user', () => {
    return request(app.getHttpServer())
      .post('/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password',
        firstName: 'Test',
        lastName: 'User',
      })
      .expect(201)
      .expect(res => {
        expect(res.body).toBeDefined();
        expect(res.body.email).toBe('<EMAIL>');
      });
  });

  it('should authenticate a user', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password',
      })
      .expect(200)
      .expect(res => {
        expect(res.body).toBeDefined();
        expect(res.body.access_token).toBeDefined();
        expect(res.body.refresh_token).toBeDefined();
      });
  });
});
```

## Mocks and Fixtures

### Mocks

Mocks should be placed in the `test/mocks` directory and should follow the naming convention `<module-name>.mock.ts`.

Example:

```typescript
// test/mocks/keycloak.mock.ts
export class MockKeycloakService {
  async authenticateUser(email: string, password: string) {
    return {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 300,
      token_type: 'bearer',
    };
  }

  async createUser(user: any) {
    return 'mock-keycloak-id';
  }
}
```

### Fixtures

Fixtures should be placed in the `test/fixtures` directory and should follow the naming convention `<module-name>.fixture.ts`.

Example:

```typescript
// test/fixtures/user.fixture.ts
export const userFixture = {
  id: 1,
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  keycloakId: 'mock-keycloak-id',
  createdAt: new Date(),
  updatedAt: new Date(),
};
```

## CI Environment

In CI environments, tests are run with the `CI=true` environment variable, which modifies test behavior:

- Integration tests will skip tests that require external services if they're not available
- E2E tests will fail if external services are not available

## Manual Testing

Manual testing scripts are available in the `test/manual/` directory:

- `test-auth-flow.sh`: Tests the authentication flow

To run manual tests:

```bash
cd services/auth-service
./test/manual/test-auth-flow.sh
```

## Troubleshooting

If tests are failing, check the following:

1. **Environment Configuration**: Ensure the correct environment file is being used
2. **External Services**: Ensure external services are running if required
3. **Test Data**: Ensure test data is properly set up
4. **Test Isolation**: Ensure tests are not interfering with each other

## Centralized Testing Utilities (@libs/testing-utils)

All services must use the centralized testing utilities library for consistency and maintainability:

### Core Components

- **MockFactory**: Standardized mocks for all common services (CacheService, EventPublisher, TracingService, etc.)
- **TestDataGenerator**: Consistent test data generation with unique identifiers
- **KeycloakTestUtils**: Real Keycloak integration testing with JWT token authentication
- **TestEnvironment**: Automatic test type detection and environment configuration
- **TestModuleBuilder**: Simplified test module creation with common dependencies

### Usage Examples

```typescript
// Import utilities
import { 
  MockFactory, 
  TestDataGenerator, 
  KeycloakTestUtils,
  TestEnvironment 
} from '@libs/testing-utils';

// Unit test with mocks
const mocks = MockFactory.createCommonMocks();
const testData = TestDataGenerator.createRegistrationData();

// Integration test with real Keycloak
const token = await KeycloakTestUtils.authenticateTestUser(email, password);
const testId = TestEnvironment.createTestId();
```

### Service-Specific Testing

- **Auth Service**: Comprehensive test suite with 46/46 tests passing
  - RBAC testing with real JWT tokens
  - Business event validation with observability
  - Role hierarchy security validation
  - See: `services/auth-service/test/README.md`

- **User Service**: Full database integration testing
- **API Gateway**: Routing and proxy testing with circuit breakers

## Best Practices

1. **Test Isolation**: Each test should be independent and not rely on the state of other tests.
2. **Clean Up**: Clean up any resources created during tests to avoid side effects.
3. **Use Descriptive Names**: Use descriptive names for test cases to make it clear what is being tested.
4. **Test Edge Cases**: Test edge cases and error conditions, not just the happy path.
5. **Avoid Test Duplication**: Avoid duplicating test code by using fixtures and helper functions.
6. **Keep Tests Fast**: Tests should run quickly to encourage frequent testing.
7. **Use Real Implementations When Possible**: Use real implementations when possible to ensure tests are accurate.
8. **Mock External Dependencies**: Mock external dependencies to avoid relying on external services.
9. **Test Business Logic**: Focus on testing business logic rather than implementation details.
10. **Maintain Test Coverage**: Aim for high test coverage (>80%) to ensure code quality.
11. **Use Centralized Utilities**: Always use `@libs/testing-utils` for consistency across services.
12. **Validate Observability**: Include business event and metrics validation in integration tests.
