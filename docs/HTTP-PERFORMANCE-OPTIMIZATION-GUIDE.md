# HTTP/2 + Caching + Messaging Performance Optimization Guide

This guide provides comprehensive strategies for optimizing the integrated HTTP/2, caching, and messaging pipeline in the polyrepo ecosystem.

## Executive Summary

The polyrepo's HTTP integration achieves exceptional performance through:
- **HTTP/2 multiplexing**: 60-80% performance improvement over HTTP/1.1
- **Redis-based caching**: ~95% cache hit rate improvement
- **Event-driven architecture**: Real-time performance monitoring and optimization
- **Circuit breaker protection**: Automatic failure isolation

This guide details advanced optimization techniques to maximize these capabilities.

## Performance Architecture Overview

### Request Flow Optimization

```mermaid
graph TB
    A[Client Request] --> B{Cache Check}
    B -->|Hit| C[Return Cached Response]
    B -->|Miss| D{Circuit Breaker State}
    D -->|Closed| E[HTTP/2 Request]
    D -->|Open| F[Fallback Response]
    E --> G[Service Response]
    G --> H[Cache Store]
    H --> I[Event Publishing]
    I --> J[Response to Client]
    
    K[Performance Events] --> L[Real-time Analytics]
    L --> M[Optimization Triggers]
    M --> N[Dynamic Configuration]
```

### Performance Metrics Hierarchy

```typescript
interface PerformanceMetrics {
  request: {
    total: number;
    successful: number;
    failed: number;
    averageResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
  };
  cache: {
    hitRate: number;
    missRate: number;
    evictionRate: number;
    averageRetrievalTime: number;
  };
  http2: {
    connectionsActive: number;
    streamsConcurrent: number;
    multiplexingEfficiency: number;
  };
  circuitBreaker: {
    openStates: number;
    failureRate: number;
    recoveryTime: number;
  };
}
```

## HTTP/2 Performance Optimization

### Connection Pool Optimization

```typescript
// Optimal HTTP/2 connection configuration
const optimizedHttp2Config = {
  http2: true,
  http2SessionTimeout: 300000, // 5 minutes
  http2Settings: {
    enablePush: false, // Disable server push for better control
    initialWindowSize: 32 * 1024 * 1024, // 32MB - large window
    maxFrameSize: 16384, // Default frame size
    maxConcurrentStreams: 100, // Allow many concurrent streams
    maxHeaderListSize: 8192, // 8KB header limit
  },
  agent: {
    keepAlive: true,
    keepAliveMsecs: 30000,
    maxSockets: 50, // Per host
    maxFreeSockets: 10,
    timeout: 60000,
    scheduling: 'fifo', // FIFO scheduling for fairness
  }
};

// Service-specific connection optimization
const serviceConnectionConfigs = {
  'high-frequency-service': {
    maxSockets: 100,
    maxConcurrentStreams: 200,
    keepAliveMsecs: 10000, // Keep connections warm
  },
  'batch-processing-service': {
    maxSockets: 20,
    maxConcurrentStreams: 50,
    keepAliveMsecs: 60000, // Longer keepalive for batch jobs
  },
  'real-time-service': {
    maxSockets: 150,
    maxConcurrentStreams: 300,
    keepAliveMsecs: 5000, // Frequent refreshing
  },
};
```

### Request Multiplexing Strategies

```typescript
@Injectable()
export class OptimizedHttpService {
  constructor(private httpClient: HttpClientService) {}

  // Batch related requests for optimal multiplexing
  async getUserCompleteProfile(userId: string): Promise<UserProfile> {
    const serviceClient = this.httpClient.createServiceClient('user-service');
    
    // All requests share the same HTTP/2 connection
    const [profile, permissions, preferences, activity] = await Promise.all([
      serviceClient.get(`/users/${userId}/profile`),
      serviceClient.get(`/users/${userId}/permissions`),
      serviceClient.get(`/users/${userId}/preferences`),
      serviceClient.get(`/users/${userId}/recent-activity`),
    ]);

    return {
      ...profile.data,
      permissions: permissions.data,
      preferences: preferences.data,
      recentActivity: activity.data,
    };
  }

  // Sequential requests when data dependencies exist
  async getOrderWithDetails(orderId: string): Promise<OrderDetails> {
    const serviceClient = this.httpClient.createServiceClient('order-service');
    
    // Get order first
    const order = await serviceClient.get(`/orders/${orderId}`);
    
    // Then get dependent data in parallel
    const [items, shipping, payment] = await Promise.all([
      serviceClient.get(`/orders/${orderId}/items`),
      serviceClient.get(`/orders/${orderId}/shipping`),
      serviceClient.get(`/orders/${orderId}/payment`),
    ]);

    return {
      order: order.data,
      items: items.data,
      shipping: shipping.data,
      payment: payment.data,
    };
  }
}
```

### HTTP/2 Push Alternative (Request Prediction)

```typescript
// Since server push is disabled, implement request prediction
@Injectable()
export class RequestPredictionService {
  constructor(
    private httpClient: HttpClientService,
    private cache: CacheService,
    private eventBus: EventBusService,
  ) {}

  @OnEvent('http.request.completed')
  async analyzeRequestPattern(event: HttpResponseEvent): Promise<void> {
    const pattern = await this.identifyRequestPattern(event);
    
    if (pattern.confidence > 0.8) {
      // Pre-fetch likely next requests
      await this.prefetchPredictedRequests(pattern.nextRequests);
    }
  }

  private async prefetchPredictedRequests(requests: PredictedRequest[]): Promise<void> {
    const prefetchPromises = requests.map(async (request) => {
      try {
        const response = await this.httpClient.get(request.url, {
          serviceName: request.serviceName,
          cache: { enabled: true, ttl: 60000 }, // Short TTL for prefetched data
          operationName: `prefetch-${request.operation}`,
        });
        
        // Data is now cached for immediate retrieval
        this.eventBus.emit('prefetch.completed', {
          url: request.url,
          success: true,
          cacheKey: this.generateCacheKey(request),
        });
      } catch (error) {
        // Prefetch failures shouldn't affect main flow
        this.eventBus.emit('prefetch.failed', {
          url: request.url,
          error: error.message,
        });
      }
    });

    // Fire-and-forget prefetching
    Promise.allSettled(prefetchPromises);
  }
}
```

## Cache Performance Optimization

### Multi-Layer Caching Strategy

```typescript
interface CacheLayerConfig {
  l1: { // In-memory cache (fastest)
    enabled: boolean;
    maxSize: number;
    ttl: number;
  };
  l2: { // Redis cache (shared)
    enabled: boolean;
    ttl: number;
    compression: boolean;
  };
  l3: { // HTTP cache (service level)
    enabled: boolean;
    ttl: number;
    shared: boolean;
  };
}

@Injectable()
export class MultiLayerCacheService {
  private l1Cache = new Map<string, CacheEntry>();
  
  constructor(
    private redisCache: CacheService,
    private httpClient: HttpClientService,
  ) {}

  async get<T>(key: string, layers: CacheLayerConfig): Promise<T | null> {
    // L1: In-memory cache (sub-millisecond)
    if (layers.l1.enabled) {
      const l1Result = this.l1Cache.get(key);
      if (l1Result && !this.isExpired(l1Result)) {
        this.publishCacheEvent('l1-hit', key);
        return l1Result.data;
      }
    }

    // L2: Redis cache (~1-2ms)
    if (layers.l2.enabled) {
      const l2Result = await this.redisCache.get(key);
      if (l2Result) {
        // Populate L1 cache
        if (layers.l1.enabled) {
          this.l1Cache.set(key, {
            data: l2Result,
            expiry: Date.now() + layers.l1.ttl,
          });
        }
        this.publishCacheEvent('l2-hit', key);
        return l2Result;
      }
    }

    this.publishCacheEvent('cache-miss', key);
    return null;
  }

  async set<T>(key: string, value: T, layers: CacheLayerConfig): Promise<void> {
    // Store in all enabled layers
    const promises: Promise<void>[] = [];

    if (layers.l1.enabled) {
      this.l1Cache.set(key, {
        data: value,
        expiry: Date.now() + layers.l1.ttl,
      });
    }

    if (layers.l2.enabled) {
      promises.push(this.redisCache.set(key, value, layers.l2.ttl));
    }

    await Promise.all(promises);
    this.publishCacheEvent('cache-set', key);
  }
}
```

### Intelligent Cache Warming

```typescript
@Injectable()
export class CacheWarmingService {
  constructor(
    private httpClient: HttpClientService,
    private cache: CacheService,
    private metrics: MetricsService,
  ) {}

  @Cron('0 */6 * * *') // Every 6 hours
  async warmFrequentlyAccessedData(): Promise<void> {
    const hotPaths = await this.identifyHotPaths();
    
    for (const path of hotPaths) {
      await this.warmCachePath(path);
    }
  }

  private async identifyHotPaths(): Promise<HotPath[]> {
    // Analyze HTTP events to find frequently accessed endpoints
    const analytics = await this.metrics.query(`
      sum by (url, method) (
        increase(http_requests_total[24h])
      ) > 100
    `);

    return analytics.map(metric => ({
      url: metric.url,
      method: metric.method,
      frequency: metric.value,
      lastAccessed: metric.lastSeen,
    }));
  }

  private async warmCachePath(path: HotPath): Promise<void> {
    try {
      await this.httpClient.request(path.method, path.url, {
        operationName: 'cache-warming',
        cache: { enabled: true, ttl: 3600000 }, // 1 hour
        timeout: 30000, // Longer timeout for warming
      });

      this.metrics.increment('cache_warming_success', {
        path: path.url,
        method: path.method,
      });
    } catch (error) {
      this.metrics.increment('cache_warming_failure', {
        path: path.url,
        method: path.method,
        error: error.message,
      });
    }
  }
}
```

### Cache Invalidation Optimization

```typescript
// Event-driven cache invalidation for better performance
@Injectable()
export class SmartCacheInvalidator {
  private invalidationRules = new Map<string, InvalidationRule[]>();
  
  constructor(
    private cache: CacheService,
    private eventBus: EventBusService,
  ) {
    this.setupInvalidationRules();
  }

  private setupInvalidationRules(): void {
    // User-related invalidations
    this.invalidationRules.set('user.profile.updated', [
      { pattern: 'users:profile:${userId}', immediate: true },
      { pattern: 'users:search:*', delay: 5000 }, // Delayed for search indices
      { pattern: 'users:list:*', delay: 10000 }, // Delayed for lists
    ]);

    // Product-related invalidations
    this.invalidationRules.set('product.updated', [
      { pattern: 'products:${productId}:*', immediate: true },
      { pattern: 'categories:${categoryId}:*', delay: 2000 },
      { pattern: 'search:products:*', delay: 30000 }, // Longer delay for search
    ]);
  }

  @OnEvent('user.profile.updated')
  async handleUserProfileUpdate(event: UserProfileUpdatedEvent): Promise<void> {
    const rules = this.invalidationRules.get('user.profile.updated') || [];
    
    for (const rule of rules) {
      const pattern = this.resolvePattern(rule.pattern, event.data);
      
      if (rule.immediate) {
        await this.cache.deletePattern(pattern);
      } else {
        // Delayed invalidation to allow for grace period
        setTimeout(async () => {
          await this.cache.deletePattern(pattern);
        }, rule.delay);
      }
    }
  }

  private resolvePattern(pattern: string, data: any): string {
    return pattern.replace(/\$\{(\w+)\}/g, (match, key) => {
      return data[key] || match;
    });
  }
}
```

## Messaging Pipeline Optimization

### Event Batching for Performance

```typescript
@Injectable()
export class OptimizedEventPublisher {
  private eventBuffer: DomainEvent[] = [];
  private batchSize = 100;
  private flushInterval = 1000; // 1 second
  private performanceMode: 'throughput' | 'latency' = 'throughput';

  constructor(
    private messagePublisher: RedisStreamsPublisher,
    private metrics: MetricsService,
  ) {
    this.startBatchProcessor();
  }

  async publishEvent(event: DomainEvent): Promise<void> {
    if (this.performanceMode === 'latency') {
      // Immediate publishing for latency-sensitive events
      await this.messagePublisher.publish(event);
      return;
    }

    // Batch publishing for throughput optimization
    this.eventBuffer.push(event);
    
    if (this.eventBuffer.length >= this.batchSize) {
      await this.flushBatch();
    }
  }

  private startBatchProcessor(): void {
    setInterval(async () => {
      if (this.eventBuffer.length > 0) {
        await this.flushBatch();
      }
    }, this.flushInterval);
  }

  private async flushBatch(): Promise<void> {
    const batch = this.eventBuffer.splice(0, this.batchSize);
    
    try {
      const start = performance.now();
      await this.messagePublisher.publishBatch(batch);
      const duration = performance.now() - start;

      this.metrics.histogram('event_batch_publish_duration', duration, {
        batchSize: batch.length.toString(),
      });

      this.metrics.increment('events_published_total', batch.length);
    } catch (error) {
      // Re-queue failed events
      this.eventBuffer.unshift(...batch);
      
      this.metrics.increment('event_batch_publish_failures', {
        batchSize: batch.length.toString(),
        error: error.message,
      });
    }
  }
}
```

### Event Filtering and Routing Optimization

```typescript
@Injectable()
export class HighPerformanceEventRouter {
  private routingRules = new Map<string, RoutingRule[]>();
  private eventFilters = new Map<string, EventFilter>();

  constructor() {
    this.setupRoutingOptimization();
  }

  private setupRoutingOptimization(): void {
    // High-frequency events get priority routing
    this.routingRules.set('http.request.*', [
      { destination: 'analytics-stream', priority: 'low' },
      { destination: 'metrics-stream', priority: 'high', filter: 'errors-only' },
    ]);

    this.routingRules.set('cache.operation.*', [
      { destination: 'performance-stream', priority: 'medium' },
    ]);

    // Setup efficient filters
    this.eventFilters.set('errors-only', (event) => {
      return event.type.includes('error') || 
             (event.data.statusCode && event.data.statusCode >= 400);
    });
  }

  async routeEvent(event: DomainEvent): Promise<void> {
    const rules = this.findMatchingRules(event.type);
    
    // Sort by priority for optimal processing
    const sortedRules = rules.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });

    const routingPromises = sortedRules.map(async (rule) => {
      // Apply filter if specified
      if (rule.filter) {
        const filter = this.eventFilters.get(rule.filter);
        if (filter && !filter(event)) {
          return; // Skip this rule
        }
      }

      await this.publishToDestination(event, rule.destination, rule.priority);
    });

    // Process all routing rules in parallel
    await Promise.allSettled(routingPromises);
  }

  private findMatchingRules(eventType: string): RoutingRule[] {
    const matchingRules: RoutingRule[] = [];
    
    for (const [pattern, rules] of this.routingRules) {
      if (this.matchesPattern(eventType, pattern)) {
        matchingRules.push(...rules);
      }
    }

    return matchingRules;
  }

  private matchesPattern(eventType: string, pattern: string): boolean {
    // Convert glob pattern to regex for efficient matching
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*');
    
    return new RegExp(`^${regexPattern}$`).test(eventType);
  }
}
```

## Circuit Breaker Performance Optimization

### Adaptive Circuit Breaker Thresholds

```typescript
@Injectable()
export class AdaptiveCircuitBreakerService {
  private serviceMetrics = new Map<string, ServiceMetrics>();
  private adaptationInterval = 60000; // 1 minute

  constructor(
    private circuitBreaker: CircuitBreakerService,
    private metrics: MetricsService,
  ) {
    this.startAdaptiveAdjustment();
  }

  private startAdaptiveAdjustment(): void {
    setInterval(async () => {
      await this.adjustCircuitBreakerThresholds();
    }, this.adaptationInterval);
  }

  private async adjustCircuitBreakerThresholds(): Promise<void> {
    const services = await this.circuitBreaker.getAllServices();
    
    for (const serviceName of services) {
      const metrics = await this.analyzeServiceMetrics(serviceName);
      const currentConfig = await this.circuitBreaker.getConfig(serviceName);
      
      const adaptedConfig = this.calculateOptimalThresholds(metrics, currentConfig);
      
      if (this.shouldUpdateConfig(currentConfig, adaptedConfig)) {
        await this.circuitBreaker.updateConfig(serviceName, adaptedConfig);
        
        this.metrics.increment('circuit_breaker_config_adapted', {
          service: serviceName,
          reason: adaptedConfig.adaptationReason,
        });
      }
    }
  }

  private calculateOptimalThresholds(
    metrics: ServiceMetrics,
    current: CircuitBreakerConfig
  ): CircuitBreakerConfig {
    const adapted = { ...current };

    // Adapt based on service reliability
    if (metrics.errorRate < 0.01) { // Very reliable service
      adapted.errorThresholdPercentage = Math.min(
        current.errorThresholdPercentage * 1.2, // Increase threshold by 20%
        0.8 // Max 80%
      );
      adapted.adaptationReason = 'high-reliability';
    } else if (metrics.errorRate > 0.1) { // Unreliable service
      adapted.errorThresholdPercentage = Math.max(
        current.errorThresholdPercentage * 0.8, // Decrease threshold by 20%
        0.1 // Min 10%
      );
      adapted.adaptationReason = 'low-reliability';
    }

    // Adapt timeout based on response time patterns
    if (metrics.p95ResponseTime > current.timeout * 0.8) {
      adapted.timeout = Math.min(
        metrics.p95ResponseTime * 1.5, // 150% of P95
        current.timeout * 2 // Max double current timeout
      );
      adapted.adaptationReason = 'slow-response';
    }

    return adapted;
  }
}
```

## Performance Monitoring and Analytics

### Real-Time Performance Dashboard

```typescript
@Injectable()
export class PerformanceDashboardService {
  constructor(
    private eventAggregator: EventAggregatorService,
    private metrics: MetricsService,
  ) {}

  async generatePerformanceSnapshot(): Promise<PerformanceSnapshot> {
    const [httpMetrics, cacheMetrics, eventMetrics] = await Promise.all([
      this.getHttpPerformanceMetrics(),
      this.getCachePerformanceMetrics(),
      this.getEventingPerformanceMetrics(),
    ]);

    return {
      timestamp: new Date(),
      http: httpMetrics,
      cache: cacheMetrics,
      eventing: eventMetrics,
      overall: this.calculateOverallScore(httpMetrics, cacheMetrics, eventMetrics),
    };
  }

  private async getHttpPerformanceMetrics(): Promise<HttpPerformanceMetrics> {
    const timeWindow = '5m';
    
    const [requestRate, errorRate, responseTime, multiplexingEfficiency] = await Promise.all([
      this.metrics.query(`sum(rate(http_requests_total[${timeWindow}]))`),
      this.metrics.query(`sum(rate(http_requests_total{status=~"5.."}[${timeWindow}])) / sum(rate(http_requests_total[${timeWindow}]))`),
      this.metrics.query(`histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[${timeWindow}]))`),
      this.calculateMultiplexingEfficiency(timeWindow),
    ]);

    return {
      requestsPerSecond: requestRate[0]?.value || 0,
      errorRate: errorRate[0]?.value || 0,
      p95ResponseTime: responseTime[0]?.value || 0,
      multiplexingEfficiency: multiplexingEfficiency,
      performanceScore: this.calculateHttpScore(requestRate[0]?.value, errorRate[0]?.value, responseTime[0]?.value),
    };
  }

  private async getCachePerformanceMetrics(): Promise<CachePerformanceMetrics> {
    const timeWindow = '5m';
    
    const [hitRate, missRate, evictionRate, avgRetrievalTime] = await Promise.all([
      this.metrics.query(`sum(rate(cache_hits_total[${timeWindow}])) / sum(rate(cache_operations_total[${timeWindow}]))`),
      this.metrics.query(`sum(rate(cache_misses_total[${timeWindow}])) / sum(rate(cache_operations_total[${timeWindow}]))`),
      this.metrics.query(`sum(rate(cache_evictions_total[${timeWindow}]))`),
      this.metrics.query(`histogram_quantile(0.95, rate(cache_operation_duration_seconds_bucket[${timeWindow}]))`),
    ]);

    return {
      hitRate: hitRate[0]?.value || 0,
      missRate: missRate[0]?.value || 0,
      evictionRate: evictionRate[0]?.value || 0,
      averageRetrievalTime: avgRetrievalTime[0]?.value || 0,
      performanceScore: this.calculateCacheScore(hitRate[0]?.value, avgRetrievalTime[0]?.value),
    };
  }

  private calculateOverallScore(
    http: HttpPerformanceMetrics,
    cache: CachePerformanceMetrics,
    eventing: EventingPerformanceMetrics
  ): number {
    // Weighted scoring based on impact
    const weights = { http: 0.5, cache: 0.3, eventing: 0.2 };
    
    return (
      http.performanceScore * weights.http +
      cache.performanceScore * weights.cache +
      eventing.performanceScore * weights.eventing
    );
  }
}
```

### Performance Optimization Triggers

```typescript
@Injectable()
export class PerformanceOptimizationService {
  constructor(
    private dashboard: PerformanceDashboardService,
    private httpConfig: HttpConfigService,
    private cacheConfig: CacheConfigService,
  ) {}

  @Cron('*/30 * * * * *') // Every 30 seconds
  async checkAndOptimize(): Promise<void> {
    const snapshot = await this.dashboard.generatePerformanceSnapshot();
    
    await Promise.all([
      this.optimizeHttpConfiguration(snapshot.http),
      this.optimizeCacheConfiguration(snapshot.cache),
      this.optimizeEventingConfiguration(snapshot.eventing),
    ]);
  }

  private async optimizeHttpConfiguration(metrics: HttpPerformanceMetrics): Promise<void> {
    // Automatically adjust connection pool based on load
    if (metrics.requestsPerSecond > 1000) {
      await this.httpConfig.updateConnectionPool({
        maxSockets: Math.min(metrics.requestsPerSecond / 10, 200),
        maxConcurrentStreams: Math.min(metrics.requestsPerSecond / 5, 400),
      });
    }

    // Adjust timeouts based on response time patterns
    if (metrics.p95ResponseTime > 5000) { // 5 seconds
      await this.httpConfig.updateTimeouts({
        responseTimeout: metrics.p95ResponseTime * 1.5,
        retryDelay: Math.min(metrics.p95ResponseTime / 10, 2000),
      });
    }
  }

  private async optimizeCacheConfiguration(metrics: CachePerformanceMetrics): Promise<void> {
    // Adjust cache TTL based on hit rates
    if (metrics.hitRate < 0.7) { // Less than 70% hit rate
      await this.cacheConfig.increaseTtl(1.2); // Increase TTL by 20%
    } else if (metrics.hitRate > 0.95) { // Very high hit rate
      await this.cacheConfig.decreaseTtl(0.9); // Decrease TTL by 10%
    }

    // Adjust cache size based on eviction rate
    if (metrics.evictionRate > 0.1) { // High eviction rate
      await this.cacheConfig.increaseCacheSize(1.5); // Increase size by 50%
    }
  }
}
```

## Best Practices and Patterns

### Performance Testing Strategies

```typescript
@Injectable()
export class PerformanceTestSuite {
  constructor(
    private httpClient: HttpClientService,
    private loadGenerator: LoadGeneratorService,
  ) {}

  async runComprehensivePerformanceTest(): Promise<PerformanceTestReport> {
    const tests = [
      this.testHttpThroughput(),
      this.testCacheEfficiency(),
      this.testCircuitBreakerResilience(),
      this.testEventingPerformance(),
    ];

    const results = await Promise.allSettled(tests);
    
    return this.generateTestReport(results);
  }

  private async testHttpThroughput(): Promise<ThroughputTestResult> {
    const testConfig = {
      duration: 60000, // 1 minute
      concurrency: 50,
      requestsPerSecond: 100,
      endpoint: '/api/test/performance',
    };

    const results = await this.loadGenerator.runLoadTest(testConfig);
    
    return {
      testName: 'HTTP Throughput',
      requestsPerSecond: results.throughput,
      averageResponseTime: results.avgResponseTime,
      p95ResponseTime: results.p95ResponseTime,
      errorRate: results.errorRate,
      passed: results.errorRate < 0.01 && results.p95ResponseTime < 1000,
    };
  }

  private async testCacheEfficiency(): Promise<CacheTestResult> {
    // Test cache warming
    await this.warmTestCache();
    
    // Run requests that should hit cache
    const cacheTestResults = await this.runCacheTest();
    
    return {
      testName: 'Cache Efficiency',
      hitRate: cacheTestResults.hitRate,
      missRate: cacheTestResults.missRate,
      averageRetrievalTime: cacheTestResults.avgRetrievalTime,
      passed: cacheTestResults.hitRate > 0.8 && cacheTestResults.avgRetrievalTime < 10,
    };
  }
}
```

### Configuration Management

```typescript
// Environment-specific performance configurations
export const performanceConfigs = {
  development: {
    http: {
      maxSockets: 10,
      maxConcurrentStreams: 20,
      timeout: 30000,
      retries: 3,
    },
    cache: {
      ttl: 60000, // 1 minute
      maxSize: 100,
      compression: false,
    },
    events: {
      batchSize: 10,
      flushInterval: 5000,
    },
  },
  
  production: {
    http: {
      maxSockets: 100,
      maxConcurrentStreams: 200,
      timeout: 10000,
      retries: 5,
    },
    cache: {
      ttl: 3600000, // 1 hour
      maxSize: 10000,
      compression: true,
    },
    events: {
      batchSize: 100,
      flushInterval: 1000,
    },
  },
  
  'high-load': {
    http: {
      maxSockets: 200,
      maxConcurrentStreams: 500,
      timeout: 5000,
      retries: 2, // Fail fast under high load
    },
    cache: {
      ttl: 7200000, // 2 hours
      maxSize: 50000,
      compression: true,
    },
    events: {
      batchSize: 500,
      flushInterval: 500,
    },
  },
};
```

## Troubleshooting Performance Issues

### Performance Bottleneck Identification

```typescript
@Injectable()
export class PerformanceProfiler {
  async identifyBottlenecks(): Promise<BottleneckAnalysis> {
    const [httpBottlenecks, cacheBottlenecks, eventBottlenecks] = await Promise.all([
      this.analyzeHttpBottlenecks(),
      this.analyzeCacheBottlenecks(),
      this.analyzeEventBottlenecks(),
    ]);

    return {
      http: httpBottlenecks,
      cache: cacheBottlenecks,
      eventing: eventBottlenecks,
      recommendations: this.generateRecommendations(httpBottlenecks, cacheBottlenecks, eventBottlenecks),
    };
  }

  private async analyzeHttpBottlenecks(): Promise<HttpBottleneckAnalysis> {
    // Query Prometheus for HTTP performance data
    const slowEndpoints = await this.metrics.query(`
      topk(10, avg_over_time(http_request_duration_seconds{quantile="0.95"}[1h]))
    `);

    const connectionPoolUtilization = await this.metrics.query(`
      avg(http_connection_pool_active / http_connection_pool_max)
    `);

    return {
      slowEndpoints: slowEndpoints.map(metric => ({
        endpoint: metric.labels.endpoint,
        avgResponseTime: metric.value,
        recommendation: this.getEndpointRecommendation(metric.value),
      })),
      connectionPoolUtilization: connectionPoolUtilization[0]?.value || 0,
      multiplexingEfficiency: await this.calculateMultiplexingEfficiency(),
    };
  }

  private getEndpointRecommendation(responseTime: number): string {
    if (responseTime > 5000) {
      return 'Critical: Review endpoint logic, add caching, or increase timeout';
    } else if (responseTime > 2000) {
      return 'Warning: Consider optimization or caching strategies';
    }
    return 'Acceptable performance';
  }
}
```

### Memory Usage Optimization

```typescript
@Injectable()
export class MemoryOptimizationService {
  private memoryMetrics = new Map<string, MemoryMetric>();

  @Cron('*/5 * * * *') // Every 5 minutes
  async monitorMemoryUsage(): Promise<void> {
    const usage = process.memoryUsage();
    
    this.memoryMetrics.set('heap_used', {
      value: usage.heapUsed,
      timestamp: Date.now(),
      threshold: 500 * 1024 * 1024, // 500MB
    });

    this.memoryMetrics.set('heap_total', {
      value: usage.heapTotal,
      timestamp: Date.now(),
      threshold: 1024 * 1024 * 1024, // 1GB
    });

    // Check for memory pressure
    if (usage.heapUsed > 500 * 1024 * 1024) {
      await this.triggerMemoryOptimization();
    }
  }

  private async triggerMemoryOptimization(): Promise<void> {
    // Clear expired cache entries
    await this.clearExpiredCacheEntries();
    
    // Reduce event buffer sizes
    await this.reduceEventBufferSizes();
    
    // Suggest garbage collection
    if (global.gc) {
      global.gc();
    }
  }
}
```

## Success Metrics and KPIs

### Performance KPIs

```typescript
interface PerformanceKPIs {
  http: {
    requestsPerSecond: number; // Target: >1000 RPS
    p95ResponseTime: number;   // Target: <500ms
    errorRate: number;         // Target: <0.1%
    multiplexingEfficiency: number; // Target: >80%
  };
  cache: {
    hitRate: number;           // Target: >90%
    averageRetrievalTime: number; // Target: <10ms
    evictionRate: number;      // Target: <5%
  };
  overall: {
    throughputImprovement: number;  // Target: >60% vs HTTP/1.1
    latencyReduction: number;       // Target: >40% vs baseline
    resourceUtilization: number;    // Target: <80% CPU/Memory
  };
}
```

This comprehensive performance optimization guide provides strategies for maximizing the efficiency of the HTTP/2 + caching + messaging pipeline while maintaining reliability and observability.