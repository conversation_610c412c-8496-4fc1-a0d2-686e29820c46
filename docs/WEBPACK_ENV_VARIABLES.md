# Environment Variable Handling in Webpack-Bundled NestJS Applications

## Summary of Approaches

We investigated several approaches to handle environment variables in a webpack-bundled NestJS application running in Docker:

1. ✅ **Docker Compose with env_file**: Most flexible, best for development and production
2. ✅ **Dockerfile with ENV statements**: Works but less flexible, embeds values in image
3. ✅ **Webpack with dotenv-webpack plugin**: Works when configured correctly
4. ❌ **Hardcoded DefinePlugin replacement**: Caused runtime errors

## What Worked

### 1. Docker Compose with env_file

The most flexible approach is using Docker Compose with an `env_file` directive. This allows environment variables to be loaded from a file without hardcoding them in the Dockerfile:

```yaml
services:
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.webpack
    ports:
      - "3001:3000"
    env_file:
      - .env.docker
```

Benefits:
- Easy to switch environment files for different environments
- No sensitive data in Docker image layers
- Clear separation of code and configuration

### 2. dotenv-webpack Plugin Configuration

For the webpack bundle to properly support runtime environment variables with NestJS's ConfigService, these settings were crucial:

```javascript
new DotenvWebpack({
  path: './.env.docker',
  systemvars: true,     // Load all system environment variables as well
  safe: false,         
  defaults: false,
  allowEmptyValues: true,
  // This ensures process.env remains intact during runtime
  ignoreStub: true  // CRITICAL setting!
})
```

The `ignoreStub: true` option was essential as it prevents dotenv-webpack from completely replacing process.env references at build time, allowing NestJS's ConfigService to access them at runtime.

## What Didn't Work

1. ❌ **Complete process.env replacement with DefinePlugin**: This caused syntax errors in the bundle because webpack tried to substitute a complete object for all process.env references:

```javascript
// This FAILED
new webpack.DefinePlugin({
  'process.env': JSON.stringify({
    NODE_ENV: 'docker',
    // other env vars...
  })
})
```

2. ❌ **Individual variable replacement with DefinePlugin**: This caused runtime issues with NestJS's ConfigService which couldn't find the variables:

```javascript
// This FAILED with NestJS's ConfigService
new webpack.DefinePlugin({
  'process.env.NODE_ENV': JSON.stringify('docker'),
  'process.env.PORT': JSON.stringify('3000'),
  // other env vars...
})
```

## Best Practices

Based on our experimentation, the recommended approach is:

1. Use `dotenv-webpack` with `ignoreStub: true` in webpack.config.js
2. Load environment variables at runtime via Docker Compose's `env_file`
3. Keep Dockerfile clean without hardcoded environment variables
4. For production, consider using Docker secrets for sensitive values

## Debugging Tips

When troubleshooting environment variable issues:

1. Check Docker logs for any error messages
2. Create a test script that outputs `process.env` values
3. Try running the container with `--env-file` flag for Docker run command
4. Verify that NestJS's ConfigService can access the variables

## Example Implementation

Our final implementation uses:

1. Docker Compose with env_file for runtime environment variables
2. dotenv-webpack with ignoreStub: true for webpack bundling
3. A clean Dockerfile without hardcoded ENV statements

This approach provides the best balance of security, flexibility, and ease of maintenance.
