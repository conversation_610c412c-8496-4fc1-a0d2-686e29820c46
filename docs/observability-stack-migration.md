# Observability Stack Migration: <PERSON><PERSON><PERSON> → Tempo + Pyroscope + Faro

## Executive Summary

Migration plan to modernize the observability stack by replacing <PERSON><PERSON><PERSON> with Grafana Tempo, adding Pyroscope for continuous profiling, and implementing basic Faro setup for future frontend observability.

## Current State Analysis

### Jaeger Infrastructure (Current)
```yaml
# infrastructure/monitoring/docker-compose.yml
services:
  jaeger:
    image: jaegertracing/all-in-one:1.57
    ports:
      - "16686:16686"    # Jaeger UI
      - "14250:14250"    # gRPC
      - "14268:14268"    # HTTP
    environment:
      - COLLECTOR_OTLP_ENABLED=true
```

### Current Integration Points
- **Observability Library**: `/libs/observability/src/tracing/tracing.service.ts`
- **Services**: All use `JAEGER_ENDPOINT=http://localhost:14268/api/traces`
- **Grafana**: Jaeger datasource configured in provisioning
- **Known Issues**: Webpack compatibility problems, test mocking challenges

## Migration Strategy

### Phase 1: Tempo Migration (Week 1)

#### Step 1: Infrastructure Setup
```yaml
# infrastructure/monitoring/docker-compose.yml - New Tempo Setup
services:
  tempo:
    image: grafana/tempo:2.3.1
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./tempo/tempo.yaml:/etc/tempo.yaml
      - tempo-data:/var/tempo
    ports:
      - "3200:3200"    # Tempo HTTP
      - "9095:9095"    # Tempo gRPC
      - "4317:4317"    # OTLP gRPC
      - "4318:4318"    # OTLP HTTP
    networks:
      - monitoring

  # OpenTelemetry Collector for better trace processing
  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.89.0
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./otel/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "8889:8889"   # Prometheus metrics
    depends_on:
      - tempo
    networks:
      - monitoring

volumes:
  tempo-data:

networks:
  monitoring:
    driver: bridge
```

#### Step 2: Tempo Configuration
```yaml
# infrastructure/monitoring/tempo/tempo.yaml
server:
  http_listen_port: 3200
  grpc_listen_port: 9095

distributor:
  receivers:
    otlp:
      protocols:
        grpc:
          endpoint: 0.0.0.0:4317
        http:
          endpoint: 0.0.0.0:4318

ingester:
  max_block_duration: 5m

compactor:
  compaction:
    block_retention: 1h

storage:
  trace:
    backend: local
    local:
      path: /var/tempo/traces
    wal:
      path: /var/tempo/wal

query_frontend:
  search:
    duration_slo: 5s
    throughput_bytes_slo: 1.073741824e+09
  trace_by_id:
    duration_slo: 5s
```

#### Step 3: OpenTelemetry Collector Configuration
```yaml
# infrastructure/monitoring/otel/otel-collector-config.yaml
receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  memory_limiter:
    limit_mib: 512

exporters:
  otlp:
    endpoint: tempo:4317
    tls:
      insecure: true
  prometheus:
    endpoint: "0.0.0.0:8889"

service:
  pipelines:
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [otlp]
    metrics:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [prometheus]
```

#### Step 4: Update Observability Library
```typescript
// libs/observability/src/tracing/tracing.service.ts - Updated for Tempo
import { NodeSDK } from '@opentelemetry/auto-instrumentations-node';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { OTLPTraceExporter } from '@opentelemetry/exporter-otlp-http';

@Injectable()
export class TracingService implements OnModuleInit, OnModuleDestroy {
  private sdk?: NodeSDK;
  
  constructor(
    @Inject('OBSERVABILITY_CONFIG') private config: ObservabilityConfig,
    private logger: ObservabilityLogger
  ) {}

  async onModuleInit() {
    if (!this.config.tracing?.enabled) {
      this.logger.log('Tracing disabled via configuration');
      return;
    }

    try {
      // Use OTLP exporter for Tempo compatibility
      const traceExporter = new OTLPTraceExporter({
        url: this.config.tracing.endpoint || 'http://localhost:4318/v1/traces',
        headers: {},
      });

      this.sdk = new NodeSDK({
        resource: new Resource({
          [SemanticResourceAttributes.SERVICE_NAME]: this.config.serviceName,
          [SemanticResourceAttributes.SERVICE_VERSION]: this.config.version || '1.0.0',
          [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: this.config.environment || 'development',
        }),
        traceExporter,
        instrumentations: [], // Auto-instrumentations
      });

      await this.sdk.start();
      this.logger.log(`✅ Tracing initialized for ${this.config.serviceName} (${this.config.environment})`);
    } catch (error) {
      this.logger.error('Failed to initialize tracing:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    if (this.sdk) {
      try {
        await this.sdk.shutdown();
        this.logger.log('Tracing terminated');
      } catch (error) {
        this.logger.error('Error shutting down tracing:', error);
      }
    }
  }

  // Enhanced tracing methods for Tempo
  async traceAsyncOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    attributes?: Record<string, string | number | boolean>
  ): Promise<T> {
    const tracer = trace.getTracer(this.config.serviceName);
    
    return tracer.startActiveSpan(operationName, { attributes }, async (span) => {
      try {
        const result = await operation();
        span.setStatus({ code: SpanStatusCode.OK });
        return result;
      } catch (error) {
        span.recordException(error);
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: error.message,
        });
        throw error;
      } finally {
        span.end();
      }
    });
  }
}
```

#### Step 5: Environment Variable Updates
```bash
# Before (Jaeger)
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# After (Tempo via OTLP)
TEMPO_ENDPOINT=http://localhost:4318/v1/traces
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://localhost:4318/v1/traces
OTEL_SERVICE_NAME=auth-service
OTEL_RESOURCE_ATTRIBUTES=service.name=auth-service,service.version=1.0.0
```

#### Step 6: Grafana Datasource Configuration
```yaml
# infrastructure/monitoring/grafana/provisioning/datasources/tempo.yaml
apiVersion: 1

datasources:
  - name: Tempo
    type: tempo
    access: proxy
    url: http://tempo:3200
    uid: tempo
    editable: true
    basicAuth: false
    isDefault: false
    version: 1
    jsonData:
      httpMethod: GET
      serviceMap:
        datasourceUid: prometheus
      search:
        hide: false
      nodeGraph:
        enabled: true
      traceQuery:
        timeShiftEnabled: true
        spanStartTimeShift: 1h
        spanEndTimeShift: 1h
      spanBar:
        type: Tag
        tag: http.method
```

### Phase 2: Pyroscope Integration (Week 2)

#### Step 1: Pyroscope Server Setup
```yaml
# infrastructure/monitoring/docker-compose.yml - Add Pyroscope
services:
  pyroscope:
    image: grafana/pyroscope:1.5.0
    ports:
      - "4040:4040"
    command:
      - "server"
    environment:
      - PYROSCOPE_LOG_LEVEL=debug
      - PYROSCOPE_STORAGE_PATH=/var/lib/pyroscope
    volumes:
      - pyroscope-data:/var/lib/pyroscope
    networks:
      - monitoring

volumes:
  pyroscope-data:
```

#### Step 2: Node.js Pyroscope Integration
```typescript
// libs/observability/src/profiling/pyroscope.service.ts
import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PyroscopeProfiler } from '@pyroscope/nodejs';
import { ObservabilityLogger } from '../logging/logger.service';

export interface PyroscopeConfig {
  enabled: boolean;
  serverAddress: string;
  applicationName: string;
  tags?: Record<string, string>;
  sampleRate?: number;
}

@Injectable()
export class PyroscopeService implements OnModuleInit, OnModuleDestroy {
  private profiler?: PyroscopeProfiler;

  constructor(
    private config: PyroscopeConfig,
    private logger: ObservabilityLogger
  ) {}

  async onModuleInit() {
    if (!this.config.enabled) {
      this.logger.log('Pyroscope profiling disabled via configuration');
      return;
    }

    try {
      this.profiler = new PyroscopeProfiler({
        serverAddress: this.config.serverAddress,
        appName: this.config.applicationName,
        tags: {
          version: process.env.APP_VERSION || '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          ...this.config.tags,
        },
        profileTypes: [
          'cpu',
          'heap',
          'goroutines',
        ],
        sampleRate: this.config.sampleRate || 100, // Hz
      });

      await this.profiler.start();
      this.logger.log(`✅ Pyroscope profiling started for ${this.config.applicationName}`);
    } catch (error) {
      this.logger.error('Failed to start Pyroscope profiling:', error);
      // Non-critical failure - continue without profiling
    }
  }

  async onModuleDestroy() {
    if (this.profiler) {
      try {
        await this.profiler.stop();
        this.logger.log('Pyroscope profiling stopped');
      } catch (error) {
        this.logger.error('Error stopping Pyroscope profiling:', error);
      }
    }
  }

  /**
   * Add custom labels to profiling data
   */
  addLabels(labels: Record<string, string>): void {
    if (this.profiler) {
      this.profiler.addLabels(labels);
    }
  }

  /**
   * Profile a specific operation
   */
  async profileOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    labels?: Record<string, string>
  ): Promise<T> {
    if (!this.profiler) {
      return operation();
    }

    const originalLabels = { operation: operationName, ...labels };
    this.addLabels(originalLabels);

    try {
      return await operation();
    } finally {
      // Remove operation-specific labels
      // Note: Pyroscope doesn't have a direct removeLabels method
      // Labels are typically scoped to the profiling session
    }
  }
}
```

#### Step 3: Service Integration Example
```typescript
// services/auth-service/src/auth/auth.service.ts
import { PyroscopeService } from '@libs/observability';

@Injectable()
export class AuthService {
  constructor(
    private pyroscope: PyroscopeService,
    // ... other dependencies
  ) {}

  async login(credentials: LoginDto): Promise<LoginResponse> {
    return this.pyroscope.profileOperation(
      'auth.login',
      async () => {
        // Actual login logic
        const user = await this.validateCredentials(credentials);
        const token = await this.generateJwtToken(user);
        return { user, token };
      },
      { 
        operation: 'login',
        userType: credentials.userType 
      }
    );
  }

  async validateJwtToken(token: string): Promise<User> {
    return this.pyroscope.profileOperation(
      'auth.validateJwt',
      async () => {
        // JWT validation logic
        const decoded = await this.jwtService.verify(token);
        return this.userService.findById(decoded.sub);
      },
      { operation: 'jwt-validation' }
    );
  }
}
```

#### Step 4: HTTP Library Profiling Integration
```typescript
// libs/http/src/http-client.service.ts - Add profiling
import { PyroscopeService } from '@libs/observability';

@Injectable()
export class HttpClientService {
  constructor(
    private pyroscope: PyroscopeService,
    // ... other dependencies
  ) {}

  async get<T>(url: string, config?: RequestConfig): Promise<HttpResponse<T>> {
    return this.pyroscope.profileOperation(
      'http.request',
      async () => {
        return this.executeRequest('GET', url, undefined, config);
      },
      {
        method: 'GET',
        url: this.sanitizeUrlForLabels(url),
        hasCircuitBreaker: config?.circuitBreaker ? 'true' : 'false'
      }
    );
  }

  private sanitizeUrlForLabels(url: string): string {
    // Remove sensitive data and normalize for profiling labels
    return url.replace(/\/\d+/g, '/:id').replace(/\?.*/, '');
  }
}
```

### Phase 3: Basic Faro Setup (Future Frontend)

#### Step 1: Faro Server Setup
```yaml
# infrastructure/monitoring/docker-compose.yml - Add Faro
services:
  faro-collector:
    image: grafana/faro-web-sdk:latest
    ports:
      - "12347:12347"
    environment:
      - FARO_LISTEN_ADDRESS=0.0.0.0:12347
      - FARO_LOG_LEVEL=info
    networks:
      - monitoring
```

#### Step 2: Basic Frontend Configuration (Future Use)
```typescript
// Future frontend setup - placeholder
// frontend/src/observability/faro.config.ts
import { initializeFaro } from '@grafana/faro-web-sdk';

export const initializeFrontendObservability = () => {
  initializeFaro({
    url: 'http://localhost:12347/collect',
    app: {
      name: 'polyrepo-frontend',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    },
    instrumentations: [
      // Add when frontend is implemented
    ],
  });
};
```

## Migration Timeline & Validation

### Week 1: Tempo Migration
- **Day 1-2**: Infrastructure setup (docker-compose, configs)
- **Day 3-4**: Observability library updates
- **Day 5**: Service-by-service migration with validation
- **Day 6-7**: Testing, performance validation, rollback testing

### Week 2: Pyroscope Integration  
- **Day 1-2**: Pyroscope server setup and basic integration
- **Day 3-4**: Service instrumentation (auth, user, api-gateway)
- **Day 5**: HTTP library profiling integration
- **Day 6-7**: Performance testing and optimization

### Validation Scripts
```bash
#!/bin/bash
# scripts/validate-observability.sh

echo "🔍 Validating Observability Stack..."

# Test Tempo
echo "Testing Tempo..."
curl -s http://localhost:3200/ready || echo "❌ Tempo not ready"

# Test Pyroscope  
echo "Testing Pyroscope..."
curl -s http://localhost:4040/api/v1/apps || echo "❌ Pyroscope not ready"

# Test trace ingestion
echo "Testing trace ingestion..."
curl -X POST http://localhost:4318/v1/traces \
  -H "Content-Type: application/json" \
  -d '{"resourceSpans":[]}' || echo "❌ Trace ingestion failed"

echo "✅ Observability stack validation complete"
```

## Rollback Strategy

### Level 1: Configuration Rollback
```bash
# Revert docker-compose to Jaeger
git checkout HEAD~1 infrastructure/monitoring/docker-compose.yml
docker-compose restart
```

### Level 2: Service Configuration Rollback
```bash
# Revert environment variables
export JAEGER_ENDPOINT=http://localhost:14268/api/traces
unset TEMPO_ENDPOINT
unset OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
```

### Level 3: Code Rollback
```bash
# Revert observability library changes
git checkout HEAD~1 libs/observability/
yarn build:libs
```

## Expected Benefits

### Tempo Benefits
- **Better Query Performance**: TraceQL vs basic Jaeger queries
- **Cost Efficiency**: Object storage backend vs expensive databases
- **Grafana Integration**: Unified observability experience
- **Better Testing APIs**: More real-time query capabilities for testing utilities

### Pyroscope Benefits
- **Performance Insights**: Code-level performance bottlenecks
- **Continuous Monitoring**: Always-on profiling with minimal overhead
- **Integration Testing**: Performance regression detection in CI/CD
- **Production Debugging**: Real-time performance analysis

This migration establishes a modern, scalable observability foundation that significantly improves testing capabilities and production insights.