# Library Testing Strategy 2025 - Implementation Plan

## Executive Summary

After successfully implementing HTTP library testing with 2024 Jest ESM support (41 tests, ~4-5 seconds), we've identified a strategic plan to implement comprehensive testing across all polyrepo libraries. This document outlines the priority order, testing approaches, and implementation roadmap.

## 🎯 Current Testing Status

### ✅ Libraries with Complete Testing
- **`@libs/http`** - 41 tests (32 unit + 9 ESM integration) - **COMPLETE**
- **`@libs/observability`** - Full test suite with coverage - **COMPLETE**

### ⚠️ Libraries with Partial Testing  
- **`@libs/resilience`** - Tests exist but 3 failing (circuit breaker issues)

### ✅ Libraries with Complete Testing  
- **`@libs/caching`** - 64 tests (48 unit + 16 integration) - **COMPLETE**
  - Redis operations with ioredis-mock, cache decorators, real-world patterns
  - Modern Jest ESM support, cross-library integration testing

### ❌ Libraries WITHOUT Tests (Critical Gap)
- **`@libs/messaging`** - 0 tests (Event factory, Redis streams, HTTP lifecycle events)
- **`@libs/error-handling`** - 0 tests (Correlation IDs, error formatting, Keycloak integration)
- **`@libs/auth-common`** - 0 tests (JWT guards, strategies, user context)
- **`@libs/keycloak-client`** - 0 tests (JWKS caching, auth flows)

## 🚀 Implementation Priority (Q1 2025)

### Priority 1: Libraries Without Any Tests (Weeks 1-3)

#### ✅ Week 1: `@libs/caching` - **COMPLETED**

**✅ Implemented**:
```typescript
// Unit Tests (48 tests) - COMPLETED
✅ Cache decorators (@UseCache, @InvalidateCache) - 28 tests
✅ Type definitions and interfaces - 20 tests
✅ Helper functions (key generation, evaluation) - comprehensive coverage

// Integration Tests (16 tests) - COMPLETED  
✅ Real Redis operations with ioredis-mock
✅ Complete cache lifecycle (set, get, delete, exists)
✅ Complex data types and JSON serialization
✅ Concurrent operations and data consistency  
✅ Pattern-based invalidation with fallbacks
✅ Metrics tracking and health checks
✅ Real-world patterns (session caching, API responses)
✅ Cross-library integration (observability, error handling)
```

**✅ Achievements**:
- Modern 2024 ioredis-mock integration with Jest ESM support
- Resilient test design handling mock limitations
- Production-ready test patterns for Redis operations

#### Week 2: `@libs/messaging` - **NEXT PRIORITY** 
**Test Coverage Needed**:
```typescript
// Unit Tests (12-15 tests)
- EventFactory event creation (HTTP events, business events)
- Event serialization and deserialization
- Redis streams publisher functionality
- In-memory event bus for testing
- Event schema validation

// Integration Tests (5-7 tests)
- Real Redis streams connectivity and publishing
- Event consumption and processing
- Cross-service event communication
- Event ordering and delivery guarantees
```

**Testing Approach**: Unit tests + Redis Streams integration with testcontainers

#### Week 3: `@libs/error-handling`
**Test Coverage Needed**:
```typescript
// Unit Tests (10-12 tests)
- CorrelationService context management and ID generation
- ErrorResponseBuilderService formatting consistency
- Error code registry and lookups
- Keycloak error handling integration
- Correlation ID middleware functionality

// Integration Tests (3-5 tests)
- Correlation ID propagation across HTTP requests
- Error response formatting with real HTTP errors
- Integration with observability logging
- Context preservation in async operations
```

**Testing Approach**: Unit tests + HTTP request simulation for correlation testing

### Priority 2: Fix Existing Issues (Week 4)

#### `@libs/resilience` - Fix Failing Tests
**Current Issues**: 3 failing tests related to circuit breaker error expectations
**Root Cause**: Tests expect "test error" but receive "Breaker is open" 
**Fix Strategy**: Update test expectations to match actual Opossum circuit breaker behavior

### Priority 3: Authentication Libraries (Weeks 5-6)

#### Week 5: `@libs/auth-common`
**Test Coverage Needed**:
```typescript
// Unit Tests (8-10 tests)
- JWT guards and strategies
- User context management and extraction
- Role-based access control decorators
- User context guards

// Integration Tests (3-5 tests)
- JWT validation with real tokens
- Role enforcement in request pipelines
- User context propagation
```

#### Week 6: `@libs/keycloak-client`
**Test Coverage Needed**:
```typescript
// Unit Tests (6-8 tests)
- JWKS caching and validation
- Authentication flow handling
- Token refresh and management
- Configuration validation

// Integration Tests (2-4 tests)
- Real Keycloak server integration (testcontainers)
- JWKS endpoint caching and rotation
- Authentication flow end-to-end
```

### Priority 4: HTTP Library Deep Integration (Week 7)

#### Missing HTTP Integration Tests
**Currently Missing from HTTP Library**:
```typescript
// Real Integration Tests (5-7 additional tests)
- Circuit breaker integration with real failures
- Redis cache integration with HTTP responses
- Event publishing for HTTP lifecycle (request/response/error)
- Correlation ID propagation through HTTP calls
- Observability metrics collection validation
```

## 🛠️ Enhanced Testing Utils Development

### New Testing Utilities to Create

#### 1. Redis Test Utils (Week 1)
```typescript
// @libs/testing-utils/src/redis/redis-test-utils.ts
export class RedisTestUtils {
  static async createTestRedisInstance(): Promise<RedisService>;
  static async cleanRedisTestData(pattern?: string): Promise<void>;
  static async assertCacheHit(key: string): Promise<CacheResult>;
  static async assertEventPublished(eventType: string, timeout?: number): Promise<Event>;
  static async waitForCacheOperation(key: string): Promise<void>;
  static generateTestCacheKey(prefix: string): string;
}
```

#### 2. Event Testing Utils (Week 2)
```typescript
// @libs/testing-utils/src/events/event-test-utils.ts
export class EventTestUtils {
  static createTestEventBus(): InMemoryEventBus;
  static assertEventPublished(eventType: string, data?: any): void;
  static async waitForEvents(count: number, timeout?: number): Promise<Event[]>;
  static validateEventSchema(event: Event, schema: EventSchema): boolean;
  static createMockEventPublisher(): jest.Mocked<EventPublisher>;
  static simulateEventFailure(eventType: string): void;
}
```

#### 3. HTTP Integration Test Utils (Week 7)
```typescript
// @libs/testing-utils/src/http/http-integration-utils.ts
export class HttpIntegrationTestUtils {
  static async testCircuitBreakerIntegration(serviceName: string): Promise<CircuitBreakerTestResult>;
  static async testCacheIntegration(endpoint: string): Promise<CacheTestResult>;
  static async testEventPublishing(httpOperation: HttpMethod): Promise<EventTestResult>;
  static async testCorrelationPropagation(request: HttpRequest): Promise<CorrelationTestResult>;
  static async simulateServiceFailure(serviceName: string, duration: number): Promise<void>;
}
```

#### 4. Error Context Test Utils (Week 3)
```typescript
// @libs/testing-utils/src/errors/error-test-utils.ts
export class ErrorTestUtils {
  static createTestCorrelationContext(id?: string): CorrelationContext;
  static assertCorrelationIdPropagated(request: any, response: any): void;
  static validateErrorResponse(error: ErrorResponse, expectedCode: string): boolean;
  static simulateKeycloakError(errorType: KeycloakErrorType): Error;
}
```

## 🔄 Cross-Library Testing Strategy

### Analysis: Test Intersection Between Libraries

**Key Question**: Do some tests intersect between libraries testing the same functionality?

**Answer**: YES - and this is actually valuable for validating integration contracts.

### Common Test Intersection Areas

#### 1. **Redis Integration** 
```typescript
// @libs/caching tests Redis operations
// @libs/messaging tests Redis Streams  
// @libs/http tests Redis for response caching

// STRATEGY: Test library-specific Redis usage, not Redis itself
✅ @libs/caching: Focus on cache patterns (get/set/invalidate)
✅ @libs/messaging: Focus on pub/sub and streams
✅ @libs/http: Focus on response caching integration
```

#### 2. **Observability Integration**
```typescript
// Multiple libraries integrate with @libs/observability
// @libs/http tests HTTP metrics logging
// @libs/caching tests cache metrics
// @libs/messaging tests event metrics

// STRATEGY: Test library-specific metrics, shared via MockFactory
✅ Use MockFactory.createLogger() consistently
✅ Test library-specific metric types and formats
✅ Integration tests verify actual observability integration
```

#### 3. **Error Handling & Correlation**
```typescript  
// Multiple libraries use @libs/error-handling
// @libs/http tests correlation ID propagation in HTTP requests
// @libs/caching tests correlation in cache operations
// @libs/messaging tests correlation in events

// STRATEGY: Test integration contract, not correlation service itself
✅ Each library tests its specific correlation integration
✅ Error handling library tests the core correlation logic
✅ Cross-library integration tests validate end-to-end flow
```

### Test Duplication vs Integration Validation

**❌ BAD Duplication** (Avoid):
```typescript
// Don't test Redis client operations in every library
// Don't test logger functionality in consuming libraries
// Don't test JWT validation in non-auth libraries
```

**✅ GOOD Integration Testing** (Keep):
```typescript
// Test how each library integrates with shared dependencies
// Test library-specific behavior with real dependencies
// Test cross-library contracts and interfaces
```

### Recommended Test Distribution

#### **@libs/http** (Current: ✅ 41 tests)
- HTTP client operations and Got library ESM integration
- Circuit breaker integration (NOT circuit breaker logic)
- Cache integration (NOT cache logic) 
- Event publishing (NOT event processing logic)

#### **@libs/caching** (Current: ✅ 64 tests)  
- Redis cache operations with ioredis-mock
- Cache decorators and key generation
- Metrics and health checks (cache-specific)
- Event publishing for cache operations

#### **@libs/messaging** (Next: 🚧)
- Event factory and schema validation
- Redis streams integration (NOT Redis itself)
- Cross-library event contracts (HTTP events, cache events)

#### **@libs/error-handling** (Future: 📋)
- Correlation ID generation and context management
- Error response formatting
- Cross-library correlation contracts

### Integration Test Strategy

**Level 1: Library-Specific Integration**
- Each library tests its real dependencies in isolation
- Example: @libs/caching tests real Redis, @libs/http tests real HTTP calls

**Level 2: Cross-Library Contract Testing**
- Test that libraries communicate correctly
- Example: HTTP library publishes events that messaging library can consume

**Level 3: End-to-End Scenarios** (Future consideration)
- Full request flow across multiple libraries
- Expensive, saved for critical business flows

### Benefits of This Approach

✅ **Contract Validation**: Ensures libraries integrate correctly
✅ **Regression Prevention**: Changes in one library don't break others  
✅ **Real Dependency Testing**: Catches issues mocks might miss
✅ **Documentation Value**: Integration tests show usage patterns

## 📊 Testing Standards & Patterns

### Proven 2024 Jest ESM Pattern
Based on our HTTP library success, all new tests will use:

```typescript
// For ESM modules (like Got, Redis clients)
import { jest } from '@jest/globals';

jest.unstable_mockModule('redis', () => ({
  createClient: jest.fn().mockImplementation(() => mockRedisClient)
}));

const { createClient } = await import('redis');
```

**Commands**: `NODE_OPTIONS='--experimental-vm-modules' jest --config=jest.esm.config.js`

### Testing Architecture Standards
```
Each Library Testing Structure:
├── test/
│   ├── unit/                    # Fast unit tests (70%)
│   │   ├── service.unit.spec.ts
│   │   └── decorators.unit.spec.ts
│   ├── integration/             # Real dependencies (30%)
│   │   ├── redis-integration.spec.ts
│   │   ├── jest.esm.config.js
│   │   └── setup-esm.ts
│   └── mocks/
│       └── library-mocks.ts
```

### Performance Targets
- **Unit tests**: < 3 seconds per library
- **Integration tests**: < 2 seconds per library  
- **Total per library**: < 5 seconds (enables TDD workflow)
- **Coverage target**: 85%+ for critical functionality

## 🎯 Success Metrics

### Quantitative Goals
- **Week 1**: Caching library - 20-25 tests, <5s execution
- **Week 2**: Messaging library - 17-22 tests, <5s execution
- **Week 3**: Error handling library - 13-17 tests, <5s execution
- **Week 4**: Resilience library - Fix 3 failing tests
- **Week 5-6**: Auth libraries - 14-18 tests each, <5s execution
- **Week 7**: HTTP integration - 5-7 additional tests

### Qualitative Goals
- All libraries have basic unit test coverage
- Critical integrations (Redis, Events, Auth) are integration tested
- Testing utils provide reusable patterns for future libraries
- Documentation includes testing examples for each library

## 🔄 Implementation Workflow

### Per-Library Implementation Process
1. **Analyze library dependencies** - Identify ESM modules, external services
2. **Create test structure** - Set up unit/integration directories
3. **Implement unit tests first** - Fast feedback, isolated logic
4. **Add integration tests** - Real dependencies, controlled environment
5. **Update library README** - Document test commands and coverage
6. **Enhance testing-utils** - Add reusable utilities for future libraries

### Quality Gates
- All new tests must pass consistently (no flaky tests)
- Integration tests must use real dependencies (Redis, Events) when valuable
- Test execution time must be <5 seconds per library
- Mock patterns must be reusable via testing-utils

## 🚀 Next Steps

**IMMEDIATE ACTION**: Start with `@libs/caching` testing implementation
- Create test structure
- Implement Redis connection testing
- Test cache decorators and operations
- Add event publishing integration tests

This strategic approach ensures comprehensive library testing coverage while maintaining development velocity and providing reusable testing patterns for the entire organization.