# Observability Integration Guide

This guide provides comprehensive information about the observability integration in the Polyrepo project, including monitoring stack setup, service integration patterns, and data access via Grafana.

## Overview

The Polyrepo project implements a complete observability stack with three pillars:
- **Logs:** Centralized logging with Loki and structured logging via Pino
- **Metrics:** Application and system metrics with Prometheus
- **Traces:** Distributed tracing with OpenTelemetry and Tempo
- **Profiling:** Continuous profiling with Pyroscope

All services integrate with this stack through the `@libs/observability` shared library, providing consistent observability patterns across the entire application.

## Monitoring Stack Architecture

### Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │   Application   │    │   Application   │
│   Services      │    │   Services      │    │   Services      │
│                 │    │                 │    │                 │
│ auth-service    │    │ user-service    │    │ api-gateway     │
│ user-service    │    │ api-gateway     │    │ ...             │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          │ logs, metrics,       │ logs, metrics,       │ logs, metrics,
          │ traces               │ traces               │ traces
          │                      │                      │
          ▼                      ▼                      ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Monitoring Stack                            │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │ Prometheus  │  │    Loki     │  │   Tempo     │  │ Pyroscope   │ │
│  │ (Metrics)   │  │   (Logs)    │  │  (Traces)   │  │ (Profiling) │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
│           │                 │                 │                │
│           └─────────────────┼─────────────────┘                │
│                             │                                  │
│                    ┌─────────▼────────┐                        │
│                    │     Grafana      │                        │
│                    │  (Visualization) │                        │
│                    └──────────────────┘                        │
└─────────────────────────────────────────────────────────────────┘
```

### Service Ports and Access

| Service    | Port | URL                    | Purpose                    |
|------------|------|------------------------|----------------------------|
| Grafana    | 3200 | http://localhost:3200  | Dashboards and visualization |
| Prometheus | 9090 | http://localhost:9090  | Metrics storage and queries |
| Loki       | 3100 | http://localhost:3100  | Log aggregation           |
| Tempo      | 3201 | http://localhost:3201  | Distributed tracing       |
| Pyroscope  | 4040 | http://localhost:4040  | Continuous profiling      |

**Grafana Access:**
- Username: `admin`
- Password: `admin`

## Observability Library Integration

### Service Configuration Pattern

Each service follows a consistent pattern for observability integration:

```typescript
// services/[service-name]/src/observability/observability.module.ts
@Module({
  imports: [
    BaseObservabilityModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        logging: {
          service: 'service-name',
          defaultContext: 'ServiceName',
          enableLoki: configService.get<string>('ENABLE_LOKI') === 'true',
          lokiHost: configService.get<string>('LOKI_HOST') || 'http://loki:3100',
          logLevel: configService.get<string>('LOG_LEVEL') || 'info',
        },
        metrics: {
          prefix: 'service_name',
          defaultLabels: {
            service: 'service_name',
            environment: configService.get<string>('NODE_ENV') || 'development',
          },
        },
        tracing: {
          serviceName: 'service-name',
          environment: configService.get<string>('NODE_ENV') || 'development',
          tempoEndpoint: configService.get<string>('TEMPO_ENDPOINT') || 'http://localhost:4318/v1/traces',
        },
      }),
    }),
  ],
  // ... rest of module configuration
})
```

### Required Environment Variables

```env
# Observability Configuration
ENABLE_LOKI=true
LOKI_HOST=http://loki:3100
LOG_LEVEL=info
ENABLE_METRICS=true
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://jaeger:14268/api/traces
OTEL_SAMPLING_PROBABILITY=1.0
NODE_ENV=development
SERVICE_NAME=auth-service
```

## Data Sources and Integration Status

### Current Grafana Data Sources

✅ **Prometheus** (UID: PBFA97CFB590B2093)
- **Status:** Connected and operational
- **Purpose:** Application and system metrics
- **Access:** Direct connection to http://prometheus:9090

✅ **Loki** (UID: P8E80F9AEF21F6940)  
- **Status:** Connected and operational
- **Purpose:** Centralized log aggregation
- **Access:** Direct connection to http://loki:3100

✅ **Jaeger** (UID: PC9A941E8F2E49454)
- **Status:** Connected and operational  
- **Purpose:** Distributed tracing
- **Access:** Direct connection to http://jaeger:16686

### Available Log Labels

Current log streams include the following labels:
- `service`: Service identifier (auth-service, user-service)
- `service_name`: Full service name 
- `level`: Log level (info, debug, warn, error)
- `app`: Application identifier
- `environment`: Deployment environment
- `filename`: Source file name
- `job`: Log collection job name

### Metrics Collection Status

**System Metrics:** ✅ Active
- Go runtime metrics from Prometheus itself
- Container metrics via Docker integration

**Application Metrics:** ⚠️ Configuration Required
- Services are configured to expose metrics via `/metrics` endpoints
- Prometheus scraping configuration may need verification
- Custom business metrics defined in services

## Grafana MCP Integration

This project supports direct Grafana access via MCP (Model Context Protocol), enabling:

### Available Operations

- **Data Source Management:** List and inspect configured data sources
- **Metrics Queries:** Query Prometheus metrics directly
- **Log Queries:** Search and filter logs in Loki using LogQL
- **Dashboard Access:** Read and modify Grafana dashboards
- **Alert Management:** Configure and manage alerts

### Example Usage Patterns

**Querying Application Metrics:**
```typescript
// Query auth service login attempts
const loginMetrics = await queryPrometheus({
  datasourceUid: 'PBFA97CFB590B2093',
  expr: 'auth_service_login_attempts_total',
  queryType: 'instant'
});
```

**Searching Application Logs:**
```typescript  
// Search for auth service errors
const errorLogs = await queryLokiLogs({
  datasourceUid: 'P8E80F9AEF21F6940',
  logql: '{service="auth-service", level="error"}',
  limit: 50
});
```

## Implementation Details

### Service Structure

Each service implements observability through dedicated modules:

```
services/service-name/
├── src/
│   ├── observability/
│   │   ├── observability.module.ts      # Main observability configuration
│   │   ├── business-logger.service.ts   # Domain-specific business logging
│   │   └── test.controller.ts           # Development test endpoints
│   ├── metrics/
│   │   ├── metrics.module.ts            # Prometheus metrics module  
│   │   └── metrics.controller.ts        # /metrics endpoint controller
│   └── health/
│       ├── health.module.ts             # Health check module
│       └── health.controller.ts         # Health endpoints
```

### Key Integration Points

1. **Structured Logging:** All services use Pino for JSON-structured logs sent to Loki
2. **Business Event Logging:** Domain-specific events logged separately for business intelligence
3. **Custom Metrics:** Services expose Prometheus metrics at `/metrics` endpoints
4. **Health Checks:** Standardized health check endpoints for monitoring
5. **Distributed Tracing:** OpenTelemetry integration for request flow tracing

## Getting Started

### 1. Start Monitoring Stack

```bash
cd infrastructure/monitoring
docker-compose up -d
```

### 2. Verify Data Sources

Access Grafana at http://localhost:3200 and verify all three data sources are connected.

### 3. Start Application Services

```bash
# Start services with observability enabled
cd infrastructure/local-dev
./start-local-services.sh
```

### 4. Generate Test Data

Services include test endpoints in development mode for generating observability data:
- Auth Service: http://localhost:3001/observability/test
- User Service: http://localhost:3002/observability/test

### 5. Explore Data

- **Grafana Dashboards:** http://localhost:3200
- **Raw Prometheus:** http://localhost:9090
- **Jaeger Traces:** http://localhost:16686

## Troubleshooting

### No Application Metrics in Prometheus

**Symptoms:** Only Prometheus system metrics visible, no `auth_service_*` or `user_service_*` metrics.

**Resolution:**
1. Verify services are running and exposing `/metrics` endpoints
2. Check Prometheus targets at http://localhost:9090/targets
3. Verify service discovery configuration in `infrastructure/monitoring/prometheus/prometheus.yml`

### No Logs in Loki

**Symptoms:** Loki data source connected but no log entries found.

**Resolution:**
1. Verify `ENABLE_LOKI=true` in service environment files
2. Check Loki host configuration (`LOKI_HOST=http://loki:3100`)
3. Verify services are running and generating logs
4. Check Promtail configuration for log collection

### Jaeger Traces Not Appearing

**Symptoms:** No traces visible in Jaeger UI.

**Resolution:**  
1. Verify `ENABLE_TRACING=true` in service environments
2. Check Jaeger endpoint configuration
3. Ensure services are generating HTTP requests to create traces
4. Verify OpenTelemetry configuration in services

## Advanced Usage

### Custom Dashboard Creation

Create service-specific dashboards in Grafana using the available data sources. Recommended dashboard structure:

- **Service Overview:** Key metrics, error rates, request volume
- **Performance:** Response times, throughput, resource usage  
- **Errors and Logs:** Error rates, log analysis, alert triggers
- **Business Metrics:** Domain-specific business event analytics

### Alert Configuration

Set up Grafana alerts for:
- High error rates in logs
- Service availability metrics
- Performance degradation
- Business rule violations

### Log Analysis Patterns

Common LogQL queries for log analysis:
```logql
# Error rate by service
rate({level="error"}[5m]) by (service)

# Authentication failures
{service="auth-service"} |= "login" |= "failure"

# Performance analysis
{service=~".+"} | json | duration > 1s
```

For detailed implementation guidance, see the [Observability Library README](../libs/observability/README.md).
