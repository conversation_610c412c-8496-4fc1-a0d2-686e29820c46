# Prisma Integration Guide

Complete guide to Prisma integration in the polyrepo, including setup, development workflow, Docker integration, and troubleshooting.

## Overview

Prisma is integrated into the polyrepo with sophisticated handling for:
- **Webpack bundling compatibility** - Avoids instanceof issues and bundling conflicts
- **Docker development workflow** - Seamless container/host development
- **Cross-library integration** - Full observability, caching, and messaging integration
- **Production-ready patterns** - Soft deletes, optimistic locking, resilience patterns

## Current Architecture

### Services Using Prisma
- **Primary Service**: `user-service` (only service with direct Prisma integration)
- **Other Services**: `auth-service` and `api-gateway` communicate with user-service via HTTP

### Prisma Libraries
The polyrepo implements a two-library approach for reusable Prisma functionality:

**A. `@libs/prisma-utils`**
```typescript
// Soft delete middleware
export function softDeleteMiddleware() {
  // Converts delete operations to update operations with deletedAt timestamp
  // Filters reads to exclude soft-deleted records unless explicitly requested
}
```

**B. `@libs/prisma-resilience`**
```typescript
// Webpack-safe retry interceptor for transient Prisma errors
@Injectable()
export class PrismaRetryInterceptor implements NestInterceptor {
  // Handles errors: P1001, P1002, P1008, P1017, P2024, P2034, P2037
  // Uses property checking instead of instanceof for webpack compatibility
}
```

## Database Schema

Current schema in `services/user-service/prisma/schema.prisma`:

```prisma
generator client {
  provider      = "prisma-client-js"
  output        = "../../node_modules/.prisma/client"  // Generate directly in workspace root
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String    @id @default(cuid())
  keycloakId String    @unique
  email      String    @unique
  firstName  String
  lastName   String
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime? // Soft delete support
  version    Int       @default(1) // Optimistic locking
  
  profile Profile? // One-to-one relationship
  
  @@index([email])
  @@index([keycloakId])
  @@index([deletedAt])
}

model Profile {
  id     String @id @default(uuid())
  bio    String?
  userId String @unique
  user   User   @relation(fields: [userId], references: [id])
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
```

## Development Workflow

### Quick Start (Recommended)

```bash
# 1. Start development environment (includes automatic Prisma generation)
yarn dev:cli start --auto

# 2. Services automatically handle:
#    - Webpack generates Prisma client to workspace root
#    - Docker containers run migrations on startup
#    - No manual intervention required
```

### Manual Commands (if needed)

```bash
# Generate Prisma client to workspace root (normally handled by webpack)
yarn prisma:generate

# Deploy migrations manually (normally handled by container startup)
yarn prisma:migrate

# Reset database (DESTRUCTIVE - development only)
cd services/user-service && yarn db:reset
```

### Current Architecture (2025 Update)

After extensive debugging of webpack bundling issues and Alpine container compatibility, we've established a clean separation:

**✅ Build-time (Webpack)**: Generates Prisma client to workspace root
**✅ Runtime (Docker)**: Runs migrations automatically when containers start

This approach eliminates complex copying scripts and provides consistent, automated Prisma management.

### Key Debugging Discoveries (2025)

During development, we encountered and resolved several critical issues:

**1. Alpine Container + Prisma Engine Compatibility**
- **Problem**: `schema-engine-linux-musl-openssl-3.0.x` not found in Alpine containers
- **Root Cause**: Prisma client generated in service-specific `node_modules`, but containers mounted workspace root `node_modules` which lacked musl engines
- **Solution**: Configure Prisma to generate directly in workspace root: `output = "../../node_modules/.prisma/client"`

**2. Webpack + Docker Environment Coordination**
- **Problem**: Complex workspace scripts trying to copy engines between locations
- **Root Cause**: Mixing build-time and runtime concerns in single commands
- **Solution**: Clean separation - webpack handles generation, docker-compose handles migrations

**3. Port Configuration Mismatch**
- **Problem**: Health checks failing due to container internal/external port confusion
- **Root Cause**: `.env.bundling` specified external port (3002) but containers expect internal port (3000)
- **Solution**: Align environment files with docker-compose port mapping expectations

**4. Read-only Volume Mount Issues**
- **Problem**: Cannot write Prisma client from within container due to `:ro` mounts
- **Root Cause**: Attempting Prisma generation inside container with read-only `node_modules`
- **Solution**: Generate on host (webpack), mount read-only to container

### Development Environment Commands

From `services/user-service/` directory (when developing schema):

```bash
# Create new migration
yarn prisma:migrate:dev --name "add_new_feature"

# Reset database (DESTRUCTIVE)
yarn db:reset

# Open Prisma Studio
yarn prisma:studio

# Generate only
yarn prisma:generate
```

## Environment Configuration

### Database URLs by Context

**Container Environment** (`.env.docker`):
```bash
# Services running in Docker containers connect to postgres_user_service:5432
DATABASE_URL="*************************************************************************************/polyrepo_user_db?schema=public"
```

**Host Environment** (Prisma CLI):
```bash
# Host commands connect to localhost:5433 (mapped port)
DATABASE_URL="postgresql://devuser_user_service:devpassword_user_service@localhost:5433/polyrepo_user_db?schema=public"
```

### Why Two Different URLs?

- **Container Services**: Use Docker service names for internal communication
- **Host Prisma CLI**: Uses localhost with mapped ports for direct database access
- **Workspace Scripts**: Automatically handle the correct URL for each context

## Webpack Integration

### Key Webpack Configuration

The user-service `webpack.config.js` includes critical Prisma-specific configuration:

```javascript
// External Prisma components (not bundled)
externals: [
  nodeExternals({
    allowlist: [/^@libs\//], // Bundle internal libraries
  }),
  '@prisma/client',
  'prisma',
  '@prisma/engines',
  
  // Custom handler for generated Prisma client
  ({request}, callback) => {
    if (request.includes('generated/prisma-client')) {
      return callback(null, 'commonjs ' + request);
    }
    if (request.includes('@prisma/engines') || request.includes('schema-engine')) {
      return callback(null, 'commonjs ' + request);
    }
    callback();
  },
],

// Bundle optimization aliases
resolve: {
  alias: {
    '@libs/prisma-utils': path.resolve(__dirname, '../../libs/prisma-utils/src'),
    '@libs/prisma-resilience': path.resolve(__dirname, '../../libs/prisma-resilience/src'),
  }
},

// Ignore problematic Prisma paths
plugins: [
  new webpack.IgnorePlugin({
    resourceRegExp: /runtime\/wasm/,
    contextRegExp: /prisma-client/,
  }),
  new webpack.IgnorePlugin({
    resourceRegExp: /\.node$/,
  }),
]
```

### Why External Prisma?

1. **Binary Dependencies**: Prisma includes native binaries that can't be bundled
2. **Runtime Generation**: Client is generated at build time, not compile time
3. **Performance**: External dependencies load faster than bundled equivalents
4. **Compatibility**: Avoids webpack resolution conflicts with native modules

## Docker Integration

### Volume Mounting Strategy

Docker Compose configuration for development:

```yaml
user-service:
  env_file:
    - ../../services/user-service/.env.bundling  # Uses bundling-specific environment
  command: ["sh", "-c", "npx prisma migrate deploy && node dist-webpack/main.bundle.js"]
  volumes:
    # Webpack bundle (optimized for development)
    - ../../services/user-service/dist-webpack:/app/dist-webpack:ro
    
    # External dependencies (including Prisma client in workspace root)
    - ../../node_modules:/app/node_modules:ro
    
    # Prisma schema and migrations
    - ../../services/user-service/prisma:/app/prisma:ro
```

### Database Container

```yaml
postgres_user_service:
  image: postgres:15-alpine
  environment:
    POSTGRES_USER: devuser_user_service
    POSTGRES_PASSWORD: devpassword_user_service
    POSTGRES_DB: polyrepo_user_db
  ports:
    - "5433:5432" # External port to avoid conflicts
  volumes:
    - postgres_user_service_data:/var/lib/postgresql/data
```

## Service Integration

### PrismaService Implementation

```typescript
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    super();
    this.logger = this.loggerFactory.createLogger('PrismaService');
    
    // Register soft delete middleware
    this.$use(softDeleteMiddleware());
  }
  
  async onModuleInit() {
    await this.$connect();
    this.logger.log('Database connection established successfully.');
  }
  
  async checkHealth(): Promise<{ status: string; responseTime: number }> {
    const start = Date.now();
    await this.$queryRaw`SELECT 1`;
    return {
      status: 'healthy',
      responseTime: Date.now() - start,
    };
  }
}
```

### Module Integration

```typescript
@Module({
  imports: [
    PrismaModule,
    // Other modules...
  ],
  providers: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
```

### Advanced Usage Patterns

**Transaction Support**:
```typescript
async createUserWithProfile(userData: CreateUserDto): Promise<User> {
  return this.prisma.$transaction(async (prisma) => {
    const user = await prisma.user.create({
      data: {
        ...userData,
        profile: {
          create: {
            bio: userData.bio,
          },
        },
      },
      include: { profile: true },
    });
    
    // Publish user created event
    await this.eventPublisher.publish(
      EventFactory.userCreated(user)
    );
    
    return user;
  });
}
```

**Optimistic Locking**:
```typescript
async updateUser(id: string, updateData: UpdateUserDto, version: number): Promise<User> {
  const updatedUser = await this.prisma.user.update({
    where: {
      id,
      version, // Ensures concurrent update protection
    },
    data: {
      ...updateData,
      version: { increment: 1 },
    },
  });
  
  if (!updatedUser) {
    throw new ConflictException('User was modified by another process');
  }
  
  return updatedUser;
}
```

**Soft Delete Usage**:
```typescript
// Soft delete (sets deletedAt timestamp)
await this.prisma.user.delete({
  where: { id: userId },
});

// Restore soft deleted user
await this.prisma.user.update({
  where: { id: userId },
  data: { deletedAt: null },
});

// Find including deleted
await this.prisma.user.findMany({
  where: { 
    deletedAt: { not: null } // Explicitly query deleted records
  },
});
```

## Cross-Library Integration

### Caching Integration

```typescript
@Injectable()
export class UsersService {
  @UseCache({ ttl: 300000 }) // 5 minutes
  async findUser(id: string): Promise<User> {
    return this.prisma.user.findUnique({
      where: { id },
      include: { profile: true },
    });
  }
  
  @InvalidateCache(['user:*'])
  async updateUser(id: string, data: UpdateUserDto): Promise<User> {
    const updated = await this.prisma.user.update({
      where: { id },
      data,
    });
    
    // Cache automatically invalidated by decorator
    return updated;
  }
}
```

### Messaging Integration

```typescript
async createUser(userData: CreateUserDto): Promise<User> {
  const user = await this.prisma.user.create({
    data: userData,
  });
  
  // Publish lifecycle event
  await this.eventPublisher.publish(
    EventFactory.userCreated({
      userId: user.id,
      email: user.email,
      keycloakId: user.keycloakId,
      timestamp: new Date(),
    })
  );
  
  return user;
}
```

### Error Handling Integration

```typescript
@UseInterceptors(PrismaRetryInterceptor)
export class UsersService {
  async findUser(id: string): Promise<User> {
    try {
      return await this.prisma.user.findUniqueOrThrow({
        where: { id },
      });
    } catch (error) {
      // Enhanced error with correlation ID and Loki query links
      throw this.errorBuilder.transformPrismaError(error, {
        operation: 'findUser',
        userId: id,
      });
    }
  }
}
```

## Health Monitoring

### Database Health Check

```typescript
@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  constructor(private readonly prismaService: PrismaService) {
    super();
  }
  
  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const dbHealth = await this.prismaService.checkHealth();
      
      return this.getStatus(key, true, {
        database: 'postgres',
        status: dbHealth.status,
        responseTime: `${dbHealth.responseTime}ms`,
      });
    } catch (error) {
      return this.getStatus(key, false, {
        database: 'postgres',
        error: error.message,
      });
    }
  }
}
```

### Health Controller Integration

```typescript
@Get('detailed')
async detailedHealthCheck(): Promise<HealthCheckResponse> {
  return this.health.check([
    () => this.database.isHealthy('database'),
    () => this.prismaHealth.isHealthy('prisma'),
  ]);
}
```

## Testing Patterns

### Unit Testing with Prisma

```typescript
describe('UsersService', () => {
  let service: UsersService;
  let prisma: PrismaService;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: {
            user: {
              create: jest.fn(),
              findUnique: jest.fn(),
              update: jest.fn(),
              delete: jest.fn(),
            },
          },
        },
      ],
    }).compile();
    
    service = module.get<UsersService>(UsersService);
    prisma = module.get<PrismaService>(PrismaService);
  });
  
  it('should create user', async () => {
    const userData = { email: '<EMAIL>', firstName: 'Test' };
    const expectedUser = { id: '1', ...userData };
    
    jest.spyOn(prisma.user, 'create').mockResolvedValue(expectedUser);
    
    const result = await service.createUser(userData);
    expect(result).toEqual(expectedUser);
  });
});
```

### Integration Testing

```typescript
describe('Users API (Integration)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  
  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    
    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    
    // Clean database before each test
    await prisma.user.deleteMany();
    
    await app.init();
  });
  
  it('/users (POST)', async () => {
    const userData = {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      keycloakId: 'test-keycloak-id',
    };
    
    return request(app.getHttpServer())
      .post('/users')
      .send(userData)
      .expect(201)
      .expect((res) => {
        expect(res.body.email).toEqual(userData.email);
        expect(res.body.id).toBeDefined();
      });
  });
});
```

## Common Issues and Solutions

### Issue 1: "Prisma client not initialized"

**Symptoms:**
```
@prisma/client did not initialize yet. Please run "prisma generate" and try to import it again.
```

**Solution:**
```bash
# Generate Prisma client
yarn prisma:setup

# Restart affected containers
docker restart polyrepo_user_service_volume
```

**Prevention:**
- Always run `yarn prisma:setup` after schema changes
- Include Prisma generation in CI/CD pipelines
- Use `postinstall` scripts for automatic generation

### Issue 2: Database connection errors

**Symptoms:**
```
Can't connect to database server at `postgres_user_service`:`5432`
```

**Solution:**
```bash
# Check container status
docker ps | grep postgres

# Check database logs
docker logs polyrepo_postgres_user_service_dev

# Restart database if needed
docker restart polyrepo_postgres_user_service_dev
```

**Prevention:**
- Verify Docker Compose services are running
- Check environment variable configuration
- Ensure proper DATABASE_URL for context (container vs host)

### Issue 3: Webpack bundling conflicts

**Symptoms:**
```
Module not found: Error: Can't resolve '.prisma/client'
```

**Solution:**
- Verify webpack externals configuration
- Check that `@prisma/client` is externalized
- Ensure proper volume mounting in Docker

**Prevention:**
- Don't modify webpack Prisma externals without testing
- Keep Prisma as external dependency in webpack config

### Issue 4: Migration conflicts

**Symptoms:**
```
Migration failed: Table 'User' already exists
```

**Solution:**
```bash
# Reset database (DESTRUCTIVE - development only)
cd services/user-service && yarn db:reset

# Or create a new migration to fix conflicts
yarn prisma:migrate:dev --name "fix_migration_conflict"
```

**Prevention:**
- Always review migration files before applying
- Use descriptive migration names
- Test migrations in staging environment

### Issue 5: Container restart needed after Prisma changes

**Symptoms:**
- Schema changes not reflected in running service
- Generated client not picked up by container

**Solution:**
```bash
# Restart specific service
docker restart polyrepo_user_service_volume

# Or use development CLI
yarn dev:cli restart user-service
```

**Prevention:**
- Use auto-restart mode: `yarn dev:cli start --auto`
- Include container restart in development scripts

## Best Practices

### Schema Design

1. **Use appropriate data types**:
   ```prisma
   model User {
     id        String   @id @default(cuid()) // CUID for better distribution
     email     String   @unique @db.VarChar(320) // RFC 5321 limit
     createdAt DateTime @default(now()) @db.Timestamptz(6)
   }
   ```

2. **Add proper indexes**:
   ```prisma
   model User {
     @@index([email])
     @@index([createdAt])
     @@index([deletedAt]) // For soft delete queries
   }
   ```

3. **Use meaningful constraint names**:
   ```prisma
   model Profile {
     userId String @unique @map("user_id")
     user   User   @relation(fields: [userId], references: [id], map: "profile_user_fk")
   }
   ```

### Development Workflow

1. **Always use workspace-level commands**:
   ```bash
   # ✅ Correct - handles environment automatically
   yarn prisma:setup
   
   # ❌ Avoid - requires manual environment setup
   cd services/user-service && npx prisma migrate dev
   ```

2. **Test locally before committing**:
   ```bash
   yarn prisma:setup
   yarn test:integration
   yarn test:e2e
   ```

3. **Use descriptive migration names**:
   ```bash
   yarn prisma:migrate:dev --name "add_user_preferences_table"
   yarn prisma:migrate:dev --name "add_email_index_for_performance"
   ```

### Production Considerations

1. **Use connection pooling**:
   ```prisma
   datasource db {
     provider = "postgresql"
     url      = env("DATABASE_URL")
     // Add connection pooling for production
     // postgresql://user:pass@host:port/db?pgbouncer=true&connection_limit=10
   }
   ```

2. **Optimize for bundle size**:
   ```javascript
   // Keep Prisma external in webpack
   externals: ['@prisma/client', 'prisma']
   ```

3. **Handle migration rollbacks**:
   ```bash
   # Create rollback migrations for critical changes
   yarn prisma:migrate:dev --name "rollback_user_table_changes"
   ```

### Error Handling

1. **Use webpack-safe error detection**:
   ```typescript
   // ✅ Webpack-safe
   if (error && error.code && typeof error.code === 'string') {
     // Handle specific Prisma error codes
   }
   
   // ❌ Webpack-unsafe
   if (error instanceof PrismaClientKnownRequestError) {
     // This breaks in bundled environments
   }
   ```

2. **Implement retry logic**:
   ```typescript
   @UseInterceptors(PrismaRetryInterceptor)
   export class UsersService {
     // Automatic retry for transient errors
   }
   ```

3. **Add correlation IDs**:
   ```typescript
   async createUser(data: CreateUserDto, correlationId: string): Promise<User> {
     try {
       return await this.prisma.user.create({ data });
     } catch (error) {
       this.logger.error({
         message: 'Failed to create user',
         correlationId,
         error: error.message,
         userData: { email: data.email }, // Don't log sensitive data
       });
       throw error;
     }
   }
   ```

## Adding Prisma to New Services

### Step 1: Install Dependencies

```bash
# In new service directory
yarn add @prisma/client @libs/prisma-utils @libs/prisma-resilience
yarn add -D prisma
```

### Step 2: Initialize Prisma

```bash
cd services/new-service
npx prisma init
```

### Step 3: Configure Schema

```prisma
// services/new-service/prisma/schema.prisma
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

### Step 4: Add to Docker Compose

```yaml
# Add database service
postgres_new_service:
  image: postgres:15-alpine
  environment:
    POSTGRES_USER: devuser_new_service
    POSTGRES_PASSWORD: devpassword_new_service
    POSTGRES_DB: polyrepo_new_db
  ports:
    - "5434:5432" # Use unique port
  volumes:
    - postgres_new_service_data:/var/lib/postgresql/data

# Add to service volumes
new-service:
  volumes:
    - ../../services/new-service/prisma:/app/prisma:ro
    - ../../services/new-service/generated:/app/generated:ro
```

### Step 5: Configure Webpack

Copy Prisma-specific webpack configuration from user-service:

```javascript
// services/new-service/webpack.config.js
externals: [
  '@prisma/client',
  'prisma',
  '@prisma/engines',
  // ... other externals
],

plugins: [
  new webpack.IgnorePlugin({
    resourceRegExp: /runtime\/wasm/,
    contextRegExp: /prisma-client/,
  }),
  // ... other plugins
]
```

### Step 6: Add Webpack Generation

Add Prisma generation to the service's webpack config:

```javascript
// services/new-service/webpack.config.js
class PrismaGenerationPlugin {
  apply(compiler) {
    compiler.hooks.beforeCompile.tapAsync('PrismaGenerationPlugin', (compilation, callback) => {
      try {
        console.log('🔧 Generating Prisma client...');
        execSync('npx prisma generate', { stdio: 'inherit', cwd: process.cwd() });
        console.log('✅ Prisma client generated successfully');
        callback();
      } catch (error) {
        console.warn('⚠️  Continuing with webpack build...');
        callback();
      }
    });
  }
}

// Add to plugins array
plugins: [
  new PrismaGenerationPlugin(),
  // ... other plugins
]
```

### Step 7: Configure Docker Startup

Add migrations to docker-compose command:

```yaml
# services/new-service in docker-compose
new-service:
  command: ["sh", "-c", "npx prisma migrate deploy && node dist-webpack/main.bundle.js"]
```

### Step 8: Implement PrismaService

```typescript
// services/new-service/src/prisma/prisma.service.ts
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    super();
    this.logger = this.loggerFactory.createLogger('PrismaService');
    this.$use(softDeleteMiddleware());
  }
  
  async onModuleInit() {
    await this.$connect();
    this.logger.log('Database connection established successfully.');
  }
}
```

### Step 9: Test Integration

```bash
# Start development workflow (handles generation and migrations automatically)
yarn dev:cli start --auto

# Test health endpoint
curl http://localhost:PORT/health
```

## Conclusion

This Prisma integration provides:
- **Seamless development workflow** with Docker and webpack
- **Production-ready patterns** with proper error handling and resilience
- **Full observability integration** across the entire stack
- **Webpack-safe implementation** avoiding common bundling pitfalls
- **Comprehensive testing strategies** for both unit and integration tests

The key to success is understanding the environment contexts (host vs container) and using the proper workspace-level commands that handle these complexities automatically.

For questions or issues not covered in this guide, check the troubleshooting section or refer to the specific library documentation for `@libs/prisma-utils` and `@libs/prisma-resilience`.