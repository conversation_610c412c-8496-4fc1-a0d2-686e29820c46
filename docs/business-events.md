# Business Events Logging Standard

This document defines the standard for business event logging across all services in the Polyrepo application.

## Purpose

Business event logging serves several key purposes:
- Provides an audit trail of important business operations
- Enables business analytics and reporting
- Facilitates debugging of business processes
- Supports compliance and security requirements

## Event Structure

All business events follow a consistent structure:

```json
{
  "eventType": "category_event",  // The category of the event (auth_event, user_activity, etc.)
  "action": "specific_action",    // The specific action within the category
  "status": "success|failure",    // The outcome of the event
  "userId": "user-id",            // The ID of the user associated with the event (if applicable)
  "timestamp": "ISO-8601 date",   // When the event occurred
  "metadata": {                   // Additional context-specific information
    "key1": "value1",
    "key2": "value2"
  }
}
```

## Event Categories

### 1. Authentication Events (`auth_event`)

Events related to user authentication and identity management.

| Action | Description | Service | Required Metadata |
|--------|-------------|---------|-------------------|
| `login` | User login attempt | Auth Service | `method` (password, oauth, etc.), `ip` |
| `logout` | User logout | Auth Service | `sessionId` |
| `register` | User registration | Auth Service | `email` |
| `password_reset_request` | Password reset request | Auth Service | `email` |
| `password_reset` | Password reset completion | Auth Service | - |
| `email_verification` | Email verification | Auth Service | - |
| `token_refresh` | Token refresh | Auth Service | - |
| `account_lock` | Account locked due to failed attempts | Auth Service | `reason` |
| `account_unlock` | Account unlocked | Auth Service | `reason` |

### 2. User Activity Events (`user_activity`)

Events related to user profile and account management.

| Action | Description | Service | Required Metadata |
|--------|-------------|---------|-------------------|
| `create` | User creation | User Service | `email`, `keycloakId` |
| `update` | User profile update | User Service | `fields` (array of updated fields) |
| `delete` | User deletion (soft or hard) | User Service | `deletionType: 'soft' | 'hard'`, `email` (if soft), `keycloakId` (if soft) |
| `restore` | User restored from soft-delete | User Service | `email`, `keycloakId` |
| `status_change` | User status change | User Service | `oldStatus`, `newStatus`, `reason` |
| `profile_view` | User profile viewed | User Service | - |

### 3. API Request Events (`api_request`)

Events related to API requests for analytics and monitoring.

| Action | Description | Service | Required Metadata |
|--------|-------------|---------|-------------------|
| N/A | API request | All Services | `method`, `path`, `statusCode`, `durationMs` |

### 4. Business Process Events (`business_event`)

Generic business process events that don't fit into other categories.

| Action | Description | Service | Required Metadata |
|--------|-------------|---------|-------------------|
| `process_started` | Business process started | Any | `processId`, `processType` |
| `process_completed` | Business process completed | Any | `processId`, `processType`, `result` |
| `process_failed` | Business process failed | Any | `processId`, `processType`, `error` |

## Implementation Guidelines

### Service-Specific Business Loggers

Each service should implement a service-specific business logger that:
1. Extends the base `BusinessLogger` from the observability library
2. Provides domain-specific methods for common business events
3. Uses consistent method naming across services

#### Auth Service Example

```typescript
@Injectable()
export class AuthBusinessLogger {
  constructor(private readonly businessLogger: BusinessLogger) {}

  logUserLoginEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('login', status, userId, metadata);
  }
  
  // Other auth-specific methods...
}
```

#### User Service Example

```typescript
@Injectable()
export class UserBusinessLogger {
  constructor(private readonly businessLogger: BusinessLogger) {}

  logUserCreationEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logUserActivity(userId, 'create', {
      status,
      ...metadata,
    });
  }
  
  // Other user-specific methods...
}
```

### When to Log Business Events

Log business events at the following points:
1. **Service Layer**: Log events at the service layer, not in controllers or repositories
2. **Outcome Points**: Log at the point where the outcome of an operation is known
3. **Both Success and Failure**: Log both successful and failed operations
4. **Sensitive Operations**: Always log sensitive operations like authentication and authorization

### Sensitive Data Handling

1. **Never log sensitive data** such as passwords, tokens, or personal information
2. **Mask sensitive fields** like email addresses (e.g., `u***@example.com`)
3. **Use user IDs instead of identifiable information** where possible

## Usage in Services

### Logging Authentication Events

```typescript
// In AuthService
async login(email: string, password: string) {
  try {
    // Login logic
    const userId = '123'; // from login result
    
    this.businessLogger.logUserLoginEvent('success', userId, {
      method: 'password',
      ip: request.ip,
    });
    
    return { success: true };
  } catch (error) {
    this.businessLogger.logUserLoginEvent('failure', null, {
      reason: error.message,
      email: maskEmail(email), // Use a utility to mask the email
    });
    throw error;
  }
}
```

### Logging User Activity

```typescript
// In UserService
async updateUser(id: string, data: UpdateUserDto) {
  try {
    // Update logic
    const updatedUser = await this.userRepository.update(id, data);
    
    this.businessLogger.logUserUpdateEvent('success', id, {
      fields: Object.keys(data),
    });
    
    return updatedUser;
  } catch (error) {
    this.businessLogger.logUserUpdateEvent('failure', id, {
      reason: error.message,
      fields: Object.keys(data),
    });
    throw error;
  }
}
```

## Querying Business Events

Business events can be queried using the logging infrastructure:

1. **Grafana/Loki**: Use Grafana to query logs with the `eventType` field
2. **Log Files**: Search log files for the `eventType` field
3. **Analytics**: Export business events to analytics platforms for reporting

## Extending the Standard

To add new business events:

1. Update this document with the new event category or action
2. Add the new event to the appropriate service-specific business logger
3. Update the base `BusinessLogger` in the observability library if needed
4. Document the new event in the service's README
