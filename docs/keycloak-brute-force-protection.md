# Keycloak Brute Force Protection Configuration

This document explains the brute force protection and account lockout settings configured in our Keycloak instance.

## Overview

Brute force protection is enabled to prevent attackers from guessing user passwords through repeated login attempts. When a user fails to authenticate multiple times, their account is temporarily locked to prevent further attempts.

## Configuration Parameters

The following parameters are configured in our Keycloak setup scripts:

| Parameter | Value | Description |
|-----------|-------|-------------|
| `bruteForceProtected` | `true` | Enables brute force protection |
| `failureFactor` | `10` | Number of login failures before the account is temporarily locked |
| `maxFailureWaitSeconds` | `900` | Maximum time (15 minutes) an account is locked after multiple failures |
| `minimumQuickLoginWaitSeconds` | `60` | Minimum time (1 minute) between login attempts after a failure |
| `waitIncrementSeconds` | `60` | Time added to the wait time for each failure |
| `quickLoginCheckMilliSeconds` | `1000` | Time window for counting quick successive login attempts |
| `maxDeltaTimeSeconds` | `43200` | Time (12 hours) after which the failure count is reset |
| `permanentLockout` | `false` | If true, accounts would be permanently locked after too many failures |

## How It Works

1. After 10 failed login attempts, the user's account is temporarily locked
2. The lockout duration increases with each failure, up to a maximum of 15 minutes
3. Users must wait at least 1 minute between login attempts after a failure
4. The failure count is reset after 12 hours of no failed attempts
5. Accounts are never permanently locked; administrators don't need to manually unlock accounts

## Implementation

These settings are automatically configured during the Keycloak setup process through our setup scripts:

- `infrastructure/local-dev/keycloak-setup/configure-keycloak.sh` for the main realm
- `infrastructure/local-dev/keycloak-setup/configure-keycloak-test.sh` for the test realm

## Manual Configuration

If you need to manually configure these settings:

1. Log in to the Keycloak Admin Console
2. Select the realm you want to configure
3. Go to Realm Settings → Security Defenses → Brute Force Detection
4. Configure the settings as described above
5. Click Save

## Testing

To test the brute force protection:

1. Attempt to log in with an incorrect password 10 times
2. On the 11th attempt, you should receive a message indicating the account is temporarily locked
3. Wait for the lockout period to expire before trying again

## Notes

- These settings provide a balance between security and usability
- For production environments, you may want to adjust these settings based on your security requirements
- The settings are applied to both the main realm and the test realm
