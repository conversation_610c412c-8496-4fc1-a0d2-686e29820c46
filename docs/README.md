# Project Documentation

Welcome to the comprehensive documentation for the Polyrepo microservices project. This page serves as your navigation hub for all documentation across the project.

## 🚀 **Getting Started**

### Essential First Steps
- **[📖 Project Introduction](../INTRODUCTION.md)** - Complete project overview, architecture, and key concepts
- **[⚙️ Local Development Setup](../infrastructure/local-dev/LOCAL_DEVELOPMENT_SETUP.md)** - Step-by-step setup guide for development environment
- **[🧪 Testing Standards](./testing-standards.md)** - Testing conventions, frameworks, and best practices

## 🏗️ **Architecture & Development**

### Core Architecture
- **[🏛️ System Overview](../INTRODUCTION.md)** - Comprehensive architecture, services, and implementation patterns
- **[📊 Observability Integration](./observability-integration.md)** - Complete monitoring, logging, and metrics guide
- **[🗺️ Development Roadmap](./development-roadmap.md)** - Future development priorities and exploration areas

### Development Guidelines
- **[📋 Documentation Guidelines](./documentation-guidelines.md)** - Standards for writing and maintaining documentation
- **[🔧 Claude Integration](./claude-integration.md)** - AI assistant tools and development workflows

## 🔐 **Authentication & Security**

### Authentication System
- **[🔑 Authentication Workplan](./workplan.md)** - Detailed authentication system development plan
- **[🛡️ Keycloak Brute Force Protection](./keycloak-brute-force-protection.md)** - Security hardening configuration

## 🔧 **Troubleshooting & Operations**

### Issue Resolution
- **[🚨 Troubleshooting Index](./troubleshooting/README.md)** - Comprehensive troubleshooting guides organized by category
- **[🐳 Docker Issues](./troubleshooting/docker_build_issues_windows.md)** - Docker and containerization problems
- **[🔤 TypeScript Issues](./troubleshooting/typescript_build_issues.md)** - Compilation and build problems
- **[🌐 Networking Issues](./troubleshooting/api_gateway_connection_issues.md)** - Service communication problems

## 🏢 **Service Documentation**

### Core Services
- **[🚪 API Gateway](../services/api-gateway/README.md)** - Request routing, authentication, and API composition
- **[🔐 Auth Service](../services/auth-service/README.md)** - User authentication and Keycloak integration
- **[👤 User Service](../services/user-service/README.md)** - User profile management and business logic

### Service Testing Documentation
- **[🧪 Auth Service Tests](../services/auth-service/test/README.md)** - Authentication service testing guide
- **[🧪 User Service Tests](../services/user-service/test/README.md)** - User service testing guide

## 📚 **Shared Libraries**

### Library Documentation
- **[🔍 Observability Library](../libs/observability/README.md)** - Logging, metrics, and tracing infrastructure
- **[🌐 NestJS Common HTTP](../libs/nestjs-common/src/http/README.md)** - HTTP client with observability integration
- **[🔗 Shared Types](../libs/shared-types/README.md)** - TypeScript interfaces and DTOs
- **[🛡️ Resilience Library](../libs/resilience/README.md)** - Circuit breakers, retries, and timeout patterns

## 🏗️ **Infrastructure Documentation**

### Environment Setup
- **[🐳 Local Development](../infrastructure/local-dev/LOCAL_DEVELOPMENT_SETUP.md)** - Complete local setup guide
- **[📊 Monitoring Stack](../infrastructure/monitoring/README.md)** - Grafana, Prometheus, Loki configuration
- **[🔑 Keycloak Setup](../infrastructure/keycloak/README.md)** - Identity management configuration

### Deployment
- **[🚀 Deployment Guides](../infrastructure/deployment/README.md)** - Production deployment documentation
- **[☁️ Cloud Configuration](../infrastructure/cloud/README.md)** - Cloud provider setup guides

## 📋 **Reference Guides**

### Development Workflows
- **[💼 Business Events](./business-events.md)** - Event logging and business analytics
- **[📝 Workplans](./workplans/)** - Detailed development plans and specifications

### Additional Resources
- **[🔍 API Documentation]** - Generated Swagger/OpenAPI documentation (available when services are running)
- **[📈 Monitoring Dashboards]** - Grafana dashboards (http://localhost:3200 when running locally)

---

## 💡 **Documentation Tips**

- **File Issues**: Use GitHub issues for documentation improvements
- **Local Links**: All relative links work when viewing files locally
- **Live Documentation**: API documentation is generated and available when services are running
- **Search**: Use your editor's search functionality to find specific topics across documentation

**Need help?** Check the [troubleshooting guides](./troubleshooting/README.md) or review the [development roadmap](./development-roadmap.md) for current priorities.
