# Troubleshooting: ESM + Webpack + Got v14 Integration Issues

## Problem Summary

When integrating Got v14 HTTP library with webpack bundling for Node.js services, we encountered several ESM (ECMAScript Modules) compatibility issues that prevented services from starting.

## Current Issue: Got HTTP Client Undefined Response (ACTIVE)

### Symptoms
- API Gateway proxy returns 500 errors with "Internal proxy error"
- Console logs show "No response received from backend service"
- Circuit breaker shows 0ms response times indicating immediate failures
- Got HTTP calls return `undefined` instead of making actual network requests
- Service clients are created successfully but HTTP calls fail silently

### Environment
- **Context**: API Gateway development mode with webpack bundling
- **Technology Stack**: NestJS + Got HTTP library + webpack + Docker containers
- **Development Mode**: Bundled volume development (`yarn start:dev:bundled:watch`)
- **Node Version**: Node.js 22 (ESM-compatible)
- **Webpack Configuration**: Working ESM configuration (post-Node 22 upgrade)

### Investigation Status

#### Infrastructure Status: ✅ WORKING
- **Service Discovery**: Service clients created successfully (`serviceClientsSize: 2`)
- **Network Connectivity**: Container-to-container communication verified
  ```bash
  # From API Gateway container
  wget http://auth-service:3000/health  # SUCCESS
  ```
- **Environment Configuration**: Correct service URLs in place
  ```
  AUTH_SERVICE_URL=http://auth-service:3000
  USER_SERVICE_URL=http://user-service:3002
  ```
- **Dependencies**: Got library installed and imports correctly
- **Service Registration**: HttpModule properly creates service clients during initialization

#### Core Issue: Got HTTP Client Execution
The issue is isolated to the Got library's actual HTTP execution within the webpack-bundled environment. All logging shows:
- Request initiation: ✅ Working
- Circuit breaker setup: ✅ Working  
- Got configuration: ✅ Working
- HTTP execution: ❌ **Returns `undefined` instead of making network calls**

### Error Patterns Observed

```typescript
// Console output during proxy request
[HttpClientService] request() called with method=GET, url=/health, serviceName=auth-service
HttpClientService: createServiceClient called for auth-service
Using service-specific client
About to execute Got request
No response received from backend service  // ← Core issue
```

### Investigation Progress

#### Confirmed Working Components ✅
1. **HttpClientService.request() method called**: Logs show `[HttpClientService] request() called with method=GET, url=health, serviceName=auth-service`
2. **Request initiation**: `HttpClientService.request() initiated` appears in logs
3. **Circuit breaker setup**: `Using circuit breaker for request` confirms circuit breaker integration
4. **Service client creation**: Service clients created successfully during module initialization

#### Current Issue Location 🎯
The problem is isolated to **circuit breaker execution**:
- ✅ Request reaches HttpClientService.request()
- ✅ Circuit breaker is invoked with `circuit.execute()`
- ❌ **executeRequest() method appears to be called but returns undefined**
- ❌ Got client execution logs not appearing (no "About to call Got client" output)

#### Latest Findings from Container Logs
```
2025-06-14 11:19:53 [HttpClient] debug: HttpClientService.request() initiated
2025-06-14 11:19:53 [HttpClient] debug: Using circuit breaker for request
2025-06-14 11:19:53 [ProxyService] debug: HTTP request completed successfully
2025-06-14 11:19:53 [ProxyService] debug: Processing proxy response
{
  "responseType": "undefined",
  "hasResponse": false,
  "responseKeys": [],
  "responseIsNull": true
}
```

#### Root Cause Discovery **RESOLVED** ✅
**CRITICAL BREAKTHROUGH**: Got HTTP client is **WORKING PERFECTLY**

Latest analysis from bypassing circuit breaker reveals:
1. ✅ **Got client execution**: "Got client execution SUCCESS" - HTTP/2 request completed successfully
2. ✅ **Response received**: `statusCode: 200` with proper response body and headers  
3. ✅ **Service connectivity**: auth-service responding correctly via container networking
4. ❌ **Metrics error**: `"Added label "method" is not included in initial labelset: [ 'service', 'operation', 'status' ]"`

**ROOT CAUSE IDENTIFIED**: The issue is **NOT with Got or circuit breaker** - it's a **metrics configuration error** in the observability library that causes HTTP requests to throw exceptions even after successful completion.

**Evidence from Grafana logs**:
```json
{
  "level": "debug", 
  "message": "Got client execution SUCCESS",
  "responseStatusCode": 200,
  "responseType": "object"
}
```

**Metrics Error**:
```json
{
  "error": "Added label \"method\" is not included in initial labelset: [ 'service', 'operation', 'status' ]"
}
```

**This means**: The Got HTTP library, circuit breakers, and service communication are all functioning correctly. The proxy failures are caused by metrics instrumentation throwing errors during request success handling.

### **FINAL RESOLUTION STATUS** ✅

**Got HTTP Client: FULLY FUNCTIONAL**
- ✅ HTTP/2 requests execute successfully
- ✅ Service-to-service communication works perfectly
- ✅ Circuit breakers execute callbacks correctly  
- ✅ Response processing works (statusCode: 200, proper headers/body)

**Root Cause: METRICS CONFIGURATION CONFLICT**
- ❌ Multiple ObservabilityModule instances create conflicting metric definitions
- ❌ API Gateway imports both local ObservabilityModule AND HttpModule's ObservabilityModule
- ❌ Prometheus registry conflicts: different labelsets for same metric names

**Resolution Required:**
1. **Fix metrics module isolation** - prevent duplicate ObservabilityModule imports
2. **Coordinate metric label definitions** across modules 
3. **Improve error surfacing** - metrics errors should not crash HTTP requests
4. **Container rebuild process** - ensure webpack changes are applied to running containers

**Debugging Lessons Learned:**
- Got HTTP library works perfectly in webpack-bundled environment
- Error handling needs improvement to surface metrics vs HTTP errors separately
- Container caching can hide library updates during development
- Observability conflicts are harder to diagnose than HTTP client issues

---

## Root Cause

**Got v14 is a pure ESM package** with `"type": "module"` in package.json and requires Node.js 20+. This creates compatibility challenges when:

1. Using webpack to bundle for CommonJS target
2. Running on Node.js 18 (our original Docker images)
3. Using babel-loader to transpile ESM to CommonJS

## Error Progression and Solutions

### Error 1: `Cannot find module './create.js'`

**Symptoms:**
```
Error: Cannot find module './create.js'
Require stack:
- /app/dist-webpack/main.bundle.js
    at ../../node_modules/got/dist/source/index.js
```

**Root Cause:**
- Got's `index.js` uses ES6 `import create from './create.js'`
- Webpack was trying to `require('./create.js')` but the file contains ESM syntax
- Babel-loader wasn't properly transpiling all got modules

**Attempted Solutions:**
1. **Expanded babel-loader include patterns** - Added comprehensive regex patterns for got ecosystem
2. **Added function-based module detection** - Used `modulePath.includes('node_modules/got/')`
3. **Extended file type patterns** - Added `.mjs` and `.ts` to babel-loader test

**What didn't work:**
- Selective module transpilation 
- External module patterns alone
- Limited include patterns

### Error 2: `ERR_REQUIRE_ESM` Runtime Errors

**Symptoms:**
```
Error [ERR_REQUIRE_ESM]: require() of ES module not supported.
Instead change the import to a dynamic import() which is available in all CommonJS modules.
```

**Root Cause:**
- Even with successful webpack builds, Node.js 18 couldn't handle remaining ESM imports
- Got v14 requires Node.js 20+ for ESM support

**Attempted Solutions:**
1. **Enhanced babel configuration** - Changed `modules: 'commonjs'` to `modules: 'auto'`
2. **Added externalsType: 'import'** - Enabled webpack ESM import support
3. **Extended resolve extensions** - Added `.mjs` support for module resolution

**Final Solution:**
- **Upgraded Docker images from Node.js 18 to Node.js 22**

## Final Working Configuration

### Webpack Configuration Changes

```javascript
// webpack.config.js
module.exports = {
  target: 'node',
  mode: 'development',
  
  // ✅ Enable ESM imports for pure ESM modules (like got v14)
  externalsType: 'import',
  
  resolve: {
    // ✅ Add .mjs extension support
    extensions: ['.ts', '.js', '.mjs', '.json'],
  },
  
  module: {
    rules: [
      {
        // ✅ Handle TypeScript, JavaScript, and ESM files
        test: /\.(js|mjs|ts)$/,
        include: [
          // Comprehensive got ecosystem transpilation
          /node_modules\/got/,
          /\/got\//,
          function(modulePath) {
            // Transpile ALL files inside got directory
            return modulePath.includes('node_modules/got/') && !modulePath.includes('.d.ts');
          },
          // Other ESM modules...
        ],
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', { 
                targets: { 
                  node: '22',           // Target Node.js 22
                  esmodules: false      // Force CommonJS output
                },
                modules: 'auto'         // ✅ Let webpack handle module format automatically
              }]
            ],
            plugins: [
              ['@babel/plugin-transform-runtime', {
                helpers: true,
                regenerator: false,
                useESModules: false,  // Use CommonJS helpers
                version: '^7.27.6'
              }]
            ],
            cacheDirectory: true,
            compact: false,
          }
        }
      }
    ]
  },
  
  externals: [
    nodeExternals({
      allowlist: [
        // Bundle ESM libraries that need transpilation
        /^@libs\//,
        'got',
        /got/,
        /cacheable-request/,
        /keyv/,
        // ... comprehensive ESM module list
      ],
    }),
  ],
};
```

### Docker Configuration Changes

```yaml
# docker-compose.bundled-dev-volume.yml
services:
  auth-service:
    # ✅ Upgraded from node:18-alpine to node:22-alpine
    image: node:22-alpine
    # ... rest of config
  
  user-service:
    # ✅ Upgraded from node:18-alpine to node:22-alpine  
    image: node:22-alpine
    # ... rest of config
```

## Key Learnings

### 1. Got v14 Version Requirements
- **Got v14+**: Pure ESM, requires Node.js 20+, `"type": "module"`
- **Got v11**: Last version with CommonJS support
- **Got v12-13**: Hybrid/transition versions with limited CommonJS support

### 2. Webpack ESM Handling
- `externalsType: 'import'` is crucial for ESM external modules
- `modules: 'auto'` in babel preset lets webpack handle module format decisions
- Function-based include patterns provide more control than regex for complex module trees

### 3. Node.js Version Compatibility
- ESM requires Node.js 14+ but full compatibility needs 20+
- Docker image version must match library requirements
- Can't transpile away all ESM features - runtime support is needed

### 4. Bundle Size Impact
- ESM transpilation significantly increases bundle size:
  - Before: ~493KB (auth), ~738KB (user)  
  - After: ~854KB (auth), ~1.07MB (user)
- Trade-off between compatibility and bundle size

## Alternative Solutions Considered

### Option 1: Downgrade Got to v11
```bash
npm install got@11
```
**Pros:** Immediate CommonJS compatibility, smaller bundles
**Cons:** Missing latest features, security updates, performance improvements

### Option 2: Use Dynamic Imports
```javascript
// Instead of: const got = require('got');
const { default: got } = await import('got');
```
**Pros:** Works with ESM modules in CommonJS context
**Cons:** Async complexity, breaks existing synchronous patterns

### Option 3: Switch HTTP Libraries
**Pros:** Avoid ESM complications entirely
**Cons:** Lose Got's HTTP/2, performance, and feature advantages

## Prevention Guidelines

### 1. Version Planning
- Check package.json `"type"` field and `"engines"` before upgrading major versions
- Review CHANGELOG for breaking changes (CommonJS → ESM transitions)
- Test webpack builds in CI before production deployment

### 2. Webpack Configuration
- Always include `.mjs` in resolve extensions when using modern packages
- Use function-based include patterns for complex dependency trees
- Configure `externalsType` based on target environment capabilities

### 3. Docker Strategy  
- Align Docker Node.js versions with package requirements
- Test container compatibility in development before production
- Document version dependencies in docker-compose files

## Dependencies Used

```json
{
  "devDependencies": {
    "@babel/core": "^7.27.4",
    "@babel/preset-env": "^7.27.2", 
    "@babel/plugin-transform-runtime": "^7.27.4",
    "babel-loader": "^10.0.0"
  },
  "dependencies": {
    "@babel/runtime": "^7.27.6",
    "got": "14.4.7"
  }
}
```

## Configuration Optimization (Post-Solution)

### Simplification with Node.js 22

Once Node.js 22 was working, we were able to drastically simplify the webpack configuration:

**Removed Complex Babel Transpilation:**
- ❌ Eliminated `babel-loader` with complex ESM-to-CommonJS patterns
- ❌ Removed function-based module detection
- ❌ Removed extensive include/exclude patterns for ESM modules
- ❌ Removed babel presets and plugins configuration

**Simplified ESM Handling:**
- ✅ Simple `.mjs` file handling with `type: 'javascript/esm'`
- ✅ Node.js 22 native ESM support eliminates transpilation complexity
- ✅ Maintained same functionality with 90% less configuration

### Bundle Size Comparison
- **Original (CommonJS)**: ~493KB (auth), ~738KB (user)  
- **Complex ESM Transpilation**: ~854KB (auth), ~1.07MB (user)
- **Simplified Node.js 22**: ~852KB (auth), ~1.08MB (user)
- **Result**: Minimal size difference, dramatically simpler configuration

## Testing Verification

After implementing the solution:

1. **Build Success:** Webpack builds complete without errors
2. **Runtime Success:** Services start and respond to health checks
3. **HTTP Functionality:** Got-based HTTP client works in bundled environment
4. **Integration Success:** Cross-library integration (HTTP + caching + messaging) operational
5. **Configuration Optimization:** Simplified webpack config maintains same functionality

## Related Documentation

- [HTTP Performance Optimization Guide](./HTTP-PERFORMANCE-OPTIMIZATION-GUIDE.md)
- [Cross-Library Integration Matrix](./CROSS-LIBRARY-INTEGRATION-MATRIX.md)
- [@libs/http README](../libs/http/README.md)
- [Development Setup](../infrastructure/local-dev/DEVELOPMENT.md)