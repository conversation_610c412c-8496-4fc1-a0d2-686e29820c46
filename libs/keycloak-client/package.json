{"name": "@libs/keycloak-client", "version": "1.0.0", "description": "Centralized Keycloak HTTP client with Got/HTTP2 performance", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "rimraf dist && rimraf tsconfig.build.tsbuildinfo && rimraf tsconfig.tsbuildinfo && tsc -p tsconfig.build.json --listEmittedFiles", "lint": "echo 'Linting keycloak-client...'", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "dependencies": {"@libs/http": "file:../http", "@libs/observability": "file:../observability", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "reflect-metadata": "^0.2.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}, "devDependencies": {"@nestjs/testing": "^10.2.10", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@libs/testing-utils": "file:../testing-utils", "jest": "^29.5.0", "ts-jest": "^29.1.0", "rimraf": "^3.0.2", "typescript": "*"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"tsconfig": "test/tsconfig.json"}]}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "moduleNameMapper": {"^@libs/http$": "<rootDir>/../http/src", "^@libs/http/(.*)$": "<rootDir>/../http/src/$1", "^@libs/observability$": "<rootDir>/../observability/src", "^@libs/observability/(.*)$": "<rootDir>/../observability/src/$1", "^@libs/testing-utils$": "<rootDir>/../testing-utils/src", "^@libs/testing-utils/(.*)$": "<rootDir>/../testing-utils/src/$1"}}}