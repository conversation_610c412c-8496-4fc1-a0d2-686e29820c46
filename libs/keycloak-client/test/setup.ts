// Jest setup for keycloak-client tests
// Mock Got library to prevent ESM issues

jest.mock('got', () => {
  const mockGot: any = jest.fn();
  
  // Add extend method for Got instances
  mockGot.extend = jest.fn(() => mockGot);
  
  // Mock common Got methods
  mockGot.get = jest.fn();
  mockGot.post = jest.fn();
  mockGot.put = jest.fn();
  mockGot.delete = jest.fn();
  mockGot.patch = jest.fn();
  
  // Mock stream method
  mockGot.stream = jest.fn();
  
  return mockGot;
});

// Mock the HTTP library entirely to avoid Got dependency
jest.mock('@libs/http', () => ({
  HttpClientService: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
    put: jest.fn(),
    patch: jest.fn()
  })),
  HttpModule: {
    forRoot: jest.fn(() => ({
      module: class MockHttpModule {},
      providers: [],
      exports: []
    }))
  }
}));

// Mock crypto functions for testing
jest.mock('crypto', () => {
  const actualCrypto = jest.requireActual('crypto');
  
  // Create a proper mock KeyObject constructor
  class MockKeyObject {
    asymmetricKeyType = 'rsa';
    asymmetricKeySize = 2048;
    
    get type() {
      return 'public';
    }
  }
  
  return {
    ...actualCrypto,
    KeyObject: MockKeyObject,
    createPublicKey: jest.fn((options) => new MockKeyObject())
  };
});