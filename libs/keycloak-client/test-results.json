{"numFailedTestSuites": 2, "numFailedTests": 30, "numPassedTestSuites": 0, "numPassedTests": 0, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 30, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750372138826, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["KeycloakClientService Integration Tests", "JWKS Operations Integration"], "duration": 48, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests JWKS Operations Integration should integrate JWKS fetching with HTTP client", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143538, "status": "failed", "title": "should integrate JWKS fetching with HTTP client"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "JWKS Operations Integration"], "duration": 14, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests JWKS Operations Integration should handle complete JWKS to signing key workflow", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143604, "status": "failed", "title": "should handle complete JWKS to signing key workflow"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "JWKS Operations Integration"], "duration": 20, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests JWKS Operations Integration should handle JWKS refresh and caching lifecycle", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143619, "status": "failed", "title": "should handle JWKS refresh and caching lifecycle"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Authentication Operations Integration"], "duration": 14, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Authentication Operations Integration should integrate user authentication with HTTP client", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143640, "status": "failed", "title": "should integrate user authentication with HTTP client"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Authentication Operations Integration"], "duration": 13, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Authentication Operations Integration should integrate token refresh with HTTP client", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143655, "status": "failed", "title": "should integrate token refresh with HTTP client"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Authentication Operations Integration"], "duration": 18, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Authentication Operations Integration should integrate logout with HTTP client", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143669, "status": "failed", "title": "should integrate logout with HTTP client"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "User Management Integration"], "duration": 9, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests User Management Integration should integrate user creation with admin authentication workflow", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143687, "status": "failed", "title": "should integrate user creation with admin authentication workflow"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Health Check Integration"], "duration": 10, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Health Check Integration should integrate health check with HTTP client and timing", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143697, "status": "failed", "title": "should integrate health check with HTTP client and timing"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Health Check Integration"], "duration": 11, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Health Check Integration should handle health check failures with proper error structure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143707, "status": "failed", "title": "should handle health check failures with proper error structure"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Cross-Library Integration"], "duration": 16, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Cross-Library Integration should integrate with observability logging", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143719, "status": "failed", "title": "should integrate with observability logging"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Cross-Library Integration"], "duration": 9, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Cross-Library Integration should integrate with HTTP client error handling", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143736, "status": "failed", "title": "should integrate with HTTP client error handling"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Cross-Library Integration"], "duration": 10, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Cross-Library Integration should properly handle HTTP client response format", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143745, "status": "failed", "title": "should properly handle HTTP client response format"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Error Recovery Integration"], "duration": 21, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Error Recovery Integration should handle and recover from transient HTTP errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143755, "status": "failed", "title": "should handle and recover from transient HTTP errors"}, {"ancestorTitles": ["KeycloakClientService Integration Tests", "Error Recovery Integration"], "duration": 8, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:12:14)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts:35:18)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientService Integration Tests Error Recovery Integration should handle authentication errors with proper context", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143777, "status": "failed", "title": "should handle authentication errors with proper context"}], "endTime": 175**********, "message": "  ● KeycloakClientService Integration Tests › JWKS Operations Integration › should integrate <PERSON><PERSON><PERSON> fetching with HTTP client\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › JWKS Operations Integration › should integrate JWKS fetching with HTTP client\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › JWKS Operations Integration › should handle complete JWKS to signing key workflow\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › JWKS Operations Integration › should handle complete JWKS to signing key workflow\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › JWKS Operations Integration › should handle JWKS refresh and caching lifecycle\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › JWKS Operations Integration › should handle JWKS refresh and caching lifecycle\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Authentication Operations Integration › should integrate user authentication with HTTP client\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Authentication Operations Integration › should integrate user authentication with HTTP client\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Authentication Operations Integration › should integrate token refresh with HTTP client\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Authentication Operations Integration › should integrate token refresh with HTTP client\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Authentication Operations Integration › should integrate logout with HTTP client\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Authentication Operations Integration › should integrate logout with HTTP client\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › User Management Integration › should integrate user creation with admin authentication workflow\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › User Management Integration › should integrate user creation with admin authentication workflow\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Health Check Integration › should integrate health check with HTTP client and timing\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Health Check Integration › should integrate health check with HTTP client and timing\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Health Check Integration › should handle health check failures with proper error structure\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Health Check Integration › should handle health check failures with proper error structure\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Cross-Library Integration › should integrate with observability logging\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Cross-Library Integration › should integrate with observability logging\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Cross-Library Integration › should integrate with HTTP client error handling\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Cross-Library Integration › should integrate with HTTP client error handling\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Cross-Library Integration › should properly handle HTTP client response format\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Cross-Library Integration › should properly handle HTTP client response format\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Error Recovery Integration › should handle and recover from transient HTTP errors\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Error Recovery Integration › should handle and recover from transient HTTP errors\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n\n  ● KeycloakClientService Integration Tests › Error Recovery Integration › should handle authentication errors with proper context\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      10 |\n      11 |   beforeEach(async () => {\n    > 12 |     module = await Test.createTestingModule({\n         |              ^\n      13 |       imports: [\n      14 |         ConfigModule.forRoot({\n      15 |           load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:12:14)\n\n  ● KeycloakClientService Integration Tests › Error Recovery Integration › should handle authentication errors with proper context\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      33 |\n      34 |   afterEach(async () => {\n    > 35 |     await module.close();\n         |                  ^\n      36 |   });\n      37 |\n      38 |   describe('JWKS Operations Integration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-service.integration.spec.ts:35:18)\n", "name": "/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-service.integration.spec.ts", "startTime": 1750372139568, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["KeycloakClientModule Integration Tests", "Module Registration and Configuration"], "duration": 62, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:19:16)"], "fullName": "KeycloakClientModule Integration Tests Module Registration and Configuration should register with default configuration", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143549, "status": "failed", "title": "should register with default configuration"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Module Registration and Configuration"], "duration": 19, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:51:16)"], "fullName": "KeycloakClientModule Integration Tests Module Registration and Configuration should register with custom options", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143623, "status": "failed", "title": "should register with custom options"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Module Registration and Configuration"], "duration": 28, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:70:16)"], "fullName": "KeycloakClientModule Integration Tests Module Registration and Configuration should auto-detect test environment", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143642, "status": "failed", "title": "should auto-detect test environment"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Module Registration and Configuration"], "duration": 16, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:89:18)"], "fullName": "KeycloakClientModule Integration Tests Module Registration and Configuration should handle Docker environment detection", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143670, "status": "failed", "title": "should handle Docker environment detection"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Module Registration and Configuration"], "duration": 11, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:111:16)"], "fullName": "KeycloakClientModule Integration Tests Module Registration and Configuration should register dependencies correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143686, "status": "failed", "title": "should register dependencies correctly"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Service Configuration Integration"], "duration": 12, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:127:16)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:149:20)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientModule Integration Tests Service Configuration Integration should initialize service with correct configuration", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143698, "status": "failed", "title": "should initialize service with correct configuration"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Service Configuration Integration"], "duration": 10, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:127:16)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:149:20)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientModule Integration Tests Service Configuration Integration should have logger factory properly configured", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143722, "status": "failed", "title": "should have logger factory properly configured"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Service Configuration Integration"], "duration": 11, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:127:16)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:149:20)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientModule Integration Tests Service Configuration Integration should have HTTP client properly injected", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143734, "status": "failed", "title": "should have HTTP client properly injected"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Service Configuration Integration"], "duration": 12, "failing": false, "failureDetails": [{}, {}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:127:16)", "TypeError: Cannot read properties of undefined (reading 'close')\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:149:20)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusHook (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:978:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:951:5)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientModule Integration Tests Service Configuration Integration should initialize caching properly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143746, "status": "failed", "title": "should initialize caching properly"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Environment Configuration Integration"], "duration": 21, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:196:16)"], "fullName": "KeycloakClientModule Integration Tests Environment Configuration Integration should use environment variables when provided", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143759, "status": "failed", "title": "should use environment variables when provided"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Environment Configuration Integration"], "duration": 11, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:219:16)"], "fullName": "KeycloakClientModule Integration Tests Environment Configuration Integration should override environment with module options", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143780, "status": "failed", "title": "should override environment with module options"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Environment Configuration Integration"], "duration": 11, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:242:16)"], "fullName": "KeycloakClientModule Integration Tests Environment Configuration Integration should handle missing configuration gracefully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143791, "status": "failed", "title": "should handle missing configuration gracefully"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Global Module Registration"], "duration": 9, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:270:16)"], "fullName": "KeycloakClientModule Integration Tests Global Module Registration should register as global module", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143802, "status": "failed", "title": "should register as global module"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Global Module Registration"], "duration": 20, "failing": false, "failureDetails": [{}], "failureMessages": ["Error: Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\n    at TestingInjector.resolveSingleParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:199:19)\n    at resolveParam (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:129:49)\n    at Array.map (<anonymous>)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:144:58)\n    at TestingInjector.loadInstance (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:70:24)\n    at TestingInjector.loadProvider (/root/code/polyrepo/node_modules/@nestjs/core/injector/injector.js:98:20)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:56:33\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n    at /root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:40:24\n    at Array.map (<anonymous>)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:284:16)"], "fullName": "KeycloakClientModule Integration Tests Global Module Registration should export all required providers", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143811, "status": "failed", "title": "should export all required providers"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Error <PERSON>"], "duration": 25, "failing": false, "failureDetails": [{"matcherResult": {"message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\nReceived message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\"\n\n      297 |   describe('Error Handling Integration', () => {\n      298 |     it('should throw error for invalid configuration during construction', async () => {\n    > 299 |       await expect(\n          |       ^\n      300 |         Test.createTestingModule({\n      301 |           imports: [\n      302 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:299:7)", "pass": false}, "message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\nReceived message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\"\n\n      297 |   describe('Error Handling Integration', () => {\n      298 |     it('should throw error for invalid configuration during construction', async () => {\n    > 299 |       await expect(\n          |       ^\n      300 |         Test.createTestingModule({\n      301 |           imports: [\n      302 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:299:7)"}], "failureMessages": ["Error: expect(received).rejects.toThrow(expected)\n\nExpected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\nReceived message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\"\n\n      297 |   describe('Error Handling Integration', () => {\n      298 |     it('should throw error for invalid configuration during construction', async () => {\n    > 299 |       await expect(\n          |       ^\n      300 |         Test.createTestingModule({\n      301 |           imports: [\n      302 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:299:7)\n    at Object.toThrow (/root/code/polyrepo/libs/http/node_modules/expect/build/index.js:2151:20)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:309:17)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientModule Integration Tests Error Handling Integration should throw error for invalid configuration during construction", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143831, "status": "failed", "title": "should throw error for invalid configuration during construction"}, {"ancestorTitles": ["KeycloakClientModule Integration Tests", "Error <PERSON>"], "duration": 10, "failing": false, "failureDetails": [{"matcherResult": {"message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\nReceived message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\"\n\n      311 |\n      312 |     it('should throw error for missing realm during construction', async () => {\n    > 313 |       await expect(\n          |       ^\n      314 |         Test.createTestingModule({\n      315 |           imports: [\n      316 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:313:7)", "pass": false}, "message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\nReceived message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\"\n\n      311 |\n      312 |     it('should throw error for missing realm during construction', async () => {\n    > 313 |       await expect(\n          |       ^\n      314 |         Test.createTestingModule({\n      315 |           imports: [\n      316 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:313:7)"}], "failureMessages": ["Error: expect(received).rejects.toThrow(expected)\n\nExpected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\nReceived message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\nPotential solutions:\n- Is KeycloakClientModule a valid NestJS module?\n- If dependency is a provider, is it part of the current KeycloakClientModule?\n- If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n  @Module({\n    imports: [ /* the Module containing dependency */ ]\n  })\n\"\n\n      311 |\n      312 |     it('should throw error for missing realm during construction', async () => {\n    > 313 |       await expect(\n          |       ^\n      314 |         Test.createTestingModule({\n      315 |           imports: [\n      316 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:313:7)\n    at Object.toThrow (/root/code/polyrepo/libs/http/node_modules/expect/build/index.js:2151:20)\n    at Object.<anonymous> (/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts:323:17)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "KeycloakClientModule Integration Tests Error Handling Integration should throw error for missing realm during construction", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750372143858, "status": "failed", "title": "should throw error for missing realm during construction"}], "endTime": 1750372143894, "message": "  ● KeycloakClientModule Integration Tests › Module Registration and Configuration › should register with default configuration\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      17 |\n      18 |     it('should register with default configuration', async () => {\n    > 19 |       module = await Test.createTestingModule({\n         |                ^\n      20 |         imports: [\n      21 |           ConfigModule.forRoot({\n      22 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:19:16)\n\n  ● KeycloakClientModule Integration Tests › Module Registration and Configuration › should register with custom options\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      49 |       };\n      50 |\n    > 51 |       module = await Test.createTestingModule({\n         |                ^\n      52 |         imports: [\n      53 |           ConfigModule.forRoot(),\n      54 |           KeycloakClientModule.forRoot(customOptions)\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:51:16)\n\n  ● KeycloakClientModule Integration Tests › Module Registration and Configuration › should auto-detect test environment\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      68 |     it('should auto-detect test environment', async () => {\n      69 |       // Jest environment should auto-detect as test mode\n    > 70 |       module = await Test.createTestingModule({\n         |                ^\n      71 |         imports: [\n      72 |           ConfigModule.forRoot(),\n      73 |           KeycloakClientModule.forRoot()\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:70:16)\n\n  ● KeycloakClientModule Integration Tests › Module Registration and Configuration › should handle Docker environment detection\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      87 |\n      88 |       try {\n    > 89 |         module = await Test.createTestingModule({\n         |                  ^\n      90 |           imports: [\n      91 |             ConfigModule.forRoot(),\n      92 |             KeycloakClientModule.forRoot()\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:89:18)\n\n  ● KeycloakClientModule Integration Tests › Module Registration and Configuration › should register dependencies correctly\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      109 |\n      110 |     it('should register dependencies correctly', async () => {\n    > 111 |       module = await Test.createTestingModule({\n          |                ^\n      112 |         imports: [\n      113 |           ConfigModule.forRoot(),\n      114 |           KeycloakClientModule.forRoot()\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:111:16)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should initialize service with correct configuration\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      125 |   describe('Service Configuration Integration', () => {\n      126 |     beforeEach(async () => {\n    > 127 |       module = await Test.createTestingModule({\n          |                ^\n      128 |         imports: [\n      129 |           ConfigModule.forRoot({\n      130 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:127:16)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should initialize service with correct configuration\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      147 |\n      148 |     afterEach(async () => {\n    > 149 |       await module.close();\n          |                    ^\n      150 |     });\n      151 |\n      152 |     it('should initialize service with correct configuration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:149:20)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should have logger factory properly configured\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      125 |   describe('Service Configuration Integration', () => {\n      126 |     beforeEach(async () => {\n    > 127 |       module = await Test.createTestingModule({\n          |                ^\n      128 |         imports: [\n      129 |           ConfigModule.forRoot({\n      130 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:127:16)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should have logger factory properly configured\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      147 |\n      148 |     afterEach(async () => {\n    > 149 |       await module.close();\n          |                    ^\n      150 |     });\n      151 |\n      152 |     it('should initialize service with correct configuration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:149:20)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should have HTTP client properly injected\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      125 |   describe('Service Configuration Integration', () => {\n      126 |     beforeEach(async () => {\n    > 127 |       module = await Test.createTestingModule({\n          |                ^\n      128 |         imports: [\n      129 |           ConfigModule.forRoot({\n      130 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:127:16)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should have HTTP client properly injected\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      147 |\n      148 |     afterEach(async () => {\n    > 149 |       await module.close();\n          |                    ^\n      150 |     });\n      151 |\n      152 |     it('should initialize service with correct configuration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:149:20)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should initialize caching properly\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      125 |   describe('Service Configuration Integration', () => {\n      126 |     beforeEach(async () => {\n    > 127 |       module = await Test.createTestingModule({\n          |                ^\n      128 |         imports: [\n      129 |           ConfigModule.forRoot({\n      130 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:127:16)\n\n  ● KeycloakClientModule Integration Tests › Service Configuration Integration › should initialize caching properly\n\n    TypeError: Cannot read properties of undefined (reading 'close')\n\n      147 |\n      148 |     afterEach(async () => {\n    > 149 |       await module.close();\n          |                    ^\n      150 |     });\n      151 |\n      152 |     it('should initialize service with correct configuration', () => {\n\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:149:20)\n\n  ● KeycloakClientModule Integration Tests › Environment Configuration Integration › should use environment variables when provided\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      194 |\n      195 |     it('should use environment variables when provided', async () => {\n    > 196 |       module = await Test.createTestingModule({\n          |                ^\n      197 |         imports: [\n      198 |           ConfigModule.forRoot({\n      199 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:196:16)\n\n  ● KeycloakClientModule Integration Tests › Environment Configuration Integration › should override environment with module options\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      217 |\n      218 |     it('should override environment with module options', async () => {\n    > 219 |       module = await Test.createTestingModule({\n          |                ^\n      220 |         imports: [\n      221 |           ConfigModule.forRoot({\n      222 |             load: [() => ({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:219:16)\n\n  ● KeycloakClientModule Integration Tests › Environment Configuration Integration › should handle missing configuration gracefully\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      240 |     it('should handle missing configuration gracefully', async () => {\n      241 |       // This should not throw during module creation\n    > 242 |       module = await Test.createTestingModule({\n          |                ^\n      243 |         imports: [\n      244 |           ConfigModule.forRoot(),\n      245 |           KeycloakClientModule.forRoot({\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:242:16)\n\n  ● KeycloakClientModule Integration Tests › Global Module Registration › should register as global module\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      268 |\n      269 |     it('should register as global module', async () => {\n    > 270 |       module = await Test.createTestingModule({\n          |                ^\n      271 |         imports: [\n      272 |           ConfigModule.forRoot(),\n      273 |           KeycloakClientModule.forRoot()\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:270:16)\n\n  ● KeycloakClientModule Integration Tests › Global Module Registration › should export all required providers\n\n    Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.\n\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n\n      282 |\n      283 |     it('should export all required providers', async () => {\n    > 284 |       module = await Test.createTestingModule({\n          |                ^\n      285 |         imports: [\n      286 |           ConfigModule.forRoot(),\n      287 |           KeycloakClientModule.forRoot()\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n          at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n          at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:284:16)\n\n  ● KeycloakClientModule Integration Tests › Error Handling Integration › should throw error for invalid configuration during construction\n\n    expect(received).rejects.toThrow(expected)\n\n    Expected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\n    Received message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n    \"\n\n          297 |   describe('Error Handling Integration', () => {\n          298 |     it('should throw error for invalid configuration during construction', async () => {\n        > 299 |       await expect(\n              |       ^\n          300 |         Test.createTestingModule({\n          301 |           imports: [\n          302 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n                at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n                at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n                at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:299:7)\n      at Object.toThrow (../http/node_modules/expect/build/index.js:2151:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:309:17)\n\n  ● KeycloakClientModule Integration Tests › Error Handling Integration › should throw error for missing realm during construction\n\n    expect(received).rejects.toThrow(expected)\n\n    Expected substring: \"Keycloak URL or Realm not configured for KeycloakClientService.\"\n    Received message:   \"Nest can't resolve dependencies of the KeycloakClientService (ConfigService, mockConstructor, LOGGER_FACTORY, ?). Please make sure that the argument dependency at index [3] is available in the KeycloakClientModule context.·\n    Potential solutions:\n    - Is KeycloakClientModule a valid NestJS module?\n    - If dependency is a provider, is it part of the current KeycloakClientModule?\n    - If dependency is exported from a separate @Module, is that module imported within KeycloakClientModule?\n      @Module({\n        imports: [ /* the Module containing dependency */ ]\n      })\n    \"\n\n          311 |\n          312 |     it('should throw error for missing realm during construction', async () => {\n        > 313 |       await expect(\n              |       ^\n          314 |         Test.createTestingModule({\n          315 |           imports: [\n          316 |             ConfigModule.forRoot(),\n\n      at TestingInjector.resolveSingleParam (../../node_modules/@nestjs/core/injector/injector.js:199:19)\n      at resolveParam (../../node_modules/@nestjs/core/injector/injector.js:129:49)\n                at Array.map (<anonymous>)\n      at TestingInjector.resolveConstructorParams (../../node_modules/@nestjs/core/injector/injector.js:144:58)\n      at TestingInjector.loadInstance (../../node_modules/@nestjs/core/injector/injector.js:70:24)\n      at TestingInjector.loadProvider (../../node_modules/@nestjs/core/injector/injector.js:98:20)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:56:33\n                at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstancesOfProviders (../../node_modules/@nestjs/core/injector/instance-loader.js:55:36)\n      at ../../node_modules/@nestjs/core/injector/instance-loader.js:40:24\n                at Array.map (<anonymous>)\n      at TestingInstanceLoader.createInstances (../../node_modules/@nestjs/core/injector/instance-loader.js:39:49)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/core/injector/instance-loader.js:22:24)\n      at TestingInstanceLoader.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-instance-loader.js:9:21)\n      at TestingModuleBuilder.createInstancesOfDependencies (../../node_modules/@nestjs/testing/testing-module.builder.js:118:30)\n      at TestingModuleBuilder.compile (../../node_modules/@nestjs/testing/testing-module.builder.js:74:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:313:7)\n      at Object.toThrow (../http/node_modules/expect/build/index.js:2151:20)\n      at Object.<anonymous> (test/integration/keycloak-module.integration.spec.ts:323:17)\n", "name": "/root/code/polyrepo/libs/keycloak-client/test/integration/keycloak-module.integration.spec.ts", "startTime": 1750372139568, "status": "failed", "summary": ""}], "wasInterrupted": false}