import { DynamicModule, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule } from '@libs/http';
import { ObservabilityModule } from '@libs/observability';
import { KeycloakClientService } from './keycloak-client.service';

export interface KeycloakClientModuleOptions {
  /**
   * Override default Keycloak base URL
   * Default: http://localhost:8080 (local) or http://keycloak:8080 (docker)
   */
  baseUrl?: string;
  
  /**
   * Force specific realm (overrides environment detection)
   * Default: auto-detected based on NODE_ENV and test environment
   */
  realm?: string;
  
  /**
   * Enable test mode (uses test realm)
   * Default: auto-detected from NODE_ENV or jest environment
   */
  testMode?: boolean;
  
  /**
   * Additional client configuration
   */
  client?: {
    id?: string;
    secret?: string;
    adminId?: string;
    adminSecret?: string;
  };
}

// Centralized Keycloak configuration with environment detection
export const KEYCLOAK_CONFIG = 'KEYCLOAK_CONFIG';

export interface KeycloakConfig {
  baseUrl: string;
  realm: string;
  clientId: string;
  clientSecret: string;
  adminClientId: string;
  adminClientSecret: string;
  testMode: boolean;
}

@Module({})
export class KeycloakClientModule {
  /**
   * Register Keycloak client with centralized configuration
   * Automatically detects environment and switches between test/production realms
   */
  static forRoot(options?: KeycloakClientModuleOptions): DynamicModule {
    return {
      module: KeycloakClientModule,
      imports: [
        ConfigModule,
        HttpModule.forRoot(), // HTTP/2 optimized for Keycloak
        ObservabilityModule.forRoot({
          logging: { defaultContext: 'KeycloakClient' },
        }),
      ],
      providers: [
        {
          provide: KEYCLOAK_CONFIG,
          useFactory: (configService: any) => {
            // Auto-detect environment and realm
            const isTest = options?.testMode ?? (
                          process.env.NODE_ENV === 'test' || 
                          typeof (global as any).it === 'function' || // jest environment
                          process.env.JEST_WORKER_ID !== undefined);
            
            const isDocker = process.env.DOCKER_ENV === 'true' || 
                           !(process.env.KEYCLOAK_BASE_URL?.includes('localhost') ?? true);
            
            // Centralized configuration with smart defaults
            const config: KeycloakConfig = {
              baseUrl: options?.baseUrl ?? 
                      configService.get('KEYCLOAK_BASE_URL') ?? 
                      (isDocker ? 'http://keycloak:8080' : 'http://localhost:8080'),
              
              realm: options?.realm ?? 
                    configService.get('KEYCLOAK_REALM_NAME') ?? 
                    (isTest ? 'polyrepo-test' : 'polyrepo-realm'),
              
              clientId: options?.client?.id ?? 
                       configService.get('KEYCLOAK_CLIENT_ID') ?? 
                       'auth-service-client',
              
              clientSecret: options?.client?.secret ?? 
                           configService.get('KEYCLOAK_CLIENT_SECRET') ?? 
                           '',
              
              adminClientId: options?.client?.adminId ?? 
                            configService.get('KEYCLOAK_ADMIN_CLIENT_ID') ?? 
                            'admin-cli',
              
              adminClientSecret: options?.client?.adminSecret ?? 
                                configService.get('KEYCLOAK_ADMIN_CLIENT_SECRET') ?? 
                                '',
              
              testMode: isTest,
            };
            
            return config;
          },
          inject: [ConfigService],
        },
        KeycloakClientService,
      ],
      exports: [KeycloakClientService, KEYCLOAK_CONFIG],
      global: true,
    };
  }

  /**
   * For services that need async configuration
   */
  static forRootAsync(options: {
    imports?: any[];
    useFactory?: (...args: any[]) => Promise<KeycloakClientModuleOptions> | KeycloakClientModuleOptions;
    inject?: any[];
  }): DynamicModule {
    return {
      module: KeycloakClientModule,
      imports: [
        ConfigModule,
        HttpModule.forRoot(),
        ObservabilityModule.forRoot({
          logging: { defaultContext: 'KeycloakClient' },
        }),
        ...(options.imports || []),
      ],
      providers: [
        KeycloakClientService,
      ],
      exports: [KeycloakClientService],
      global: true,
    };
  }
}