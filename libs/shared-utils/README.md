# Shared Utils Library

## Purpose
Common utility functions and helper methods for the Polyrepo microservices architecture. This library provides reusable functionality that is needed across multiple services within the template, promoting code reuse and consistency.

## Features

### Core Functionality
- **Environment Variable Management**: Safe environment variable access with defaults
- **Function Utilities**: Debouncing, throttling, and timing utilities
- **Type-Safe Helpers**: Strongly typed utility functions
- **Error Handling**: Consistent error handling patterns

### Available Utilities

#### Environment Management
```typescript
// Safe environment variable access
export function getEnvVariable(key: string, defaultValue?: string): string;

// Usage examples
const dbUrl = getEnvVariable('DATABASE_URL');
const port = getEnvVariable('PORT', '3000');
const debug = getEnvVariable('DEBUG_MODE', 'false');
```

#### Function Control
```typescript
// Simple debounce implementation
export function simpleDebounce<T extends (...args: any[]) => any>(
  func: T,
  waitFor: number
): (...args: Parameters<T>) => void;

// Usage example
const debouncedSave = simpleDebounce(saveData, 300);
debouncedSave(data); // Will wait 300ms before executing
```

## Template Customization

### Adding String Utilities
```typescript
// src/string-utils.ts
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

export function camelToKebab(str: string): string {
  return str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`);
}

export function truncate(str: string, length: number, suffix = '...'): string {
  return str.length <= length ? str : str.slice(0, length) + suffix;
}

// Export in index.ts
export * from './string-utils';
```

### Adding Array Utilities
```typescript
// src/array-utils.ts
export function chunk<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

export function unique<T>(array: T[]): T[] {
  return [...new Set(array)];
}

export function groupBy<T, K extends string | number>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
}

export function sortBy<T>(
  array: T[],
  keyFn: (item: T) => string | number,
  direction: 'asc' | 'desc' = 'asc'
): T[] {
  return [...array].sort((a, b) => {
    const aVal = keyFn(a);
    const bVal = keyFn(b);
    const modifier = direction === 'asc' ? 1 : -1;
    return aVal < bVal ? -modifier : aVal > bVal ? modifier : 0;
  });
}
```

### Adding Date Utilities
```typescript
// src/date-utils.ts
export function formatDate(date: Date, format: string): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export function diffInDays(date1: Date, date2: Date): number {
  const timeDiff = Math.abs(date2.getTime() - date1.getTime());
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

export function isWeekend(date: Date): boolean {
  const day = date.getDay();
  return day === 0 || day === 6; // Sunday = 0, Saturday = 6
}
```

### Adding Validation Utilities
```typescript
// src/validation-utils.ts
export function isEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

export function isUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function isPhoneNumber(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}
```

### Adding Object Utilities
```typescript
// src/object-utils.ts
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

export function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  const result = { ...obj };
  keys.forEach(key => delete result[key]);
  return result;
}

export function pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}

export function merge<T, U>(obj1: T, obj2: U): T & U {
  return { ...obj1, ...obj2 };
}
```

### Adding Async Utilities
```typescript
// src/async-utils.ts
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function retry<T>(
  fn: () => Promise<T>,
  attempts: number = 3,
  delay: number = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    if (attempts <= 1) {
      throw error;
    }
    await sleep(delay);
    return retry(fn, attempts - 1, delay * 2); // Exponential backoff
  }
}

export function timeout<T>(
  promise: Promise<T>,
  ms: number,
  timeoutMessage = 'Operation timed out'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(timeoutMessage)), ms)
    ),
  ]);
}

export async function sequential<T, R>(
  items: T[],
  fn: (item: T) => Promise<R>
): Promise<R[]> {
  const results: R[] = [];
  for (const item of items) {
    results.push(await fn(item));
  }
  return results;
}

export async function parallel<T, R>(
  items: T[],
  fn: (item: T) => Promise<R>,
  concurrency: number = 5
): Promise<R[]> {
  const results: R[] = [];
  const chunks = chunk(items, concurrency);
  
  for (const chunk of chunks) {
    const chunkResults = await Promise.all(chunk.map(fn));
    results.push(...chunkResults);
  }
  
  return results;
}
```

## Usage Examples

### Environment Configuration
```typescript
import { getEnvVariable } from '@libs/shared-utils';

// Service configuration
const config = {
  port: parseInt(getEnvVariable('PORT', '3000')),
  dbUrl: getEnvVariable('DATABASE_URL'),
  jwtSecret: getEnvVariable('JWT_SECRET'),
  redisUrl: getEnvVariable('REDIS_URL', 'redis://localhost:6379'),
  logLevel: getEnvVariable('LOG_LEVEL', 'info'),
};

// Type-safe environment loading
class ConfigService {
  readonly port = parseInt(getEnvVariable('PORT', '3000'));
  readonly dbUrl = getEnvVariable('DATABASE_URL');
  readonly isProd = getEnvVariable('NODE_ENV') === 'production';
  readonly features = {
    caching: getEnvVariable('CACHE_ENABLED', 'true') === 'true',
    tracing: getEnvVariable('TRACING_ENABLED', 'true') === 'true',
  };
}
```

### Function Control
```typescript
import { simpleDebounce } from '@libs/shared-utils';

// Debounced search
const debouncedSearch = simpleDebounce(async (query: string) => {
  const results = await searchService.search(query);
  updateSearchResults(results);
}, 300);

// User input handler
const handleSearchInput = (event: Event) => {
  const query = (event.target as HTMLInputElement).value;
  debouncedSearch(query);
};

// Debounced save operation
const debouncedSave = simpleDebounce(async (data: any) => {
  await apiService.save(data);
  showSaveNotification();
}, 1000);
```

### Advanced Patterns
```typescript
import { 
  getEnvVariable, 
  simpleDebounce, 
  retry, 
  timeout,
  groupBy,
  sortBy 
} from '@libs/shared-utils';

// Resilient API call with retry and timeout
const resilientApiCall = async (url: string) => {
  const timeoutMs = parseInt(getEnvVariable('API_TIMEOUT', '5000'));
  const maxRetries = parseInt(getEnvVariable('MAX_RETRIES', '3'));
  
  return retry(
    () => timeout(fetch(url), timeoutMs),
    maxRetries,
    1000
  );
};

// Data processing with utilities
const processUserData = (users: User[]) => {
  // Group users by role
  const usersByRole = groupBy(users, user => user.role);
  
  // Sort users by creation date
  const sortedUsers = sortBy(users, user => user.createdAt, 'desc');
  
  return {
    byRole: usersByRole,
    sorted: sortedUsers,
    total: users.length
  };
};
```

## Performance Considerations

### Debouncing Best Practices
```typescript
// Good: Reuse debounced functions
const debouncedSave = simpleDebounce(saveData, 300);

// Use in component/service
class DataService {
  private debouncedSave = simpleDebounce(this.saveData.bind(this), 300);
  
  async handleChange(data: any) {
    this.debouncedSave(data);
  }
}

// Bad: Creating new debounced function each time
const handleChange = (data: any) => {
  const debounced = simpleDebounce(saveData, 300); // Creates new function
  debounced(data);
};
```

### Memory Management
```typescript
// Clean up debounced functions
class ComponentManager {
  private cleanupFunctions: Array<() => void> = [];
  
  setupDebouncedHandlers() {
    const debounced = simpleDebounce(this.handler, 300);
    this.cleanupFunctions.push(() => {
      // Cancel any pending debounced calls
      debounced.cancel?.();
    });
  }
  
  destroy() {
    this.cleanupFunctions.forEach(cleanup => cleanup());
  }
}
```

## Development

### Prerequisites
- Node.js 20+
- TypeScript 5+
- Yarn package manager

### Build Commands
```bash
# Build the library
cd libs/shared-utils
yarn build

# Type checking
tsc --noEmit

# Linting
yarn lint
```

### Testing Utilities
```typescript
// test/utils.spec.ts
import { getEnvVariable, simpleDebounce } from '../src';

describe('Environment Utils', () => {
  it('should return environment variable', () => {
    process.env.TEST_VAR = 'test-value';
    expect(getEnvVariable('TEST_VAR')).toBe('test-value');
  });
  
  it('should return default value', () => {
    expect(getEnvVariable('MISSING_VAR', 'default')).toBe('default');
  });
  
  it('should throw for missing required variable', () => {
    expect(() => getEnvVariable('MISSING_REQUIRED')).toThrow();
  });
});

describe('Function Utils', () => {
  it('should debounce function calls', async () => {
    const mockFn = jest.fn();
    const debounced = simpleDebounce(mockFn, 100);
    
    debounced('call1');
    debounced('call2');
    debounced('call3');
    
    expect(mockFn).not.toHaveBeenCalled();
    
    await new Promise(resolve => setTimeout(resolve, 150));
    expect(mockFn).toHaveBeenCalledTimes(1);
    expect(mockFn).toHaveBeenCalledWith('call3');
  });
});
```

## Integration with Services

### Service Configuration
```typescript
import { getEnvVariable } from '@libs/shared-utils';

@Injectable()
export class ConfigService {
  readonly database = {
    url: getEnvVariable('DATABASE_URL'),
    poolSize: parseInt(getEnvVariable('DB_POOL_SIZE', '10')),
    timeout: parseInt(getEnvVariable('DB_TIMEOUT', '30000')),
  };
  
  readonly redis = {
    url: getEnvVariable('REDIS_URL', 'redis://localhost:6379'),
    ttl: parseInt(getEnvVariable('CACHE_TTL', '3600')),
    enabled: getEnvVariable('CACHE_ENABLED', 'true') === 'true',
  };
  
  readonly server = {
    port: parseInt(getEnvVariable('PORT', '3000')),
    host: getEnvVariable('HOST', '0.0.0.0'),
    cors: getEnvVariable('CORS_ORIGIN', '*'),
  };
}
```

### Controller Usage
```typescript
import { simpleDebounce } from '@libs/shared-utils';

@Injectable()
export class SearchService {
  private debouncedIndex = simpleDebounce(
    this.updateSearchIndex.bind(this),
    5000
  );
  
  async handleDataUpdate(data: any) {
    await this.saveData(data);
    this.debouncedIndex(); // Debounced search index update
  }
  
  private async updateSearchIndex() {
    // Expensive search index update
    await this.searchIndexService.rebuild();
  }
}
```

## Best Practices

### Utility Design Guidelines
1. **Keep functions pure** when possible
2. **Use TypeScript generics** for reusable patterns
3. **Provide sensible defaults** for optional parameters
4. **Handle edge cases** gracefully
5. **Document performance characteristics**

### Error Handling
```typescript
export function safeParseJson<T>(json: string, defaultValue: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return defaultValue;
  }
}

export function safeNumber(value: string | number, defaultValue: number = 0): number {
  if (typeof value === 'number') {
    return isNaN(value) ? defaultValue : value;
  }
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}
```

### Type Safety
```typescript
// Use generics for type-safe utilities
export function identity<T>(value: T): T {
  return value;
}

export function pipe<T, U, V>(
  fn1: (input: T) => U,
  fn2: (input: U) => V
): (input: T) => V {
  return (input: T) => fn2(fn1(input));
}

// Strongly typed event handlers
export function createEventHandler<T>(
  handler: (event: T) => void
): (event: T) => void {
  return (event: T) => {
    try {
      handler(event);
    } catch (error) {
      console.error('Event handler error:', error);
    }
  };
}
```

## Dependencies

### Core Dependencies
- No runtime dependencies (zero-dependency utility library)

### Development Dependencies
- `typescript` - TypeScript compiler
- `eslint` - Code linting

## Contributing

### Adding New Utilities
1. Create utility modules in `src/`
2. Export functions from `index.ts`
3. Add comprehensive unit tests
4. Document usage examples
5. Update this README
6. Follow TypeScript best practices

### Utility Requirements
1. **No external dependencies** (keep library lightweight)
2. **Type-safe implementations** with generics where appropriate
3. **Comprehensive error handling** for edge cases
4. **Performance-conscious design** for frequently used utilities
5. **Clear documentation** with examples

## License
Private - Part of Polyrepo template project