// import type { Prisma } from '@prisma/client';

// Define a local interface for MiddlewareParams based on its known structure
interface MiddlewareParams {
  model?: any; // Using any to ensure assignability to Prisma.ModelName | undefined
  action: any;  // Using any to ensure assignability to Prisma.PrismaAction
  args: any;
  dataPath: string[];
  runInTransaction: boolean;
}

export function softDeleteMiddleware() { 
  return async (params: MiddlewareParams, next: (params: MiddlewareParams) => Promise<any>) => { 
    if (params.args?._unsafeBypassSoftDelete) {
      delete params.args._unsafeBypassSoftDelete; 
      return next(params); 
    }

    if (params.model === 'User') { 
      if (params.action === 'delete') {
        params.action = 'update';
        params.args.data = { ...params.args.data, deletedAt: new Date(), version: { increment: 1 } };
      }

      if (params.action === 'deleteMany') {
        params.action = 'updateMany';
        params.args.where = params.args.where || {};
        params.args.data = { ...params.args.data, deletedAt: new Date(), version: { increment: 1 } };
      }

      const readActions: string[] = ['findUnique', 'findFirst', 'findMany', 'count', 'aggregate', 'groupBy']; 
      if (readActions.includes(params.action)) {
        params.args = params.args || {};
        if (!params.args.includeDeleted) {
          if (params.args.where) {
            if (params.args.where.AND) {
              if (Array.isArray(params.args.where.AND)) {
                params.args.where.AND.push({ deletedAt: null });
              } else {
                params.args.where.AND = [params.args.where.AND, { deletedAt: null }];
              }
            } else {
              params.args.where.deletedAt = null;
            }
          } else {
            params.args.where = { deletedAt: null };
          }
        }
        delete params.args.includeDeleted;
      }

      const modifyActions: string[] = ['update', 'updateMany', 'upsert']; 
      if (modifyActions.includes(params.action)) {
        params.args = params.args || {};
        if (!params.args.includeDeleted) {
          if (params.args.where) {
            params.args.where.deletedAt = null;
          } else {
            params.args.where = { deletedAt: null };
          }
        }
        delete params.args.includeDeleted;
      }
    }

    return next(params);
  };
}
