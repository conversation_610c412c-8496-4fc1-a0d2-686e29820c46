declare module 'winston-loki' {
  import * as winston from 'winston';
  import Transport from 'winston-transport';
  
  interface LokiTransportOptions {
    host: string;
    labels?: Record<string, string>;
    json?: boolean;
    batching?: boolean;
    interval?: number;
    format?: winston.Logform.Format;
    replaceTimestamp?: boolean;
    onConnectionError?: (err: Error) => void;
  }
  
  class LokiTransport extends Transport {
    constructor(options: LokiTransportOptions);
  }
  
  export = LokiTransport;
}
