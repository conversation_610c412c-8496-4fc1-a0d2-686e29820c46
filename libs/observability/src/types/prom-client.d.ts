declare module 'prom-client' {
  export const register: Registry;

  export interface Registry {
    metrics(): Promise<string>;
    resetMetrics(): void;
    setDefaultLabels(labels: Record<string, string>): void;
  }

  export interface MetricConfiguration<T extends string> {
    name: string;
    help: string;
    labelNames?: T[];
  }

  export class Counter<T extends string = string> {
    constructor(config: MetricConfiguration<T> & { buckets?: number[] });
    inc(labels?: Record<string, string | number>, value?: number): void;
  }

  export class Gauge<T extends string = string> {
    constructor(config: MetricConfiguration<T>);
    set(labels: Record<string, string | number>, value: number): void;
    inc(labels?: Record<string, string | number>, value?: number): void;
    dec(labels?: Record<string, string | number>, value?: number): void;
  }

  export class Histogram<T extends string = string> {
    constructor(config: MetricConfiguration<T> & { buckets?: number[] });
    observe(labels: Record<string, string | number>, value: number): void;
  }

  export class Summary<T extends string = string> {
    constructor(config: MetricConfiguration<T> & { percentiles?: number[] });
    observe(labels: Record<string, string | number>, value: number): void;
  }
}
