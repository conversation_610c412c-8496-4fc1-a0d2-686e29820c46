declare module '@opentelemetry/sdk-node' {
  export class NodeSDK {
    constructor(options: any);
    start(): void;
    shutdown(): Promise<void>;
  }
}

declare module '@opentelemetry/auto-instrumentations-node' {
  export function getNodeAutoInstrumentations(): any[];
}

declare module '@opentelemetry/exporter-trace-otlp-http' {
  export class OTLPTraceExporter {
    constructor(options: { url: string; headers?: Record<string, string> });
  }
}

declare module '@opentelemetry/resources' {
  export class Resource {
    constructor(attributes: Record<string, string>);
  }
}

declare module '@opentelemetry/semantic-conventions' {
  export const SemanticResourceAttributes: {
    SERVICE_NAME: string;
    DEPLOYMENT_ENVIRONMENT: string;
  };
}

declare module '@opentelemetry/sdk-trace-base' {
  export class BatchSpanProcessor {
    constructor(exporter: any);
  }
}

declare module '@opentelemetry/api' {
  export type SpanStatus = {
    code: string;
    message?: string;
  };

  export const SpanStatusCode: {
    ERROR: string;
    OK: string;
  };

  export interface Span {
    setAttribute(key: string, value: string | number | boolean): void;
    setStatus(status: SpanStatus): void;
    recordException(exception: Error): void;
    end(): void;
    addEvent(name: string, attributes?: Record<string, string | number | boolean>): void;
  }

  export const trace: {
    getTracer(name: string): {
      startSpan(name: string): Span;
    };
    getSpan(context: any): Span | undefined;
    setSpan(context: any, span: Span): any;
  };

  export const context: {
    active(): any;
    with<T>(context: any, fn: () => T): T;
  };
}
