import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import * as winston from 'winston';
import 'winston-daily-rotate-file';
import LokiTransport from 'winston-loki';

export interface LoggerConfig {
  service?: string;
  defaultContext?: string;
  enableConsole?: boolean;
  enableLoki?: boolean;
  lokiHost?: string;
  logLevel?: string;
  enableFileRotation?: boolean;
  fileRotationConfig?: {
    dirname?: string;
    filename?: string;
    maxSize?: string;
    maxFiles?: string;
  };
}

@Injectable()
export class ObservabilityLogger implements NestLoggerService {
  private readonly logger: winston.Logger;
  private context?: string;

  constructor(private config: LoggerConfig = {}) {
    this.context = config.defaultContext || 'Application';
    this.logger = this.createWinstonLogger();
  }

  private createWinstonLogger(): winston.Logger {
    const {
      service = process.env.SERVICE_NAME || 'auth-service',
      enableConsole = true,
      enableLoki = process.env.ENABLE_LOKI === 'true',
      lokiHost = process.env.LOKI_HOST || 'http://loki:3100',
      logLevel = this.config.logLevel || process.env.LOG_LEVEL || 'info',
      enableFileRotation = false,
      fileRotationConfig = {}
    } = this.config;

    // DEBUG: Log the determined log level
    console.log(`[ObservabilityLogger] Determined logLevel for Winston: ${logLevel}, from config: ${this.config.logLevel}, from env: ${process.env.LOG_LEVEL}`);

    // Standard format for all transports
    const standardFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    );

    // Pretty console format
    const consoleFormat = winston.format.combine(
      winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
      winston.format.printf(({ level, message, context, timestamp, ...meta }) => {
        // Check both the extracted context parameter and meta.context
        // Winston sometimes extracts context from meta and sometimes keeps it in meta
        const ctx = context || meta.context || this.context || 'Application';
        
        // Debug the context issue
        if (process.env.LOG_LEVEL === 'debug') {
          console.log(`[DEBUG] Context resolution: extracted=${context}, meta=${meta.context}, this=${this.context}, final=${ctx}`);
        }
        
        // Include metadata in the log message if present
        const metaString = Object.keys(meta).length > 0 
          ? `\n${JSON.stringify(meta, null, 2)}` 
          : '';
        return `${timestamp} [${ctx}] ${level}: ${message}${metaString}`;
      }),
      winston.format.colorize({ all: true }),
    );

    const transports: winston.transport[] = [];

    // Add console transport if enabled
    if (enableConsole) {
      transports.push(
        new winston.transports.Console({
          format: consoleFormat,
          level: logLevel,
        })
      );
    }

    // Add file rotation transport if enabled
    if (enableFileRotation) {
      const {
        dirname = 'logs',
        filename = 'application-%DATE%.log',
        maxSize = '20m',
        maxFiles = '14d'
      } = fileRotationConfig;

      transports.push(
        new winston.transports.DailyRotateFile({
          dirname,
          filename,
          maxSize,
          maxFiles,
          format: standardFormat,
        })
      );
    }

    // Add Loki transport if enabled
    if (enableLoki) {
      transports.push(
        new LokiTransport({
          host: lokiHost,
          labels: {
            app: process.env.APP_NAME || 'polyrepo',
            environment: process.env.NODE_ENV || 'development',
            service,
          },
          json: true,
          batching: true,
          interval: 5, // Send logs every 5 seconds
          format: standardFormat,
        })
      );
    }

    return winston.createLogger({
      level: logLevel,
      defaultMeta: {
        service,
      },
      transports,
      // Exception and rejection handlers
      exceptionHandlers: [
        new winston.transports.Console({ format: consoleFormat }),
      ],
      rejectionHandlers: [
        new winston.transports.Console({ format: consoleFormat }),
      ],
    });
  }

  setContext(context: string): void {
    this.context = context;
  }

  getContext(): string | undefined {
    return this.context;
  }

  log(message: any, contextOverride?: string): void {
    const effectiveContext = contextOverride || this.context;
    if (message instanceof Object) {
      this.logger.info('', { ...message, context: effectiveContext });
    } else {
      this.logger.info(message, { context: effectiveContext });
    }
  }

  error(message: any, trace?: string, contextOverride?: string): void {
    const effectiveContext = contextOverride || this.context;
    if (message instanceof Object) {
      this.logger.error('', { 
        ...message, 
        trace,
        context: effectiveContext,
      });
    } else {
      this.logger.error(message, { trace, context: effectiveContext });
    }
  }

  warn(message: any, contextOverride?: string): void {
    const effectiveContext = contextOverride || this.context;
    if (message instanceof Object) {
      this.logger.warn('', { ...message, context: effectiveContext });
    } else {
      this.logger.warn(message, { context: effectiveContext });
    }
  }

  debug(message: any, contextOverride?: string): void {
    const effectiveContext = contextOverride || this.context;
    if (message instanceof Object) {
      this.logger.debug('', { ...message, context: effectiveContext });
    } else {
      this.logger.debug(message, { context: effectiveContext });
    }
  }

  verbose(message: any, contextOverride?: string): void {
    const effectiveContext = contextOverride || this.context;
    if (message instanceof Object) {
      this.logger.verbose('', { ...message, context: effectiveContext });
    } else {
      this.logger.verbose(message, { context: effectiveContext });
    }
  }
}
