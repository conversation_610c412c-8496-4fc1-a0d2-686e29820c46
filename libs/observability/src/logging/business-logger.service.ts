import { Injectable } from '@nestjs/common';
import { ObservabilityLogger } from './logger.service';

export interface BusinessEvent {
  eventType: string;
  userId?: string;
  timestamp?: string;
  [key: string]: any;
}

@Injectable()
export class BusinessLogger {
  constructor(private readonly logger: ObservabilityLogger) {
    this.logger.setContext('BusinessEvents');
  }

  /**
   * Log a user authentication event
   *
   * @param action The authentication action being performed
   * @param status The outcome of the action (success or failure)
   * @param userId The ID of the user performing the action
   * @param details Additional details about the action
   *
   * @see docs/business-events.md for the complete list of auth events
   */
  logAuthEvent(
    action: 'login' | 'register' | 'logout' | 'password_reset' | 'password_reset_request' |
            'email_verification' | 'token_refresh' | 'account_lock' | 'account_unlock',
    status: 'success' | 'failure',
    userId: string,
    details: Record<string, any> = {}
  ): void {
    this.logger.log({
      eventType: 'auth_event',
      action,
      status,
      userId,
      timestamp: new Date().toISOString(),
      ...details
    });
  }

  /**
   * Log a user activity event
   *
   * @param userId The ID of the user performing the activity
   * @param action The activity being performed
   * @param details Additional details about the activity
   *
   * @see docs/business-events.md for the complete list of user activity events
   */
  logUserActivity(
    userId: string,
    action: 'create' | 'update' | 'delete' | 'status_change' | 'profile_view' | string,
    details: Record<string, any> = {}
  ): void {
    this.logger.log({
      eventType: 'user_activity',
      userId,
      action,
      timestamp: new Date().toISOString(),
      ...details
    });
  }

  /**
   * Log a generic business event
   *
   * @param eventType The type of business event
   * @param payload Additional details about the event
   *
   * @see docs/business-events.md for the complete list of business events
   */
  logBusinessEvent(
    eventType: 'process_started' | 'process_completed' | 'process_failed' | string,
    payload: Record<string, any> = {}
  ): void {
    this.logger.log({
      eventType: 'business_event',
      businessEventType: eventType,
      timestamp: new Date().toISOString(),
      ...payload
    });
  }

  /**
   * Log an API request for analytics
   *
   * @param method The HTTP method of the request
   * @param path The path of the request
   * @param statusCode The HTTP status code of the response
   * @param durationMs The duration of the request in milliseconds
   * @param userId The ID of the user making the request (if available)
   * @param payload Additional details about the request
   *
   * @see docs/business-events.md for more information about API request events
   */
  logApiRequest(
    method: string,
    path: string,
    statusCode: number,
    durationMs: number,
    userId?: string,
    payload: Record<string, any> = {}
  ): void {
    this.logger.log({
      eventType: 'api_request',
      method,
      path,
      statusCode,
      durationMs,
      userId,
      timestamp: new Date().toISOString(),
      ...payload
    });
  }
}
