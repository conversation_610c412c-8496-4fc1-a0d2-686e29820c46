import { Module, DynamicModule, Provider } from '@nestjs/common';
import { LoggerService } from '@nestjs/common';
import { ObservabilityLogger, LoggerConfig } from './logger.service';
import { BusinessLogger } from './business-logger.service';
import { 
  OBSERVABILITY_LOGGER, 
  METRICS_SERVICE, 
  TRACING_SERVICE, 
  BUSINESS_LOGGER,
  LOGGER_FACTORY,
  LOGGER_SERVICE 
} from '../tokens';

@Module({})
export class LoggingModule {
  static forRoot(config: LoggerConfig = {}): DynamicModule {
    // Create a LoggerFactory class to preserve contextual behavior
    class LoggerFactory {
      private readonly baseLogger: ObservabilityLogger;
      private readonly contextLoggers: Map<string, ObservabilityLogger> = new Map();

      constructor() {
        // Debug info
        console.log('[LoggingModule] Factory for LOGGER_SERVICE in @libs/observability called. Configured logLevel:', config.logLevel);

        // Create the base logger
        this.baseLogger = new ObservabilityLogger(config);
      }

      // Factory method that creates or returns a logger instance
      // Each instance maintains its own context
      createLogger(context?: string): ObservabilityLogger {
        if (!context) {
          return this.baseLogger;
        }

        if (!this.contextLoggers.has(context)) {
          const logger = new ObservabilityLogger(config);
          logger.setContext(context);
          this.contextLoggers.set(context, logger);
          return logger;
        }

        return this.contextLoggers.get(context)!;
      }
    }

    // Main logger factory instance
    const loggerFactoryProvider: Provider = {
      provide: LOGGER_FACTORY,
      useClass: LoggerFactory,
    };

    // Provider for the actual logger service
    const mainLoggerProvider: Provider = {
      provide: LOGGER_SERVICE,
      useFactory: (factory: LoggerFactory) => {
        return factory.createLogger(config.defaultContext);
      },
      inject: [LOGGER_FACTORY],
    };

    const businessLoggerProvider: Provider = {
      provide: BusinessLogger,
      useFactory: (factory: LoggerFactory) => {
        return new BusinessLogger(factory.createLogger('BusinessEvents'));
      },
      inject: [LOGGER_FACTORY],
    };

    // Unified token providers for consistent injection
    const observabilityLoggerProvider: Provider = {
      provide: OBSERVABILITY_LOGGER,
      useFactory: (factory: LoggerFactory) => {
        return factory.createLogger('HttpClient');
      },
      inject: [LOGGER_FACTORY],
    };

    const businessLoggerTokenProvider: Provider = {
      provide: BUSINESS_LOGGER,
      useFactory: (factory: LoggerFactory) => {
        return new BusinessLogger(factory.createLogger('BusinessEvents'));
      },
      inject: [LOGGER_FACTORY],
    };

    // Direct class providers for backward compatibility
    const directObservabilityLoggerProvider: Provider = {
      provide: ObservabilityLogger,
      useFactory: (factory: LoggerFactory) => {
        return factory.createLogger('Default');
      },
      inject: [LOGGER_FACTORY],
    };

    return {
      module: LoggingModule,
      providers: [
        loggerFactoryProvider, 
        mainLoggerProvider, 
        businessLoggerProvider,
        observabilityLoggerProvider,
        businessLoggerTokenProvider,
        directObservabilityLoggerProvider,
      ],
      exports: [
        LOGGER_SERVICE, 
        BusinessLogger, 
        LOGGER_FACTORY,
        OBSERVABILITY_LOGGER,
        BUSINESS_LOGGER,
        ObservabilityLogger,
      ],
      global: true,
    };
  }

  static forRootAsync(options: {
    imports?: any[];
    inject?: any[];
    useFactory: (...args: any[]) => Promise<LoggerConfig> | LoggerConfig;
  }): DynamicModule {
    // Provider for the async configuration
    const asyncConfigProvider: Provider = {
      provide: 'LOGGING_CONFIG',
      useFactory: options.useFactory,
      inject: options.inject || [],
    };

    // Provider for the LoggerFactory
    const loggerFactoryProvider: Provider = {
      provide: LOGGER_FACTORY,
      useFactory: (config: LoggerConfig) => {
        class LoggerFactory {
          private readonly baseLogger: ObservabilityLogger;
          private readonly contextLoggers: Map<string, ObservabilityLogger> = new Map();

          constructor() {
            // Debug info
            console.log('[LoggingModule] Factory for LOGGER_SERVICE in @libs/observability called. Configured logLevel:', config.logLevel);

            // Create the base logger
            this.baseLogger = new ObservabilityLogger(config);
          }

          createLogger(context?: string): ObservabilityLogger {
            if (!context) {
              return this.baseLogger;
            }

            if (!this.contextLoggers.has(context)) {
              const logger = new ObservabilityLogger(config);
              logger.setContext(context);
              this.contextLoggers.set(context, logger);
              return logger;
            }

            return this.contextLoggers.get(context)!;
          }
        }

        return new LoggerFactory();
      },
      inject: ['LOGGING_CONFIG'],
    };

    // Provider for the actual logger service
    const mainLoggerProvider: Provider = {
      provide: LOGGER_SERVICE,
      useFactory: (factory: any, config: LoggerConfig) => {
        return factory.createLogger(config.defaultContext);
      },
      inject: [LOGGER_FACTORY, 'LOGGING_CONFIG'],
    };

    const businessLoggerProvider: Provider = {
      provide: BusinessLogger,
      useFactory: (factory: any) => {
        return new BusinessLogger(factory.createLogger('BusinessEvents'));
      },
      inject: [LOGGER_FACTORY],
    };

    // Unified token providers for consistent injection - same as forRoot
    const observabilityLoggerProvider: Provider = {
      provide: OBSERVABILITY_LOGGER,
      useFactory: (factory: any) => {
        return factory.createLogger('HttpClient');
      },
      inject: [LOGGER_FACTORY],
    };

    const businessLoggerTokenProvider: Provider = {
      provide: BUSINESS_LOGGER,
      useFactory: (factory: any) => {
        return new BusinessLogger(factory.createLogger('BusinessEvents'));
      },
      inject: [LOGGER_FACTORY],
    };

    // Direct class providers for backward compatibility
    const directObservabilityLoggerProvider: Provider = {
      provide: ObservabilityLogger,
      useFactory: (factory: any) => {
        return factory.createLogger('Default');
      },
      inject: [LOGGER_FACTORY],
    };

    return {
      module: LoggingModule,
      imports: options.imports || [],
      providers: [
        asyncConfigProvider,
        loggerFactoryProvider,
        mainLoggerProvider,
        businessLoggerProvider,
        observabilityLoggerProvider,
        businessLoggerTokenProvider,
        directObservabilityLoggerProvider,
      ],
      exports: [
        LOGGER_SERVICE, 
        BusinessLogger, 
        LOGGER_FACTORY,
        OBSERVABILITY_LOGGER,
        BUSINESS_LOGGER,
        ObservabilityLogger,
      ],
      global: true,
    };
  }
}
