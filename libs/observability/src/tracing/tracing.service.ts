import { Injectable, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base';
// To avoid peer dependency warnings, we need to add @opentelemetry/api
import * as api from '@opentelemetry/api';

type Span = api.Span;
const { trace, context } = api;
const SpanStatusCode = api.SpanStatusCode;

export interface TracingConfig {
  serviceName?: string;
  environment?: string;
  tempoEndpoint?: string;
  samplingRatio?: number;
}

@Injectable()
export class TracingService implements OnModuleInit, OnModuleDestroy {
  private sdk!: NodeSDK; // Using definite assignment assertion
  private initialized = false;
  private readonly serviceName: string;

  constructor(private config: TracingConfig = {}) {
    this.serviceName = config.serviceName || process.env.SERVICE_NAME || 'unknown-service';
  }

  onModuleInit() {
    if (process.env.ENABLE_TRACING === 'true') {
      this.initialize();
    }
  }

  onModuleDestroy() {
    this.shutdown();
  }

  initialize() {
    if (this.initialized) {
      return;
    }

    // Check if we're in a webpack bundled environment
    // In webpack bundled environments, OpenTelemetry has compatibility issues
    const isWebpackBundled = typeof (global as any).__webpack_require__ !== 'undefined';
    
    if (isWebpackBundled) {
      console.log('🔄 Tracing disabled in webpack bundled environment due to compatibility limitations');
      console.log('   For full tracing support, run services in non-bundled development mode');
      
      // Mark as initialized but don't actually initialize OpenTelemetry
      this.initialized = true;
      return;
    }

    const {
      environment = process.env.NODE_ENV || 'development',
      tempoEndpoint = process.env.TEMPO_ENDPOINT || process.env.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT || 'http://localhost:4318/v1/traces',
      // Sampling ratio is not currently used but kept for future reference
      // samplingRatio = process.env.OTEL_SAMPLING_PROBABILITY
      //   ? parseFloat(process.env.OTEL_SAMPLING_PROBABILITY)
      //   : environment === 'production' ? 0.1 : 1.0,
    } = this.config;

    try {
      const otlpExporter = new OTLPTraceExporter({
        url: tempoEndpoint,
        headers: {},
      });

      const spanProcessor = new BatchSpanProcessor(otlpExporter);

      // Create proper Resource object with merge capability
      // Handle potential import/bundling issues with dynamic access
      const { Resource } = require('@opentelemetry/resources');
      const resource = new Resource({
        [SemanticResourceAttributes.SERVICE_NAME]: this.serviceName,
        [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: environment,
      });

      this.sdk = new NodeSDK({
        resource,
        spanProcessor,
        instrumentations: [getNodeAutoInstrumentations()],
        // Configure the sampling rate based on the environment
        // samplingRatio, // Removed as not in current API
      });

      // Start the tracing
      this.sdk.start();
      this.initialized = true;

      console.log(`✅ Tracing initialized for ${this.serviceName} (${environment})`);

      // Gracefully shut down the SDK on process exit
      process.on('SIGTERM', () => {
        this.shutdown()
          .catch((error) => console.log('Error terminating tracing', error))
          .finally(() => process.exit(0));
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('⚠️  Failed to initialize tracing:', errorMessage);
      console.log('   Service will continue without tracing capabilities');
      
      // Mark as initialized to prevent retry loops
      this.initialized = true;
    }
  }

  async shutdown(): Promise<void> {
    if (this.initialized && this.sdk) {
      try {
        await this.sdk.shutdown();
        console.log('Tracing terminated');
        this.initialized = false;
      } catch (error) {
        console.error('Error shutting down tracing', error);
        throw error;
      }
    }
  }

  /**
   * Start a new span and run the provided function within its context
   * @param name Name of the span
   * @param fn Function to execute within the span
   * @param attributes Optional attributes to add to the span
   * @returns The result of the function execution
   */
  async traceAsyncFunction<T>(
    name: string,
    fn: (span: Span) => Promise<T>,
    attributes: Record<string, string | number | boolean> = {}
  ): Promise<T> {
    if (!this.initialized) {
      return fn({} as Span);
    }

    const tracer = trace.getTracer(this.serviceName);
    const span = tracer.startSpan(name);

    // Add attributes
    Object.entries(attributes).forEach(([key, value]) => {
      span.setAttribute(key, value);
    });

    try {
      // Run the function within the context of the span
      return await context.with(trace.setSpan(context.active(), span), async () => {
        try {
          const result = await fn(span);
          span.setStatus({ code: SpanStatusCode.OK });
          return result;
        } catch (error) {
          span.setStatus({
            code: SpanStatusCode.ERROR,
            message: error instanceof Error ? error.message : String(error),
          });
          span.recordException(error instanceof Error ? error : new Error(String(error)));
          throw error;
        }
      });
    } finally {
      span.end();
    }
  }

  /**
   * Get the current span from the active context
   * @returns The current span or undefined if not in a span context
   */
  getCurrentSpan(): Span | undefined {
    if (!this.initialized) {
      return undefined;
    }
    return trace.getSpan(context.active());
  }

  /**
   * Add an attribute to the current span
   * @param key Attribute key
   * @param value Attribute value
   */
  addAttribute(key: string, value: string | number | boolean): void {
    const span = this.getCurrentSpan();
    if (span && span.setAttribute) {
      span.setAttribute(key, value);
    }
  }

  /**
   * Add an event to the current span
   * @param name Event name
   * @param attributes Event attributes
   */
  addEvent(name: string, attributes: Record<string, string | number | boolean> = {}): void {
    const span = this.getCurrentSpan();
    if (span && span.addEvent) {
      span.addEvent(name, attributes);
    }
  }
}
