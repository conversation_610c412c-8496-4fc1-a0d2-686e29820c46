import { Module, DynamicModule, Provider } from '@nestjs/common';
import { TracingService, TracingConfig } from './tracing.service';
import { TRACING_SERVICE, TRACING_CONFIG } from '../tokens';

@Module({})
export class TracingModule {
  static forRoot(config: TracingConfig = {}): DynamicModule {
    const tracingProvider: Provider = {
      provide: TracingService,
      useFactory: () => {
        return new TracingService(config);
      },
    };

    // Token-based provider for consistent injection
    const tokenTracingProvider: Provider = {
      provide: TRACING_SERVICE,
      useExisting: TracingService,
    };

    return {
      module: TracingModule,
      providers: [tracingProvider, tokenTracingProvider],
      exports: [TracingService, TRACING_SERVICE],
      global: true,
    };
  }

  static forRootAsync(options: {
    imports?: any[];
    inject?: any[];
    useFactory: (...args: any[]) => Promise<TracingConfig> | TracingConfig;
  }): DynamicModule {
    const asyncConfigProvider: Provider = {
      provide: TRACING_CONFIG,
      useFactory: options.useFactory,
      inject: options.inject || [],
    };

    const tracingProvider: Provider = {
      provide: TracingService,
      useFactory: (config: TracingConfig) => {
        return new TracingService(config);
      },
      inject: [TRACING_CONFIG],
    };

    // Token-based provider for consistent injection
    const tokenTracingProvider: Provider = {
      provide: TRACING_SERVICE,
      useExisting: TracingService,
    };

    return {
      module: TracingModule,
      imports: options.imports || [],
      providers: [asyncConfigProvider, tracingProvider, tokenTracingProvider],
      exports: [TracingService, TRACING_SERVICE],
      global: true,
    };
  }
}
