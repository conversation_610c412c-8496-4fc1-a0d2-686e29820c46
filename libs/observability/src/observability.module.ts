import { Module, DynamicModule, Type, ModuleMetadata, Provider } from '@nestjs/common';
import { LoggingModule } from './logging/logging.module';
import { MetricsModule } from './metrics/metrics.module';
import { TracingModule } from './tracing/tracing.module';
import { LoggerConfig } from './logging/logger.service';
import { TracingConfig } from './tracing/tracing.service';

export interface ObservabilityModuleOptions {
  logging?: LoggerConfig;
  metrics?: {
    prefix?: string;
    defaultLabels?: Record<string, string>;
  };
  tracing?: TracingConfig;
}

export interface ObservabilityModuleAsyncOptions extends Pick<ModuleMetadata, 'imports'> {
  useFactory: (...args: any[]) => Promise<ObservabilityModuleOptions> | ObservabilityModuleOptions;
  inject?: any[];
}

@Module({})
export class ObservabilityModule {
  static forRoot(options: ObservabilityModuleOptions = {}): DynamicModule {
    return {
      module: ObservabilityModule,
      imports: [
        LoggingModule.forRoot(options.logging),
        MetricsModule.forRoot(options.metrics),
        TracingModule.forRoot(options.tracing),
      ],
      exports: [LoggingModule, MetricsModule, TracingModule],
    };
  }

  static forRootAsync(asyncOptions: ObservabilityModuleAsyncOptions): DynamicModule {
    return {
      module: ObservabilityModule,
      imports: [
        ...(asyncOptions.imports || []),
        this.createLoggingImport(asyncOptions),
        this.createMetricsImport(asyncOptions),
        this.createTracingImport(asyncOptions),
      ],
      exports: [LoggingModule, MetricsModule, TracingModule],
    };
  }

  private static createLoggingImport(options: ObservabilityModuleAsyncOptions): DynamicModule {
    return LoggingModule.forRootAsync({
      imports: options.imports || [],
      inject: options.inject || [],
      useFactory: async (...args: any[]) => {
        const config = await options.useFactory(...args);
        return config.logging || {};
      },
    });
  }

  private static createMetricsImport(options: ObservabilityModuleAsyncOptions): DynamicModule {
    return MetricsModule.forRootAsync({
      imports: options.imports || [],
      inject: options.inject || [],
      useFactory: async (...args: any[]) => {
        const config = await options.useFactory(...args);
        return config.metrics || {};
      },
    });
  }

  private static createTracingImport(options: ObservabilityModuleAsyncOptions): DynamicModule {
    return TracingModule.forRootAsync({
      imports: options.imports || [],
      inject: options.inject || [],
      useFactory: async (...args: any[]) => {
        const config = await options.useFactory(...args);
        return config.tracing || {};
      },
    });
  }
}
