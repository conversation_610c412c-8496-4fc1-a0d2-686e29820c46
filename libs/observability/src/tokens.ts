/**
 * Unified dependency injection tokens for observability services
 * Provides consistent token system across all modules
 */

// Primary observability tokens
export const OBSERVABILITY_LOGGER = Symbol('ObservabilityLogger');
export const METRICS_SERVICE = Symbol('MetricsService'); 
export const TRACING_SERVICE = Symbol('TracingService');
export const BUSINESS_LOGGER = Symbol('BusinessLogger');

// Factory tokens
export const LOGGER_FACTORY = 'LOGGER_FACTORY';
export const LOGGER_SERVICE = 'LoggerService';

// Configuration tokens
export const OBSERVABILITY_CONFIG = Symbol('ObservabilityConfig');
export const LOGGING_CONFIG = Symbol('LoggingConfig');
export const METRICS_CONFIG = Symbol('MetricsConfig');
export const TRACING_CONFIG = Symbol('TracingConfig');