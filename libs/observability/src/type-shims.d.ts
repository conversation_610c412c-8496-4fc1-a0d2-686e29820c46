// This file is used to provide shims for type definitions that may cause issues
// during TypeScript compilation, particularly with implicit type library lookups.

declare global {
  // Attempt to satisfy the "implicit type library 'hapi__shot'"
  // by providing a minimal, empty global declaration.
  // This is a workaround for TS2688 if the compiler is looking
  // for a type/namespace literally named 'hapi__shot'.
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  interface hapi__shot {}
}

// Ensure this file is treated as a module by TypeScript, which can affect how global declarations are processed.
export {};
