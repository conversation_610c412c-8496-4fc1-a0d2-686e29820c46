import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';

export interface PyroscopeConfig {
  enabled: boolean;
  serverAddress: string;
  applicationName: string;
  tags?: Record<string, string>;
  sampleRate?: number;
}

// Import Pyroscope dynamically to handle cases where it's not available
let PyroscopeProfiler: any;
try {
  const pyroscope = require('@pyroscope/nodejs');
  PyroscopeProfiler = pyroscope.default || pyroscope;
} catch (error) {
  // Pyroscope not available, will handle gracefully
  PyroscopeProfiler = null;
}

@Injectable()
export class PyroscopeService implements OnModuleInit, OnModuleDestroy {
  private profiler?: any;
  private initialized = false;

  constructor(private config: PyroscopeConfig) {}

  async onModuleInit() {
    if (!this.config.enabled) {
      console.log('🔄 Pyroscope profiling disabled via configuration');
      return;
    }

    if (!PyroscopeProfiler) {
      console.log('⚠️  Pyroscope library not available, profiling disabled');
      return;
    }

    try {
      this.profiler = new PyroscopeProfiler({
        serverAddress: this.config.serverAddress,
        appName: this.config.applicationName,
        tags: {
          version: process.env.APP_VERSION || '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          ...this.config.tags,
        },
        profileTypes: [
          'cpu',
          'heap',
        ],
        sampleRate: this.config.sampleRate || 100, // Hz
      });

      await this.profiler.start();
      this.initialized = true;
      console.log(`✅ Pyroscope profiling started for ${this.config.applicationName}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log('⚠️  Failed to start Pyroscope profiling:', errorMessage);
      console.log('   Service will continue without profiling capabilities');
      
      // Non-critical failure - continue without profiling
      this.initialized = false;
    }
  }

  async onModuleDestroy() {
    if (this.profiler && this.initialized) {
      try {
        await this.profiler.stop();
        console.log('Pyroscope profiling stopped');
        this.initialized = false;
      } catch (error) {
        console.error('Error stopping Pyroscope profiling:', error);
      }
    }
  }

  /**
   * Add custom labels to profiling data
   */
  addLabels(labels: Record<string, string>): void {
    if (this.profiler && this.initialized) {
      try {
        this.profiler.addLabels(labels);
      } catch (error) {
        console.warn('Failed to add Pyroscope labels:', error);
      }
    }
  }

  /**
   * Profile a specific operation
   */
  async profileOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    labels?: Record<string, string>
  ): Promise<T> {
    if (!this.profiler || !this.initialized) {
      return operation();
    }

    const originalLabels = { operation: operationName, ...labels };
    this.addLabels(originalLabels);

    try {
      return await operation();
    } catch (error) {
      // Add error information to profiling
      this.addLabels({ 
        error: 'true', 
        errorType: error instanceof Error ? error.constructor.name : 'UnknownError' 
      });
      throw error;
    }
    // Note: Pyroscope doesn't have a direct removeLabels method
    // Labels are typically scoped to the profiling session
  }

  /**
   * Create a profiling wrapper for frequently called functions
   */
  createProfiledWrapper<T extends (...args: any[]) => Promise<any>>(
    operationName: string,
    fn: T,
    staticLabels?: Record<string, string>
  ): T {
    if (!this.profiler || !this.initialized) {
      return fn;
    }

    return (async (...args: any[]) => {
      return this.profileOperation(
        operationName,
        () => fn(...args),
        staticLabels
      );
    }) as T;
  }

  /**
   * Check if profiling is active
   */
  isProfilingActive(): boolean {
    return this.initialized && !!this.profiler;
  }
}