import { Module, DynamicModule, Provider } from '@nestjs/common';
import { MetricsService } from './metrics.service';
import { METRICS_SERVICE, METRICS_CONFIG } from '../tokens';

@Module({})
export class MetricsModule {
  static forRoot(options: {
    prefix?: string;
    defaultLabels?: Record<string, string>;
  } = {}): DynamicModule {
    const metricsProvider: Provider = {
      provide: MetricsService,
      useFactory: () => {
        return new MetricsService(options);
      },
    };

    // Token-based provider for consistent injection
    const tokenMetricsProvider: Provider = {
      provide: METRICS_SERVICE,
      useExisting: MetricsService,
    };

    return {
      module: MetricsModule,
      providers: [metricsProvider, tokenMetricsProvider],
      exports: [MetricsService, METRICS_SERVICE],
      global: true,
    };
  }

  static forRootAsync(options: {
    imports?: any[];
    inject?: any[];
    useFactory: (...args: any[]) => Promise<{
      prefix?: string;
      defaultLabels?: Record<string, string>;
    }> | {
      prefix?: string;
      defaultLabels?: Record<string, string>;
    };
  }): DynamicModule {
    const asyncConfigProvider: Provider = {
      provide: METRICS_CONFIG,
      useFactory: options.useFactory,
      inject: options.inject || [],
    };

    const metricsProvider: Provider = {
      provide: MetricsService,
      useFactory: (config: {
        prefix?: string;
        defaultLabels?: Record<string, string>;
      }) => {
        return new MetricsService(config);
      },
      inject: [METRICS_CONFIG],
    };

    // Token-based provider for consistent injection
    const tokenMetricsProvider: Provider = {
      provide: METRICS_SERVICE,
      useExisting: MetricsService,
    };

    return {
      module: MetricsModule,
      imports: options.imports || [],
      providers: [asyncConfigProvider, metricsProvider, tokenMetricsProvider],
      exports: [MetricsService, METRICS_SERVICE],
      global: true,
    };
  }
}
