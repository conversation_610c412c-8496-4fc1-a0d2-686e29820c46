import { Injectable } from '@nestjs/common';
import { register, Counter, Histogram, Gauge, Summary } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly prefix: string;
  private readonly defaultLabels: Record<string, string>;

  private metrics: {
    [key: string]: Counter | Histogram | Gauge | Summary;
  } = {};

  /**
   * Sanitize a metric name to conform to Prometheus naming conventions
   * @param name The name to sanitize
   * @returns Sanitized name
   */
  private sanitizeMetricName(name: string): string {
    // Replace any non-alphanumeric characters with underscores
    // Metric names must match regex [a-zA-Z_:][a-zA-Z0-9_:]*
    return name.replace(/[^a-zA-Z0-9_:]/g, '_');
  }

  constructor(
    options: {
      prefix?: string;
      defaultLabels?: Record<string, string>;
    } = {}
  ) {
    // Sanitize the prefix to ensure it's valid for Prometheus
    const rawPrefix = options.prefix || process.env.SERVICE_NAME || 'app';
    this.prefix = this.sanitizeMetricName(rawPrefix);

    // Create default labels
    this.defaultLabels = options.defaultLabels || {
      service: process.env.SERVICE_NAME || 'unknown_service',
      environment: process.env.NODE_ENV || 'development',
    };

    // Set default labels for metrics
    register.setDefaultLabels(this.defaultLabels);

    // Initialize standard metrics
    this.initializeDefaultMetrics();
  }

  /**
   * Initialize standard metrics that all services should track
   */
  private initializeDefaultMetrics(): void {
    // Request counter
    this.createCounter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code', 'service'],
    });

    // Request duration histogram
    this.createHistogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'route', 'status_code', 'service'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
    });

    // Active sessions gauge
    this.createGauge({
      name: 'active_sessions',
      help: 'Number of currently active user sessions',
    });

    // External service call duration
    this.createHistogram({
      name: 'external_service_duration_seconds',
      help: 'Duration of external service calls in seconds',
      labelNames: ['service', 'operation', 'method', 'status'],
      buckets: [0.05, 0.1, 0.5, 1, 2, 5, 10],
    });

    // External service call counter
    this.createCounter({
      name: 'external_service_requests_total',
      help: 'Total number of HTTP requests made to external services.',
      labelNames: ['service', 'operation', 'method', 'status_code', 'error'],
    });
  }

  /**
   * Create a new counter metric
   */
  createCounter({
    name,
    help,
    labelNames = [],
  }: {
    name: string;
    help: string;
    labelNames?: string[];
  }): Counter<string> {
    // Sanitize the metric name
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;

    if (!this.metrics[fullName]) {
      try {
        this.metrics[fullName] = new Counter({
          name: fullName,
          help,
          labelNames,
        });
      } catch (error) {
        // If metric already exists in global registry, try to get it
        if (error instanceof Error && error.message.includes('already been registered')) {
          // Get the existing metric from the registry
          const existingMetric = (register as any).getSingleMetric(fullName);
          if (existingMetric && existingMetric instanceof Counter) {
            this.metrics[fullName] = existingMetric;
            console.debug(`Reusing existing counter metric: ${fullName}`);
          } else {
            console.warn(`Failed to reuse existing metric ${fullName}, clearing registry and creating new one`);
            // Clear and recreate if needed for development
            (register as any).clear();
            this.metrics[fullName] = new Counter({
              name: fullName,
              help,
              labelNames,
            });
          }
        } else {
          throw error;
        }
      }
    }
    return this.metrics[fullName] as Counter<string>;
  }

  /**
   * Create a new histogram metric
   */
  createHistogram({
    name,
    help,
    labelNames = [],
    buckets = [0.1, 0.5, 1, 2, 5],
  }: {
    name: string;
    help: string;
    labelNames?: string[];
    buckets?: number[];
  }): Histogram<string> {
    // Sanitize the metric name
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;

    if (!this.metrics[fullName]) {
      try {
        this.metrics[fullName] = new Histogram({
          name: fullName,
          help,
          labelNames,
          buckets,
        });
      } catch (error) {
        // If metric already exists in global registry, try to get it
        if (error instanceof Error && error.message.includes('already been registered')) {
          // Get the existing metric from the registry
          // TypeScript doesn't know about getSingleMetric, but it exists at runtime
          const existingMetric = (register as any).getSingleMetric(fullName);
          if (existingMetric && existingMetric instanceof Histogram) {
            this.metrics[fullName] = existingMetric;
            console.debug(`Reusing existing histogram metric: ${fullName}`);
          } else {
            console.warn(`Failed to reuse existing histogram metric ${fullName}`, error);
            // For development, clear and recreate
            (register as any).clear();
            this.metrics[fullName] = new Histogram({
              name: fullName,
              help,
              labelNames,
              buckets,
            });
          }
        } else {
          throw error;
        }
      }
    }
    return this.metrics[fullName] as Histogram<string>;
  }

  /**
   * Create a new gauge metric
   */
  createGauge({
    name,
    help,
    labelNames = [],
  }: {
    name: string;
    help: string;
    labelNames?: string[];
  }): Gauge<string> {
    // Sanitize the metric name
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;

    if (!this.metrics[fullName]) {
      try {
        this.metrics[fullName] = new Gauge({
          name: fullName,
          help,
          labelNames,
        });
      } catch (error) {
        // If metric already exists in global registry, try to get it
        if (error instanceof Error && error.message.includes('already been registered')) {
          // Get the existing metric from the registry
          const existingMetric = (register as any).getSingleMetric(fullName);
          if (existingMetric && existingMetric instanceof Gauge) {
            this.metrics[fullName] = existingMetric;
            console.debug(`Reusing existing gauge metric: ${fullName}`);
          } else {
            console.warn(`Failed to reuse existing gauge metric ${fullName}, clearing registry and creating new one`);
            // For development, clear and recreate
            (register as any).clear();
            this.metrics[fullName] = new Gauge({
              name: fullName,
              help,
              labelNames,
            });
          }
        } else {
          throw error;
        }
      }
    }
    return this.metrics[fullName] as Gauge<string>;
  }

  /**
   * Create a new summary metric
   */
  createSummary({
    name,
    help,
    labelNames = [],
    percentiles = [0.5, 0.9, 0.95, 0.99],
  }: {
    name: string;
    help: string;
    labelNames?: string[];
    percentiles?: number[];
  }): Summary<string> {
    // Sanitize the metric name
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;

    if (!this.metrics[fullName]) {
      try {
        this.metrics[fullName] = new Summary({
          name: fullName,
          help,
          labelNames,
          percentiles,
        });
      } catch (error) {
        // If metric already exists in global registry, try to get it
        if (error instanceof Error && error.message.includes('already been registered')) {
          // Get the existing metric from the registry
          const existingMetric = (register as any).getSingleMetric(fullName);
          if (existingMetric && existingMetric instanceof Summary) {
            this.metrics[fullName] = existingMetric;
            console.debug(`Reusing existing summary metric: ${fullName}`);
          } else {
            console.warn(`Failed to reuse existing summary metric ${fullName}, clearing registry and creating new one`);
            // For development, clear and recreate
            (register as any).clear();
            this.metrics[fullName] = new Summary({
              name: fullName,
              help,
              labelNames,
              percentiles,
            });
          }
        } else {
          throw error;
        }
      }
    }
    return this.metrics[fullName] as Summary<string>;
  }

  /**
   * Increment a counter with the given labels
   */
  incrementCounter(
    name: string,
    labels: Record<string, string | number> = {},
    value: number = 1
  ): void {
    const sanitizedName = this.sanitizeMetricName(name);
    const counter = this.metrics[`${this.prefix}_${sanitizedName}`] as Counter<string>;
    if (counter) {
      counter.inc(labels, value);
    }
  }

  /**
   * Observe a value in a histogram with the given labels
   */
  observeHistogram(
    name: string,
    value: number,
    labels: Record<string, string | number> = {}
  ): void {
    const sanitizedName = this.sanitizeMetricName(name);
    const histogram = this.metrics[`${this.prefix}_${sanitizedName}`] as Histogram<string>;
    if (histogram) {
      histogram.observe(labels, value);
    }
  }

  /**
   * Set a gauge value with the given labels
   */
  setGauge(
    name: string,
    value: number,
    labels: Record<string, string | number> = {}
  ): void {
    const sanitizedName = this.sanitizeMetricName(name);
    const gauge = this.metrics[`${this.prefix}_${sanitizedName}`] as Gauge<string>;
    if (gauge) {
      gauge.set(labels, value);
    }
  }

  /**
   * Observe a value in a summary with the given labels
   */
  observeSummary(
    name: string,
    value: number,
    labels: Record<string, string | number> = {}
  ): void {
    const sanitizedName = this.sanitizeMetricName(name);
    const summary = this.metrics[`${this.prefix}_${sanitizedName}`] as Summary<string>;
    if (summary) {
      summary.observe(labels, value);
    }
  }

  /**
   * Get all metrics in Prometheus format
   */
  async getMetrics(): Promise<string> {
    return register.metrics();
  }

  /**
   * Get an existing counter metric by name
   */
  getCounter(name: string): Counter<string> | undefined {
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;
    return this.metrics[fullName] as Counter<string>;
  }

  /**
   * Get an existing histogram metric by name
   */
  getHistogram(name: string): Histogram<string> | undefined {
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;
    return this.metrics[fullName] as Histogram<string>;
  }

  /**
   * Get an existing gauge metric by name
   */
  getGauge(name: string): Gauge<string> | undefined {
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;
    return this.metrics[fullName] as Gauge<string>;
  }

  /**
   * Get an existing summary metric by name
   */
  getSummary(name: string): Summary<string> | undefined {
    const sanitizedName = this.sanitizeMetricName(name);
    const fullName = `${this.prefix}_${sanitizedName}`;
    return this.metrics[fullName] as Summary<string>;
  }

  /**
   * Reset all metrics (mainly for tests)
   */
  resetMetrics(): void {
    (register as any).resetMetrics();
    this.metrics = {};
    this.initializeDefaultMetrics();
  }
}
