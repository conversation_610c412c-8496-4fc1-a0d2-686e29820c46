# Observability Library

This library provides centralized observability features for polyrepo microservices, integrating:

- **Logging**: Enhanced Winston-based logging with Loki integration
- **Metrics**: Prometheus metrics collection and exposure
- **Tracing**: Distributed tracing with OpenTelemetry and Tempo
- **Profiling**: Continuous profiling with Pyroscope

## Installation

Since this is an internal library, it should be included in the project's workspace. Use it in your NestJS services:

```typescript
import { ObservabilityModule } from '@libs/observability';
```

## Basic Usage

### Configure in AppModule

#### Basic Configuration

```typescript
import { Module } from '@nestjs/common';
import { ObservabilityModule } from '@libs/observability';

@Module({
  imports: [
    ObservabilityModule.forRoot({
      logging: {
        service: 'auth-service',
        defaultContext: 'AuthService',
        enableLoki: process.env.ENABLE_LOKI === 'true',
        lokiHost: process.env.LOKI_HOST || 'http://loki:3100',
        logLevel: process.env.LOG_LEVEL || 'info',
      },
      metrics: {
        prefix: 'auth_service',
        defaultLabels: {
          service: 'auth_service',
          environment: process.env.NODE_ENV || 'development',
        },
      },
      tracing: {
        serviceName: 'auth-service',
        environment: process.env.NODE_ENV || 'development',
        tempoEndpoint: process.env.TEMPO_ENDPOINT || 'http://localhost:4318/v1/traces',
      },
    }),
    // other modules...
  ],
})
export class AppModule {}
```

#### Recommended: Async Configuration with ConfigService (Better Environment Variable Handling)

```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ObservabilityModule } from '@libs/observability';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: process.env.NODE_ENV === 'test' ? '.env.test' :
                  process.env.NODE_ENV === 'development' ? '.env.local' : '.env.docker',
    }),
    ObservabilityModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        logging: {
          service: configService.get<string>('SERVICE_NAME', 'auth-service'),
          defaultContext: 'AuthService',
          enableLoki: configService.get<string>('ENABLE_LOKI') === 'true',
          lokiHost: configService.get<string>('LOKI_HOST', 'http://loki:3100'),
          logLevel: configService.get<string>('LOG_LEVEL', 'info'),
        },
        metrics: {
          prefix: 'auth_service',
          defaultLabels: {
            service: configService.get<string>('SERVICE_NAME', 'auth_service'),
            environment: configService.get<string>('NODE_ENV', 'development'),
          },
        },
        tracing: {
          serviceName: configService.get<string>('SERVICE_NAME', 'auth-service'),
          environment: configService.get<string>('NODE_ENV', 'development'),
          tempoEndpoint: configService.get<string>('TEMPO_ENDPOINT', 'http://localhost:4318/v1/traces'),
        },
      }),
    }),
    // other modules...
  ],
})
export class AppModule {}
```

### Environment Variables

```env
# Basic configuration
SERVICE_NAME=auth-service
NODE_ENV=development

# Logging
ENABLE_LOKI=true
LOKI_HOST=http://loki:3100
LOG_LEVEL=info

# Metrics
ENABLE_METRICS=true

# Tracing
ENABLE_TRACING=true
TEMPO_ENDPOINT=http://localhost:4318/v1/traces
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://localhost:4318/v1/traces
OTEL_TRACES_SAMPLER=traceidratio
OTEL_TRACES_SAMPLER_ARG=1.0

# Profiling
ENABLE_PROFILING=true
PYROSCOPE_SERVER_ADDRESS=http://localhost:4040
```

## Logging Features

### Technical Logging

#### Basic Usage

```typescript
import { Injectable } from '@nestjs/common';
import { ObservabilityLogger, LOGGER_SERVICE } from '@libs/observability';
import { Inject } from '@nestjs/common';

@Injectable()
export class MyService {
  constructor(@Inject(LOGGER_SERVICE) private readonly logger: ObservabilityLogger) {
    // Set the context to the current class name for better log attribution
    this.logger.setContext(MyService.name);
  }

  doSomething() {
    this.logger.log('Operation started');
    try {
      // business logic
      this.logger.debug('Operation details', { key: 'value' });
    } catch (error) {
      this.logger.error('Operation failed', error.stack);
      throw error;
    }
  }
}
```

#### Recommended: Using Logger Factory (Better Context Handling)

```typescript
import { Injectable } from '@nestjs/common';
import { Inject } from '@nestjs/common';

@Injectable()
export class MyService {
  // Inject the logger factory instead of the logger directly
  constructor(@Inject('LOGGER_FACTORY') private readonly loggerFactory: any) {
    // Create a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger(MyService.name);
  }

  // Declare the logger as a class property
  private readonly logger;

  doSomething() {
    this.logger.log('Operation started');
    try {
      // business logic
      this.logger.debug('Operation details', { key: 'value' });
    } catch (error) {
      this.logger.error('Operation failed', error.stack);
      throw error;
    }
  }
}
```

### Business Event Logging

```typescript
import { Injectable } from '@nestjs/common';
import { BusinessLogger } from '@libs/observability';

@Injectable()
export class AuthService {
  constructor(private readonly businessLogger: BusinessLogger) {}

  async login(username: string, password: string) {
    try {
      // Login logic
      const userId = '123'; // from login result

      this.businessLogger.logAuthEvent('login', 'success', userId, {
        method: 'password',
      });

      return { success: true };
    } catch (error) {
      this.businessLogger.logAuthEvent('login', 'failure', null, {
        reason: error.message,
      });
      throw error;
    }
  }
}
```

## Metrics Features

### Setup in Controller

```typescript
import { Controller, Get } from '@nestjs/common';
import { MetricsService } from '@libs/observability';

@Controller('metrics')
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  @Get()
  async getMetrics(): Promise<string> {
    return this.metricsService.getMetrics();
  }
}
```

### Custom Metrics

```typescript
import { Injectable } from '@nestjs/common';
import { MetricsService } from '@libs/observability';

@Injectable()
export class AuthService {
  private userLoginCounter;

  constructor(private readonly metricsService: MetricsService) {
    this.userLoginCounter = this.metricsService.createCounter({
      name: 'user_login_total',
      help: 'Total number of user logins',
      labelNames: ['status', 'provider'],
    });
  }

  async login(username: string, password: string) {
    try {
      // Login logic
      this.metricsService.incrementCounter(
        'user_login_total',
        { status: 'success', provider: 'password' }
      );
      return { success: true };
    } catch (error) {
      this.metricsService.incrementCounter(
        'user_login_total',
        { status: 'failure', provider: 'password' }
      );
      throw error;
    }
  }
}
```

## Tracing Features

### Service Integration

```typescript
import { Injectable } from '@nestjs/common';
import { TracingService } from '@libs/observability';

@Injectable()
export class ExternalApiService {
  constructor(private readonly tracingService: TracingService) {}

  async fetchUserData(userId: string) {
    return this.tracingService.traceAsyncFunction(
      'fetchUserData',
      async (span) => {
        span.setAttribute('userId', userId);

        try {
          // Fetch logic
          const result = await this.apiClient.get(`/users/${userId}`);
          return result.data;
        } catch (error) {
          // Error is automatically handled by traceAsyncFunction
          throw error;
        }
      }
    );
  }
}
```

## Integration with Monitoring Stack

This library works with the monitoring infrastructure defined in `infrastructure/monitoring`. See the README in that directory for setup instructions.

## Recommended Project Structure

### Service-Level Observability Structure

Each service should follow this structure for observability:

```
services/my-service/
├── src/
│   ├── observability/
│   │   ├── observability.module.ts      # Main module that configures observability
│   │   ├── business-logger.service.ts   # Service-specific business event logging
│   │   └── test.controller.ts           # Test endpoints for observability (dev/test only)
│   ├── metrics/
│   │   ├── metrics.module.ts            # Module for exposing Prometheus metrics
│   │   └── metrics.controller.ts        # Controller that exposes metrics endpoint
│   └── health/
│       ├── health.module.ts             # Module for health checks
│       └── health.controller.ts         # Controller with health check endpoints
```

### Conditional Test Endpoints

Test endpoints should only be available in development and testing environments:

```typescript
@Module({
  imports: [
    // ... other imports
  ],
  controllers: process.env.NODE_ENV === 'production' ? [] : [ObservabilityTestController],
  providers: [BusinessLoggerService],
  exports: [BusinessLoggerService, BaseObservabilityModule],
})
export class ObservabilityModule {}
```

## Best Practices

### Logging Best Practices

1. **Use Appropriate Log Levels**
   - `error`: For errors that require immediate attention
   - `warn`: For potential issues that don't interrupt operation
   - `info`: For important operational events (default level in production)
   - `debug`: For detailed information useful during development
   - `verbose`: For extremely detailed tracing information

2. **Structured Logging**
   - Always use structured logging for machine-readable logs
   - Include relevant metadata as object properties instead of string concatenation
   - Example: `logger.info('User action', { userId, action, resource })` instead of `logger.info(`User ${userId} performed ${action} on ${resource}`)`

3. **Context Management**
   - Set the context to the class name in each service/controller
   - Use the logger factory pattern for consistent context handling
   - Don't rely on string literals for context names; use `ClassName.name`

4. **Environment-Specific Configuration**
   - Use different log levels for different environments:
     - Development: `debug` or `verbose`
     - Testing: `info`
     - Production: `info` or `warn`
   - Configure appropriate transports per environment (console for dev, Loki for prod)

### Business Logging Best Practices

1. **Event Taxonomy**
   - Define a consistent taxonomy for business events
   - Use verb-noun format for event types (e.g., `user_login`, `order_created`)
   - Include standardized status values (`success`, `failure`)

2. **User Attribution**
   - Always include user identifiers in business logs when available
   - Anonymize sensitive data while maintaining traceability

3. **Correlation**
   - Include correlation IDs across related logs
   - Link business events to technical logs for full traceability

4. **Service-Specific Business Loggers**
   - Create a service-specific business logger (e.g., `AuthBusinessLogger`, `UserBusinessLogger`)
   - Define domain-specific methods for common business events
   - Use consistent method naming across services

### Metrics Best Practices

1. **Consistent Endpoint Structure**
   - Expose metrics at a standard `/metrics` endpoint
   - Use a dedicated metrics controller for Prometheus metrics
   - Return metrics in plain text format with appropriate content type

2. **Naming Conventions**
   - Use snake_case for metric names
   - Include service name as prefix (e.g., `auth_login_attempts_total`)
   - Use standard suffixes for metric types:
     - Counters: `_total` (e.g., `http_requests_total`)
     - Gauges: no suffix (e.g., `active_connections`)
     - Histograms: `_seconds`, `_bytes`, etc. (e.g., `http_request_duration_seconds`)
   - **Important**: Prometheus metric names must follow these rules:
     - Must match regex `[a-zA-Z_:][a-zA-Z0-9_:]*`
     - Cannot contain spaces, dashes, or special characters
     - The library automatically sanitizes metric names by replacing invalid characters with underscores

3. **Label Usage**
   - Use labels for dimensions that have low cardinality
   - Include standard labels like `service`, `environment`, `status`
   - Avoid high-cardinality labels like user IDs or request IDs

4. **Health Checks**
   - Include metrics in health check endpoints
   - Expose service health metrics for monitoring systems

### Tracing Best Practices

1. **Span Naming**
   - Use consistent span naming conventions
   - Include service name and operation (e.g., `auth-service.login`)
   - Keep span names stable for better visualization

2. **Attribute Management**
   - Add relevant attributes to spans for filtering and analysis
   - Include standard attributes like `service.name`, `http.method`, `http.status_code`
   - Avoid sensitive information in span attributes

3. **Error Handling**
   - Record exceptions in spans for error tracking
   - Use the `traceAsyncFunction` helper for automatic error handling
   - Add error-specific attributes for better debugging

## Troubleshooting

For detailed troubleshooting information, refer to `docs/troubleshooting/observability_integration.md`. Common issues include:

### Debug Logs Not Appearing

**Problem**: Debug logs don't appear in the console even when `LOG_LEVEL=debug` is set.

**Solution**: Ensure you're using `forRootAsync` with `ConfigService` to properly load environment variables before logger initialization.

### Incorrect Log Context

**Problem**: Logs show a default context (e.g., `[AuthService]`) instead of the specific class name (e.g., `[UserController]`).

**Solution**: Use the logger factory pattern instead of directly injecting the logger:

```typescript
constructor(@Inject('LOGGER_FACTORY') private readonly loggerFactory: any) {
  this.logger = this.loggerFactory.createLogger(ClassName.name);
}
```

### Connection to Loki/Grafana

**Problem**: Logs aren't appearing in Grafana/Loki dashboard.

**Solution**:
- Verify the Loki URL is correct in your environment (`LOKI_HOST`)
- Ensure `ENABLE_LOKI=true` is set
- Check that Docker containers for Loki and Grafana are running
- Verify network connectivity between your service and the monitoring stack

### Invalid Metric Name Error

**Problem**: Error when starting the application: `ERROR [ExceptionHandler] Invalid metric name`

**Root Cause**:
Prometheus metrics have strict naming conventions that must be followed:
- Must match regex `[a-zA-Z_:][a-zA-Z0-9_:]*`
- Cannot contain spaces, dashes, or special characters

**Solution**:
- Use only letters, numbers, and underscores in metric names and prefixes
- Replace dashes with underscores in service names used as prefixes
- For example, use `auth_service` instead of `auth-service`
- The library automatically sanitizes metric names, but it's best to follow the conventions

## Continuous Profiling with Pyroscope

### Overview

Pyroscope provides continuous profiling for your applications, helping identify performance bottlenecks and optimize resource usage. The observability library includes PyroscopeService for seamless integration.

### Configuration

#### Environment Variables

```env
# Profiling
ENABLE_PROFILING=true
PYROSCOPE_SERVER_ADDRESS=http://localhost:4040
APP_VERSION=1.0.0
```

#### Service Configuration

```typescript
import { PyroscopeService, PyroscopeConfig } from '@libs/observability';

const pyroscopeConfig: PyroscopeConfig = {
  enabled: process.env.ENABLE_PROFILING === 'true',
  serverAddress: process.env.PYROSCOPE_SERVER_ADDRESS || 'http://localhost:4040',
  applicationName: 'auth-service',
  tags: {
    environment: process.env.NODE_ENV || 'development',
    version: process.env.APP_VERSION || '1.0.0',
  },
  sampleRate: 100, // Hz
};

const pyroscopeService = new PyroscopeService(pyroscopeConfig);
```

### Usage

#### Profile Specific Operations

```typescript
@Injectable()
export class PaymentService {
  constructor(private pyroscopeService: PyroscopeService) {}

  async processPayment(userId: string, amount: number) {
    return this.pyroscopeService.profileOperation(
      'process-payment',
      async () => {
        // Your business logic here
        return await this.executePayment(userId, amount);
      },
      {
        'user.type': 'premium',
        'payment.method': 'credit-card'
      }
    );
  }
}
```

#### Create Profiled Wrappers

```typescript
// Create a profiled wrapper for frequently called functions
const profiledCalculatePrice = this.pyroscopeService.createProfiledWrapper(
  'calculate-price',
  this.calculatePrice.bind(this),
  { operation: 'pricing' }
);

// Use the wrapper normally
const price = await profiledCalculatePrice(productId, userId);
```

#### Add Custom Labels

```typescript
// Add labels for better profiling context
this.pyroscopeService.addLabels({
  'request.type': 'api',
  'user.tier': 'enterprise'
});
```

### Integration with Tracing

Pyroscope works seamlessly with distributed tracing. Use both together for comprehensive performance analysis:

```typescript
async processComplexOperation(data: any) {
  return this.tracingService.traceAsyncFunction(
    'complex-operation',
    async (span) => {
      return this.pyroscopeService.profileOperation(
        'complex-operation-profiling',
        async () => {
          // Your intensive operation here
          span.setAttribute('operation.complexity', 'high');
          return await this.performIntensiveComputation(data);
        }
      );
    }
  );
}
```

### Viewing Profiling Data

1. **Access Pyroscope UI**: Navigate to http://localhost:4040
2. **Select Application**: Choose your service from the dropdown
3. **Choose Profile Type**: 
   - CPU profiling (default)
   - Memory heap profiling
4. **Analyze**: Use the flame graph to identify bottlenecks
5. **Compare**: Compare different time periods or versions

### Best Practices

#### 1. Meaningful Operation Names
```typescript
// Good: Descriptive, hierarchical naming
'user-service.database.user-lookup'
'auth-service.token.validation'
'payment-service.external-api.stripe-charge'

// Avoid: Generic names
'operation'
'function'
```

#### 2. Strategic Labeling
```typescript
// Add business context labels
{
  'database.query_type': 'complex_join',
  'cache.hit': 'false',
  'external_service': 'payment_gateway'
}
```

#### 3. Profile Critical Paths
Focus profiling on:
- High-traffic endpoints
- Performance-critical business logic
- Resource-intensive operations
- External service integrations

#### 4. Environment-Specific Configuration
```typescript
// Adjust sample rate based on environment
const sampleRate = process.env.NODE_ENV === 'production' ? 10 : 100;
```

### Troubleshooting

#### Profiling Not Working

**Check:**
- `ENABLE_PROFILING=true` is set
- Pyroscope server is running: `curl http://localhost:4040/api/apps`
- Application name is configured correctly
- Network connectivity to Pyroscope server

#### High Overhead

**Solutions:**
- Reduce sample rate in production
- Profile only critical operations
- Use profiled wrappers sparingly for high-frequency functions

#### Missing Data

**Verify:**
- Application is generating sufficient load
- Profiling duration is adequate (minimum 10-30 seconds)
- Labels are consistent across operations
