import { ObservabilityLogger } from '../src/logging/logger.service';

jest.mock('winston', () => {
  const mockFormat = {
    combine: jest.fn().mockReturnThis(),
    timestamp: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    printf: jest.fn((cb) => cb),
    colorize: jest.fn().mockReturnThis(),
  };

  const mockTransports = {
    Console: jest.fn(),
    DailyRotateFile: jest.fn(),
  };

  const mockLogger = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  };

  return {
    format: mockFormat,
    transports: mockTransports,
    createLogger: jest.fn().mockReturnValue(mockLogger),
  };
});

jest.mock('winston-loki', () => {
  return jest.fn().mockImplementation(() => ({}));
});

describe('ObservabilityLogger', () => {
  let logger: ObservabilityLogger;
  
  beforeEach(() => {
    jest.clearAllMocks();
    logger = new ObservabilityLogger({
      service: 'test-service',
      enableLoki: false,
    });
  });

  it('should create an instance with default config', () => {
    expect(logger).toBeDefined();
  });

  it('should allow setting and getting context', () => {
    logger.setContext('TestContext');
    expect(logger.getContext()).toBe('TestContext');
  });

  it('should log messages with the correct level', () => {
    // Access the underlying Winston logger for spying
    const winstonLogger = (logger as any).logger;
    
    logger.log('info message');
    expect(winstonLogger.info).toHaveBeenCalled();
    
    logger.error('error message');
    expect(winstonLogger.error).toHaveBeenCalled();
    
    logger.warn('warning message');
    expect(winstonLogger.warn).toHaveBeenCalled();
    
    logger.debug('debug message');
    expect(winstonLogger.debug).toHaveBeenCalled();
    
    logger.verbose('verbose message');
    expect(winstonLogger.verbose).toHaveBeenCalled();
  });

  it('should log object messages properly', () => {
    const winstonLogger = (logger as any).logger;
    const objMessage = { key: 'value', nested: { data: true } };
    
    logger.log(objMessage);
    expect(winstonLogger.info).toHaveBeenCalledWith('', expect.objectContaining(objMessage));
  });

  it('should include context in log messages', () => {
    const winstonLogger = (logger as any).logger;
    const testContext = 'TestContext';
    
    logger.setContext(testContext);
    logger.log('test message');
    
    expect(winstonLogger.info).toHaveBeenCalledWith(
      'test message', 
      expect.objectContaining({ context: testContext })
    );
  });
});
