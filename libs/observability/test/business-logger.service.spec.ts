import { BusinessLogger } from '../src/logging/business-logger.service';
import { ObservabilityLogger } from '../src/logging/logger.service';

// Mock the ObservabilityLogger
jest.mock('../src/logging/logger.service');

describe('BusinessLogger', () => {
  let businessLogger: BusinessLogger;
  let mockObservabilityLogger: jest.Mocked<ObservabilityLogger>;
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockObservabilityLogger = new ObservabilityLogger() as jest.Mocked<ObservabilityLogger>;
    businessLogger = new BusinessLogger(mockObservabilityLogger);
  });

  it('should create an instance', () => {
    expect(businessLogger).toBeDefined();
  });

  it('should set context to BusinessEvents', () => {
    expect(mockObservabilityLogger.setContext).toHaveBeenCalledWith('BusinessEvents');
  });

  it('should log auth events', () => {
    // Mock Date.now for consistent timestamps
    const mockDate = new Date('2025-05-21T00:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

    businessLogger.logAuthEvent('login', 'success', 'user123', { ip: '127.0.0.1' });
    
    expect(mockObservabilityLogger.log).toHaveBeenCalledWith({
      eventType: 'auth_event',
      action: 'login',
      status: 'success',
      userId: 'user123',
      timestamp: mockDate.toISOString(),
      ip: '127.0.0.1',
    });
  });

  it('should log user activity events', () => {
    // Mock Date.now for consistent timestamps
    const mockDate = new Date('2025-05-21T00:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

    businessLogger.logUserActivity('user123', 'profile_update', { field: 'email' });
    
    expect(mockObservabilityLogger.log).toHaveBeenCalledWith({
      eventType: 'user_activity',
      userId: 'user123',
      action: 'profile_update',
      timestamp: mockDate.toISOString(),
      field: 'email',
    });
  });

  it('should log generic business events', () => {
    // Mock Date.now for consistent timestamps
    const mockDate = new Date('2025-05-21T00:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

    businessLogger.logBusinessEvent('payment_processed', { amount: 100, currency: 'USD' });
    
    expect(mockObservabilityLogger.log).toHaveBeenCalledWith({
      eventType: 'business_event',
      businessEventType: 'payment_processed',
      timestamp: mockDate.toISOString(),
      amount: 100,
      currency: 'USD',
    });
  });

  it('should log API requests', () => {
    // Mock Date.now for consistent timestamps
    const mockDate = new Date('2025-05-21T00:00:00Z');
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);

    businessLogger.logApiRequest(
      'GET', 
      '/api/users', 
      200, 
      150, 
      'user123', 
      { query: 'filter=active' }
    );
    
    expect(mockObservabilityLogger.log).toHaveBeenCalledWith({
      eventType: 'api_request',
      method: 'GET',
      path: '/api/users',
      statusCode: 200,
      durationMs: 150,
      userId: 'user123',
      timestamp: mockDate.toISOString(),
      query: 'filter=active',
    });
  });
});
