import { MetricsService } from '../src/metrics/metrics.service';

// Mock prom-client
jest.mock('prom-client', () => {
  const mockCounter = jest.fn().mockImplementation(() => ({
    inc: jest.fn(),
  }));
  
  const mockHistogram = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
  }));
  
  const mockGauge = jest.fn().mockImplementation(() => ({
    set: jest.fn(),
  }));
  
  const mockSummary = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
  }));

  const mockRegister = {
    metrics: jest.fn().mockResolvedValue('metric1 10\nmetric2 20'),
    resetMetrics: jest.fn(),
    setDefaultLabels: jest.fn(),
  };

  return {
    register: mockRegister,
    Counter: mockCounter,
    Histogram: mockHistogram,
    Gauge: mockGauge,
    Summary: mockSummary,
  };
});

describe('MetricsService', () => {
  let metricsService: MetricsService;
  
  beforeEach(() => {
    jest.clearAllMocks();
    metricsService = new MetricsService({
      prefix: 'test',
      defaultLabels: {
        service: 'test-service',
        environment: 'test',
      }
    });
  });

  it('should create a new instance', () => {
    expect(metricsService).toBeDefined();
  });

  it('should initialize default metrics', () => {
    // This is a bit tricky to test without exposing internals
    // We're just checking if the constructor doesn't throw
    expect(() => new MetricsService()).not.toThrow();
  });

  it('should create a counter metric', () => {
    const counter = metricsService.createCounter({
      name: 'test_counter',
      help: 'Test counter help',
    });
    
    expect(counter).toBeDefined();
  });

  it('should create a histogram metric', () => {
    const histogram = metricsService.createHistogram({
      name: 'test_histogram',
      help: 'Test histogram help',
    });
    
    expect(histogram).toBeDefined();
  });

  it('should create a gauge metric', () => {
    const gauge = metricsService.createGauge({
      name: 'test_gauge',
      help: 'Test gauge help',
    });
    
    expect(gauge).toBeDefined();
  });

  it('should create a summary metric', () => {
    const summary = metricsService.createSummary({
      name: 'test_summary',
      help: 'Test summary help',
    });
    
    expect(summary).toBeDefined();
  });

  it('should increment a counter', () => {
    const counter = metricsService.createCounter({
      name: 'test_counter',
      help: 'Test counter help',
    });
    
    // Access private field for testing
    const incSpy = jest.spyOn(counter, 'inc');
    
    metricsService.incrementCounter('test_counter', { label: 'value' });
    
    expect(incSpy).toHaveBeenCalledWith({ label: 'value' }, 1);
  });

  it('should observe a histogram', () => {
    const histogram = metricsService.createHistogram({
      name: 'test_histogram',
      help: 'Test histogram help',
    });
    
    // Access private field for testing
    const observeSpy = jest.spyOn(histogram, 'observe');
    
    metricsService.observeHistogram('test_histogram', 10, { label: 'value' });
    
    expect(observeSpy).toHaveBeenCalledWith({ label: 'value' }, 10);
  });

  it('should get metrics in Prometheus format', async () => {
    const metrics = await metricsService.getMetrics();
    
    expect(metrics).toBe('metric1 10\nmetric2 20');
  });

  it('should reset all metrics', () => {
    const { register } = jest.requireMock('prom-client');
    
    metricsService.resetMetrics();
    
    expect(register.resetMetrics).toHaveBeenCalled();
  });
});
