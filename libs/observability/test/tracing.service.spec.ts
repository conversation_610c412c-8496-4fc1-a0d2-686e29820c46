import { TracingService } from '../src/tracing/tracing.service';

// Mock OpenTelemetry
jest.mock('@opentelemetry/sdk-node', () => {
  return {
    NodeSDK: jest.fn().mockImplementation(() => ({
      start: jest.fn(),
      shutdown: jest.fn().mockResolvedValue(undefined),
    })),
  };
});

jest.mock('@opentelemetry/api', () => {
  const mockSpan = {
    setAttribute: jest.fn(),
    setStatus: jest.fn(),
    recordException: jest.fn(),
    end: jest.fn(),
    addEvent: jest.fn(),
  };
  
  const mockTracer = {
    startSpan: jest.fn().mockReturnValue(mockSpan),
  };
  
  return {
    trace: {
      getTracer: jest.fn().mockReturnValue(mockTracer),
      getSpan: jest.fn().mockReturnValue(mockSpan),
      setSpan: jest.fn().mockReturnThis(),
    },
    context: {
      active: jest.fn(),
      with: jest.fn().mockImplementation((ctx, fn) => fn()),
    },
    SpanStatusCode: {
      ERROR: 'ERROR',
      OK: 'OK',
    },
  };
});

jest.mock('@opentelemetry/auto-instrumentations-node', () => {
  return {
    getNodeAutoInstrumentations: jest.fn().mockReturnValue([]),
  };
});

jest.mock('@opentelemetry/exporter-trace-otlp-http', () => {
  return {
    OTLPTraceExporter: jest.fn().mockImplementation(() => ({})),
  };
});

jest.mock('@opentelemetry/resources', () => {
  return {
    Resource: jest.fn().mockImplementation(() => ({})),
  };
});

jest.mock('@opentelemetry/semantic-conventions', () => {
  return {
    SemanticResourceAttributes: {
      SERVICE_NAME: 'service.name',
      DEPLOYMENT_ENVIRONMENT: 'deployment.environment',
    },
  };
});

jest.mock('@opentelemetry/sdk-trace-base', () => {
  return {
    BatchSpanProcessor: jest.fn().mockImplementation(() => ({})),
  };
});

// Mock process.env and process.on
const originalEnv = process.env;
const originalProcessOn = process.on;

describe('TracingService', () => {
  let tracingService: TracingService;
  
  beforeEach(() => {
    process.env = { ...originalEnv, ENABLE_TRACING: 'true' };
    process.on = jest.fn() as any;
    jest.clearAllMocks();
    
    tracingService = new TracingService({
      serviceName: 'test-service',
      environment: 'test',
      tempoEndpoint: 'http://test:4318/v1/traces',
    });
  });
  
  afterEach(() => {
    process.env = originalEnv;
    process.on = originalProcessOn;
  });
  
  it('should create an instance', () => {
    expect(tracingService).toBeDefined();
  });
  
  it('should initialize tracing when onModuleInit is called', () => {
    const NodeSDK = require('@opentelemetry/sdk-node').NodeSDK;
    
    tracingService.onModuleInit();
    
    expect(NodeSDK).toHaveBeenCalled();
    expect(NodeSDK.mock.instances[0].start).toHaveBeenCalled();
  });
  
  it('should shut down tracing when onModuleDestroy is called', async () => {
    const NodeSDK = require('@opentelemetry/sdk-node').NodeSDK;
    
    tracingService.onModuleInit();
    await tracingService.onModuleDestroy();
    
    expect(NodeSDK.mock.instances[0].shutdown).toHaveBeenCalled();
  });
  
  it('should trace async functions', async () => {
    const { trace } = require('@opentelemetry/api');
    const mockSpan = trace.getTracer().startSpan();
    const mockFn = jest.fn().mockResolvedValue('result');
    
    tracingService.onModuleInit();
    const result = await tracingService.traceAsyncFunction('test-span', mockFn);
    
    expect(trace.getTracer).toHaveBeenCalledWith('test-service');
    expect(trace.getTracer().startSpan).toHaveBeenCalledWith('test-span');
    expect(mockFn).toHaveBeenCalledWith(mockSpan);
    expect(mockSpan.end).toHaveBeenCalled();
    expect(result).toBe('result');
  });
  
  it('should handle errors in traced functions', async () => {
    const { trace, SpanStatusCode } = require('@opentelemetry/api');
    const mockSpan = trace.getTracer().startSpan();
    const mockError = new Error('test error');
    const mockFn = jest.fn().mockRejectedValue(mockError);
    
    tracingService.onModuleInit();
    
    await expect(tracingService.traceAsyncFunction('test-span', mockFn))
      .rejects.toThrow(mockError);
    
    expect(mockSpan.setStatus).toHaveBeenCalledWith({
      code: SpanStatusCode.ERROR,
      message: 'test error',
    });
    expect(mockSpan.recordException).toHaveBeenCalledWith(mockError);
    expect(mockSpan.end).toHaveBeenCalled();
  });
  
  it('should add attributes to current span', () => {
    const { trace } = require('@opentelemetry/api');
    const mockSpan = trace.getSpan();
    
    tracingService.onModuleInit();
    tracingService.addAttribute('test-key', 'test-value');
    
    expect(trace.getSpan).toHaveBeenCalled();
    expect(mockSpan.setAttribute).toHaveBeenCalledWith('test-key', 'test-value');
  });
  
  it('should add events to current span', () => {
    const { trace } = require('@opentelemetry/api');
    const mockSpan = trace.getSpan();
    
    tracingService.onModuleInit();
    tracingService.addEvent('test-event', { key: 'value' });
    
    expect(trace.getSpan).toHaveBeenCalled();
    expect(mockSpan.addEvent).toHaveBeenCalledWith('test-event', { key: 'value' });
  });
});
