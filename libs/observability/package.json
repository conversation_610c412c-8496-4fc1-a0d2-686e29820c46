{"name": "@libs/observability", "version": "0.1.0", "description": "Observability utilities for polyrepo microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rimraf dist tsconfig.tsbuildinfo && tsc", "test": "jest", "lint": "eslint \"src/**/*.ts\" --fix"}, "dependencies": {"@nestjs/common": "^10.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.59.0", "@opentelemetry/exporter-trace-otlp-http": "^0.202.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-node": "^0.201.1", "@opentelemetry/semantic-conventions": "^1.33.1", "@pyroscope/nodejs": "^0.4.5", "prom-client": "^15.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1", "winston-loki": "^6.1.3"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}, "devDependencies": {"@types/jest": "^29.5.7", "eslint": "^8.56.0", "jest": "^29.7.0", "rimraf": "^4.4.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "directories": {"test": "test"}, "keywords": [], "author": "", "license": "ISC"}