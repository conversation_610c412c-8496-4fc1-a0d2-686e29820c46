# @libs/resilience

A production-ready circuit breaker library built on Opossum with comprehensive observability, event-driven state management, and deep integration with HTTP, messaging, and error handling systems.

## Table of Contents

- [Overview](#overview)
- [Core Features](#core-features)
- [Architecture](#architecture)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [Circuit Breaker States](#circuit-breaker-states)
- [Cross-Library Integration](#cross-library-integration)
- [Monitoring & Observability](#monitoring--observability)
- [Best Practices](#best-practices)
- [API Reference](#api-reference)
- [Examples](#examples)
- [Troubleshooting](#troubleshooting)

## Overview

The `@libs/resilience` library implements the **Circuit Breaker pattern** using the battle-tested [Opossum](https://github.com/nodeshift/opossum) library, providing automatic failure detection and recovery for external service calls.

**Key Benefits:**
- **Automatic Failure Detection**: Monitors error rates and response times
- **Self-Healing**: Automatic state transitions with configurable recovery periods
- **Event-Driven**: Publishes circuit breaker state changes via `@libs/messaging`
- **Full Observability**: Integrated logging, metrics, and distributed tracing
- **HTTP Integration**: Automatic protection for all HTTP service calls
- **Type Safety**: Complete TypeScript support with compile-time validation

**Production Impact**: Protects against cascading failures, reduces resource consumption during outages, and provides automatic recovery with comprehensive monitoring.

## Core Features

### 🛡️ **Circuit Breaker Protection**
- **Three States**: CLOSED → OPEN → HALF_OPEN → CLOSED transitions
- **Failure Detection**: Configurable error rate thresholds (default: 50%)
- **Timeout Protection**: Request timeout handling (default: 10 seconds)
- **Recovery Logic**: Automatic retry attempts after cooldown periods

### 📊 **Observability Integration**
- **State Change Events**: Published to `@libs/messaging` for system-wide awareness
- **Comprehensive Metrics**: Success/failure rates, response times, state duration
- **Structured Logging**: All operations logged with correlation IDs
- **Health Monitoring**: Circuit health status and performance tracking

### 🔧 **Service Integration**
- **HTTP Client Protection**: Automatic circuit breaker for HTTP service calls
- **Service-Specific Circuits**: Separate circuit breakers per external service
- **Named Circuits**: Easy identification and monitoring
- **Fallback Support**: Custom fallback functions for circuit open states

### ⚙️ **Advanced Configuration**
- **Per-Service Settings**: Service-specific timeout and threshold configuration
- **Rolling Windows**: Statistical windows for failure rate calculation
- **Volume Thresholds**: Minimum request counts before circuit opening
- **Bulkhead Patterns**: Resource isolation through separate circuits

## Architecture

### Module Structure

```
libs/resilience/src/
├── circuit-breaker/
│   ├── circuit-breaker.service.ts    # Main service with circuit management
│   ├── circuit-breaker.module.ts     # NestJS dynamic module
│   ├── circuit.ts                    # Circuit wrapper with event integration
│   └── types.ts                      # TypeScript definitions and options
└── index.ts                          # Library exports
```

### Integration Architecture

```mermaid
graph TB
    A[HTTP Client] --> B[CircuitBreakerService]
    B --> C[Opossum Circuit]
    C --> D[External Service]
    
    E[Circuit Events] --> F[Event Publisher]
    F --> G[Redis Streams]
    
    H[Observability] --> I[Metrics Collection]
    H --> J[Structured Logging]
    
    B --> K[Circuit Registry]
    K --> L[Named Circuits]
    L --> M[Service-Specific Config]
```

## Quick Start

### 1. Installation

```bash
# Already included in polyrepo workspace
yarn install
```

### 2. Basic Module Setup

```typescript
// app.module.ts
import { CircuitBreakerModule } from '@libs/resilience';

@Module({
  imports: [
    CircuitBreakerModule.register(), // Uses default configuration
  ],
})
export class AppModule {}
```

### 3. Service Integration

```typescript
// users.service.ts
import { CircuitBreakerService, Circuit } from '@libs/resilience';

@Injectable()
export class UsersService {
  private userServiceCircuit: Circuit;

  constructor(
    private readonly circuitBreakerService: CircuitBreakerService
  ) {
    // Create a circuit breaker for user service calls
    this.userServiceCircuit = this.circuitBreakerService.getCircuitBreaker('user-service', {
      timeout: 5000,           // 5 second timeout
      errorThresholdPercentage: 30, // Open circuit at 30% error rate
      resetTimeout: 20000,     // Try again after 20 seconds
      name: 'UserServiceClient'
    });
  }

  async getUserFromExternalService(userId: string): Promise<User> {
    return this.userServiceCircuit.execute(async () => {
      // This call is protected by the circuit breaker
      const response = await this.httpClient.get(`/users/${userId}`, {
        serviceName: 'user-service'
      });
      return response.data;
    });
  }
}
```

### 4. Automatic HTTP Protection

```typescript
// HTTP calls automatically get circuit breaker protection
@Injectable()
export class PaymentService extends BaseServiceClient {
  async processPayment(paymentData: PaymentDto): Promise<PaymentResult> {
    // Circuit breaker protection is automatic via BaseServiceClient
    const response = await this.post('/payments', paymentData, {
      serviceName: 'payment-service',  // This creates a dedicated circuit
      operationName: 'process-payment',
      timeout: 15000
    });
    return response.data;
  }
}
```

## Configuration

### Environment Variables

```bash
# Default circuit breaker configuration
CIRCUIT_BREAKER_TIMEOUT=10000
CIRCUIT_BREAKER_ERROR_THRESHOLD=50
CIRCUIT_BREAKER_RESET_TIMEOUT=30000
CIRCUIT_BREAKER_VOLUME_THRESHOLD=10
```

### Circuit Breaker Options

```typescript
interface OpossumCircuitBreakerOptions {
  timeout?: number;                    // Request timeout in ms (default: 10000)
  errorThresholdPercentage?: number;   // Error rate to open circuit (default: 50)
  resetTimeout?: number;               // Recovery timeout in ms (default: 30000)
  capacity?: number;                   // Max concurrent requests (default: 10)
  rollingCountTimeout?: number;        // Statistical window in ms (default: 10000)
  rollingCountBuckets?: number;        // Window buckets (default: 10)
  volumeThreshold?: number;            // Min requests before opening (default: 10)
  name?: string;                       // Circuit identifier
  group?: string;                      // Circuit grouping
  fallback?: Function;                 // Custom fallback function
  allowWarmUp?: boolean;               // Manual control
}
```

### Service-Specific Configuration

```typescript
// Different configurations for different services
const serviceConfigs = {
  'payment-service': {
    timeout: 15000,           // Payment requires longer timeout
    errorThresholdPercentage: 20, // More sensitive to failures
    resetTimeout: 60000,      // Longer recovery time
  },
  'notification-service': {
    timeout: 5000,            // Notifications should be fast
    errorThresholdPercentage: 60, // More tolerant of failures
    resetTimeout: 15000,      // Quick recovery
  },
  'analytics-service': {
    timeout: 30000,           // Analytics can take longer
    errorThresholdPercentage: 80, // Very tolerant
    resetTimeout: 10000,      // Quick retry
  }
};
```

## Circuit Breaker States

### State Transitions

```mermaid
stateDiagram-v2
    [*] --> CLOSED
    CLOSED --> OPEN : Error threshold exceeded
    OPEN --> HALF_OPEN : Reset timeout elapsed
    HALF_OPEN --> CLOSED : Success
    HALF_OPEN --> OPEN : Failure
```

### State Descriptions

#### **CLOSED State** (Normal Operation)
- **Behavior**: All requests pass through to the service
- **Monitoring**: Tracks success/failure rates
- **Transition**: Opens when error threshold is exceeded

#### **OPEN State** (Circuit Open)
- **Behavior**: All requests are immediately rejected
- **Fallback**: Returns fallback response or throws circuit open error
- **Transition**: Moves to HALF_OPEN after reset timeout

#### **HALF_OPEN State** (Testing Recovery)
- **Behavior**: Limited requests allowed through
- **Testing**: Evaluates if service has recovered
- **Transition**: CLOSED if successful, OPEN if failures continue

### State Events

```typescript
// Circuit breaker events automatically published
const events = {
  'open': {
    type: 'circuit-breaker.state-changed',
    data: {
      serviceName: 'payment-service',
      previousState: 'CLOSED',
      newState: 'OPEN',
      errorRate: 0.55,
      requestCount: 100,
      failureCount: 55
    }
  },
  'close': {
    type: 'circuit-breaker.state-changed',
    data: {
      serviceName: 'payment-service',
      previousState: 'HALF_OPEN',
      newState: 'CLOSED',
      errorRate: 0.1
    }
  }
};
```

## Cross-Library Integration

### HTTP Client Integration

```typescript
// Automatic circuit breaker protection in HTTP requests
import { HttpClientService } from '@libs/http';

@Injectable()
export class UserServiceClient extends BaseServiceClient {
  async getUser(id: string): Promise<User> {
    // Circuit breaker protection is automatic
    const response = await this.get(`/users/${id}`, {
      serviceName: 'user-service',     // Creates 'user-service' circuit
      operationName: 'get-user',
      timeout: 8000,                   // Service-specific timeout
    });
    return response.data;
  }
}

// Under the hood in HttpClientService
if (this.circuitBreakerService && serviceName !== 'default') {
  const circuit = this.circuitBreakerService.getCircuitBreaker(serviceName, {
    timeout: options.timeout || 10000,
    errorThresholdPercentage: 30,
    resetTimeout: 20000,
  });

  return await circuit.execute(async () => {
    return this.executeRequest(method, url, options, serviceName, operation);
  });
}
```

### Messaging Integration

```typescript
// Circuit breaker state changes are automatically published as events
import { EventFactory, EventPublisher } from '@libs/messaging';

class Circuit {
  private setupEventListeners(): void {
    this.opossumCircuit.on('open', (reason) => {
      this.publishStateChangeEvent('OPEN', reason);
    });

    this.opossumCircuit.on('close', () => {
      this.publishStateChangeEvent('CLOSED');
    });

    this.opossumCircuit.on('halfOpen', () => {
      this.publishStateChangeEvent('HALF_OPEN');
    });
  }

  private publishStateChangeEvent(newState: string, reason?: string): void {
    const event = EventFactory.circuitBreakerStateChanged({
      serviceName: this.serviceName,
      previousState: this.previousState,
      newState: newState as any,
      errorRate: this.getErrorRate(),
      requestCount: this.getTotalRequests(),
      failureCount: this.getTotalFailures(),
      lastFailureReason: reason,
      resetTime: newState === 'OPEN' ? new Date(Date.now() + this.resetTimeout) : undefined
    });

    this.eventPublisher?.publish(event).catch(/* handle error */);
  }
}
```

### Observability Integration

```typescript
// Comprehensive logging and metrics integration
import { ObservabilityLogger, MetricsService } from '@libs/observability';

class CircuitBreakerService {
  constructor(
    private readonly loggerFactory: any,
    private readonly eventPublisher?: EventPublisher
  ) {
    this.logger = this.loggerFactory.createLogger('CircuitBreakerService');
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await this.opossumCircuit.execute(operation);
      const duration = Date.now() - startTime;
      
      this.logger.debug(`Circuit ${this.serviceName} SUCCESS in ${duration}ms`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.logger.warn(`Circuit ${this.serviceName} FAILED in ${duration}ms`, {
        error: error.message,
        serviceName: this.serviceName,
        state: this.state
      });
      
      throw error;
    }
  }
}
```

### API Gateway Integration

```typescript
// API Gateway proxy with automatic circuit breaker protection
@Injectable()
export class ProxyService {
  async forwardRequest(req: Request, serviceName: string): Promise<any> {
    // Circuit breaker protection for proxy requests
    const response = await this.httpClient.request(
      req.method.toUpperCase() as any,
      this.transformPath(req.path),
      {
        serviceName,           // Automatic circuit breaker creation
        operationName: 'proxy-request',
        data: req.body,
        headers: this.transformHeaders(req.headers),
        timeout: this.getServiceTimeout(serviceName),
      }
    );
    
    return response;
  }
}
```

## Monitoring & Observability

### Circuit Breaker Metrics

```typescript
interface CircuitBreakerMetrics {
  failures: number;              // Current failure count
  successes: number;             // Current success count
  state: CircuitBreakerState;    // Current circuit state
  totalRequests: number;         // Total request count
  totalSuccesses: number;        // Total success count
  totalFailures: number;         // Total failure count
  errorRate: number;             // Calculated error rate (0-1)
  lastFailureTime: string | null;
  nextAttemptTime: string | null;
  requestCount: number;          // Rolling window request count
  rollingCountTimeout: number;   // Window duration in ms
}

// Get metrics for monitoring
const metrics = circuit.getMetrics();
console.log(`Error rate: ${(metrics.errorRate * 100).toFixed(2)}%`);
console.log(`Total requests: ${metrics.totalRequests}`);
console.log(`Current state: ${metrics.state}`);
```

### Health Monitoring

```typescript
@Injectable()
export class CircuitBreakerHealthService {
  constructor(
    private circuitBreakerService: CircuitBreakerService
  ) {}

  async getCircuitBreakerHealth(): Promise<CircuitBreakerHealth> {
    const circuits = this.circuitBreakerService.getAllCircuits();
    const healthStatus = {};

    for (const [name, circuit] of circuits) {
      const metrics = circuit.getMetrics();
      healthStatus[name] = {
        state: metrics.state,
        errorRate: metrics.errorRate,
        totalRequests: metrics.totalRequests,
        isHealthy: metrics.state !== 'OPEN',
        lastFailureTime: metrics.lastFailureTime,
        nextAttemptTime: metrics.nextAttemptTime
      };
    }

    return {
      overall: Object.values(healthStatus).every(c => c.isHealthy),
      circuits: healthStatus
    };
  }
}
```

### Event Monitoring

```typescript
// Monitor circuit breaker events for alerting
@Injectable()
export class CircuitBreakerEventHandler implements EventHandler {
  supportedEvents = ['circuit-breaker.state-changed'];

  async handle(event: CircuitBreakerStateChangedEvent): Promise<void> {
    const { serviceName, newState, errorRate, failureCount } = event.data;

    switch (newState) {
      case 'OPEN':
        // Alert: Circuit breaker opened
        await this.alertingService.sendAlert('circuit-breaker-open', {
          service: serviceName,
          errorRate: `${(errorRate * 100).toFixed(2)}%`,
          failures: failureCount
        });
        break;

      case 'CLOSED':
        // Info: Circuit breaker recovered
        await this.alertingService.sendInfo('circuit-breaker-recovered', {
          service: serviceName
        });
        break;
    }
  }
}
```

### Performance Monitoring

```typescript
// Real-time circuit breaker dashboard
@Injectable()
export class CircuitBreakerDashboard {
  async getRealtimeMetrics(): Promise<CircuitBreakerDashboardData> {
    const circuits = this.circuitBreakerService.getAllCircuits();
    const metrics = {};

    for (const [name, circuit] of circuits) {
      const circuitMetrics = circuit.getMetrics();
      metrics[name] = {
        state: circuitMetrics.state,
        errorRate: circuitMetrics.errorRate,
        requestsPerMinute: this.calculateRequestsPerMinute(circuitMetrics),
        averageResponseTime: this.getAverageResponseTime(name),
        lastStateChange: this.getLastStateChangeTime(name)
      };
    }

    return {
      timestamp: new Date(),
      totalCircuits: circuits.size,
      openCircuits: Object.values(metrics).filter(m => m.state === 'OPEN').length,
      metrics
    };
  }
}
```

## Best Practices

### 1. Service-Specific Configuration

```typescript
// ✅ Good: Different configurations for different service types
const getCircuitConfig = (serviceName: string): OpossumCircuitBreakerOptions => {
  const configs = {
    'payment-service': {
      timeout: 15000,           // Payment needs longer timeout
      errorThresholdPercentage: 20, // Very sensitive to payment failures
      resetTimeout: 60000,      // Longer recovery for payment issues
    },
    'notification-service': {
      timeout: 3000,            // Notifications should be fast
      errorThresholdPercentage: 60, // More tolerant of notification failures
      resetTimeout: 15000,      // Quick recovery
    },
    'analytics-service': {
      timeout: 30000,           // Analytics can take longer
      errorThresholdPercentage: 80, // Very tolerant for non-critical service
      resetTimeout: 10000,      // Quick retry
    }
  };

  return configs[serviceName] || {
    timeout: 10000,
    errorThresholdPercentage: 50,
    resetTimeout: 30000
  };
};
```

### 2. Fallback Strategies

```typescript
// ✅ Good: Implement meaningful fallbacks
@Injectable()
export class UserService {
  async getUser(id: string): Promise<User> {
    return this.userServiceCircuit.execute(async () => {
      return await this.externalUserService.getUser(id);
    });
  }

  async getUserWithFallback(id: string): Promise<User> {
    try {
      return await this.getUser(id);
    } catch (error) {
      if (this.isCircuitOpen(error)) {
        // Return cached user data when circuit is open
        const cachedUser = await this.cacheService.get<User>(`user:${id}`);
        if (cachedUser.hit) {
          this.logger.info(`Returning cached user for ${id} (circuit open)`);
          return cachedUser.value;
        }
        
        // Return minimal user object as last resort
        return this.createMinimalUser(id);
      }
      throw error;
    }
  }
}
```

### 3. Circuit Naming and Organization

```typescript
// ✅ Good: Descriptive circuit names and grouping
const circuitNaming = {
  // Service-based naming
  'user-service': 'User Management API',
  'payment-service': 'Payment Processing API',
  'notification-service': 'Notification Delivery Service',
  
  // Operation-based naming for complex services
  'order-service:create': 'Order Creation',
  'order-service:update': 'Order Updates',
  'order-service:cancel': 'Order Cancellation',
  
  // Resource-based naming
  'database:users': 'User Database',
  'database:orders': 'Order Database',
  'cache:redis': 'Redis Cache'
};

// Group related circuits
const circuitGroups = {
  'critical': ['payment-service', 'order-service:create'],
  'user-facing': ['user-service', 'notification-service'],
  'analytics': ['analytics-service', 'reporting-service'],
  'infrastructure': ['database:users', 'cache:redis']
};
```

### 4. Monitoring and Alerting

```typescript
// ✅ Good: Comprehensive monitoring strategy
@Injectable()
export class CircuitBreakerMonitoring {
  @Cron('*/30 * * * * *') // Every 30 seconds
  async monitorCircuitHealth(): Promise<void> {
    const circuits = this.circuitBreakerService.getAllCircuits();
    
    for (const [name, circuit] of circuits) {
      const metrics = circuit.getMetrics();
      
      // Record metrics
      this.metricsService.gauge('circuit_breaker_error_rate', metrics.errorRate, { circuit: name });
      this.metricsService.gauge('circuit_breaker_request_count', metrics.requestCount, { circuit: name });
      this.metricsService.gauge('circuit_breaker_state', this.stateToNumber(metrics.state), { circuit: name });
      
      // Check for concerning patterns
      if (metrics.errorRate > 0.3 && metrics.state === 'CLOSED') {
        this.logger.warn(`High error rate detected`, {
          circuit: name,
          errorRate: metrics.errorRate,
          requestCount: metrics.requestCount
        });
      }
      
      if (metrics.state === 'OPEN') {
        const openDuration = Date.now() - new Date(metrics.lastFailureTime).getTime();
        if (openDuration > 300000) { // 5 minutes
          this.alertingService.sendAlert('circuit-breaker-prolonged-open', {
            circuit: name,
            duration: Math.round(openDuration / 1000)
          });
        }
      }
    }
  }
}
```

### 5. Testing Circuit Breakers

```typescript
// ✅ Good: Test circuit breaker behavior
describe('CircuitBreaker', () => {
  let circuitBreakerService: CircuitBreakerService;
  let mockService: jest.MockedFunction<any>;

  beforeEach(() => {
    circuitBreakerService = new CircuitBreakerService(mockLoggerFactory);
    mockService = jest.fn();
  });

  it('should open circuit after threshold failures', async () => {
    const circuit = circuitBreakerService.getCircuitBreaker('test-service', {
      timeout: 1000,
      errorThresholdPercentage: 50,
      volumeThreshold: 2
    });

    // Simulate failures
    mockService.mockRejectedValue(new Error('Service unavailable'));
    
    // Execute until circuit opens
    for (let i = 0; i < 3; i++) {
      try {
        await circuit.execute(mockService);
      } catch (error) {
        // Expected failures
      }
    }

    // Circuit should now be open
    expect(circuit.state).toBe('OPEN');
    
    // Subsequent calls should be rejected immediately
    await expect(circuit.execute(mockService)).rejects.toThrow('Circuit breaker is OPEN');
  });

  it('should recover after successful call in half-open state', async () => {
    // Test recovery logic
    const circuit = circuitBreakerService.getCircuitBreaker('recovery-test', {
      resetTimeout: 100
    });

    // Force circuit open
    await this.forceCircuitOpen(circuit);
    expect(circuit.state).toBe('OPEN');

    // Wait for reset timeout
    await new Promise(resolve => setTimeout(resolve, 150));
    
    // Circuit should be half-open
    expect(circuit.state).toBe('HALF_OPEN');
    
    // Successful call should close circuit
    mockService.mockResolvedValue('success');
    await circuit.execute(mockService);
    
    expect(circuit.state).toBe('CLOSED');
  });
});
```

## API Reference

### CircuitBreakerService

```typescript
class CircuitBreakerService {
  // Create or get circuit breaker
  getCircuitBreaker(serviceName: string, options?: OpossumCircuitBreakerOptions): Circuit;
  
  // Get all registered circuits
  getAllCircuits(): Map<string, Circuit>;
  
  // Check if circuit exists
  hasCircuit(serviceName: string): boolean;
  
  // Remove circuit
  removeCircuit(serviceName: string): boolean;
  
  // Reset all circuits
  resetAllCircuits(): void;
}
```

### Circuit

```typescript
class Circuit {
  // Execute operation with circuit breaker protection
  execute<T>(operation: () => Promise<T>): Promise<T>;
  
  // Get current circuit state
  get state(): CircuitBreakerState;
  
  // Get circuit metrics
  getMetrics(): CircuitBreakerMetrics;
  
  // Event subscription
  on(event: string, listener: (...args: any[]) => void): void;
  off(event: string, listener: (...args: any[]) => void): void;
  
  // Manual control
  open(): void;
  close(): void;
  
  // Health check
  isHealthy(): boolean;
}
```

### Types

```typescript
enum CircuitBreakerState {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  HALF_OPEN = 'HALF_OPEN'
}

interface OpossumCircuitBreakerOptions {
  timeout?: number;
  errorThresholdPercentage?: number;
  resetTimeout?: number;
  capacity?: number;
  rollingCountTimeout?: number;
  rollingCountBuckets?: number;
  volumeThreshold?: number;
  name?: string;
  group?: string;
  fallback?: Function;
  allowWarmUp?: boolean;
}

interface CircuitBreakerMetrics {
  failures: number;
  successes: number;
  state: CircuitBreakerState;
  totalRequests: number;
  totalSuccesses: number;
  totalFailures: number;
  errorRate: number;
  lastFailureTime: string | null;
  nextAttemptTime: string | null;
}
```

## Examples

### Complete Service Implementation

```typescript
@Injectable()
export class OrderService {
  private paymentCircuit: Circuit;
  private inventoryCircuit: Circuit;
  private notificationCircuit: Circuit;

  constructor(
    private circuitBreakerService: CircuitBreakerService,
    private paymentService: PaymentService,
    private inventoryService: InventoryService,
    private notificationService: NotificationService,
    @Inject('LOGGER_FACTORY') private loggerFactory: any
  ) {
    this.logger = loggerFactory.createLogger(OrderService.name);
    this.setupCircuitBreakers();
  }

  private logger: ObservabilityLogger;

  private setupCircuitBreakers(): void {
    // Critical payment circuit - very sensitive
    this.paymentCircuit = this.circuitBreakerService.getCircuitBreaker('payment-service', {
      timeout: 15000,
      errorThresholdPercentage: 20,
      resetTimeout: 60000,
      name: 'PaymentProcessing'
    });

    // Inventory circuit - moderately sensitive
    this.inventoryCircuit = this.circuitBreakerService.getCircuitBreaker('inventory-service', {
      timeout: 8000,
      errorThresholdPercentage: 40,
      resetTimeout: 30000,
      name: 'InventoryCheck'
    });

    // Notification circuit - tolerant of failures
    this.notificationCircuit = this.circuitBreakerService.getCircuitBreaker('notification-service', {
      timeout: 3000,
      errorThresholdPercentage: 70,
      resetTimeout: 15000,
      name: 'OrderNotifications'
    });

    // Setup event listeners for monitoring
    this.setupCircuitEventListeners();
  }

  async createOrder(orderData: CreateOrderDto): Promise<Order> {
    const correlationId = this.generateCorrelationId();
    
    try {
      // Step 1: Check inventory (with circuit breaker protection)
      const inventoryResult = await this.checkInventoryWithFallback(orderData.items, correlationId);
      
      // Step 2: Process payment (with circuit breaker protection)
      const paymentResult = await this.processPaymentWithRetry(orderData.payment, correlationId);
      
      // Step 3: Create order record
      const order = await this.orderRepository.create({
        ...orderData,
        paymentId: paymentResult.id,
        status: 'confirmed',
        correlationId
      });

      // Step 4: Send notifications (non-blocking with circuit breaker)
      this.sendOrderNotification(order, correlationId);

      return order;
    } catch (error) {
      this.logger.error('Order creation failed', {
        error: error.message,
        correlationId,
        orderData: { itemCount: orderData.items.length }
      });
      throw error;
    }
  }

  private async checkInventoryWithFallback(items: OrderItem[], correlationId: string): Promise<InventoryResult> {
    try {
      return await this.inventoryCircuit.execute(async () => {
        return await this.inventoryService.checkAvailability(items, { correlationId });
      });
    } catch (error) {
      if (this.isCircuitOpen(error)) {
        // Fallback: Allow order if circuit is open (manual verification later)
        this.logger.warn('Inventory check circuit open, using fallback', { correlationId });
        return {
          available: true,
          items: items.map(item => ({ ...item, needsVerification: true })),
          fallbackUsed: true
        };
      }
      throw error;
    }
  }

  private async processPaymentWithRetry(paymentData: PaymentDto, correlationId: string): Promise<PaymentResult> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        return await this.paymentCircuit.execute(async () => {
          return await this.paymentService.processPayment(paymentData, { 
            correlationId,
            attempt 
          });
        });
      } catch (error) {
        lastError = error;
        
        if (this.isCircuitOpen(error)) {
          // Circuit is open - don't retry
          throw new PaymentUnavailableError('Payment service is currently unavailable');
        }
        
        if (attempt < 3) {
          // Wait before retry (exponential backoff)
          await this.delay(Math.pow(2, attempt) * 1000);
          this.logger.warn(`Payment attempt ${attempt} failed, retrying`, {
            correlationId,
            error: error.message
          });
        }
      }
    }
    
    throw lastError;
  }

  private sendOrderNotification(order: Order, correlationId: string): void {
    // Non-blocking notification with circuit breaker protection
    setImmediate(async () => {
      try {
        await this.notificationCircuit.execute(async () => {
          await this.notificationService.sendOrderConfirmation(order, { correlationId });
        });
        
        this.logger.debug('Order notification sent successfully', {
          orderId: order.id,
          correlationId
        });
      } catch (error) {
        this.logger.warn('Order notification failed', {
          orderId: order.id,
          correlationId,
          error: error.message,
          circuitOpen: this.isCircuitOpen(error)
        });
        // Don't throw - notification failure shouldn't break order creation
      }
    });
  }

  private setupCircuitEventListeners(): void {
    const circuits = [
      { circuit: this.paymentCircuit, name: 'Payment' },
      { circuit: this.inventoryCircuit, name: 'Inventory' },
      { circuit: this.notificationCircuit, name: 'Notification' }
    ];

    circuits.forEach(({ circuit, name }) => {
      circuit.on('open', () => {
        this.logger.error(`${name} circuit breaker opened`, {
          circuit: name,
          metrics: circuit.getMetrics()
        });
      });

      circuit.on('close', () => {
        this.logger.info(`${name} circuit breaker closed (recovered)`, {
          circuit: name
        });
      });

      circuit.on('halfOpen', () => {
        this.logger.info(`${name} circuit breaker half-open (testing recovery)`, {
          circuit: name
        });
      });
    });
  }

  private isCircuitOpen(error: Error): boolean {
    return error.message.includes('Circuit breaker is OPEN') || 
           error.name === 'CircuitBreakerOpenError';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateCorrelationId(): string {
    return `order-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

### Circuit Breaker Health Check

```typescript
@Controller('health')
export class HealthController {
  constructor(
    private circuitBreakerService: CircuitBreakerService
  ) {}

  @Get('circuit-breakers')
  async getCircuitBreakerHealth(): Promise<CircuitBreakerHealthResponse> {
    const circuits = this.circuitBreakerService.getAllCircuits();
    const healthData = {};
    let overallHealthy = true;

    for (const [name, circuit] of circuits) {
      const metrics = circuit.getMetrics();
      const isHealthy = metrics.state !== 'OPEN';
      
      healthData[name] = {
        state: metrics.state,
        healthy: isHealthy,
        errorRate: `${(metrics.errorRate * 100).toFixed(2)}%`,
        totalRequests: metrics.totalRequests,
        totalFailures: metrics.totalFailures,
        lastFailure: metrics.lastFailureTime,
        nextRetry: metrics.nextAttemptTime
      };

      if (!isHealthy) {
        overallHealthy = false;
      }
    }

    return {
      status: overallHealthy ? 'healthy' : 'degraded',
      timestamp: new Date(),
      circuitCount: circuits.size,
      openCircuits: Object.values(healthData).filter(c => !c.healthy).length,
      circuits: healthData
    };
  }

  @Post('circuit-breakers/:name/reset')
  async resetCircuitBreaker(@Param('name') name: string): Promise<{ success: boolean }> {
    const circuit = this.circuitBreakerService.getAllCircuits().get(name);
    
    if (!circuit) {
      throw new NotFoundException(`Circuit breaker '${name}' not found`);
    }

    circuit.close(); // Force close the circuit
    
    return { success: true };
  }
}
```

## Migration from Manual Circuit Breakers

**⚠️ IMPORTANT**: As of the HTTP lib rework, services should **NOT** manually manage circuit breakers. Circuit breaker protection is now **automatic** via the HTTP lib integration.

### ❌ **Old Pattern (Deprecated)**
```typescript
// DON'T DO THIS - Manual circuit breaker management
@Injectable()
export class MyService {
  constructor(private readonly circuitBreakerService: CircuitBreakerService) {}

  async callExternalService() {
    const circuit = this.circuitBreakerService.getCircuitBreaker('external-service', {
      timeout: 5000,
      errorThresholdPercentage: 50,
    });
    
    return await circuit.execute(() => 
      this.httpClient.get('/api/data')
    );
  }
}
```

### ✅ **New Pattern (Automatic)**
```typescript
// DO THIS - Automatic circuit breaker protection
@Injectable()
export class MyService {
  constructor(private readonly httpClient: HttpClientService) {}

  async callExternalService() {
    // Circuit breaker protection is automatic when using serviceName
    return await this.httpClient.get('/api/data', {
      serviceName: 'external-service',  // Triggers automatic circuit breaker
      operationName: 'fetch-data',
    });
  }
}
```

### Migration Checklist

When migrating services to the new pattern:

1. **Remove manual circuit breaker dependencies**:
   ```diff
   - import { CircuitBreakerService } from '@libs/resilience';
   - private readonly circuitBreakerService: CircuitBreakerService
   ```

2. **Remove manual circuit breaker execution**:
   ```diff
   - const circuit = this.circuitBreakerService.getCircuitBreaker('service-name', config);
   - return await circuit.execute(() => this.httpClient.get('/api/data'));
   + return await this.httpClient.get('/api/data', { serviceName: 'service-name' });
   ```

3. **Use HttpClientService for all external calls** with `serviceName` parameter

4. **Monitor circuit breaker status** via HTTP lib health endpoints:
   ```typescript
   const status = this.httpClient.getCircuitBreakerStatus();
   ```

### Benefits of New Architecture

- **Automatic Protection**: No manual circuit breaker setup required
- **Consistent Configuration**: Centralized circuit breaker settings
- **Better Observability**: Unified monitoring via HTTP lib
- **Reduced Boilerplate**: Eliminates manual circuit breaker code
- **Performance**: HTTP/2 optimizations + circuit breaker protection

## Troubleshooting

### Common Issues

#### 1. Circuit Opening Too Frequently

**Symptoms**: Circuit breaker opens quickly under normal load
**Solution**:
```typescript
// Adjust threshold and volume settings
const circuit = circuitBreakerService.getCircuitBreaker('service-name', {
  errorThresholdPercentage: 60,    // Increase threshold
  volumeThreshold: 20,             // Require more requests before opening
  rollingCountTimeout: 15000,      // Longer statistical window
});
```

#### 2. Circuit Not Opening When Expected

**Symptoms**: Service continues to fail but circuit stays closed
**Solution**:
```typescript
// Check volume threshold and error calculation
const circuit = circuitBreakerService.getCircuitBreaker('service-name', {
  volumeThreshold: 5,              // Lower threshold for testing
  errorThresholdPercentage: 30,    // Lower error rate threshold
  rollingCountBuckets: 5,          // Fewer buckets for faster response
});

// Debug circuit state
const metrics = circuit.getMetrics();
console.log('Circuit metrics:', {
  requestCount: metrics.requestCount,
  errorRate: metrics.errorRate,
  volumeThreshold: circuit.options.volumeThreshold
});
```

#### 3. Slow Recovery After Outages

**Symptoms**: Circuit takes too long to recover after service is restored
**Solution**:
```typescript
// Reduce reset timeout for faster recovery testing
const circuit = circuitBreakerService.getCircuitBreaker('service-name', {
  resetTimeout: 15000,             // Faster recovery testing
  capacity: 3,                     // Limit concurrent test requests
});
```

#### 4. Missing Circuit Breaker Events

**Symptoms**: No events published for circuit state changes
**Solution**:
```typescript
// Verify event publisher is configured
CircuitBreakerModule.register(); // Ensure module is properly registered

// Check event publisher injection
constructor(
  @Inject('EVENT_PUBLISHER') private eventPublisher?: EventPublisher
) {
  if (!eventPublisher) {
    console.warn('Event publisher not available for circuit breaker events');
  }
}
```

### Debug Commands

```typescript
// Debug circuit breaker state
const debugCircuit = (circuitName: string) => {
  const circuit = circuitBreakerService.getAllCircuits().get(circuitName);
  if (circuit) {
    const metrics = circuit.getMetrics();
    console.log(`Circuit: ${circuitName}`, {
      state: metrics.state,
      errorRate: metrics.errorRate,
      requestCount: metrics.requestCount,
      totalRequests: metrics.totalRequests,
      totalFailures: metrics.totalFailures,
      lastFailure: metrics.lastFailureTime
    });
  }
};

// Monitor circuit events
circuit.on('fire', () => console.log('Request fired'));
circuit.on('success', () => console.log('Request succeeded'));
circuit.on('failure', (error) => console.log('Request failed:', error.message));
circuit.on('timeout', () => console.log('Request timed out'));
circuit.on('reject', () => console.log('Request rejected (circuit open)'));
```

---

## Integration Status

✅ **Fully Integrated Libraries:**
- `@libs/observability` - Structured logging, metrics collection, state monitoring
- `@libs/messaging` - Circuit breaker state change events via EventFactory
- `@libs/http` - Automatic circuit breaker protection for all HTTP service calls
- `@libs/error-handling` - Error correlation and consistent error responses

✅ **Service Integration:**
- `api-gateway` - Proxy requests protected by service-specific circuit breakers
- `auth-service` - External service calls protected (Keycloak, user service)
- `user-service` - External API calls with automatic circuit breaker protection

**Key Metrics:**
- Service protection: 100% of external HTTP calls protected by circuit breakers
- Automatic recovery: Self-healing circuits with configurable recovery timeouts
- Event coverage: All circuit state changes published to messaging system
- Failure isolation: Prevents cascading failures across service boundaries