{"numFailedTestSuites": 2, "numFailedTests": 3, "numPassedTestSuites": 1, "numPassedTests": 12, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 1, "numTodoTests": 0, "numTotalTestSuites": 3, "numTotalTests": 15, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750240252146, "success": false, "testResults": [{"assertionResults": [], "coverage": {}, "endTime": 1750240259136, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/circuit.spec.ts\u001b[0m:\u001b[93m70\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18046: \u001b[0m'error' is of type 'unknown'.\n\n    \u001b[7m70\u001b[0m         expect(error.message).toMatch(/test error|Breaker is open/);\n    \u001b[7m  \u001b[0m \u001b[91m               ~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/resilience/test/circuit.spec.ts", "startTime": 1750240259136, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should start in CLOSED state", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should start in CLOSED state"}, {"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should execute function successfully in CLOSED state", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should execute function successfully in CLOSED state"}, {"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should provide metrics", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should provide metrics"}, {"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should manually open and close circuit", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should manually open and close circuit"}, {"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should reset circuit breaker", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should reset circuit breaker"}, {"ancestorTitles": ["Circuit - Basic Functionality"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "Circuit - Basic Functionality should reject calls when circuit is open", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should reject calls when circuit is open"}], "endTime": 1750240258871, "message": "", "name": "/root/code/polyrepo/libs/resilience/test/circuit-simple.spec.ts", "startTime": 1750240253344, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["CircuitBreakerService"], "duration": 24, "failureDetails": [], "failureMessages": [], "fullName": "CircuitBreakerService should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "CircuitBreakerService should create a circuit breaker", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should create a circuit breaker"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "CircuitBreakerService should return the same circuit breaker for the same service", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return the same circuit breaker for the same service"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "CircuitBreakerService should create different circuit breakers for different services", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should create different circuit breakers for different services"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 55, "failureDetails": [{"matcherResult": {"message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:70:28)", "pass": false}, "message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:70:28)"}], "failureMessages": ["Error: expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:70:28)\n    at Object.toThrow (/root/code/polyrepo/node_modules/expect/build/index.js:218:22)\n    at Object.<anonymous> (/root/code/polyrepo/libs/resilience/test/circuit-breaker.service.spec.ts:70:53)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "CircuitBreakerService should create a circuit breaker with custom options", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "failed", "title": "should create a circuit breaker with custom options"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "CircuitBreakerService should get status of all circuit breakers", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should get status of all circuit breakers"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 6, "failureDetails": [{"matcherResult": {"message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:101:26)", "pass": false}, "message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:101:26)"}], "failureMessages": ["Error: expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:101:26)\n    at Object.toThrow (/root/code/polyrepo/node_modules/expect/build/index.js:218:22)\n    at Object.<anonymous> (/root/code/polyrepo/libs/resilience/test/circuit-breaker.service.spec.ts:101:90)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "CircuitBreakerService should reset a specific circuit breaker", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should reset a specific circuit breaker"}, {"ancestorTitles": ["CircuitBreakerService"], "duration": 15, "failureDetails": [{"matcherResult": {"message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:118:27)", "pass": false}, "message": "expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:118:27)"}], "failureMessages": ["Error: expect(received).rejects.toThrow(expected)\n\nExpected substring: \"test error\"\nReceived message:   \"Breaker is open\"\n\n      141 |     if (fn) {\n      142 |       // Call the circuit breaker with the function as the first argument\n    > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n          |                                   ^\n      144 |     }\n      145 |     // This assumes the action was provided in the constructor\n      146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:118:27)\n    at Object.toThrow (/root/code/polyrepo/node_modules/expect/build/index.js:218:22)\n    at Object.<anonymous> (/root/code/polyrepo/libs/resilience/test/circuit-breaker.service.spec.ts:118:91)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)"], "fullName": "CircuitBreakerService should reset all circuit breakers", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should reset all circuit breakers"}], "endTime": 1750240259044, "message": "  ● CircuitBreakerService › should create a circuit breaker with custom options\n\n    expect(received).rejects.toThrow(expected)\n\n    Expected substring: \"test error\"\n    Received message:   \"Breaker is open\"\n\n          141 |     if (fn) {\n          142 |       // Call the circuit breaker with the function as the first argument\n        > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n              |                                   ^\n          144 |     }\n          145 |     // This assumes the action was provided in the constructor\n          146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:70:28)\n      at Object.toThrow (../../node_modules/expect/build/index.js:218:22)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:70:53)\n\n  ● CircuitBreakerService › should reset a specific circuit breaker\n\n    expect(received).rejects.toThrow(expected)\n\n    Expected substring: \"test error\"\n    Received message:   \"Breaker is open\"\n\n          141 |     if (fn) {\n          142 |       // Call the circuit breaker with the function as the first argument\n        > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n              |                                   ^\n          144 |     }\n          145 |     // This assumes the action was provided in the constructor\n          146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:101:26)\n      at Object.toThrow (../../node_modules/expect/build/index.js:218:22)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:101:90)\n\n  ● CircuitBreakerService › should reset all circuit breakers\n\n    expect(received).rejects.toThrow(expected)\n\n    Expected substring: \"test error\"\n    Received message:   \"Breaker is open\"\n\n          141 |     if (fn) {\n          142 |       // Call the circuit breaker with the function as the first argument\n        > 143 |       return this.opossumInstance.fire(fn, ...args) as Promise<T>;\n              |                                   ^\n          144 |     }\n          145 |     // This assumes the action was provided in the constructor\n          146 |     return this.opossumInstance.fire(...args) as Promise<T>;\n\n      at buildError (../../node_modules/opossum/lib/circuit.js:1015:17)\n      at CircuitBreaker.call (../../node_modules/opossum/lib/circuit.js:717:21)\n      at CircuitBreaker.fire (../../node_modules/opossum/lib/circuit.js:616:17)\n      at Circuit.execute (src/circuit-breaker/circuit.ts:143:35)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:118:27)\n      at Object.toThrow (../../node_modules/expect/build/index.js:218:22)\n      at Object.<anonymous> (test/circuit-breaker.service.spec.ts:118:91)\n", "name": "/root/code/polyrepo/libs/resilience/test/circuit-breaker.service.spec.ts", "startTime": 1750240253287, "status": "failed", "summary": ""}], "wasInterrupted": false}