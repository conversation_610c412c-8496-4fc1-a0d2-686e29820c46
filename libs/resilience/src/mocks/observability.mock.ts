/**
 * Mock ObservabilityLogger for testing
 */
export class MockObservabilityLogger {
  constructor(private context: string = 'Test') {}

  setContext(context: string): void {
    this.context = context;
  }

  log(message: string, ...optionalParams: any[]): void {
    console.log(`[${this.context}] info: ${message}`, ...optionalParams);
  }

  error(message: string, trace?: string, ...optionalParams: any[]): void {
    console.error(`[${this.context}] error: ${message}`, trace || '', ...optionalParams);
  }

  warn(message: string, ...optionalParams: any[]): void {
    console.warn(`[${this.context}] warn: ${message}`, ...optionalParams);
  }

  debug(message: string, ...optionalParams: any[]): void {
    console.debug(`[${this.context}] debug: ${message}`, ...optionalParams);
  }

  verbose(message: string, ...optionalParams: any[]): void {
    console.debug(`[${this.context}] verbose: ${message}`, ...optionalParams);
  }
}

/**
 * Mock logger factory for testing
 */
export const mockLoggerFactory = {
  createLogger: (context: string) => new MockObservabilityLogger(context),
};
