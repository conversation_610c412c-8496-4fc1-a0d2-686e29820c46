import { ObservabilityLogger } from '@libs/observability';
import { EventPublisher, EventFactory } from '@libs/messaging';
import Opossum from 'opossum';
import { CircuitBreakerOptions, CircuitBreakerState, CircuitBreakerMetrics, OpossumCircuitBreakerOptions } from './circuit-breaker.types';

// Legacy options for backward compatibility with tests
export const DEFAULT_OPTIONS = {
  failureThreshold: 5,
  successThreshold: 3,
  resetTimeout: 30000,
  timeoutMs: 10000,
  failureWindow: 60000,
};

// Default options for Opossum, mapping from your existing defaults where applicable
// Opossum defaults: timeout: 3000, errorThresholdPercentage: 50, resetTimeout: 30000
export const DEFAULT_OPOSSUM_OPTIONS: OpossumCircuitBreakerOptions = {
  timeout: 10000, // Corresponds to how long an action can run before timing out (default in Opossum is 3s)
  errorThresholdPercentage: 50, // Percentage of errors to open the circuit (default is 50)
  resetTimeout: 30000, // Time in ms to wait before trying to close the circuit (matches your resetTimeout)
  // Opossum's 'capacity' (default 10) is the number of executions allowed in halfOpen state.
  // Your 'successThreshold' was for consecutive successes in halfOpen.
  // Opossum's 'rollingCountTimeout' (default 10000) and 'rollingCountBuckets' (default 10) define the window for error rate calculation.
  // Your 'failureWindow' was 60000 and 'failureThreshold' was a count.
  // We might need to adjust these based on how Opossum's error rate calculation works if precise mapping is needed.
  // For now, using Opossum defaults for rolling counts and focusing on errorThresholdPercentage.
  name: '', // Will be set in constructor
  group: '', // Optional grouping, can be set
};

/**
 * Circuit breaker for a specific service, now using Opossum internally.
 */
export class Circuit {
  private readonly opossumInstance: Opossum;
  private readonly logger: ObservabilityLogger;

  constructor(
    private readonly serviceName: string,
    options: Partial<OpossumCircuitBreakerOptions> = {},
    loggerFactory: any,
    private readonly eventPublisher?: EventPublisher,
    action?: (...args: any[]) => Promise<any>, // The function to be wrapped by Opossum, can be optional if .fire() is used externally
  ) {
    this.logger = loggerFactory.createLogger(`Circuit:${serviceName}`);
    const opossumOptions: Opossum.Options = {
      ...DEFAULT_OPOSSUM_OPTIONS,
      ...options,
      name: serviceName, // Opossum uses 'name' for identification
      group: options.group || serviceName, // Optional grouping
    };

    // If an action is provided, Opossum wraps it directly. Otherwise, .fire() must be used with the action.
    this.opossumInstance = new Opossum(action || (async (...args: any[]) => { 
      console.log('Default action called with args:', args);
      const fn = args[0];
      return fn && typeof fn === 'function' ? await fn(...args.slice(1)) : undefined; 
    }), opossumOptions);

    this.setupEventListeners();
    this.logger.log(`Opossum Circuit breaker created for ${serviceName} with options: ${JSON.stringify(opossumOptions)}`);
  }

  private setupEventListeners(): void {
    // State change events with messaging integration
    this.opossumInstance.on('open', () => {
      this.logger.error(`🚨 CIRCUIT BREAKER OPENED for ${this.serviceName}! Service requests will be blocked. Check service health and reset via /health/circuit-breakers/reset`);
      this.publishStateChangeEvent('CLOSED', 'OPEN', 'Circuit opened due to failures');
    });
    
    this.opossumInstance.on('halfOpen', () => {
      this.logger.warn(`⚠️ Circuit for ${this.serviceName} is HALF_OPEN - testing recovery.`);
      this.publishStateChangeEvent('OPEN', 'HALF_OPEN', 'Circuit testing recovery');
    });
    
    this.opossumInstance.on('close', () => {
      this.logger.log(`✅ Circuit for ${this.serviceName} is CLOSED - service recovered.`);
      this.publishStateChangeEvent('HALF_OPEN', 'CLOSED', 'Circuit recovered successfully');
    });

    // Operational events with enhanced logging
    this.opossumInstance.on('success', (result, duration) => 
      this.logger.debug(`Circuit for ${this.serviceName} call SUCCEEDED in ${duration}ms.`)
    );
    
    this.opossumInstance.on('failure', (error, duration) => 
      this.logger.warn(`Circuit for ${this.serviceName} call FAILED in ${duration}ms: ${error.message}`)
    );
    
    this.opossumInstance.on('timeout', (duration) => 
      this.logger.warn(`Circuit for ${this.serviceName} call TIMED OUT after ${duration}ms.`)
    );
    
    this.opossumInstance.on('reject', () => {
      this.logger.error(`🚫 Circuit for ${this.serviceName} call REJECTED - Circuit is OPEN! Reset via: curl -X POST http://localhost:3000/health/circuit-breakers/reset/${this.serviceName}`);
    });
  }

  /**
   * Publish circuit breaker state change events
   */
  private publishStateChangeEvent(
    previousState: 'CLOSED' | 'OPEN' | 'HALF_OPEN',
    newState: 'CLOSED' | 'OPEN' | 'HALF_OPEN',
    reason: string
  ): void {
    if (!this.eventPublisher) return;

    const metrics = this.getMetrics();
    const stateChangeEvent = EventFactory.circuitBreakerStateChanged({
      serviceName: this.serviceName,
      previousState,
      newState,
      errorRate: metrics.errorRate || 0,
      requestCount: metrics.totalRequests || 0,
      failureCount: metrics.totalFailures || 0,
      lastFailureReason: reason,
      resetTime: newState === 'OPEN' ? new Date(Date.now() + 30000) : undefined, // Default reset timeout
    });

    // Fire-and-forget event publishing (don't block circuit operations)
    this.eventPublisher.publish(stateChangeEvent).catch(error => {
      this.logger.warn({
        message: 'Failed to publish circuit breaker state change event',
        error: error.message,
        serviceName: this.serviceName,
        stateChange: `${previousState} -> ${newState}`,
      });
    });
  }

  /**
   * Execute a function with circuit breaker protection.
   * If the Circuit was constructed without a default action, the function must be passed here.
   * @param fn Function to execute
   * @param args Arguments for the function
   * @returns Result of the function
   * @throws Error if circuit is open or function fails/times out
   */
  async execute<T>(fn?: (...args: any[]) => Promise<T>, ...args: any[]): Promise<T> {
    if (fn) {
      // Call the circuit breaker with the function as the first argument
      return this.opossumInstance.fire(fn, ...args) as Promise<T>;
    }
    // This assumes the action was provided in the constructor
    return this.opossumInstance.fire(...args) as Promise<T>;
  }

  /**
   * Get the current state of the circuit breaker from Opossum's perspective.
   * Opossum states: 'closed', 'open', 'halfOpen'.
   * We map these to our CircuitBreakerState enum for consistency if needed, or use Opossum's directly.
   * @returns Circuit breaker state string ('closed', 'open', 'halfOpen')
   */
  getState(): 'closed' | 'open' | 'halfOpen' {
    if (this.opossumInstance.opened) return 'open';
    if (this.opossumInstance.halfOpen) return 'halfOpen';
    return 'closed';
  }
  
  // For compatibility with tests that expect enum values
  getStateEnum(): CircuitBreakerState {
    if (this.opossumInstance.opened) return CircuitBreakerState.OPEN;
    if (this.opossumInstance.halfOpen) return CircuitBreakerState.HALF_OPEN;
    return CircuitBreakerState.CLOSED;
  }

  /**
   * Get metrics for the circuit breaker from Opossum's status snapshot.
   * @returns Circuit breaker metrics (simplified for now, Opossum provides rich stats)
   */
  getMetrics(): CircuitBreakerMetrics {
    const currentStats = this.opossumInstance.status.stats;
    return {
      state: this.getStateEnum(),
      failures: currentStats.failures, // Failures in the current window or since last reset
      successes: currentStats.successes, // Successes in the current window or since last reset
      totalRequests: currentStats.fires, // Total times .fire() was called
      totalSuccesses: currentStats.successes, // Opossum's 'successes' is more like recent/windowed
      totalFailures: currentStats.failures,   // Opossum's 'failures' is more like recent/windowed
      // Opossum provides detailed stats like latencyMean, percentiles, etc.
      // For simplicity, we'll map what's straightforward. More detailed mapping can be added.
      errorRate: currentStats.fires > 0 ? currentStats.failures / currentStats.fires : 0, // Approximate error rate
      lastFailureTime: null, // Opossum doesn't directly expose lastFailureTime in status.snapshot in this format
      nextAttemptTime: null, // Opossum manages this internally; not directly exposed in status.snapshot
    };
  }

  /**
   * Manually open the circuit.
   */
  open(): void {
    this.opossumInstance.open();
    this.logger.warn(`Circuit for ${this.serviceName} manually opened.`);
  }

  /**
   * Manually close the circuit.
   */
  close(): void {
    this.opossumInstance.close();
    this.logger.log(`Circuit for ${this.serviceName} manually closed.`);
  }

  /**
   * Reset the circuit breaker (clears stats and closes the circuit).
   * In Opossum, 'close()' also resets stats if it was open or half-open.
   */
  reset(): void {
    const previousState = this.getState();
    this.close(); // Closing an Opossum circuit effectively resets its stats and state.
    this.logger.log(`Circuit breaker for ${this.serviceName} has been reset from ${previousState} to closed.`);
    
    // Publish reset event for monitoring
    if (this.eventPublisher && previousState !== 'closed') {
      const resetEvent = EventFactory.circuitBreakerStateChanged({
        serviceName: this.serviceName,
        previousState: previousState === 'open' ? 'OPEN' : 'HALF_OPEN',
        newState: 'CLOSED',
        errorRate: 0,
        requestCount: 0,
        failureCount: 0,
        lastFailureReason: 'Manual reset',
        resetTime: new Date(),
      });
      
      this.eventPublisher.publish(resetEvent).catch(error => {
        this.logger.warn({
          message: 'Failed to publish circuit breaker reset event',
          error: error.message,
          serviceName: this.serviceName,
        });
      });
    }
  }

  /**
   * Get the underlying Opossum instance if direct access is needed.
   */
  getOpossumInstance(): Opossum {
    return this.opossumInstance;
  }
}

