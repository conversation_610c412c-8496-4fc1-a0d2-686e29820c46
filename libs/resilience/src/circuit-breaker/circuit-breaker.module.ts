import { Module, DynamicModule } from '@nestjs/common';
import { CircuitBreakerService } from './circuit-breaker.service';
import { EventPublisher } from '@libs/messaging';

/**
 * Module for circuit breaker functionality
 */
@Module({})
export class CircuitBreakerModule {
  /**
   * Register the circuit breaker module
   * @returns Dynamic module
   */
  static register(): DynamicModule {
    return {
      module: CircuitBreakerModule,
      providers: [
        {
          provide: CircuitBreakerService,
          useFactory: (loggerFactory: any, eventPublisher?: EventPublisher) => {
            return new CircuitBreakerService(loggerFactory, eventPublisher);
          },
          inject: [
            'LOGGER_FACTORY',
            { token: 'EVENT_PUBLISHER', optional: true },
          ],
        },
      ],
      exports: [
        CircuitBreakerService,
      ],
    };
  }
}
