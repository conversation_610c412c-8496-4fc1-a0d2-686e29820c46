import { Injectable, Inject, Optional } from '@nestjs/common';
import { ObservabilityLogger } from '@libs/observability';
import { EventPublisher } from '@libs/messaging';
import { Circuit, DEFAULT_OPOSSUM_OPTIONS } from './circuit';
import { OpossumCircuitBreakerOptions, CircuitBreakerState, CircuitBreakerMetrics } from './circuit-breaker.types';

/**
 * Service for managing circuit breakers
 */
@Injectable()
export class CircuitBreakerService {
  private circuits: Map<string, Circuit> = new Map();

  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    @Optional() private readonly eventPublisher?: EventPublisher,
  ) {
    // Get a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger('CircuitBreakerService');
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Get or create a circuit breaker for a service
   * @param serviceName Name of the service
   * @param options Circuit breaker options
   * @returns Circuit breaker instance
   */
  getCircuitBreaker(serviceName: string, options?: Partial<OpossumCircuitBreakerOptions>): Circuit {
    if (!this.circuits.has(serviceName)) {
      this.logger.log(`Creating circuit breaker for service: ${serviceName}`);
      const circuitOptions = { ...DEFAULT_OPOSSUM_OPTIONS, ...options };
      this.circuits.set(serviceName, new Circuit(serviceName, circuitOptions, this.loggerFactory, this.eventPublisher));
    }
    return this.circuits.get(serviceName)!;
  }

  /**
   * Get the status of all circuit breakers
   * @returns Map of service names to circuit breaker states and metrics
   */
  getStatus(): Record<string, { state: CircuitBreakerState; metrics: CircuitBreakerMetrics }> {
    const status: Record<string, { state: CircuitBreakerState; metrics: CircuitBreakerMetrics }> = {};

    this.circuits.forEach((circuit, serviceName) => {
      status[serviceName] = {
        state: this.mapOpossumStateToEnum(circuit.getState()),
        metrics: circuit.getMetrics(),
      };
    });

    return status;
  }

  /**
   * Reset a specific circuit breaker
   * @param serviceName Name of the service
   */
  resetCircuitBreaker(serviceName: string): void {
    const circuit = this.circuits.get(serviceName);
    if (circuit) {
      circuit.reset();
      this.logger.log(`Reset circuit breaker for service: ${serviceName}`);
    } else {
      this.logger.warn(`Attempted to reset non-existent circuit breaker: ${serviceName}`);
    }
  }

  /**
   * Reset all circuit breakers
   */
  private mapOpossumStateToEnum(opossumState: 'closed' | 'open' | 'halfOpen'): CircuitBreakerState {
    switch (opossumState) {
      case 'open':
        return CircuitBreakerState.OPEN;
      case 'halfOpen':
        return CircuitBreakerState.HALF_OPEN;
      case 'closed':
      default:
        return CircuitBreakerState.CLOSED;
    }
  }

  resetAllCircuitBreakers(): void {
    this.circuits.forEach((circuit, serviceName) => {
      circuit.reset();
    });
    this.logger.log(`Reset all circuit breakers`);
  }

  /**
   * Get summary of all circuit breaker states for monitoring
   */
  getHealthSummary(): {
    total: number;
    open: number;
    halfOpen: number;
    closed: number;
    openServices: string[];
    healthyServices: string[];
  } {
    const status = this.getStatus();
    const openServices: string[] = [];
    const healthyServices: string[] = [];
    
    let open = 0;
    let halfOpen = 0;
    let closed = 0;
    
    Object.entries(status).forEach(([serviceName, { state }]) => {
      switch (state) {
        case CircuitBreakerState.OPEN:
          open++;
          openServices.push(serviceName);
          break;
        case CircuitBreakerState.HALF_OPEN:
          halfOpen++;
          break;
        case CircuitBreakerState.CLOSED:
          closed++;
          healthyServices.push(serviceName);
          break;
      }
    });
    
    return {
      total: this.circuits.size,
      open,
      halfOpen,
      closed,
      openServices,
      healthyServices,
    };
  }

  /**
   * Check if any circuit breakers are in unhealthy states
   */
  hasUnhealthyCircuits(): boolean {
    const summary = this.getHealthSummary();
    return summary.open > 0;
  }

  /**
   * Get services with open circuit breakers that may need attention
   */
  getServicesNeedingAttention(): string[] {
    return this.getHealthSummary().openServices;
  }
}
