/**
 * Circuit breaker states
 */
export enum CircuitBreakerState {
  CLOSED = 'CLOSED',   // Normal operation, requests pass through
  OPEN = 'OPEN',       // Circuit is open, requests fail fast
  HALF_OPEN = 'HALF_OPEN', // Testing if service is back online
}

/**
 * Circuit breaker options
 */
export interface CircuitBreakerOptions {
  /** @deprecated Use OpossumCircuitBreakerOptions instead. Kept for potential mapping. */
  /**
   * Number of failures before opening circuit
   * @default 5
   */
  failureThreshold: number;
  
  /**
   * Time in ms to wait before trying again (half-open)
   * @default 30000 (30 seconds)
   */
  resetTimeout: number;
  
  /**
   * Time window in ms to count failures
   * @default 60000 (60 seconds)
   */
  failureWindow: number;
  
  /**
   * Number of successes in half-open state to close circuit
   * @default 2
   */
  successThreshold: number;
}

/**
 * Options for Opossum circuit breaker.
 * See Opossum documentation for full details: https://github.com/nodeshift/opossum
 */
export interface OpossumCircuitBreakerOptions {
  /**
   * The maximum time in milliseconds for an action to run before timing out.
   * @default 3000 (Opossum default), but our DEFAULT_OPOSSUM_OPTIONS sets it to 10000
   */
  timeout?: number;
  /**
   * The error rate percentage at which to open the circuit.
   * @default 50
   */
  errorThresholdPercentage?: number;
  /**
   * The time in milliseconds to wait before attempting to close the circuit (half-open state).
   * @default 30000
   */
  resetTimeout?: number;
  /**
   * The number of consecutive successful executions in halfOpen state required to close the circuit.
   * @default 10 (Opossum's 'capacity' option)
   */
  capacity?: number; 
  /**
   * The time in milliseconds that a rolling statistical window lasts.
   * @default 10000 (Opossum default)
   */
  rollingCountTimeout?: number;
  /**
   * The number of buckets in the rolling statistical window.
   * @default 10 (Opossum default)
   */
  rollingCountBuckets?: number;
  /**
   * A name for the circuit, used in metrics and events.
   */
  name?: string;
  /**
   * A group for the circuit, used for grouping circuits.
   */
  group?: string;
  /**
   * Whether to enable volume threshold. If true, the circuit will not open until a minimum number of requests have been made.
   * @default false (Opossum default is 0, meaning no threshold)
   */
  volumeThreshold?: number; 
  /**
   * Function to be called when the circuit rejects a call (e.g., when it's open).
   * @default undefined
   */
  fallback?: (...args: any[]) => any;
  /**
   * Allow the circuit to be opened/closed/halfOpen manually.
   * @default false
   */
  allowWarmUp?: boolean;
  // Add other Opossum options as needed
}


/**
 * Circuit breaker metrics, adapted for Opossum.
 */
export interface CircuitBreakerMetrics {
  /**
   * Current number of failures
   */
  failures: number;
  
  /**
   * Current number of consecutive successes
   */
  successes: number;
  
  /**
   * Current state of the circuit breaker
   */
  state: CircuitBreakerState;
  
  /**
   * Timestamp of the last failure (Note: Opossum does not directly expose this in status.snapshot).
   * This field might be null or represent a different metric if adapted.
   */
  lastFailureTime: string | null;
  
  /**
   * Timestamp of the next attempt (Note: Opossum manages this internally).
   * This field might be null.
   */
  nextAttemptTime: string | null;
  
  /**
   * Total number of requests processed
   */
  totalRequests: number;
  
  /**
   * Total number of successful requests
   */
  totalSuccesses: number;
  
  /**
   * Total number of failed requests
   */
  totalFailures: number;
  
  /**
   * Error rate (failures / total requests)
   */
  errorRate: number;
}
