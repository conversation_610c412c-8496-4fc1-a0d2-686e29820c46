import { Circuit, DEFAULT_OPTIONS } from '../src/circuit-breaker/circuit';
import { CircuitBreakerState } from '../src/circuit-breaker/circuit-breaker.types';

describe('Circuit - Basic Functionality', () => {
  let circuit: Circuit;
  const mockLoggerFactory = {
    createLogger: jest.fn().mockReturnValue({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Use Opossum-compatible options for testing
    const opossumOptions = {
      errorThresholdPercentage: 50,
      rollingCountTimeout: 1000,
      resetTimeout: 1000,
      timeout: 5000
    };
    circuit = new Circuit('test-service', opossumOptions, mockLoggerFactory);
  });

  it('should be defined', () => {
    expect(circuit).toBeDefined();
  });

  it('should start in CLOSED state', () => {
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should execute function successfully in CLOSED state', async () => {
    const mockFn = jest.fn().mockResolvedValue('success');
    const result = await circuit.execute(mockFn);
    
    expect(result).toBe('success');
    expect(mockFn).toHaveBeenCalled();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should provide metrics', async () => {
    const mockFn = jest.fn().mockResolvedValue('success');
    
    // Execute a few successful calls
    await circuit.execute(mockFn);
    await circuit.execute(mockFn);
    
    const metrics = circuit.getMetrics();
    
    expect(metrics).toBeDefined();
    expect(metrics.totalRequests).toBeGreaterThan(0);
    expect(metrics.state).toBe(CircuitBreakerState.CLOSED);
  });

  it('should manually open and close circuit', () => {
    // Test manual open
    circuit.open();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);
    
    // Test manual close
    circuit.close();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should reset circuit breaker', () => {
    // Open the circuit
    circuit.open();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);
    
    // Reset should close it
    circuit.reset();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should reject calls when circuit is open', async () => {
    circuit.open();
    
    await expect(circuit.execute(() => Promise.resolve('test')))
      .rejects.toThrow('Breaker is open');
  });
});