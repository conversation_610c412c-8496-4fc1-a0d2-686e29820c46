import { Circuit, DEFAULT_OPTIONS } from '../src/circuit-breaker/circuit';
import { CircuitBreakerState } from '../src/circuit-breaker/circuit-breaker.types';

describe('Circuit', () => {
  let circuit: Circuit;
  const mockLoggerFactory = {
    createLogger: jest.fn().mockReturnValue({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    }),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Use Opossum-compatible options for testing
    // Higher error threshold to allow multiple failures before opening
    const opossumOptions = {
      errorThresholdPercentage: 80, // Allow more failures before opening
      rollingCountTimeout: 10000,
      resetTimeout: DEFAULT_OPTIONS.resetTimeout,
      timeout: DEFAULT_OPTIONS.timeoutMs,
      capacity: 1, // Number of successful calls needed to close from half-open
      rollingCountBuckets: 10 // More granular failure tracking
    };
    circuit = new Circuit('test-service', opossumOptions, mockLoggerFactory);
  });

  it('should be defined', () => {
    expect(circuit).toBeDefined();
  });

  it('should start in CLOSED state', () => {
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should execute function successfully in CLOSED state', async () => {
    const mockFn = jest.fn().mockResolvedValue('success');
    const result = await circuit.execute(mockFn);
    
    expect(result).toBe('success');
    expect(mockFn).toHaveBeenCalled();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should record failure and stay CLOSED if below threshold', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));
    
    // Execute with failure but stay below threshold
    // With 80% threshold, we need multiple calls where failures are < 80%
    await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
    expect(mockFn).toHaveBeenCalledTimes(2);
  });

  it('should transition to OPEN after failure threshold is reached', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));
    
    // Execute with multiple failures to exceed 80% threshold
    // Need enough calls to trigger the circuit opening
    for (let i = 0; i < 10; i++) {
      try {
        await circuit.execute(mockFn);
      } catch (error) {
        // Expect either the original error or "Breaker is open"
        expect(error.message).toMatch(/test error|Breaker is open/);
      }
    }
    
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);
    
    // Next call should fail fast without calling the function
    await expect(circuit.execute(mockFn)).rejects.toThrow('Breaker is open');
  });

  it('should transition to HALF_OPEN after reset timeout', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));
    
    // Execute with failure until threshold is reached
    for (let i = 0; i < DEFAULT_OPTIONS.failureThreshold; i++) {
      await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    }
    
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);
    
    // Mock Date.now to simulate time passing
    const originalNow = Date.now;
    const mockNow = jest.fn().mockReturnValue(Date.now() + DEFAULT_OPTIONS.resetTimeout + 1000);
    global.Date.now = mockNow;
    
    // Next call should go through (HALF_OPEN state)
    mockFn.mockResolvedValueOnce('success');
    const result = await circuit.execute(mockFn);
    
    expect(result).toBe('success');
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.HALF_OPEN);
    
    // Restore Date.now
    global.Date.now = originalNow;
  });

  it('should transition back to OPEN if failure in HALF_OPEN state', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));
    
    // Execute with failure until threshold is reached
    for (let i = 0; i < DEFAULT_OPTIONS.failureThreshold; i++) {
      await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    }
    
    expect(circuit.getState()).toBe(CircuitBreakerState.OPEN);
    
    // Mock Date.now to simulate time passing
    const originalNow = Date.now;
    const mockNow = jest.fn().mockReturnValue(Date.now() + DEFAULT_OPTIONS.resetTimeout + 1000);
    global.Date.now = mockNow;
    
    // Next call should fail (in HALF_OPEN state)
    await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    
    expect(circuit.getState()).toBe(CircuitBreakerState.OPEN);
    
    // Restore Date.now
    global.Date.now = originalNow;
  });

  it('should transition to CLOSED after success threshold in HALF_OPEN state', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));
    
    // Execute with failure until threshold is reached
    for (let i = 0; i < DEFAULT_OPTIONS.failureThreshold; i++) {
      await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    }
    
    expect(circuit.getState()).toBe(CircuitBreakerState.OPEN);
    
    // Mock Date.now to simulate time passing
    const originalNow = Date.now;
    const mockNow = jest.fn().mockReturnValue(Date.now() + DEFAULT_OPTIONS.resetTimeout + 1000);
    global.Date.now = mockNow;
    
    // Switch to success for the required number of calls
    mockFn.mockResolvedValue('success');
    
    // Execute with success until threshold is reached
    for (let i = 0; i < DEFAULT_OPTIONS.successThreshold; i++) {
      await circuit.execute(mockFn);
    }
    
    expect(circuit.getState()).toBe(CircuitBreakerState.CLOSED);
    
    // Restore Date.now
    global.Date.now = originalNow;
  });

  it('should reset the circuit breaker', async () => {
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));
    
    // Execute with failure until threshold is reached
    for (let i = 0; i < DEFAULT_OPTIONS.failureThreshold; i++) {
      await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    }
    
    expect(circuit.getState()).toBe(CircuitBreakerState.OPEN);
    
    // Reset the circuit
    circuit.reset();
    
    expect(circuit.getState()).toBe(CircuitBreakerState.CLOSED);
    
    // Next call should go through
    mockFn.mockResolvedValueOnce('success');
    const result = await circuit.execute(mockFn);
    
    expect(result).toBe('success');
  });

  it('should provide metrics', async () => {
    const mockFn = jest.fn().mockResolvedValue('success');
    
    // Execute a few successful calls
    await circuit.execute(mockFn);
    await circuit.execute(mockFn);
    
    // Execute a few failed calls
    mockFn.mockRejectedValue(new Error('test error'));
    await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    
    const metrics = circuit.getMetrics();
    
    expect(metrics).toBeDefined();
    expect(metrics.totalRequests).toBe(4);
    expect(metrics.totalSuccesses).toBe(2);
    expect(metrics.totalFailures).toBe(2);
    expect(metrics.errorRate).toBe(0.5);
  });
});
