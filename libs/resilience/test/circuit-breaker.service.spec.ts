import { Test, TestingModule } from '@nestjs/testing';
import { CircuitBreakerService } from '../src/circuit-breaker/circuit-breaker.service';
import { CircuitBreakerState } from '../src/circuit-breaker/circuit-breaker.types';

describe('CircuitBreakerService', () => {
  let service: CircuitBreakerService;
  const mockLoggerFactory = {
    createLogger: jest.fn().mockReturnValue({
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CircuitBreakerService,
        {
          provide: 'LOGGER_FACTORY',
          useValue: mockLoggerFactory,
        },
      ],
    }).compile();

    service = module.get<CircuitBreakerService>(CircuitBreakerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create a circuit breaker', () => {
    const circuit = service.getCircuitBreaker('test-service');
    expect(circuit).toBeDefined();
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should return the same circuit breaker for the same service', () => {
    const circuit1 = service.getCircuitBreaker('test-service');
    const circuit2 = service.getCircuitBreaker('test-service');
    expect(circuit1).toBe(circuit2);
  });

  it('should create different circuit breakers for different services', () => {
    const circuit1 = service.getCircuitBreaker('service-1');
    const circuit2 = service.getCircuitBreaker('service-2');
    expect(circuit1).not.toBe(circuit2);
  });

  it('should create a circuit breaker with custom options', async () => {
    const customOptions = {
      failureThreshold: 3,
      resetTimeout: 10000,
      failureWindow: 30000,
      successThreshold: 1,
    };

    const circuit = service.getCircuitBreaker('custom-service', customOptions);
    expect(circuit).toBeDefined();

    // We can't directly test the options, but we can test the behavior
    // by causing failures and checking the state transitions
    const mockFn = jest.fn().mockRejectedValue(new Error('test error'));

    // Execute with failure until threshold is reached
    for (let i = 0; i < customOptions.failureThreshold; i++) {
      await expect(circuit.execute(mockFn)).rejects.toThrow('test error');
    }

    // After 3 failures, the circuit should be OPEN
    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);
  });

  it('should get status of all circuit breakers', async () => {
    // Create a few circuit breakers
    const circuit1 = service.getCircuitBreaker('service-1');
    const circuit2 = service.getCircuitBreaker('service-2');

    // Execute some calls
    await circuit1.execute(() => Promise.resolve('success'));
    await expect(circuit2.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');

    const status = service.getStatus();

    expect(status).toBeDefined();
    expect(status['service-1']).toBeDefined();
    expect(status['service-2']).toBeDefined();
    expect(status['service-1'].state).toBe(CircuitBreakerState.CLOSED);
    expect(status['service-2'].metrics.totalFailures).toBe(1);
  });

  it('should reset a specific circuit breaker', async () => {
    // Create a circuit breaker and cause failures
    const circuit = service.getCircuitBreaker('reset-test', { errorThresholdPercentage: 1, rollingCountBuckets: 1, rollingCountTimeout: 100, capacity: 1 });

    // Cause multiple failures to open the circuit
    await expect(circuit.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');
    await expect(circuit.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');

    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.OPEN);

    // Reset the circuit
    service.resetCircuitBreaker('reset-test');

    expect(circuit.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });

  it('should reset all circuit breakers', async () => {
    // Create a few circuit breakers and cause failures
    const circuit1 = service.getCircuitBreaker('reset-all-1', { errorThresholdPercentage: 1, rollingCountBuckets: 1, rollingCountTimeout: 100 });
    const circuit2 = service.getCircuitBreaker('reset-all-2', { errorThresholdPercentage: 1, rollingCountBuckets: 1, rollingCountTimeout: 100 });

    // Cause multiple failures to open both circuits
    await expect(circuit1.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');
    await expect(circuit1.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');
    await expect(circuit2.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');
    await expect(circuit2.execute(() => Promise.reject(new Error('test error')))).rejects.toThrow('test error');

    expect(circuit1.getStateEnum()).toBe(CircuitBreakerState.OPEN);
    expect(circuit2.getStateEnum()).toBe(CircuitBreakerState.OPEN);

    // Reset all circuits
    service.resetAllCircuitBreakers();

    expect(circuit1.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
    expect(circuit2.getStateEnum()).toBe(CircuitBreakerState.CLOSED);
  });
});
