{"name": "@libs/resilience", "version": "1.0.0", "description": "Resilience patterns for microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "rimraf dist && rimraf tsconfig.build.tsbuildinfo && rimraf tsconfig.tsbuildinfo && tsc -p tsconfig.build.json --listEmittedFiles", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "dependencies": {"@libs/observability": "file:../observability", "@nestjs/common": "^10.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "opossum": "^8.1.0"}, "devDependencies": {"@nestjs/testing": "^10.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.8.3", "@types/opossum": "^6.2.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "moduleNameMapper": {"^@libs/observability$": "<rootDir>/../observability/src", "^@libs/observability/(.*)$": "<rootDir>/../observability/src/$1"}, "globals": {"ts-jest": {"tsconfig": "test/tsconfig.json"}}}}