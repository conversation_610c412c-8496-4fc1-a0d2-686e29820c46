{"name": "@libs/error-handling", "version": "1.0.0", "description": "Comprehensive error handling library with correlation ID support for microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rimraf dist && tsc -p tsconfig.build.json", "dev": "tsc -p tsconfig.build.json --watch", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "keywords": ["error-handling", "correlation-id", "observability", "<PERSON><PERSON><PERSON>", "microservices"], "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@libs/observability": "*", "express": "^4.18.0", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/testing": "^10.0.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/uuid": "^9.0.0", "@libs/testing-utils": "file:../testing-utils", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"tsconfig": "test/tsconfig.json"}]}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "moduleNameMapper": {"^@libs/observability$": "<rootDir>/../observability/src", "^@libs/observability/(.*)$": "<rootDir>/../observability/src/$1", "^@libs/testing-utils$": "<rootDir>/../testing-utils/src", "^@libs/testing-utils/(.*)$": "<rootDir>/../testing-utils/src/$1"}}}