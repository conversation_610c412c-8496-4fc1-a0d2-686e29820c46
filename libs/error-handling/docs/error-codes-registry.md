# Error Codes Registry

## Overview
This document provides a comprehensive registry of all error codes used in our Netflix-style error handling system. Each error code includes HTTP status mapping, usage guidelines, and frontend display recommendations.

## Error Code Format
- **Machine-readable**: ALL_CAPS_UNDERSCORE format for API responses
- **Human-readable**: Descriptive messages for user display
- **Correlation tracking**: All errors include correlation IDs for debugging

## Authentication & Authorization Errors

### UNAUTHORIZED (401)
- **When to use**: Invalid or missing authentication tokens
- **Frontend action**: Redirect to login page
- **Example**: `{"error": "UNAUTHORIZED", "message": "Invalid or expired authentication token", "correlationId": "req-1749578400-abc123"}`

### INVALID_CREDENTIALS (401)
- **When to use**: Wrong email/password during login
- **Frontend action**: Show validation error, allow retry
- **Example**: Login attempts with incorrect credentials

### TOKEN_EXPIRED (401)
- **When to use**: JWT token has expired, refresh needed
- **Frontend action**: Attempt token refresh, fallback to login
- **Example**: API calls with expired access tokens

### FORBIDDEN (403)
- **When to use**: Valid authentication but insufficient permissions
- **Frontend action**: Show "access denied" message
- **Example**: Regular user accessing admin endpoints

## User Management Errors

### USER_NOT_FOUND (404)
- **When to use**: Requested user doesn't exist in system
- **Frontend action**: Show "user not found" message
- **Example**: GET /users/invalid-id

### EMAIL_ALREADY_EXISTS (409)
- **When to use**: Registration with existing email address
- **Frontend action**: Show validation error with suggestion
- **Example**: User registration with duplicate email

### USER_INACTIVE (403)
- **When to use**: User account is disabled or suspended
- **Frontend action**: Show account status message with support contact
- **Example**: Login attempt with deactivated account

## Validation & Request Errors

### VALIDATION_FAILED (400)
- **When to use**: Request body validation errors
- **Frontend action**: Show field-specific validation messages
- **Example**: Missing required fields, invalid email format
- **Details**: Includes field-level error breakdown in development

### INVALID_INPUT (400)
- **When to use**: Malformed request parameters
- **Frontend action**: Show input format requirements
- **Example**: Invalid UUID format for user ID

### RATE_LIMIT_EXCEEDED (429)
- **When to use**: Client exceeded rate limiting thresholds
- **Frontend action**: Show retry delay, implement exponential backoff
- **Example**: Too many login attempts from same IP

## Service & Infrastructure Errors

### INTERNAL_SERVER_ERROR (500)
- **When to use**: Unexpected server errors, fallback error type
- **Frontend action**: Show generic error message with correlation ID
- **Example**: Database connection failures, unhandled exceptions

### SERVICE_UNAVAILABLE (503)
- **When to use**: Dependent service is temporarily unavailable
- **Frontend action**: Show maintenance message, suggest retry
- **Example**: Keycloak service downtime, database maintenance

### KEYCLOAK_CONNECTION_ERROR (502)
- **When to use**: Authentication service communication failures
- **Frontend action**: Show auth service unavailable message
- **Example**: Keycloak server timeout, network connectivity issues

## Business Logic Errors

### INSUFFICIENT_PERMISSIONS (403)
- **When to use**: User lacks specific business permissions
- **Frontend action**: Show permission requirement details
- **Example**: Non-admin user trying to delete users

### RESOURCE_CONFLICT (409)
- **When to use**: Operation conflicts with current resource state
- **Frontend action**: Show conflict details, suggest resolution
- **Example**: Attempting to delete user with active sessions

### OPERATION_NOT_ALLOWED (400)
- **When to use**: Valid request but operation not permitted in current context
- **Frontend action**: Show context-specific guidance
- **Example**: Trying to activate already active user

## Frontend Integration Guide

### Error Response Structure
```typescript
interface ErrorResponse {
  statusCode: number;         // HTTP status code
  error: string;             // Machine-readable error code
  message: string;           // Human-readable message
  correlationId: string;     // Request tracking ID
  timestamp: string;         // ISO timestamp
  path: string;             // Request path
  logQuery?: string;        // Direct link to logs (dev only)
  details?: any;            // Additional context (dev only)
}
```

### TypeScript Error Handling Utility
```typescript
export class ApiErrorHandler {
  // Check if error is retryable
  static isRetryable(error: ErrorResponse): boolean {
    const retryableCodes = [
      'INTERNAL_SERVER_ERROR',
      'SERVICE_UNAVAILABLE', 
      'KEYCLOAK_CONNECTION_ERROR',
      'RATE_LIMIT_EXCEEDED'
    ];
    return retryableCodes.includes(error.error);
  }

  // Get user-friendly message
  static getUserMessage(error: ErrorResponse): string {
    const messages = {
      'UNAUTHORIZED': 'Please log in to continue',
      'INVALID_CREDENTIALS': 'Invalid email or password',
      'USER_NOT_FOUND': 'User not found',
      'EMAIL_ALREADY_EXISTS': 'An account with this email already exists',
      'VALIDATION_FAILED': 'Please check your input and try again',
      'RATE_LIMIT_EXCEEDED': 'Too many attempts. Please wait before trying again',
      'SERVICE_UNAVAILABLE': 'Service is temporarily unavailable. Please try again later'
    };
    return messages[error.error as keyof typeof messages] || 'An unexpected error occurred';
  }

  // Extract correlation ID for support
  static getCorrelationId(error: ErrorResponse): string {
    return error.correlationId;
  }

  // Get retry delay for rate limiting
  static getRetryDelay(error: ErrorResponse): number {
    if (error.error === 'RATE_LIMIT_EXCEEDED') {
      return 5000; // 5 seconds
    }
    return 1000; // 1 second default
  }
}
```

### React Error Boundary Example
```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: ErrorResponse;
}

export class ApiErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: any): ErrorBoundaryState {
    return {
      hasError: true,
      error: error.response?.data
    };
  }

  render() {
    if (this.state.hasError && this.state.error) {
      const userMessage = ApiErrorHandler.getUserMessage(this.state.error);
      const correlationId = ApiErrorHandler.getCorrelationId(this.state.error);
      
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>{userMessage}</p>
          <details>
            <summary>Technical Details</summary>
            <p>Correlation ID: {correlationId}</p>
            <p>Please provide this ID when contacting support</p>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Backend Usage Examples

### Controller Error Handling
```typescript
@Controller('users')
export class UsersController {
  constructor(
    private readonly errorBuilder: ErrorResponseBuilderService
  ) {}

  @Get(':id')
  @ApiErrorResponse('USER_NOT_FOUND', 'User with specified ID not found')
  @ApiErrorResponse('INTERNAL_SERVER_ERROR', 'Internal server error')
  async getUser(@Param('id') id: string): Promise<UserDto> {
    const user = await this.usersService.findById(id);
    if (!user) {
      throw this.errorBuilder.createHttpException(
        'USER_NOT_FOUND',
        `User with ID ${id} not found`
      );
    }
    return user;
  }
}
```

### Service Layer Error Handling
```typescript
@Injectable()
export class AuthService {
  constructor(
    private readonly keycloakErrorHandler: KeycloakErrorHandlerService
  ) {}

  async loginUser(credentials: LoginDto): Promise<TokenResponse> {
    try {
      return await this.keycloak.authenticate(credentials);
    } catch (error) {
      // Automatic error transformation with correlation tracking
      throw this.keycloakErrorHandler.handleAuthenticationError(error, 'loginUser');
    }
  }
}
```

## Monitoring & Analytics

### Error Rate Thresholds
- **Green**: < 1% error rate
- **Yellow**: 1-5% error rate  
- **Red**: > 5% error rate

### Alert Conditions
- Authentication errors > 10/minute
- User management errors > 5/minute
- Service unavailable errors > 1/minute
- Any error rate > 10% for 5 minutes

### Business Impact Metrics
- Critical path errors (auth, user creation)
- User-facing vs system errors
- Error resolution time with correlation IDs

## Support & Debugging

### For Support Teams
1. **Get correlation ID** from user
2. **Use correlation debug dashboard** in Grafana
3. **Follow request timeline** across services
4. **Identify root cause** from error details

### For Developers
1. **Check error code registry** for proper usage
2. **Use TypeScript validation** for error codes
3. **Test error scenarios** in development
4. **Monitor error patterns** in production

### For Frontend Teams
1. **Implement error boundaries** for graceful degradation
2. **Use correlation IDs** for support tickets
3. **Implement retry logic** for retryable errors
4. **Provide user-friendly messages** for all error types