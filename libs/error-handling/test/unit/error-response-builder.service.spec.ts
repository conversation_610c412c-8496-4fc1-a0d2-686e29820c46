import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorResponseBuilderService } from '../../src/services/error-response-builder.service';
import { CorrelationService } from '../../src/services/correlation.service';
import { ErrorResponse } from '../../src/interfaces/error-response.interface';
import { MockFactory } from '@libs/testing-utils';

describe('ErrorResponseBuilderService Unit Tests', () => {
  let service: ErrorResponseBuilderService;
  let correlationService: CorrelationService;
  let mockCorrelationService: any;

  beforeEach(async () => {
    mockCorrelationService = {
      getContext: jest.fn(),
      generateCorrelationId: jest.fn().mockReturnValue('test-correlation-123'),
      getRequestDuration: jest.fn().mockReturnValue(150)
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ErrorResponseBuilderService,
        { provide: CorrelationService, useValue: mockCorrelationService }
      ],
    }).compile();

    service = module.get<ErrorResponseBuilderService>(ErrorResponseBuilderService);
    correlationService = module.get<CorrelationService>(CorrelationService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Service Initialization', () => {
    it('should initialize with default configuration', () => {
      expect(service).toBeDefined();
    });

    it('should initialize with custom configuration', () => {
      const customConfig = {
        environment: 'production' as const,
        includeSensitiveDetails: false,
        serviceName: 'custom-service'
      };

      const customService = new ErrorResponseBuilderService(
        correlationService,
        customConfig
      );

      expect(customService).toBeDefined();
    });
  });

  describe('Error Response Building from Exceptions', () => {
    beforeEach(() => {
      mockCorrelationService.getContext.mockReturnValue({
        correlationId: 'test-correlation-123',
        startTime: Date.now() - 150,
        request: {
          method: 'GET',
          path: '/api/users',
          url: 'https://api.example.com/users'
        },
        service: {
          name: 'user-service'
        }
      });
    });

    it('should build error response from HttpException', () => {
      const exception = new HttpException('User not found', HttpStatus.NOT_FOUND);
      const path = '/api/users/123';

      const response = service.buildFromException(exception, path);

      expect(response).toMatchObject({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'User not found',
          details: expect.objectContaining({
            statusCode: 404,
            path: '/api/users/123'
          })
        },
        correlationId: 'test-correlation-123',
        timestamp: expect.any(String)
      });
    });

    it('should build error response from custom error with code', () => {
      const customError = {
        name: 'ValidationError',
        message: 'Invalid user data',
        code: 'VALIDATION_FAILED',
        statusCode: 400
      };

      const response = service.buildFromException(customError, '/api/users');

      expect(response.error.code).toBe('VALIDATION_FAILED');
      expect(response.error.message).toBe('Invalid user data');
      expect(response.error.details.statusCode).toBe(400);
    });

    it('should handle Got HTTP client errors', () => {
      const gotError = {
        name: 'HTTPError',
        message: 'Response code 503 (Service Unavailable)',
        response: {
          statusCode: 503,
          statusMessage: 'Service Unavailable',
          body: { error: 'Service temporarily unavailable' }
        }
      };

      const response = service.buildFromException(gotError, '/api/external');

      expect(response.error.code).toBe('EXTERNAL_SERVICE_ERROR');
      expect(response.error.message).toContain('External service error');
      expect(response.error.details.statusCode).toBe(503);
      expect(response.error.details.upstreamError).toBeDefined();
    });

    it('should handle timeout errors from Got', () => {
      const timeoutError = {
        name: 'TimeoutError',
        message: 'Timeout awaiting \'request\' for 5000ms',
        code: 'ETIMEDOUT'
      };

      const response = service.buildFromException(timeoutError, '/api/timeout');

      expect(response.error.code).toBe('REQUEST_TIMEOUT');
      expect(response.error.message).toContain('Request timeout');
      expect(response.error.details.statusCode).toBe(408);
    });

    it('should handle network connection errors', () => {
      const connectionError = {
        name: 'RequestError',
        message: 'connect ECONNREFUSED 127.0.0.1:3000',
        code: 'ECONNREFUSED'
      };

      const response = service.buildFromException(connectionError, '/api/connection');

      expect(response.error.code).toBe('CONNECTION_ERROR');
      expect(response.error.message).toContain('Connection failed');
      expect(response.error.details.statusCode).toBe(503);
    });

    it('should default to internal server error for unknown exceptions', () => {
      const unknownError = new Error('Unknown error occurred');

      const response = service.buildFromException(unknownError, '/api/unknown');

      expect(response.error.code).toBe('INTERNAL_ERROR');
      expect(response.error.message).toBe('An internal error occurred');
      expect(response.error.details.statusCode).toBe(500);
    });

    it('should include request duration in response', () => {
      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);
      
      const response = service.buildFromException(exception, '/api/test');

      expect(response.error.details.duration).toBe(150);
    });

    it('should include Loki query links when enabled', () => {
      // Set up service with Loki links enabled
      const serviceWithLoki = new ErrorResponseBuilderService(
        correlationService,
        {
          includeLokiLinks: true,
          lokiBaseUrl: 'http://localhost:3100',
          serviceName: 'test-service'
        }
      );

      const exception = new HttpException('Test error', HttpStatus.INTERNAL_SERVER_ERROR);
      
      const response = serviceWithLoki.buildFromException(exception, '/api/test');

      expect(response.error.details.lokiQuery).toBeDefined();
      expect(response.error.details.lokiQuery).toContain('http://localhost:3100');
      expect(response.error.details.lokiQuery).toContain('test-correlation-123');
    });
  });

  describe('Environment-Based Response Filtering', () => {
    it('should include sensitive details in development environment', () => {
      const devService = new ErrorResponseBuilderService(
        correlationService,
        {
          environment: 'development',
          includeSensitiveDetails: true
        }
      );

      const exception = new Error('Database connection failed: invalid credentials');
      
      const response = devService.buildFromException(exception, '/api/test');

      expect(response.error.details.originalError).toBeDefined();
      expect(response.error.details.stack).toBeDefined();
    });

    it('should exclude sensitive details in production environment', () => {
      const prodService = new ErrorResponseBuilderService(
        correlationService,
        {
          environment: 'production',
          includeSensitiveDetails: false
        }
      );

      const exception = new Error('Database connection failed: invalid credentials');
      
      const response = prodService.buildFromException(exception, '/api/test');

      expect(response.error.details.originalError).toBeUndefined();
      expect(response.error.details.stack).toBeUndefined();
      expect(response.error.message).toBe('An internal error occurred');
    });
  });

  describe('Custom Error Response Building', () => {
    beforeEach(() => {
      mockCorrelationService.getContext.mockReturnValue({
        correlationId: 'custom-correlation-456',
        startTime: Date.now() - 200,
        request: {
          method: 'POST',
          path: '/api/custom',
          url: 'https://api.example.com/custom'
        },
        service: {
          name: 'custom-service'
        }
      });
    });

    it('should build custom error response with all fields', () => {
      const response = service.buildCustomError({
        code: 'BUSINESS_RULE_VIOLATION',
        message: 'Cannot delete user with active orders',
        statusCode: 409,
        path: '/api/users/123',
        details: {
          userId: '123',
          activeOrderCount: 5,
          reason: 'User has pending orders'
        }
      });

      expect(response).toMatchObject({
        success: false,
        error: {
          code: 'BUSINESS_RULE_VIOLATION',
          message: 'Cannot delete user with active orders',
          details: expect.objectContaining({
            statusCode: 409,
            path: '/api/users/123',
            userId: '123',
            activeOrderCount: 5,
            reason: 'User has pending orders'
          })
        },
        correlationId: 'custom-correlation-456'
      });
    });

    it('should use defaults for missing optional fields', () => {
      const response = service.buildCustomError({
        code: 'SIMPLE_ERROR',
        message: 'Simple error message'
      });

      expect(response.error.details.statusCode).toBe(500);
      expect(response.error.details.path).toBe('unknown');
      expect(response.correlationId).toBe('custom-correlation-456');
    });

    it('should include request duration in custom error response', () => {
      mockCorrelationService.getRequestDuration.mockReturnValue(275);

      const response = service.buildCustomError({
        code: 'DURATION_TEST',
        message: 'Test error for duration'
      });

      expect(response.error.details.duration).toBe(275);
    });
  });

  describe('Validation Error Handling', () => {
    it('should format validation errors correctly', () => {
      const validationException = {
        name: 'ValidationError',
        message: 'Validation failed',
        errors: [
          {
            field: 'email',
            message: 'Invalid email format',
            value: 'invalid-email'
          },
          {
            field: 'age',
            message: 'Age must be at least 18',
            value: 16
          }
        ]
      };

      const response = service.buildFromException(validationException, '/api/users');

      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.details.validationErrors).toEqual([
        {
          field: 'email',
          message: 'Invalid email format',
          value: 'invalid-email'
        },
        {
          field: 'age',
          message: 'Age must be at least 18',
          value: 16
        }
      ]);
    });

    it('should handle class-validator style errors', () => {
      const classValidatorError = {
        name: 'ValidationError',
        message: 'Validation failed',
        constraints: {
          email: {
            isEmail: 'email must be an email'
          },
          age: {
            min: 'age must not be less than 18'
          }
        }
      };

      const response = service.buildFromException(classValidatorError, '/api/validation');

      expect(response.error.code).toBe('VALIDATION_ERROR');
      expect(response.error.details.validationConstraints).toBeDefined();
    });
  });

  describe('Security and Information Leakage Prevention', () => {
    it('should sanitize database error messages in production', () => {
      const prodService = new ErrorResponseBuilderService(
        correlationService,
        { 
          environment: 'production',
          includeSensitiveDetails: false
        }
      );

      const dbError = new Error('ECONNREFUSED: Connection to database user:password@host:5432/db failed');
      
      const response = prodService.buildFromException(dbError, '/api/data');

      expect(response.error.message).not.toContain('user:password');
      expect(response.error.message).not.toContain('host:5432');
      expect(response.error.message).toBe('An internal error occurred');
    });

    it('should remove stack traces in production', () => {
      const prodService = new ErrorResponseBuilderService(
        correlationService,
        { 
          environment: 'production',
          includeSensitiveDetails: false
        }
      );

      const errorWithStack = new Error('Test error');
      errorWithStack.stack = 'Error: Test error\n    at /app/src/sensitive-file.ts:123:45';
      
      const response = prodService.buildFromException(errorWithStack, '/api/test');

      expect(response.error.details.stack).toBeUndefined();
    });

    it('should preserve debug information in development', () => {
      const devService = new ErrorResponseBuilderService(
        correlationService,
        { 
          environment: 'development',
          includeSensitiveDetails: true
        }
      );

      const errorWithDetails = new Error('Detailed error with sensitive info');
      errorWithDetails.stack = 'Error: Detailed error\n    at /app/src/debug-file.ts:456:78';
      
      const response = devService.buildFromException(errorWithDetails, '/api/debug');

      expect(response.error.message).toContain('Detailed error with sensitive info');
      expect(response.error.details.stack).toBeDefined();
      expect(response.error.details.originalError).toBeDefined();
    });
  });

  describe('Correlation Context Integration', () => {
    it('should handle missing correlation context gracefully', () => {
      mockCorrelationService.getContext.mockReturnValue(undefined);
      mockCorrelationService.getRequestDuration.mockReturnValue(-1);

      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);
      
      const response = service.buildFromException(exception, '/api/test');

      expect(response.correlationId).toBe('test-correlation-123'); // Generated fallback
      expect(response.error.details.duration).toBe(-1);
    });

    it('should use context correlation ID when available', () => {
      mockCorrelationService.getContext.mockReturnValue({
        correlationId: 'context-correlation-789',
        startTime: Date.now() - 100,
        request: {
          method: 'GET',
          path: '/api/context',
          url: 'https://api.example.com/context'
        },
        service: {
          name: 'context-service'
        }
      });

      const exception = new HttpException('Context test', HttpStatus.OK);
      
      const response = service.buildFromException(exception, '/api/context');

      expect(response.correlationId).toBe('context-correlation-789');
      expect(mockCorrelationService.generateCorrelationId).not.toHaveBeenCalled();
    });
  });
});