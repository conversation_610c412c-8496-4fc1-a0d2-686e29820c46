import { Test, TestingModule } from '@nestjs/testing';
import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import { CorrelationExceptionFilter } from '../../src/filters/correlation-exception.filter';
import { ErrorResponseBuilderService } from '../../src/services/error-response-builder.service';
import { CorrelationService } from '../../src/services/correlation.service';
import { MockFactory } from '@libs/testing-utils';

describe('CorrelationExceptionFilter Unit Tests', () => {
  let filter: CorrelationExceptionFilter;
  let mockErrorBuilder: any;
  let mockCorrelationService: any;
  let mockArgumentsHost: ArgumentsHost;
  let mockResponse: Partial<Response>;

  beforeEach(async () => {
    mockErrorBuilder = {
      buildFromException: jest.fn(),
      buildCustomError: jest.fn()
    };

    mockCorrelationService = {
      getContext: jest.fn(),
      getRequestDuration: jest.fn().mockReturnValue(125)
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      getHeader: jest.fn(),
      setHeader: jest.fn()
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
        getRequest: jest.fn().mockReturnValue({
          url: '/api/test',
          method: 'GET',
          path: '/api/test'
        })
      })
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CorrelationExceptionFilter,
        { provide: ErrorResponseBuilderService, useValue: mockErrorBuilder },
        { provide: CorrelationService, useValue: mockCorrelationService }
      ],
    }).compile();

    filter = module.get<CorrelationExceptionFilter>(CorrelationExceptionFilter);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('HTTP Exception Handling', () => {
    it('should handle standard HttpException', () => {
      const exception = new HttpException('User not found', HttpStatus.NOT_FOUND);
      const expectedResponse = {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'User not found',
          details: {
            statusCode: 404,
            path: '/api/test'
          }
        },
        correlationId: 'test-correlation-123',
        timestamp: expect.any(String)
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockErrorBuilder.buildFromException).toHaveBeenCalledWith(
        exception,
        '/api/test',
        404
      );
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });

    it('should handle HttpException with custom response object', () => {
      const customResponse = {
        statusCode: 422,
        message: ['email must be valid', 'password too short'],
        error: 'Unprocessable Entity'
      };
      const exception = new HttpException(customResponse, HttpStatus.UNPROCESSABLE_ENTITY);

      const expectedResponse = {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: {
            statusCode: 422,
            validationErrors: ['email must be valid', 'password too short']
          }
        },
        correlationId: 'test-correlation-456'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(422);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });

    it('should handle HttpException with array messages', () => {
      const exception = new HttpException(['Field is required', 'Invalid format'], HttpStatus.BAD_REQUEST);

      const expectedResponse = {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Validation failed',
          details: {
            statusCode: 400,
            messages: ['Field is required', 'Invalid format']
          }
        },
        correlationId: 'test-correlation-789'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });
  });

  describe('Generic Error Handling', () => {
    it('should handle generic Error instances', () => {
      const exception = new Error('Database connection failed');
      
      const expectedResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred',
          details: {
            statusCode: 500,
            path: '/api/test'
          }
        },
        correlationId: 'test-correlation-internal'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockErrorBuilder.buildFromException).toHaveBeenCalledWith(
        exception,
        '/api/test',
        undefined
      );
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });

    it('should handle custom error objects', () => {
      const customError = {
        name: 'CustomBusinessError',
        message: 'Business rule violation',
        code: 'BUSINESS_ERROR',
        statusCode: 409
      };

      const expectedResponse = {
        success: false,
        error: {
          code: 'BUSINESS_ERROR',
          message: 'Business rule violation',
          details: {
            statusCode: 409,
            path: '/api/test'
          }
        },
        correlationId: 'test-correlation-business'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(customError, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });

    it('should handle string exceptions', () => {
      const stringException = 'Something went wrong';

      const expectedResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred',
          details: {
            statusCode: 500,
            path: '/api/test'
          }
        },
        correlationId: 'test-correlation-string'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(stringException, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });
  });

  describe('Correlation Context Integration', () => {
    it('should use correlation context when available', () => {
      mockCorrelationService.getContext.mockReturnValue({
        correlationId: 'context-correlation-123',
        startTime: Date.now() - 150,
        request: {
          method: 'POST',
          path: '/api/users',
          url: 'https://api.example.com/users'
        },
        service: {
          name: 'user-service'
        }
      });

      const exception = new HttpException('Test error', HttpStatus.BAD_REQUEST);
      
      const expectedResponse = {
        success: false,
        error: {
          code: 'BAD_REQUEST',
          message: 'Test error',
          details: {
            statusCode: 400,
            path: '/api/test',
            duration: 125
          }
        },
        correlationId: 'context-correlation-123'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });

    it('should handle missing correlation context gracefully', () => {
      mockCorrelationService.getContext.mockReturnValue(undefined);

      const exception = new HttpException('No context error', HttpStatus.INTERNAL_SERVER_ERROR);
      
      const expectedResponse = {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred',
          details: {
            statusCode: 500,
            path: '/api/test'
          }
        },
        correlationId: 'generated-fallback-id'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.json).toHaveBeenCalledWith(expectedResponse);
    });
  });

  describe('Response Headers', () => {
    it('should set correlation ID in response header', () => {
      const exception = new HttpException('Header test', HttpStatus.OK);
      
      const expectedResponse = {
        success: false,
        error: {
          code: 'OK',
          message: 'Header test'
        },
        correlationId: 'header-test-123'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'header-test-123');
    });

    it('should set content type header', () => {
      const exception = new HttpException('Content type test', HttpStatus.BAD_REQUEST);
      
      mockErrorBuilder.buildFromException.mockReturnValue({
        success: false,
        error: { code: 'BAD_REQUEST', message: 'Content type test' },
        correlationId: 'content-type-test'
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Type', 'application/json');
    });

    it('should preserve existing correlation header from response', () => {
      mockResponse.getHeader = jest.fn().mockReturnValue('existing-correlation-header');

      const exception = new HttpException('Preserve header test', HttpStatus.FORBIDDEN);
      
      const expectedResponse = {
        correlationId: 'existing-correlation-header'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(expectedResponse);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'existing-correlation-header');
    });
  });

  describe('Request Path Extraction', () => {
    it('should extract path from request URL', () => {
      const mockRequest = {
        url: '/api/users/123?include=profile',
        method: 'GET',
        path: '/api/users/123'
      };

      mockArgumentsHost.switchToHttp = jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
        getRequest: jest.fn().mockReturnValue(mockRequest)
      });

      const exception = new HttpException('Path test', HttpStatus.NOT_FOUND);

      mockErrorBuilder.buildFromException.mockReturnValue({
        success: false,
        error: { code: 'NOT_FOUND', message: 'Path test' },
        correlationId: 'path-test'
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockErrorBuilder.buildFromException).toHaveBeenCalledWith(
        exception,
        '/api/users/123',
        404
      );
    });

    it('should handle requests without path', () => {
      const mockRequest = {
        url: '/',
        method: 'GET'
      };

      mockArgumentsHost.switchToHttp = jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
        getRequest: jest.fn().mockReturnValue(mockRequest)
      });

      const exception = new HttpException('Root path test', HttpStatus.OK);

      filter.catch(exception, mockArgumentsHost);

      expect(mockErrorBuilder.buildFromException).toHaveBeenCalledWith(
        exception,
        '/',
        200
      );
    });

    it('should fallback to unknown path when request info unavailable', () => {
      mockArgumentsHost.switchToHttp = jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
        getRequest: jest.fn().mockReturnValue({})
      });

      const exception = new HttpException('Unknown path test', HttpStatus.INTERNAL_SERVER_ERROR);

      filter.catch(exception, mockArgumentsHost);

      expect(mockErrorBuilder.buildFromException).toHaveBeenCalledWith(
        exception,
        'unknown',
        500
      );
    });
  });

  describe('Status Code Handling', () => {
    it('should extract status code from HttpException', () => {
      const exception = new HttpException('Status test', HttpStatus.UNAUTHORIZED);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
    });

    it('should default to 500 for non-HTTP exceptions', () => {
      const exception = new Error('Generic error');

      mockErrorBuilder.buildFromException.mockReturnValue({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          details: { statusCode: 500 }
        }
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
    });

    it('should handle custom status codes in error response', () => {
      const exception = new Error('Custom status error');

      mockErrorBuilder.buildFromException.mockReturnValue({
        success: false,
        error: {
          code: 'CUSTOM_ERROR',
          details: { statusCode: 418 } // I'm a teapot
        }
      });

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(418);
    });
  });

  describe('Error Response Validation', () => {
    it('should ensure response has required structure', () => {
      const exception = new HttpException('Structure test', HttpStatus.BAD_REQUEST);
      
      const malformedResponse = {
        // Missing required fields
        message: 'Incomplete response'
      };

      mockErrorBuilder.buildFromException.mockReturnValue(malformedResponse);

      filter.catch(exception, mockArgumentsHost);

      // Should still send response even if malformed
      expect(mockResponse.json).toHaveBeenCalledWith(malformedResponse);
      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });

    it('should handle null/undefined error responses', () => {
      const exception = new HttpException('Null response test', HttpStatus.INTERNAL_SERVER_ERROR);
      
      mockErrorBuilder.buildFromException.mockReturnValue(null);

      filter.catch(exception, mockArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(null);
    });
  });

  describe('Performance and Efficiency', () => {
    it('should process exceptions efficiently', () => {
      const startTime = Date.now();
      const exception = new HttpException('Performance test', HttpStatus.OK);

      mockErrorBuilder.buildFromException.mockReturnValue({
        success: false,
        error: { code: 'OK', message: 'Performance test' },
        correlationId: 'perf-test'
      });

      filter.catch(exception, mockArgumentsHost);

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(50); // Should complete within 50ms
    });

    it('should not throw errors during error handling', () => {
      const exception = new HttpException('Error in error handling', HttpStatus.INTERNAL_SERVER_ERROR);

      // Simulate error in error builder
      mockErrorBuilder.buildFromException.mockImplementation(() => {
        throw new Error('Error builder failed');
      });

      expect(() => {
        filter.catch(exception, mockArgumentsHost);
      }).not.toThrow();

      // Should still attempt to send a response
      expect(mockResponse.status).toHaveBeenCalled();
    });
  });
});