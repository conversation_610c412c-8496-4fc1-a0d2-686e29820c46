import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { KeycloakErrorHandlerService } from '../../src/services/keycloak-error-handler.service';
import { ErrorResponseBuilderService } from '../../src/services/error-response-builder.service';
import { CorrelationService } from '../../src/services/correlation.service';

describe('KeycloakErrorHandlerService Unit Tests', () => {
  let service: KeycloakErrorHandlerService;
  let mockErrorBuilder: any;
  let mockCorrelationService: any;

  beforeEach(async () => {
    mockErrorBuilder = {
      createHttpException: jest.fn(),
      buildFromException: jest.fn()
    };

    mockCorrelationService = {
      getContext: jest.fn(),
      generateCorrelationId: jest.fn().mockReturnValue('keycloak-test-123')
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeycloakErrorHandlerService,
        { provide: ErrorResponseBuilderService, useValue: mockErrorBuilder },
        { provide: CorrelationService, useValue: mockCorrelationService }
      ],
    }).compile();

    service = module.get<KeycloakErrorHandlerService>(KeycloakErrorHandlerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Authentication Error Handling', () => {
    it('should handle invalid credentials (400 - invalid_grant)', () => {
      const keycloakError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            error: 'invalid_grant',
            error_description: 'Invalid user credentials'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Invalid email or password', HttpStatus.UNAUTHORIZED)
      );

      const result = service.handleAuthenticationError(keycloakError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'INVALID_CREDENTIALS',
        'Invalid email or password'
      );
      expect(result).toBeInstanceOf(HttpException);
    });

    it('should handle malformed authentication requests (400)', () => {
      const keycloakError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            error: 'invalid_request',
            error_description: 'Missing grant_type parameter'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Invalid request to Keycloak', HttpStatus.BAD_REQUEST)
      );

      const result = service.handleAuthenticationError(keycloakError, 'token-endpoint');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'VALIDATION_FAILED',
        'Invalid request to Keycloak [token-endpoint]'
      );
    });

    it('should handle expired tokens (401 - invalid_token)', () => {
      const keycloakError = {
        isAxiosError: true,
        response: {
          status: 401,
          data: {
            error: 'invalid_token',
            error_description: 'Token expired'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Your session has expired, please log in again', HttpStatus.UNAUTHORIZED)
      );

      const result = service.handleAuthenticationError(keycloakError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'TOKEN_EXPIRED',
        'Your session has expired, please log in again'
      );
    });

    it('should handle unauthorized access (401)', () => {
      const keycloakError = {
        isAxiosError: true,
        response: {
          status: 401,
          data: {
            error: 'unauthorized',
            error_description: 'Invalid client credentials'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Authentication failed', HttpStatus.UNAUTHORIZED)
      );

      const result = service.handleAuthenticationError(keycloakError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'AUTHENTICATION_FAILED',
        'Authentication failed'
      );
    });

    it('should handle insufficient permissions (403)', () => {
      const keycloakError = {
        isAxiosError: true,
        response: {
          status: 403,
          data: {
            error: 'insufficient_scope',
            error_description: 'Insufficient permissions for this operation'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Insufficient permissions for this operation', HttpStatus.FORBIDDEN)
      );

      const result = service.handleAuthenticationError(keycloakError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'INSUFFICIENT_PERMISSIONS',
        'Insufficient permissions for this operation'
      );
    });

    it('should handle Keycloak server errors (500)', () => {
      const keycloakError = {
        isAxiosError: true,
        response: {
          status: 500,
          data: {
            error: 'server_error',
            error_description: 'Keycloak internal server error'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Authentication service temporarily unavailable', HttpStatus.SERVICE_UNAVAILABLE)
      );

      const result = service.handleAuthenticationError(keycloakError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'AUTH_SERVICE_ERROR',
        'Authentication service temporarily unavailable'
      );
    });

    it('should handle network errors', () => {
      const networkError = {
        isAxiosError: true,
        code: 'ECONNREFUSED',
        message: 'connect ECONNREFUSED 127.0.0.1:8080'
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Authentication service unavailable', HttpStatus.SERVICE_UNAVAILABLE)
      );

      const result = service.handleAuthenticationError(networkError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'AUTH_SERVICE_UNAVAILABLE',
        'Authentication service unavailable'
      );
    });

    it('should handle timeout errors', () => {
      const timeoutError = {
        isAxiosError: true,
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded'
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Authentication service timeout', HttpStatus.REQUEST_TIMEOUT)
      );

      const result = service.handleAuthenticationError(timeoutError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'AUTH_SERVICE_TIMEOUT',
        'Authentication service timeout'
      );
    });
  });

  describe('User Management Error Handling', () => {
    it('should handle user not found errors', () => {
      const userNotFoundError = {
        isAxiosError: true,
        response: {
          status: 404,
          data: {
            errorMessage: 'User not found'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('User not found', HttpStatus.NOT_FOUND)
      );

      const result = service.handleUserManagementError(userNotFoundError, 'get-user');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'USER_NOT_FOUND',
        'User not found'
      );
    });

    it('should handle user already exists errors', () => {
      const userExistsError = {
        isAxiosError: true,
        response: {
          status: 409,
          data: {
            errorMessage: 'User exists with same email'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('User with this email already exists', HttpStatus.CONFLICT)
      );

      const result = service.handleUserManagementError(userExistsError, 'create-user');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'USER_ALREADY_EXISTS',
        'User with this email already exists'
      );
    });

    it('should handle invalid user data errors', () => {
      const invalidDataError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            errorMessage: 'Invalid email format',
            errors: [
              {
                field: 'email',
                message: 'Email format is invalid'
              }
            ]
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Invalid user data provided', HttpStatus.BAD_REQUEST)
      );

      const result = service.handleUserManagementError(invalidDataError, 'update-user');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'INVALID_USER_DATA',
        'Invalid user data provided'
      );
    });
  });

  describe('Token Management Error Handling', () => {
    it('should handle token introspection errors', () => {
      const introspectionError = {
        isAxiosError: true,
        response: {
          status: 401,
          data: {
            error: 'invalid_token',
            error_description: 'Token is not active'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Invalid token provided', HttpStatus.UNAUTHORIZED)
      );

      const result = service.handleTokenError(introspectionError, 'introspect');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'INVALID_TOKEN',
        'Invalid token provided'
      );
    });

    it('should handle token refresh errors', () => {
      const refreshError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            error: 'invalid_grant',
            error_description: 'Refresh token expired'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Refresh token has expired', HttpStatus.UNAUTHORIZED)
      );

      const result = service.handleTokenError(refreshError, 'refresh');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'REFRESH_TOKEN_EXPIRED',
        'Refresh token has expired'
      );
    });

    it('should handle token revocation errors', () => {
      const revocationError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            error: 'unsupported_token_type',
            error_description: 'Token type not supported for revocation'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Token revocation failed', HttpStatus.BAD_REQUEST)
      );

      const result = service.handleTokenError(revocationError, 'revoke');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'TOKEN_REVOCATION_FAILED',
        'Token revocation failed'
      );
    });
  });

  describe('Role and Permission Error Handling', () => {
    it('should handle role assignment errors', () => {
      const roleError = {
        isAxiosError: true,
        response: {
          status: 404,
          data: {
            errorMessage: 'Role not found'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Role not found', HttpStatus.NOT_FOUND)
      );

      const result = service.handleRoleError(roleError, 'assign-role');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'ROLE_NOT_FOUND',
        'Role not found'
      );
    });

    it('should handle insufficient permissions for role operations', () => {
      const permissionError = {
        isAxiosError: true,
        response: {
          status: 403,
          data: {
            errorMessage: 'Insufficient permissions to manage roles'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Insufficient permissions to manage roles', HttpStatus.FORBIDDEN)
      );

      const result = service.handleRoleError(permissionError, 'manage-roles');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'INSUFFICIENT_ROLE_PERMISSIONS',
        'Insufficient permissions to manage roles'
      );
    });
  });

  describe('Generic Keycloak Error Handling', () => {
    it('should handle non-Axios errors', () => {
      const genericError = new Error('Unknown Keycloak error');

      mockErrorBuilder.buildFromException.mockReturnValue({
        success: false,
        error: {
          code: 'KEYCLOAK_ERROR',
          message: 'Keycloak service error'
        },
        correlationId: 'keycloak-test-123'
      });

      const result = service.handleGenericKeycloakError(genericError, '/auth/login');

      expect(mockErrorBuilder.buildFromException).toHaveBeenCalledWith(
        genericError,
        '/auth/login'
      );
    });

    it('should include context in error messages', () => {
      const contextualError = {
        isAxiosError: true,
        response: {
          status: 500,
          data: {
            error: 'server_error'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Authentication service temporarily unavailable', HttpStatus.SERVICE_UNAVAILABLE)
      );

      const result = service.handleAuthenticationError(contextualError, 'user-login-flow');

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'AUTH_SERVICE_ERROR',
        'Authentication service temporarily unavailable'
      );
    });

    it('should handle malformed Keycloak responses', () => {
      const malformedError = {
        isAxiosError: true,
        response: {
          status: 200,
          data: 'Invalid JSON response'
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Invalid response from authentication service', HttpStatus.BAD_GATEWAY)
      );

      const result = service.handleAuthenticationError(malformedError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'INVALID_AUTH_RESPONSE',
        'Invalid response from authentication service'
      );
    });
  });

  describe('Error Classification and Mapping', () => {
    it('should classify errors correctly by HTTP status', () => {
      const testCases = [
        { status: 400, expectedCode: 'VALIDATION_FAILED' },
        { status: 401, expectedCode: 'AUTHENTICATION_FAILED' },
        { status: 403, expectedCode: 'INSUFFICIENT_PERMISSIONS' },
        { status: 404, expectedCode: 'RESOURCE_NOT_FOUND' },
        { status: 409, expectedCode: 'CONFLICT' },
        { status: 429, expectedCode: 'RATE_LIMIT_EXCEEDED' },
        { status: 500, expectedCode: 'AUTH_SERVICE_ERROR' },
        { status: 503, expectedCode: 'AUTH_SERVICE_UNAVAILABLE' }
      ];

      testCases.forEach(({ status, expectedCode }) => {
        const error = {
          isAxiosError: true,
          response: {
            status,
            data: { error: 'test_error' }
          }
        };

        mockErrorBuilder.createHttpException.mockClear();
        service.handleAuthenticationError(error);

        expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
          expectedCode,
          expect.any(String)
        );
      });
    });

    it('should preserve Keycloak error details for debugging', () => {
      const detailedError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: {
            error: 'invalid_client_credentials',
            error_description: 'Client authentication failed',
            error_uri: 'https://keycloak.example.com/docs/errors#invalid_client'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockImplementation((code, message) => {
        const exception = new HttpException(message, HttpStatus.BAD_REQUEST);
        (exception as any).keycloakDetails = detailedError.response.data;
        return exception;
      });

      const result = service.handleAuthenticationError(detailedError);

      expect((result as any).keycloakDetails).toEqual(detailedError.response.data);
    });
  });

  describe('Rate Limiting and Service Availability', () => {
    it('should handle rate limiting errors from Keycloak', () => {
      const rateLimitError = {
        isAxiosError: true,
        response: {
          status: 429,
          data: {
            error: 'too_many_requests',
            error_description: 'Rate limit exceeded'
          },
          headers: {
            'retry-after': '60'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Rate limit exceeded, please try again later', HttpStatus.TOO_MANY_REQUESTS)
      );

      const result = service.handleAuthenticationError(rateLimitError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'RATE_LIMIT_EXCEEDED',
        'Rate limit exceeded, please try again later'
      );
    });

    it('should handle service maintenance errors', () => {
      const maintenanceError = {
        isAxiosError: true,
        response: {
          status: 503,
          data: {
            error: 'service_unavailable',
            error_description: 'Keycloak is under maintenance'
          }
        }
      };

      mockErrorBuilder.createHttpException.mockReturnValue(
        new HttpException('Authentication service temporarily unavailable', HttpStatus.SERVICE_UNAVAILABLE)
      );

      const result = service.handleAuthenticationError(maintenanceError);

      expect(mockErrorBuilder.createHttpException).toHaveBeenCalledWith(
        'AUTH_SERVICE_UNAVAILABLE',
        'Authentication service temporarily unavailable'
      );
    });
  });
});