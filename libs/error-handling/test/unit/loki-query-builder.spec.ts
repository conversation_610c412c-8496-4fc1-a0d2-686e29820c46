import { LokiQueryBuilder } from '../../src/utils/loki-query-builder';

describe('LokiQueryBuilder Unit Tests', () => {
  let queryBuilder: LokiQueryBuilder;
  
  const defaultConfig = {
    baseUrl: 'http://localhost:3100',
    defaultTimeRange: 60,
    serviceLabelName: 'service',
    correlationLabelName: 'correlationId'
  };

  beforeEach(() => {
    queryBuilder = new LokiQueryBuilder(defaultConfig);
  });

  describe('Initialization and Configuration', () => {
    it('should initialize with provided configuration', () => {
      expect(queryBuilder).toBeDefined();
    });

    it('should handle custom configuration', () => {
      const customConfig = {
        baseUrl: 'https://loki.example.com',
        defaultTimeRange: 120,
        serviceLabelName: 'app',
        correlationLabelName: 'traceId'
      };

      const customBuilder = new LokiQueryBuilder(customConfig);
      expect(customBuilder).toBeDefined();
    });
  });

  describe('Basic Query Building', () => {
    it('should build query for correlation ID', () => {
      const query = queryBuilder.buildCorrelationQuery('test-correlation-123');
      
      expect(query).toContain('correlationId="test-correlation-123"');
      expect(query).toContain('http://localhost:3100');
      expect(query).toContain('range=60m');
    });

    it('should build query for service and correlation ID', () => {
      const query = queryBuilder.buildServiceCorrelationQuery('user-service', 'correlation-456');
      
      expect(query).toContain('service="user-service"');
      expect(query).toContain('correlationId="correlation-456"');
      expect(query).toContain('http://localhost:3100');
    });

    it('should build query for service logs only', () => {
      const query = queryBuilder.buildServiceQuery('auth-service');
      
      expect(query).toContain('service="auth-service"');
      expect(query).toContain('http://localhost:3100');
      expect(query).toContain('range=60m');
    });

    it('should build error logs query', () => {
      const query = queryBuilder.buildErrorQuery('payment-service', 'error-correlation-789');
      
      expect(query).toContain('service="payment-service"');
      expect(query).toContain('correlationId="error-correlation-789"');
      expect(query).toContain('level="error"');
    });
  });

  describe('Time Range Handling', () => {
    it('should use default time range when not specified', () => {
      const query = queryBuilder.buildCorrelationQuery('test-123');
      
      expect(query).toContain('range=60m');
    });

    it('should accept custom time range in minutes', () => {
      const query = queryBuilder.buildCorrelationQuery('test-123', { timeRangeMinutes: 30 });
      
      expect(query).toContain('range=30m');
    });

    it('should accept custom time range in hours', () => {
      const query = queryBuilder.buildCorrelationQuery('test-123', { timeRangeHours: 2 });
      
      expect(query).toContain('range=2h');
    });

    it('should prioritize hours over minutes when both provided', () => {
      const query = queryBuilder.buildCorrelationQuery('test-123', { 
        timeRangeMinutes: 30,
        timeRangeHours: 1
      });
      
      expect(query).toContain('range=1h');
      expect(query).not.toContain('range=30m');
    });

    it('should handle zero time ranges', () => {
      const query = queryBuilder.buildCorrelationQuery('test-123', { timeRangeMinutes: 0 });
      
      expect(query).toContain('range=60m'); // Should fallback to default
    });
  });

  describe('Label Filtering', () => {
    it('should build query with log level filter', () => {
      const query = queryBuilder.buildServiceQuery('api-service', { 
        logLevel: 'warn' 
      });
      
      expect(query).toContain('service="api-service"');
      expect(query).toContain('level="warn"');
    });

    it('should build query with multiple filters', () => {
      const query = queryBuilder.buildServiceQuery('order-service', {
        logLevel: 'error',
        customLabels: {
          environment: 'production',
          version: '1.2.0'
        }
      });
      
      expect(query).toContain('service="order-service"');
      expect(query).toContain('level="error"');
      expect(query).toContain('environment="production"');
      expect(query).toContain('version="1.2.0"');
    });

    it('should handle empty custom labels', () => {
      const query = queryBuilder.buildServiceQuery('test-service', {
        customLabels: {}
      });
      
      expect(query).toContain('service="test-service"');
      expect(query).not.toContain('=""'); // No empty label filters
    });
  });

  describe('URL Encoding and Special Characters', () => {
    it('should handle correlation IDs with special characters', () => {
      const correlationId = 'req-2024-01-01_12:30:45-abc123';
      const query = queryBuilder.buildCorrelationQuery(correlationId);
      
      expect(query).toContain(encodeURIComponent('correlationId="req-2024-01-01_12:30:45-abc123"'));
    });

    it('should handle service names with hyphens and underscores', () => {
      const serviceName = 'user-management_service';
      const query = queryBuilder.buildServiceQuery(serviceName);
      
      expect(query).toContain('service="user-management_service"');
    });

    it('should properly encode query parameters', () => {
      const query = queryBuilder.buildServiceCorrelationQuery(
        'test-service',
        'correlation-with-special-chars!@#$'
      );
      
      // URL should be properly encoded
      expect(query).toContain('http://localhost:3100');
      expect(query).toContain(encodeURIComponent('{'));
      expect(query).toContain(encodeURIComponent('}'));
    });
  });

  describe('Query Structure Validation', () => {
    it('should generate valid Loki query syntax', () => {
      const query = queryBuilder.buildServiceCorrelationQuery('test-service', 'test-correlation');
      
      // Should contain proper LogQL syntax
      expect(query).toMatch(/\{[^}]+\}/); // Contains label matchers in braces
      expect(query).toContain('service="test-service"');
      expect(query).toContain('correlationId="test-correlation"');
    });

    it('should separate multiple labels with commas', () => {
      const query = queryBuilder.buildServiceQuery('multi-label-service', {
        logLevel: 'info',
        customLabels: {
          component: 'auth',
          operation: 'login'
        }
      });
      
      // Should have proper comma separation
      const labelSection = query.match(/\{([^}]+)\}/)?.[1];
      expect(labelSection).toContain(',');
      expect(labelSection?.split(',').length).toBeGreaterThan(1);
    });

    it('should handle single label queries', () => {
      const query = queryBuilder.buildServiceQuery('single-service');
      
      expect(query).toContain('{service="single-service"}');
      expect(query).not.toContain(',,'); // No double commas
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle null correlation ID', () => {
      const query = queryBuilder.buildCorrelationQuery(null as any);
      
      expect(query).toContain('correlationId=""');
    });

    it('should handle undefined service name', () => {
      const query = queryBuilder.buildServiceQuery(undefined as any);
      
      expect(query).toContain('service=""');
    });

    it('should handle empty strings', () => {
      const query = queryBuilder.buildServiceCorrelationQuery('', '');
      
      expect(query).toContain('service=""');
      expect(query).toContain('correlationId=""');
    });

    it('should handle very long correlation IDs', () => {
      const longCorrelationId = 'a'.repeat(500);
      const query = queryBuilder.buildCorrelationQuery(longCorrelationId);
      
      expect(query).toContain(`correlationId="${longCorrelationId}"`);
      expect(query.length).toBeGreaterThan(500);
    });
  });

  describe('Custom Configuration Labels', () => {
    it('should use custom label names', () => {
      const customBuilder = new LokiQueryBuilder({
        baseUrl: 'http://custom-loki:3100',
        defaultTimeRange: 30,
        serviceLabelName: 'app_name',
        correlationLabelName: 'trace_id'
      });

      const query = customBuilder.buildServiceCorrelationQuery('my-app', 'trace-123');
      
      expect(query).toContain('app_name="my-app"');
      expect(query).toContain('trace_id="trace-123"');
      expect(query).not.toContain('service=');
      expect(query).not.toContain('correlationId=');
    });

    it('should handle custom base URL with path', () => {
      const customBuilder = new LokiQueryBuilder({
        baseUrl: 'https://loki.example.com/loki',
        defaultTimeRange: 60,
        serviceLabelName: 'service',
        correlationLabelName: 'correlationId'
      });

      const query = customBuilder.buildCorrelationQuery('test-123');
      
      expect(query).toContain('https://loki.example.com/loki');
    });
  });

  describe('Query Link Generation', () => {
    it('should generate complete Loki explore URL', () => {
      const query = queryBuilder.buildCorrelationQuery('explore-test');
      
      expect(query).toContain('/explore');
      expect(query).toContain('orgId=1');
      expect(query).toContain('left=');
    });

    it('should include proper query parameters', () => {
      const query = queryBuilder.buildServiceQuery('param-test', {
        timeRangeMinutes: 45
      });
      
      expect(query).toContain('range=45m');
      expect(query).toContain('expr=');
    });

    it('should generate clickable URLs', () => {
      const query = queryBuilder.buildCorrelationQuery('clickable-test');
      
      expect(query).toMatch(/^https?:\/\//); // Starts with http:// or https://
      expect(query).not.toContain(' '); // No spaces in URL
    });
  });

  describe('Performance and Optimization', () => {
    it('should build queries efficiently', () => {
      const startTime = Date.now();
      
      for (let i = 0; i < 100; i++) {
        queryBuilder.buildServiceCorrelationQuery(`service-${i}`, `correlation-${i}`);
      }
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(100); // Should complete 100 queries within 100ms
    });

    it('should handle concurrent query building', () => {
      const queries = Array.from({ length: 10 }, (_, i) => 
        queryBuilder.buildCorrelationQuery(`concurrent-test-${i}`)
      );
      
      expect(queries).toHaveLength(10);
      queries.forEach((query, index) => {
        expect(query).toContain(`correlationId="concurrent-test-${index}"`);
      });
    });
  });

  describe('Integration with Different Loki Versions', () => {
    it('should generate queries compatible with modern LogQL', () => {
      const query = queryBuilder.buildErrorQuery('modern-service', 'modern-correlation');
      
      // Should use modern LogQL syntax
      expect(query).toContain('{');
      expect(query).toContain('}');
      expect(query).toContain('=');
      expect(query).not.toContain('|~'); // Not using deprecated regex syntax
    });

    it('should handle label values that need escaping', () => {
      const query = queryBuilder.buildServiceQuery('service-with-quotes', {
        customLabels: {
          message: 'Error: "Connection failed"',
          path: '/api/users?filter={"active":true}'
        }
      });
      
      expect(query).toContain('service="service-with-quotes"');
      // Values with quotes should be properly handled
      expect(query).toBeDefined();
    });
  });
});