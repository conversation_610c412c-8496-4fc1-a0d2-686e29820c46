import { Test, TestingModule } from '@nestjs/testing';
import { CorrelationService } from '../../src/services/correlation.service';
import { CorrelationContext } from '../../src/interfaces/correlation-context.interface';

describe('CorrelationService Unit Tests', () => {
  let service: CorrelationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CorrelationService],
    }).compile();

    service = module.get<CorrelationService>(CorrelationService);
  });

  afterEach(() => {
    // Clear any async local storage context
    service.clearContext();
  });

  describe('Correlation ID Generation', () => {
    it('should generate correlation ID with correct format', () => {
      const correlationId = service.generateCorrelationId();
      
      expect(correlationId).toMatch(/^req-\d+-[a-f0-9]{8}$/);
      expect(correlationId).toContain('req-');
    });

    it('should generate unique correlation IDs', () => {
      const id1 = service.generateCorrelationId();
      const id2 = service.generateCorrelationId();
      
      expect(id1).not.toBe(id2);
    });

    it('should include timestamp in correlation ID', () => {
      const beforeTime = Date.now();
      const correlationId = service.generateCorrelationId();
      const afterTime = Date.now();
      
      const timestampPart = correlationId.split('-')[1];
      const timestamp = parseInt(timestampPart);
      
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(timestamp).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('Context Creation', () => {
    it('should create context with provided correlation ID', () => {
      const correlationId = 'test-correlation-123';
      const context = service.createContext({
        correlationId,
        request: {
          method: 'GET',
          path: '/api/users',
          url: 'https://api.example.com/users'
        },
        service: {
          name: 'user-service',
          version: '1.0.0'
        }
      });

      expect(context.correlationId).toBe(correlationId);
      expect(context.request.method).toBe('GET');
      expect(context.request.path).toBe('/api/users');
      expect(context.service.name).toBe('user-service');
      expect(context.startTime).toBeDefined();
      expect(context.metadata).toEqual({});
    });

    it('should generate correlation ID when not provided', () => {
      const context = service.createContext({
        request: {
          method: 'POST',
          path: '/api/orders',
          url: 'https://api.example.com/orders'
        },
        service: {
          name: 'order-service'
        }
      });

      expect(context.correlationId).toMatch(/^req-\d+-[a-f0-9]{8}$/);
      expect(context.request.method).toBe('POST');
      expect(context.service.name).toBe('order-service');
    });

    it('should include user context when provided', () => {
      const userContext = {
        userId: 'user-123',
        roles: ['admin', 'user']
      };

      const context = service.createContext({
        request: {
          method: 'DELETE',
          path: '/api/users/123',
          url: 'https://api.example.com/users/123'
        },
        user: userContext,
        service: {
          name: 'user-service'
        }
      });

      expect(context.user).toEqual(userContext);
      expect(context.user?.userId).toBe('user-123');
      expect(context.user?.roles).toEqual(['admin', 'user']);
    });

    it('should include custom metadata', () => {
      const metadata = {
        clientVersion: '2.1.0',
        feature: 'user-profile',
        experimentId: 'exp-456'
      };

      const context = service.createContext({
        request: {
          method: 'PATCH',
          path: '/api/profile',
          url: 'https://api.example.com/profile'
        },
        service: {
          name: 'profile-service'
        },
        metadata
      });

      expect(context.metadata).toEqual(metadata);
    });

    it('should set startTime when creating context', () => {
      const beforeTime = Date.now();
      const context = service.createContext({
        request: {
          method: 'GET',
          path: '/health',
          url: 'https://api.example.com/health'
        },
        service: {
          name: 'health-service'
        }
      });
      const afterTime = Date.now();

      expect(context.startTime).toBeGreaterThanOrEqual(beforeTime);
      expect(context.startTime).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('Context Management', () => {
    it('should store and retrieve context', async () => {
      const testContext: CorrelationContext = {
        correlationId: 'test-123',
        startTime: Date.now(),
        request: {
          method: 'GET',
          path: '/test',
          url: 'https://api.test.com/test'
        },
        service: {
          name: 'test-service'
        },
        metadata: {}
      };

      await service.runWithContext(testContext, async () => {
        const retrievedContext = service.getContext();
        expect(retrievedContext).toEqual(testContext);
        expect(retrievedContext?.correlationId).toBe('test-123');
      });
    });

    it('should return undefined when no context is set', () => {
      const context = service.getContext();
      expect(context).toBeUndefined();
    });

    it('should isolate context between different executions', async () => {
      const context1: CorrelationContext = {
        correlationId: 'test-123',
        startTime: Date.now(),
        request: { method: 'GET', path: '/test1', url: 'https://api.test.com/test1' },
        service: { name: 'test-service-1' },
        metadata: {}
      };

      const context2: CorrelationContext = {
        correlationId: 'test-456',
        startTime: Date.now(),
        request: { method: 'POST', path: '/test2', url: 'https://api.test.com/test2' },
        service: { name: 'test-service-2' },
        metadata: {}
      };

      const promises = [
        service.runWithContext(context1, async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
          const ctx = service.getContext();
          expect(ctx?.correlationId).toBe('test-123');
          expect(ctx?.service.name).toBe('test-service-1');
        }),
        service.runWithContext(context2, async () => {
          await new Promise(resolve => setTimeout(resolve, 25));
          const ctx = service.getContext();
          expect(ctx?.correlationId).toBe('test-456');
          expect(ctx?.service.name).toBe('test-service-2');
        })
      ];

      await Promise.all(promises);
    });

    it('should calculate request duration correctly', async () => {
      const testContext: CorrelationContext = {
        correlationId: 'duration-test',
        startTime: Date.now(),
        request: { method: 'GET', path: '/duration-test', url: 'https://api.test.com/duration-test' },
        service: { name: 'duration-service' },
        metadata: {}
      };

      await service.runWithContext(testContext, async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const duration = service.getRequestDuration();
        expect(duration).toBeGreaterThanOrEqual(100);
        expect(duration).toBeLessThan(200); // Should complete within reasonable time
      });
    });

    it('should return -1 for duration when no context exists', () => {
      const duration = service.getRequestDuration();
      expect(duration).toBe(-1);
    });

    it('should clear context properly', async () => {
      const testContext: CorrelationContext = {
        correlationId: 'clear-test',
        startTime: Date.now(),
        request: { method: 'GET', path: '/clear-test', url: 'https://api.test.com/clear-test' },
        service: { name: 'clear-service' },
        metadata: {}
      };

      await service.runWithContext(testContext, async () => {
        expect(service.getContext()).toBeDefined();
        service.clearContext();
        expect(service.getContext()).toBeUndefined();
      });
    });
  });

  describe('Context Enrichment', () => {
    it('should enrich context with additional metadata', async () => {
      const initialContext: CorrelationContext = {
        correlationId: 'enrich-test',
        startTime: Date.now(),
        request: { method: 'PUT', path: '/enrich', url: 'https://api.test.com/enrich' },
        service: { name: 'enrich-service' },
        metadata: { initial: 'value' }
      };

      await service.runWithContext(initialContext, async () => {
        service.enrichContext({
          operation: 'user-update',
          resourceId: 'user-456'
        });

        const enrichedContext = service.getContext();
        expect(enrichedContext?.metadata).toEqual({
          initial: 'value',
          operation: 'user-update',
          resourceId: 'user-456'
        });
      });
    });

    it('should handle enriching context when no context exists', () => {
      expect(() => {
        service.enrichContext({ test: 'value' });
      }).not.toThrow();

      // Should still return undefined since no context was set
      const context = service.getContext();
      expect(context).toBeUndefined();
    });

    it('should overwrite existing metadata keys when enriching', async () => {
      const initialContext: CorrelationContext = {
        correlationId: 'overwrite-test',
        startTime: Date.now(),
        request: { method: 'GET', path: '/overwrite', url: 'https://api.test.com/overwrite' },
        service: { name: 'overwrite-service' },
        metadata: { 
          operation: 'initial-operation',
          unchanged: 'stays-same'
        }
      };

      await service.runWithContext(initialContext, async () => {
        service.enrichContext({
          operation: 'updated-operation',
          newField: 'new-value'
        });

        const enrichedContext = service.getContext();
        expect(enrichedContext?.metadata).toEqual({
          operation: 'updated-operation',
          unchanged: 'stays-same',
          newField: 'new-value'
        });
      });
    });
  });

  describe('Async Context Propagation', () => {
    it('should propagate context through async operations', async () => {
      const testContext: CorrelationContext = {
        correlationId: 'async-test',
        startTime: Date.now(),
        request: { method: 'GET', path: '/async', url: 'https://api.test.com/async' },
        service: { name: 'async-service' },
        metadata: {}
      };

      await service.runWithContext(testContext, async () => {
        // Simulate async database call
        const dbResult = await simulateAsyncDatabaseCall();
        expect(dbResult.correlationId).toBe('async-test');

        // Simulate async HTTP call
        const httpResult = await simulateAsyncHttpCall();
        expect(httpResult.correlationId).toBe('async-test');
      });

      async function simulateAsyncDatabaseCall() {
        await new Promise(resolve => setTimeout(resolve, 10));
        const context = service.getContext();
        return { 
          data: 'db-result',
          correlationId: context?.correlationId
        };
      }

      async function simulateAsyncHttpCall() {
        await new Promise(resolve => setTimeout(resolve, 20));
        const context = service.getContext();
        return {
          data: 'http-result',
          correlationId: context?.correlationId
        };
      }
    });

    it('should maintain context through nested async operations', async () => {
      const testContext: CorrelationContext = {
        correlationId: 'nested-async-test',
        startTime: Date.now(),
        request: { method: 'POST', path: '/nested', url: 'https://api.test.com/nested' },
        service: { name: 'nested-service' },
        metadata: {}
      };

      await service.runWithContext(testContext, async () => {
        const level1Result = await level1Operation();
        expect(level1Result.level3CorrelationId).toBe('nested-async-test');
      });

      async function level1Operation() {
        await new Promise(resolve => setTimeout(resolve, 5));
        const level2Result = await level2Operation();
        return {
          level: 1,
          level2CorrelationId: level2Result.level3CorrelationId,
          level3CorrelationId: level2Result.level3CorrelationId
        };
      }

      async function level2Operation() {
        await new Promise(resolve => setTimeout(resolve, 5));
        const level3Result = await level3Operation();
        return {
          level: 2,
          level3CorrelationId: level3Result.correlationId
        };
      }

      async function level3Operation() {
        await new Promise(resolve => setTimeout(resolve, 5));
        const context = service.getContext();
        return {
          level: 3,
          correlationId: context?.correlationId
        };
      }
    });
  });
});