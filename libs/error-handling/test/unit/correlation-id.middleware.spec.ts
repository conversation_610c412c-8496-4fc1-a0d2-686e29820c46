import { Test, TestingModule } from '@nestjs/testing';
import { Request, Response, NextFunction } from 'express';
import { CorrelationIdMiddleware } from '../../src/middleware/correlation-id.middleware';
import { CorrelationService } from '../../src/services/correlation.service';
import { MockFactory } from '@libs/testing-utils';

describe('CorrelationIdMiddleware Unit Tests', () => {
  let middleware: CorrelationIdMiddleware;
  let mockCorrelationService: any;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(async () => {
    mockCorrelationService = {
      generateCorrelationId: jest.fn().mockReturnValue('generated-123'),
      createContext: jest.fn(),
      runWithContext: jest.fn((context, callback) => callback()),
      clearContext: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CorrelationIdMiddleware,
        { provide: CorrelationService, useValue: mockCorrelationService }
      ],
    }).compile();

    middleware = module.get<CorrelationIdMiddleware>(CorrelationIdMiddleware);

    mockRequest = {
      headers: {},
      method: 'GET',
      path: '/api/test',
      url: 'https://api.example.com/test',
      ip: '127.0.0.1',
      get: jest.fn()
    };

    mockResponse = {
      setHeader: jest.fn(),
      locals: {}
    };

    mockNext = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Correlation ID Header Processing', () => {
    it('should use existing correlation ID from X-Correlation-ID header', async () => {
      mockRequest.headers = {
        'x-correlation-id': 'existing-correlation-456'
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          correlationId: 'existing-correlation-456'
        })
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'existing-correlation-456');
    });

    it('should use existing correlation ID from x-request-id header', async () => {
      mockRequest.headers = {
        'x-request-id': 'request-id-789'
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          correlationId: 'request-id-789'
        })
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'request-id-789');
    });

    it('should prioritize X-Correlation-ID over X-Request-ID', async () => {
      mockRequest.headers = {
        'x-correlation-id': 'correlation-priority',
        'x-request-id': 'request-secondary'
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          correlationId: 'correlation-priority'
        })
      );
    });

    it('should generate new correlation ID when none provided', async () => {
      mockRequest.headers = {};

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.generateCorrelationId).toHaveBeenCalled();
      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          correlationId: 'generated-123'
        })
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'generated-123');
    });

    it('should handle case-insensitive header names', async () => {
      mockRequest.headers = {
        'X-CORRELATION-ID': 'uppercase-header'
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          correlationId: 'uppercase-header'
        })
      );
    });
  });

  describe('Context Creation', () => {
    it('should create context with request information', async () => {
      mockRequest.method = 'POST';
      mockRequest.path = '/api/users';
      mockRequest.url = 'https://api.example.com/users';

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith({
        correlationId: 'generated-123',
        request: {
          method: 'POST',
          path: '/api/users',
          url: 'https://api.example.com/users'
        },
        service: {
          name: process.env.SERVICE_NAME || 'unknown-service',
          version: process.env.SERVICE_VERSION
        },
        metadata: {
          userAgent: undefined,
          ipAddress: '127.0.0.1'
        }
      });
    });

    it('should include user agent when provided', async () => {
      mockRequest.headers = {
        'user-agent': 'Mozilla/5.0 (Test Browser)'
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            userAgent: 'Mozilla/5.0 (Test Browser)'
          })
        })
      );
    });

    it('should use environment variables for service information', async () => {
      const originalServiceName = process.env.SERVICE_NAME;
      const originalServiceVersion = process.env.SERVICE_VERSION;

      process.env.SERVICE_NAME = 'test-service';
      process.env.SERVICE_VERSION = '2.1.0';

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          service: {
            name: 'test-service',
            version: '2.1.0'
          }
        })
      );

      // Restore original values
      process.env.SERVICE_NAME = originalServiceName;
      process.env.SERVICE_VERSION = originalServiceVersion;
    });

    it('should extract IP address from X-Forwarded-For header', async () => {
      mockRequest.headers = {
        'x-forwarded-for': '*************, ********'
      };
      mockRequest.ip = '127.0.0.1';

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            ipAddress: '*************' // Should use first IP from X-Forwarded-For
          })
        })
      );
    });

    it('should fallback to request.ip when no X-Forwarded-For header', async () => {
      mockRequest.ip = '***********';

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          metadata: expect.objectContaining({
            ipAddress: '***********'
          })
        })
      );
    });
  });

  describe('Context Execution', () => {
    it('should run request processing within correlation context', async () => {
      const mockContext = {
        correlationId: 'context-test',
        startTime: Date.now(),
        request: {
          method: 'GET',
          path: '/api/context',
          url: 'https://api.example.com/context'
        },
        service: {
          name: 'context-service'
        },
        metadata: {}
      };

      mockCorrelationService.createContext.mockReturnValue(mockContext);

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.runWithContext).toHaveBeenCalledWith(
        mockContext,
        expect.any(Function)
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('should call next() within the correlation context', async () => {
      let nextCalledInContext = false;

      mockCorrelationService.runWithContext.mockImplementation((context, callback) => {
        nextCalledInContext = true;
        return callback();
      });

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(nextCalledInContext).toBe(true);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should store correlation ID in response locals', async () => {
      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.locals).toEqual({
        correlationId: 'generated-123'
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle errors in context creation gracefully', async () => {
      mockCorrelationService.createContext.mockImplementation(() => {
        throw new Error('Context creation failed');
      });

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Should still call next() even if context creation fails
      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'generated-123');
    });

    it('should handle errors in context execution gracefully', async () => {
      mockCorrelationService.runWithContext.mockImplementation((context, callback) => {
        throw new Error('Context execution failed');
      });

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Should still call next() even if context execution fails
      expect(mockNext).toHaveBeenCalled();
    });

    it('should cleanup context on errors', async () => {
      mockCorrelationService.runWithContext.mockImplementation((context, callback) => {
        throw new Error('Execution error');
      });

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.clearContext).toHaveBeenCalled();
    });
  });

  describe('Header Validation', () => {
    it('should reject invalid correlation ID formats', async () => {
      mockRequest.headers = {
        'x-correlation-id': 'invalid<>characters'
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Should generate new ID instead of using invalid one
      expect(mockCorrelationService.generateCorrelationId).toHaveBeenCalled();
      expect(mockCorrelationService.createContext).toHaveBeenCalledWith(
        expect.objectContaining({
          correlationId: 'generated-123'
        })
      );
    });

    it('should handle very long correlation IDs', async () => {
      const longCorrelationId = 'a'.repeat(200);
      mockRequest.headers = {
        'x-correlation-id': longCorrelationId
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Should truncate or generate new ID for very long ones
      expect(mockCorrelationService.generateCorrelationId).toHaveBeenCalled();
    });

    it('should handle empty correlation ID headers', async () => {
      mockRequest.headers = {
        'x-correlation-id': ''
      };

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.generateCorrelationId).toHaveBeenCalled();
    });
  });

  describe('Performance Considerations', () => {
    it('should process headers efficiently', async () => {
      const startTime = Date.now();

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(50); // Should complete within 50ms
    });

    it('should not block request processing', async () => {
      let nextCallOrder = 0;
      let contextCallOrder = 0;

      mockNext.mockImplementation(() => {
        nextCallOrder = Date.now();
      });

      mockCorrelationService.runWithContext.mockImplementation((context, callback) => {
        contextCallOrder = Date.now();
        return callback();
      });

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(contextCallOrder).toBeLessThanOrEqual(nextCallOrder);
    });
  });

  describe('Integration with Request Flow', () => {
    it('should work with multiple sequential requests', async () => {
      // First request
      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      // Second request with different correlation ID
      mockRequest.headers = { 'x-correlation-id': 'second-request' };
      mockResponse = { setHeader: jest.fn(), locals: {} };
      mockNext = jest.fn();

      await middleware.use(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockCorrelationService.createContext).toHaveBeenCalledTimes(2);
      expect(mockCorrelationService.runWithContext).toHaveBeenCalledTimes(2);
    });

    it('should maintain context isolation between concurrent requests', async () => {
      const request1 = { ...mockRequest, headers: { 'x-correlation-id': 'req-1' } };
      const request2 = { ...mockRequest, headers: { 'x-correlation-id': 'req-2' } };
      const response1 = { setHeader: jest.fn(), locals: {} };
      const response2 = { setHeader: jest.fn(), locals: {} };
      const next1 = jest.fn();
      const next2 = jest.fn();

      const promises = [
        middleware.use(request1 as Request, response1 as Response, next1),
        middleware.use(request2 as Request, response2 as Response, next2)
      ];

      await Promise.all(promises);

      expect(response1.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'req-1');
      expect(response2.setHeader).toHaveBeenCalledWith('X-Correlation-ID', 'req-2');
      expect(response1.locals).toEqual({ correlationId: 'req-1' });
      expect(response2.locals).toEqual({ correlationId: 'req-2' });
    });
  });
});