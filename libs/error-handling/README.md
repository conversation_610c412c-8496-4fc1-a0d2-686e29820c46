# @libs/error-handling

Comprehensive error handling library with correlation ID support and Netflix-style patterns for microservices architecture.

## Overview

`@libs/error-handling` provides enterprise-grade error handling with:

- **Correlation ID Management** - Request tracking across distributed services
- **Standardized Error Responses** - Consistent error format across all services
- **Environment-Aware Filtering** - Production vs development error details
- **Loki Integration** - Automatic log correlation links for debugging
- **Got HTTP Error Handling** - Specialized handling for @libs/http errors
- **Circuit Breaker Integration** - Error handling for resilience patterns
- **Type-Safe Error Codes** - Compile-time validation of error codes
- **Swagger Documentation** - Automatic API error response documentation

## Quick Start

### Installation

```bash
yarn add @libs/error-handling
```

### Module Setup

```typescript
import { ErrorHandlingModule } from '@libs/error-handling';

@Module({
  imports: [
    ErrorHandlingModule.forRoot({
      environment: process.env.NODE_ENV,
      includeSensitiveDetails: process.env.NODE_ENV !== 'production',
      includeLokiLinks: true,
      lokiBaseUrl: process.env.LOKI_BASE_URL || 'http://loki:3100',
      serviceName: process.env.SERVICE_NAME || 'my-service',
    }),
  ],
})
export class AppModule {}
```

### Basic Usage

```typescript
import { ErrorResponseBuilderService, ERROR_CODES } from '@libs/error-handling';

@Injectable()
export class UserService {
  constructor(
    private readonly errorBuilder: ErrorResponseBuilderService,
  ) {}

  async getUser(id: string) {
    const user = await this.userRepository.findById(id);
    
    if (!user) {
      // Type-safe error with compile-time validation
      throw this.errorBuilder.createHttpException(
        ERROR_CODES.USER_NOT_FOUND,
        `User with ID ${id} not found`
      );
    }
    
    return user;
  }
}
```

## Core Features

### Correlation ID Management

Automatic request tracking across distributed services:

```typescript
import { CorrelationService, CorrelationId } from '@libs/error-handling';

@Controller('users')
export class UserController {
  constructor(
    private readonly correlationService: CorrelationService,
  ) {}

  @Get(':id')
  async getUser(
    @Param('id') id: string,
    @CorrelationId() correlationId: string  // Auto-injected
  ) {
    // Correlation ID automatically available in all logs and errors
    this.logger.log('Fetching user', { userId: id, correlationId });
    
    try {
      return await this.userService.getUser(id);
    } catch (error) {
      // Error automatically includes correlation context
      throw error;
    }
  }
}
```

### Standardized Error Responses

Consistent error format across all services:

```typescript
// All errors return this standardized format
{
  "statusCode": 404,
  "error": "USER_NOT_FOUND",
  "message": "User with ID 123 not found",
  "correlationId": "req_1234567890_abc123",
  "timestamp": "2023-12-01T10:30:00.000Z",
  "path": "/api/users/123",
  "logQuery": "http://loki:3100/loki/api/v1/query_range?query={correlationId=\"req_1234567890_abc123\"}"
}

// Development includes additional details
{
  // ... standard fields ...
  "details": {
    "stack": "Error: User not found\n    at UserService.getUser...",
    "originalError": { /* full error object */ },
    "context": { /* correlation context */ },
    "requestDuration": 150
  }
}
```

### Type-Safe Error Codes

Compile-time validation prevents invalid error codes:

```typescript
import { ERROR_CODES, ErrorResponseBuilderService } from '@libs/error-handling';

@Injectable()
export class PaymentService {
  constructor(
    private readonly errorBuilder: ErrorResponseBuilderService,
  ) {}

  async processPayment(amount: number) {
    if (amount <= 0) {
      // ✅ Compile-time validated
      throw this.errorBuilder.createHttpException(
        ERROR_CODES.VALIDATION_FAILED,
        'Amount must be greater than zero'
      );
    }

    if (amount > 10000) {
      // ✅ Type-safe with custom message
      throw this.errorBuilder.createHttpException(
        ERROR_CODES.PAYMENT_AMOUNT_EXCEEDED,
        `Amount ${amount} exceeds maximum limit`
      );
    }

    // ❌ Compile error - invalid error code
    // throw this.errorBuilder.createHttpException('INVALID_CODE');
  }
}
```

### Got HTTP Error Handling

Specialized handling for @libs/http client errors:

```typescript
import { ErrorResponseBuilderService } from '@libs/error-handling';

@Injectable()
export class ExternalApiService {
  constructor(
    private readonly httpClient: HttpClientService,
    private readonly errorBuilder: ErrorResponseBuilderService,
  ) {}

  async callExternalService(data: any) {
    try {
      const response = await this.httpClient.post('/api/process', data, {
        serviceName: 'external-service',
      });
      return response.data;
    } catch (error) {
      // Automatically handles Got errors with proper status codes
      const errorResponse = this.errorBuilder.buildFromException(
        error,
        '/api/process'
      );
      
      // Got errors are automatically transformed:
      // - 404 → USER_NOT_FOUND with proper message
      // - 429 → RATE_LIMIT_EXCEEDED
      // - 503 → SERVICE_UNAVAILABLE
      // - ECONNREFUSED → SERVICE_UNAVAILABLE with network details
      // - ETIMEDOUT → GATEWAY_TIMEOUT
      
      throw new HttpException(errorResponse, errorResponse.statusCode);
    }
  }
}
```

### Circuit Breaker Error Integration

Automatic handling of circuit breaker failures:

```typescript
try {
  const response = await this.httpClient.request('GET', '/api/data', {
    serviceName: 'payment-service',
  });
} catch (error) {
  // Circuit breaker errors automatically detected and handled
  if (error.code === 'CIRCUIT_BREAKER_OPEN') {
    // Transformed to:
    // {
    //   "statusCode": 503,
    //   "error": "SERVICE_UNAVAILABLE", 
    //   "message": "Service payment-service is temporarily unavailable (circuit breaker open)",
    //   "correlationId": "...",
    //   "logQuery": "..."
    // }
  }
}
```

### Loki Integration

Automatic log correlation for debugging:

```typescript
// Every error response includes a Loki query link
{
  "statusCode": 500,
  "error": "INTERNAL_SERVER_ERROR",
  "message": "Database connection failed",
  "correlationId": "req_1234567890_abc123",
  "logQuery": "http://loki:3100/loki/api/v1/query_range?query={correlationId=\"req_1234567890_abc123\"}&start=1701424200000000000&end=1701426000000000000"
}

// Click the logQuery URL to see all related logs in Grafana
// Includes logs from all services involved in the request
```

## Integration with Other Libraries

### HTTP Client Integration

Seamless integration with `@libs/http`:

```typescript
// Error handling service automatically recognizes Got errors
// and transforms them appropriately:

// Network errors (ECONNREFUSED, ENOTFOUND)
// → SERVICE_UNAVAILABLE with friendly message

// HTTP errors (4xx, 5xx responses)
// → Appropriate error codes with service context

// Timeout errors
// → GATEWAY_TIMEOUT with timeout details

// Circuit breaker errors
// → SERVICE_UNAVAILABLE with circuit breaker context
```

### Observability Integration

Full integration with `@libs/observability`:

```typescript
// All error logs automatically include:
// - Correlation ID for tracking
// - Request duration
// - Service context
// - Structured metadata

// Error metrics automatically recorded:
// - error_responses_total{error_code, status_code, service}
// - error_response_duration_seconds
```

### Correlation Context

Request context propagation:

```typescript
import { CorrelationService } from '@libs/error-handling';

@Injectable()
export class BusinessService {
  constructor(
    private readonly correlationService: CorrelationService,
  ) {}

  async processRequest() {
    // Get current correlation context
    const context = this.correlationService.getContext();
    
    console.log({
      correlationId: context.correlationId,
      traceId: context.traceId,
      spanId: context.spanId,
      service: context.service,
      user: context.user,
      requestDuration: this.correlationService.getRequestDuration()
    });
    
    // Context automatically propagated to:
    // - All logs
    // - Error responses  
    // - HTTP requests via addCorrelationToRequestOptions()
    // - Metrics and tracing
  }
}
```

## Advanced Usage

### Global Exception Filter

Catch all unhandled exceptions:

```typescript
import { CorrelationExceptionFilter } from '@libs/error-handling';

@Module({
  providers: [
    {
      provide: APP_FILTER,
      useClass: CorrelationExceptionFilter,
    },
  ],
})
export class AppModule {}

// All unhandled exceptions automatically include correlation context
// and proper error response format
```

### Correlation Middleware

Ensure every request has correlation tracking:

```typescript
import { CorrelationIdMiddleware } from '@libs/error-handling';

@Module({})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CorrelationIdMiddleware)
      .forRoutes('*');
  }
}

// Automatically:
// - Extracts correlation ID from headers
// - Generates new ID if none provided
// - Makes ID available throughout request lifecycle
```

### HTTP Interceptor

Add correlation to outgoing requests:

```typescript
import { CorrelationHttpInterceptor } from '@libs/error-handling';

@Module({
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: CorrelationHttpInterceptor,
    },
  ],
})
export class AppModule {}

// Automatically adds correlation headers to HTTP responses:
// x-correlation-id: req_1234567890_abc123
// x-request-duration: 150ms
```

## API Documentation

### Swagger Integration

Automatic API error documentation:

```typescript
import { ApiErrorResponse, ApiErrorResponses, CommonApiErrors } from '@libs/error-handling';

@Controller('users')
export class UserController {
  @Get(':id')
  @ApiErrorResponse(404, 'USER_NOT_FOUND', 'User with specified ID not found')
  @ApiErrorResponse(400, 'VALIDATION_FAILED', 'Invalid user ID format')
  @CommonApiErrors() // Adds 401, 403, 500 automatically
  async getUser(@Param('id') id: string) {
    // Implementation
  }

  @Post()
  @ApiErrorResponses([
    { status: 400, code: 'VALIDATION_FAILED', description: 'Invalid user data' },
    { status: 409, code: 'USER_ALREADY_EXISTS', description: 'User email already registered' },
  ])
  @CommonApiErrors()
  async createUser(@Body() userData: CreateUserDto) {
    // Implementation
  }
}
```

## Error Codes Registry

Complete list of standardized error codes:

```typescript
export const ERROR_CODES = {
  // Authentication & Authorization
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // Validation
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Resources
  NOT_FOUND: 'NOT_FOUND',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  
  // Conflicts
  CONFLICT: 'CONFLICT',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  DUPLICATE_RESOURCE: 'DUPLICATE_RESOURCE',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Server Errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  GATEWAY_TIMEOUT: 'GATEWAY_TIMEOUT',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // Business Logic
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  
  // Payment
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  PAYMENT_AMOUNT_EXCEEDED: 'PAYMENT_AMOUNT_EXCEEDED',
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
} as const;

// Each error code has corresponding:
// - HTTP status code mapping
// - Default error message
// - Type safety at compile time
```

## Environment Configuration

### Environment Variables

```env
# Error Handling Configuration
NODE_ENV=development                          # Environment mode
LOKI_BASE_URL=http://loki:3100               # Loki instance for log correlation
SERVICE_NAME=my-service                       # Service name for correlation

# Optional
INCLUDE_SENSITIVE_DETAILS=false               # Override environment-based setting
INCLUDE_LOKI_LINKS=true                       # Enable/disable Loki query links
```

## Best Practices

### Error Handling Strategy

1. **Use Type-Safe Error Codes**
   ```typescript
   // ✅ Good - compile-time validation
   throw this.errorBuilder.createHttpException(ERROR_CODES.USER_NOT_FOUND);
   
   // ❌ Avoid - no type safety
   throw this.errorBuilder.createHttpExceptionLegacy('INVALID_CODE');
   ```

2. **Provide Meaningful Messages**
   ```typescript
   // ✅ Good - specific and actionable
   throw this.errorBuilder.createHttpException(
     ERROR_CODES.VALIDATION_FAILED,
     'Email must be a valid email address'
   );
   
   // ❌ Avoid - generic and unhelpful
   throw this.errorBuilder.createHttpException(ERROR_CODES.VALIDATION_FAILED);
   ```

3. **Handle External Service Errors**
   ```typescript
   try {
     await this.externalService.call();
   } catch (error) {
     // ✅ Good - transform external errors to internal format
     const errorResponse = this.errorBuilder.buildFromException(error, '/api/external');
     throw new HttpException(errorResponse, errorResponse.statusCode);
   }
   ```

### Production Security

1. **Environment-Aware Details**
   ```typescript
   // Production: Generic error messages, no stack traces
   // Development: Detailed errors with stack traces and context
   
   // Automatically handled by configuration:
   ErrorHandlingModule.forRoot({
     environment: process.env.NODE_ENV,
     includeSensitiveDetails: process.env.NODE_ENV !== 'production',
   })
   ```

2. **Sanitize External Errors**
   ```typescript
   // External service errors are automatically sanitized:
   // - Network details removed in production
   // - Stack traces filtered
   // - Service names kept for debugging
   ```

## License

Internal library for polyrepo services.