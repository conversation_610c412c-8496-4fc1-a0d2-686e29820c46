/**
 * Frontend TypeScript Types for Error Handling
 * 
 * This file provides type-safe error handling for frontend applications
 * consuming our Netflix-style error handling API.
 */

// Core error response structure
export interface ErrorResponse {
  statusCode: number;
  error: ErrorCode;
  message: string;
  correlationId: string;
  timestamp: string;
  path: string;
  logQuery?: string;  // Development only - direct link to logs
  details?: any;      // Development only - additional error context
}

// All possible error codes (matches backend ERROR_CODES)
export type ErrorCode = 
  // Authentication & Authorization
  | 'UNAUTHORIZED'
  | 'INVALID_CREDENTIALS'
  | 'TOKEN_EXPIRED'
  | 'FORBIDDEN'
  | 'INSUFFICIENT_PERMISSIONS'
  
  // User Management
  | 'USER_NOT_FOUND'
  | 'EMAIL_ALREADY_EXISTS'
  | 'USER_INACTIVE'
  
  // Validation & Input
  | 'VALIDATION_FAILED'
  | 'INVALID_INPUT'
  | 'RATE_LIMIT_EXCEEDED'
  
  // Business Logic
  | 'RESOURCE_CONFLICT'
  | 'OPERATION_NOT_ALLOWED'
  
  // Service & Infrastructure
  | 'INTERNAL_SERVER_ERROR'
  | 'SERVICE_UNAVAILABLE'
  | 'KEYCLOAK_CONNECTION_ERROR';

// HTTP status code mapping
export const ERROR_HTTP_STATUS: Record<ErrorCode, number> = {
  // 400 series - Client errors
  'VALIDATION_FAILED': 400,
  'INVALID_INPUT': 400,
  'OPERATION_NOT_ALLOWED': 400,
  
  // 401 series - Authentication
  'UNAUTHORIZED': 401,
  'INVALID_CREDENTIALS': 401,
  'TOKEN_EXPIRED': 401,
  
  // 403 series - Authorization
  'FORBIDDEN': 403,
  'INSUFFICIENT_PERMISSIONS': 403,
  'USER_INACTIVE': 403,
  
  // 404 series - Not Found
  'USER_NOT_FOUND': 404,
  
  // 409 series - Conflict
  'EMAIL_ALREADY_EXISTS': 409,
  'RESOURCE_CONFLICT': 409,
  
  // 429 series - Rate Limiting
  'RATE_LIMIT_EXCEEDED': 429,
  
  // 500 series - Server errors
  'INTERNAL_SERVER_ERROR': 500,
  'KEYCLOAK_CONNECTION_ERROR': 502,
  'SERVICE_UNAVAILABLE': 503
};

// User-friendly error messages
export const USER_FRIENDLY_MESSAGES: Record<ErrorCode, string> = {
  'UNAUTHORIZED': 'Please log in to continue',
  'INVALID_CREDENTIALS': 'Invalid email or password',
  'TOKEN_EXPIRED': 'Your session has expired. Please log in again',
  'FORBIDDEN': 'You do not have permission to access this resource',
  'INSUFFICIENT_PERMISSIONS': 'You do not have sufficient permissions for this action',
  'USER_NOT_FOUND': 'User not found',
  'EMAIL_ALREADY_EXISTS': 'An account with this email already exists',
  'USER_INACTIVE': 'Your account is inactive. Please contact support',
  'VALIDATION_FAILED': 'Please check your input and try again',
  'INVALID_INPUT': 'Invalid input format. Please check your data',
  'RATE_LIMIT_EXCEEDED': 'Too many attempts. Please wait before trying again',
  'RESOURCE_CONFLICT': 'This operation conflicts with the current state',
  'OPERATION_NOT_ALLOWED': 'This operation is not allowed in the current context',
  'INTERNAL_SERVER_ERROR': 'An unexpected error occurred. Please try again',
  'SERVICE_UNAVAILABLE': 'Service is temporarily unavailable. Please try again later',
  'KEYCLOAK_CONNECTION_ERROR': 'Authentication service is unavailable. Please try again later'
};

// Error categories for different handling strategies
export type ErrorCategory = 'auth' | 'validation' | 'business' | 'system';

export const ERROR_CATEGORIES: Record<ErrorCode, ErrorCategory> = {
  'UNAUTHORIZED': 'auth',
  'INVALID_CREDENTIALS': 'auth',
  'TOKEN_EXPIRED': 'auth',
  'FORBIDDEN': 'auth',
  'INSUFFICIENT_PERMISSIONS': 'auth',
  'USER_NOT_FOUND': 'business',
  'EMAIL_ALREADY_EXISTS': 'business',
  'USER_INACTIVE': 'business',
  'VALIDATION_FAILED': 'validation',
  'INVALID_INPUT': 'validation',
  'RATE_LIMIT_EXCEEDED': 'system',
  'RESOURCE_CONFLICT': 'business',
  'OPERATION_NOT_ALLOWED': 'business',
  'INTERNAL_SERVER_ERROR': 'system',
  'SERVICE_UNAVAILABLE': 'system',
  'KEYCLOAK_CONNECTION_ERROR': 'system'
};

// Retryable error codes
export const RETRYABLE_ERRORS: ErrorCode[] = [
  'INTERNAL_SERVER_ERROR',
  'SERVICE_UNAVAILABLE',
  'KEYCLOAK_CONNECTION_ERROR',
  'RATE_LIMIT_EXCEEDED'
];

// Errors that should trigger logout/reauthentication
export const AUTH_ERRORS: ErrorCode[] = [
  'UNAUTHORIZED',
  'TOKEN_EXPIRED'
];

// Errors that should be shown to users vs logged only
export const USER_FACING_ERRORS: ErrorCode[] = [
  'INVALID_CREDENTIALS',
  'EMAIL_ALREADY_EXISTS',
  'USER_NOT_FOUND',
  'VALIDATION_FAILED',
  'INVALID_INPUT',
  'RATE_LIMIT_EXCEEDED',
  'INSUFFICIENT_PERMISSIONS',
  'OPERATION_NOT_ALLOWED'
];

// Type guard to check if response is an error
export function isErrorResponse(response: any): response is ErrorResponse {
  return response && 
         typeof response.statusCode === 'number' &&
         typeof response.error === 'string' &&
         typeof response.message === 'string' &&
         typeof response.correlationId === 'string';
}

// API response wrapper type
export type ApiResponse<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: ErrorResponse;
};

// Axios error wrapper
export interface AxiosErrorWithCorrelation extends Error {
  response?: {
    status: number;
    data: ErrorResponse;
  };
}

// Error handling hook types for React
export interface UseErrorHandlerOptions {
  onAuthError?: (error: ErrorResponse) => void;
  onValidationError?: (error: ErrorResponse) => void;
  onSystemError?: (error: ErrorResponse) => void;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface ErrorHandlerResult {
  handleError: (error: any) => void;
  retry: () => void;
  clearError: () => void;
  error: ErrorResponse | null;
  isRetrying: boolean;
}