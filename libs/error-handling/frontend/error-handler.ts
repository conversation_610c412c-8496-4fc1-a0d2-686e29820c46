/**
 * Frontend Error Handler Utility
 * 
 * Provides comprehensive error handling utilities for frontend applications
 * consuming our Netflix-style error handling API.
 */

import {
  ErrorResponse,
  ErrorCode,
  ErrorCategory,
  USER_FRIENDLY_MESSAGES,
  ERROR_CATEGORIES,
  RETRYABLE_ERRORS,
  AUTH_ERRORS,
  USER_FACING_ERRORS,
  isErrorResponse,
  AxiosErrorWithCorrelation
} from './types';

export class ApiErrorHandler {
  /**
   * Check if an error is retryable
   */
  static isRetryable(error: ErrorResponse): boolean {
    return RETRYABLE_ERRORS.includes(error.error);
  }

  /**
   * Check if an error should trigger logout/reauthentication
   */
  static isAuthError(error: ErrorResponse): boolean {
    return AUTH_ERRORS.includes(error.error);
  }

  /**
   * Check if an error should be shown to users
   */
  static isUserFacing(error: ErrorResponse): boolean {
    return USER_FACING_ERRORS.includes(error.error);
  }

  /**
   * Get user-friendly error message
   */
  static getUserMessage(error: ErrorResponse): string {
    return USER_FRIENDLY_MESSAGES[error.error] || 'An unexpected error occurred';
  }

  /**
   * Get error category for handling strategy
   */
  static getErrorCategory(error: ErrorResponse): ErrorCategory {
    return ERROR_CATEGORIES[error.error];
  }

  /**
   * Extract correlation ID for support tickets
   */
  static getCorrelationId(error: ErrorResponse): string {
    return error.correlationId;
  }

  /**
   * Get appropriate retry delay based on error type
   */
  static getRetryDelay(error: ErrorResponse, attempt: number = 1): number {
    if (error.error === 'RATE_LIMIT_EXCEEDED') {
      return Math.min(5000 * Math.pow(2, attempt - 1), 30000); // Exponential backoff, max 30s
    }
    return Math.min(1000 * attempt, 10000); // Linear backoff, max 10s
  }

  /**
   * Transform Axios error to standardized ErrorResponse
   */
  static fromAxiosError(axiosError: AxiosErrorWithCorrelation): ErrorResponse | null {
    if (axiosError.response?.data && isErrorResponse(axiosError.response.data)) {
      return axiosError.response.data;
    }

    // Fallback for non-standard errors
    return {
      statusCode: axiosError.response?.status || 500,
      error: 'INTERNAL_SERVER_ERROR',
      message: axiosError.message || 'An unexpected error occurred',
      correlationId: `frontend-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      timestamp: new Date().toISOString(),
      path: 'unknown' // Remove config access that doesn't exist
    };
  }

  /**
   * Create error context for logging/analytics
   */
  static createErrorContext(error: ErrorResponse, additionalContext?: Record<string, any>): object {
    return {
      correlationId: error.correlationId,
      errorCode: error.error,
      errorCategory: this.getErrorCategory(error),
      statusCode: error.statusCode,
      timestamp: error.timestamp,
      path: error.path,
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...additionalContext
    };
  }

  /**
   * Handle error with automatic categorization and actions
   */
  static handleError(
    error: any,
    options: {
      onAuthError?: (error: ErrorResponse) => void;
      onValidationError?: (error: ErrorResponse) => void;
      onBusinessError?: (error: ErrorResponse) => void;
      onSystemError?: (error: ErrorResponse) => void;
      onUnknownError?: (error: any) => void;
    } = {}
  ): void {
    const errorResponse = this.fromAxiosError(error);
    
    if (!errorResponse) {
      options.onUnknownError?.(error);
      return;
    }

    const category = this.getErrorCategory(errorResponse);

    switch (category) {
      case 'auth':
        options.onAuthError?.(errorResponse);
        break;
      case 'validation':
        options.onValidationError?.(errorResponse);
        break;
      case 'business':
        options.onBusinessError?.(errorResponse);
        break;
      case 'system':
        options.onSystemError?.(errorResponse);
        break;
      default:
        options.onUnknownError?.(error);
    }
  }
}

/**
 * Retry utility with exponential backoff
 */
export class RetryHandler {
  private attempts: number = 0;
  private maxAttempts: number;
  private baseDelay: number;

  constructor(maxAttempts: number = 3, baseDelay: number = 1000) {
    this.maxAttempts = maxAttempts;
    this.baseDelay = baseDelay;
  }

  async execute<T>(
    operation: () => Promise<T>,
    shouldRetry: (error: any) => boolean = (error) => {
      const errorResponse = ApiErrorHandler.fromAxiosError(error);
      return errorResponse ? ApiErrorHandler.isRetryable(errorResponse) : false;
    }
  ): Promise<T> {
    while (this.attempts < this.maxAttempts) {
      try {
        const result = await operation();
        this.reset();
        return result;
      } catch (error) {
        this.attempts++;
        
        if (this.attempts >= this.maxAttempts || !shouldRetry(error)) {
          this.reset();
          throw error;
        }

        const errorResponse = ApiErrorHandler.fromAxiosError(error);
        const delay = errorResponse 
          ? ApiErrorHandler.getRetryDelay(errorResponse, this.attempts)
          : this.baseDelay * this.attempts;
          
        await this.delay(delay);
      }
    }

    throw new Error('Max retry attempts exceeded');
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private reset(): void {
    this.attempts = 0;
  }

  get currentAttempt(): number {
    return this.attempts;
  }
}

/**
 * Circuit breaker for preventing cascading failures
 */
export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private maxFailures: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime < this.timeout) {
        throw new Error('Circuit breaker is open');
      } else {
        this.state = 'half-open';
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.maxFailures) {
      this.state = 'open';
    }
  }

  get isOpen(): boolean {
    return this.state === 'open';
  }

  get currentFailures(): number {
    return this.failures;
  }
}