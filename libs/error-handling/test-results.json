{"numFailedTestSuites": 6, "numFailedTests": 21, "numPassedTestSuites": 0, "numPassedTests": 0, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 5, "numTodoTests": 0, "numTotalTestSuites": 6, "numTotalTests": 21, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750368071330, "success": false, "testResults": [{"assertionResults": [], "coverage": {}, "endTime": 1750368077954, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m317\u001b[0m:\u001b[93m67\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '\"introspect\"' is not assignable to parameter of type '\"access\" | \"refresh\" | \"reset\"'.\n\n    \u001b[7m317\u001b[0m       const result = service.handleTokenError(introspectionError, 'introspect');\n    \u001b[7m   \u001b[0m \u001b[91m                                                                  ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m365\u001b[0m:\u001b[93m64\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '\"revoke\"' is not assignable to parameter of type '\"access\" | \"refresh\" | \"reset\"'.\n\n    \u001b[7m365\u001b[0m       const result = service.handleTokenError(revocationError, 'revoke');\n    \u001b[7m   \u001b[0m \u001b[91m                                                               ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m390\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'handleRoleError' does not exist on type 'KeycloakErrorHandlerService'. Did you mean 'handleTokenError'?\n\n    \u001b[7m390\u001b[0m       const result = service.handleRoleError(roleError, 'assign-role');\n    \u001b[7m   \u001b[0m \u001b[91m                             ~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/services/keycloak-error-handler.service.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m169\u001b[0m   handleTokenError(error: any, tokenType: 'access' | 'refresh' | 'reset') {\n        \u001b[7m   \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~\u001b[0m\n        'handleTokenError' is declared here.\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m413\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'handleRoleError' does not exist on type 'KeycloakErrorHandlerService'. Did you mean 'handleTokenError'?\n\n    \u001b[7m413\u001b[0m       const result = service.handleRoleError(permissionError, 'manage-roles');\n    \u001b[7m   \u001b[0m \u001b[91m                             ~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/services/keycloak-error-handler.service.ts\u001b[0m:\u001b[93m169\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m169\u001b[0m   handleTokenError(error: any, tokenType: 'access' | 'refresh' | 'reset') {\n        \u001b[7m   \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~\u001b[0m\n        'handleTokenError' is declared here.\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m435\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'handleGenericKeycloakError' does not exist on type 'KeycloakErrorHandlerService'.\n\n    \u001b[7m435\u001b[0m       const result = service.handleGenericKeycloakError(genericError, '/auth/login');\n    \u001b[7m   \u001b[0m \u001b[91m                             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m533\u001b[0m:\u001b[93m64\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'code' implicitly has an 'any' type.\n\n    \u001b[7m533\u001b[0m       mockErrorBuilder.createHttpException.mockImplementation((code, message) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                               ~~~~\u001b[0m\n    \u001b[96mtest/unit/keycloak-error-handler.service.spec.ts\u001b[0m:\u001b[93m533\u001b[0m:\u001b[93m70\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'message' implicitly has an 'any' type.\n\n    \u001b[7m533\u001b[0m       mockErrorBuilder.createHttpException.mockImplementation((code, message) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                     ~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/error-handling/test/unit/keycloak-error-handler.service.spec.ts", "startTime": 1750368077954, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750368077954, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m103\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m103\u001b[0m       expect(response.error.code).toBe('VALIDATION_FAILED');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m104\u001b[0m       expect(response.error.message).toBe('Invalid user data');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m105\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m105\u001b[0m       expect(response.error.details.statusCode).toBe(400);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m121\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m121\u001b[0m       expect(response.error.code).toBe('EXTERNAL_SERVICE_ERROR');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m122\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m122\u001b[0m       expect(response.error.message).toContain('External service error');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m123\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m123\u001b[0m       expect(response.error.details.statusCode).toBe(503);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m124\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m124\u001b[0m       expect(response.error.details.upstreamError).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m136\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m136\u001b[0m       expect(response.error.code).toBe('REQUEST_TIMEOUT');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m137\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m137\u001b[0m       expect(response.error.message).toContain('Request timeout');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m138\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m138\u001b[0m       expect(response.error.details.statusCode).toBe(408);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m150\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m150\u001b[0m       expect(response.error.code).toBe('CONNECTION_ERROR');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m151\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m151\u001b[0m       expect(response.error.message).toContain('Connection failed');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m152\u001b[0m       expect(response.error.details.statusCode).toBe(503);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m160\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m160\u001b[0m       expect(response.error.code).toBe('INTERNAL_ERROR');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m161\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m161\u001b[0m       expect(response.error.message).toBe('An internal error occurred');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m162\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m162\u001b[0m       expect(response.error.details.statusCode).toBe(500);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m170\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m170\u001b[0m       expect(response.error.details.duration).toBe(150);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m188\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m188\u001b[0m       expect(response.error.details.lokiQuery).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m189\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m189\u001b[0m       expect(response.error.details.lokiQuery).toContain('http://localhost:3100');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m190\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m190\u001b[0m       expect(response.error.details.lokiQuery).toContain('test-correlation-123');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m208\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m208\u001b[0m       expect(response.error.details.originalError).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m209\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m209\u001b[0m       expect(response.error.details.stack).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m225\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m225\u001b[0m       expect(response.error.details.originalError).toBeUndefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m226\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m226\u001b[0m       expect(response.error.details.stack).toBeUndefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m227\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m227\u001b[0m       expect(response.error.message).toBe('An internal error occurred');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m248\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildCustomError' does not exist on type 'ErrorResponseBuilderService'.\n\n    \u001b[7m248\u001b[0m       const response = service.buildCustomError({\n    \u001b[7m   \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m278\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildCustomError' does not exist on type 'ErrorResponseBuilderService'.\n\n    \u001b[7m278\u001b[0m       const response = service.buildCustomError({\n    \u001b[7m   \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m291\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildCustomError' does not exist on type 'ErrorResponseBuilderService'.\n\n    \u001b[7m291\u001b[0m       const response = service.buildCustomError({\n    \u001b[7m   \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m321\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m321\u001b[0m       expect(response.error.code).toBe('VALIDATION_ERROR');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m322\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m322\u001b[0m       expect(response.error.details.validationErrors).toEqual([\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m352\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'code' does not exist on type 'string'.\n\n    \u001b[7m352\u001b[0m       expect(response.error.code).toBe('VALIDATION_ERROR');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m353\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m353\u001b[0m       expect(response.error.details.validationConstraints).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m371\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m371\u001b[0m       expect(response.error.message).not.toContain('user:password');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m372\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m372\u001b[0m       expect(response.error.message).not.toContain('host:5432');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m373\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m373\u001b[0m       expect(response.error.message).toBe('An internal error occurred');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m390\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m390\u001b[0m       expect(response.error.details.stack).toBeUndefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m407\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type 'string'.\n\n    \u001b[7m407\u001b[0m       expect(response.error.message).toContain('Detailed error with sensitive info');\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m408\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m408\u001b[0m       expect(response.error.details.stack).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m409\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m409\u001b[0m       expect(response.error.details.originalError).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/error-response-builder.service.spec.ts\u001b[0m:\u001b[93m423\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'details' does not exist on type 'string'.\n\n    \u001b[7m423\u001b[0m       expect(response.error.details.duration).toBe(-1);\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/error-handling/test/unit/error-response-builder.service.spec.ts", "startTime": 1750368077954, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750368077954, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m130\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2540: \u001b[0mCannot assign to 'path' because it is a read-only property.\n\n    \u001b[7m130\u001b[0m       mockRequest.path = '/api/users';\n    \u001b[7m   \u001b[0m \u001b[91m                  ~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m196\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2540: \u001b[0mCannot assign to 'ip' because it is a read-only property.\n\n    \u001b[7m196\u001b[0m       mockRequest.ip = '127.0.0.1';\n    \u001b[7m   \u001b[0m \u001b[91m                  ~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m210\u001b[0m:\u001b[93m19\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2540: \u001b[0mCannot assign to 'ip' because it is a read-only property.\n\n    \u001b[7m210\u001b[0m       mockRequest.ip = '***********';\n    \u001b[7m   \u001b[0m \u001b[91m                  ~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'context' implicitly has an 'any' type.\n\n    \u001b[7m254\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m74\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'callback' implicitly has an 'any' type.\n\n    \u001b[7m254\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                         ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m288\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'context' implicitly has an 'any' type.\n\n    \u001b[7m288\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m288\u001b[0m:\u001b[93m74\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'callback' implicitly has an 'any' type.\n\n    \u001b[7m288\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                         ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m299\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'context' implicitly has an 'any' type.\n\n    \u001b[7m299\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m299\u001b[0m:\u001b[93m74\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'callback' implicitly has an 'any' type.\n\n    \u001b[7m299\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                         ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m363\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'mockImplementation' does not exist on type 'NextFunction'.\n\n    \u001b[7m363\u001b[0m       mockNext.mockImplementation(() => {\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m367\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'context' implicitly has an 'any' type.\n\n    \u001b[7m367\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m367\u001b[0m:\u001b[93m74\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'callback' implicitly has an 'any' type.\n\n    \u001b[7m367\u001b[0m       mockCorrelationService.runWithContext.mockImplementation((context, callback) => {\n    \u001b[7m   \u001b[0m \u001b[91m                                                                         ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m403\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2352: \u001b[0mConversion of type '{ setHeader: jest.Mock<any, any, any>; locals: {}; }' to type 'Response<any, Record<string, any>>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n      Type '{ setHeader: Mock<any, any, any>; locals: {}; }' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 87 more.\n\n    \u001b[7m403\u001b[0m         middleware.use(request1 as Request, response1 as Response, next1),\n    \u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation-id.middleware.spec.ts\u001b[0m:\u001b[93m404\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2352: \u001b[0mConversion of type '{ setHeader: jest.Mock<any, any, any>; locals: {}; }' to type 'Response<any, Record<string, any>>' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.\n      Type '{ setHeader: Mock<any, any, any>; locals: {}; }' is missing the following properties from type 'Response<any, Record<string, any>>': status, sendStatus, links, send, and 87 more.\n\n    \u001b[7m404\u001b[0m         middleware.use(request2 as Request, response2 as Response, next2)\n    \u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/error-handling/test/unit/correlation-id.middleware.spec.ts", "startTime": 1750368077954, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750368077954, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/correlation.service.spec.ts\u001b[0m:\u001b[93m18\u001b[0m:\u001b[93m13\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'clearContext' does not exist on type 'CorrelationService'.\n\n    \u001b[7m18\u001b[0m     service.clearContext();\n    \u001b[7m  \u001b[0m \u001b[91m            ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation.service.spec.ts\u001b[0m:\u001b[93m251\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'clearContext' does not exist on type 'CorrelationService'.\n\n    \u001b[7m251\u001b[0m         service.clearContext();\n    \u001b[7m   \u001b[0m \u001b[91m                ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation.service.spec.ts\u001b[0m:\u001b[93m268\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'enrichContext' does not exist on type 'CorrelationService'.\n\n    \u001b[7m268\u001b[0m         service.enrichContext({\n    \u001b[7m   \u001b[0m \u001b[91m                ~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation.service.spec.ts\u001b[0m:\u001b[93m284\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'enrichContext' does not exist on type 'CorrelationService'.\n\n    \u001b[7m284\u001b[0m         service.enrichContext({ test: 'value' });\n    \u001b[7m   \u001b[0m \u001b[91m                ~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/correlation.service.spec.ts\u001b[0m:\u001b[93m305\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'enrichContext' does not exist on type 'CorrelationService'.\n\n    \u001b[7m305\u001b[0m         service.enrichContext({\n    \u001b[7m   \u001b[0m \u001b[91m                ~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/error-handling/test/unit/correlation.service.spec.ts", "startTime": 1750368077954, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "HTTP Exception Handling"], "duration": 30, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:79:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests HTTP Exception Handling should handle standard HttpException", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077419, "status": "failed", "title": "should handle standard HttpException"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "HTTP Exception Handling"], "duration": 6, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:113:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests HTTP Exception Handling should handle HttpException with custom response object", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077475, "status": "failed", "title": "should handle HttpException with custom response object"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "HTTP Exception Handling"], "duration": 5, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:137:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests HTTP Exception Handling should handle HttpException with array messages", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077482, "status": "failed", "title": "should handle HttpException with array messages"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "<PERSON><PERSON><PERSON>"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:163:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Generic Error Handling should handle generic Error instances", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077488, "status": "failed", "title": "should handle generic Error instances"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "<PERSON><PERSON><PERSON>"], "duration": 5, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:197:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Generic Error Handling should handle custom error objects", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077492, "status": "failed", "title": "should handle custom error objects"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "<PERSON><PERSON><PERSON>"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:221:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Generic Error Handling should handle string exceptions", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077499, "status": "failed", "title": "should handle string exceptions"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Correlation Context Integration"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:260:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Correlation Context Integration should use correlation context when available", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077503, "status": "failed", "title": "should use correlation context when available"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Correlation Context Integration"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:285:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Correlation Context Integration should handle missing correlation context gracefully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077507, "status": "failed", "title": "should handle missing correlation context gracefully"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Response Headers"], "duration": 7, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:306:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Response Headers should set correlation ID in response header", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077511, "status": "failed", "title": "should set correlation ID in response header"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Response Headers"], "duration": 4, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:320:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Response Headers should set content type header", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077519, "status": "failed", "title": "should set content type header"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Response Headers"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:336:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Response Headers should preserve existing correlation header from response", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077523, "status": "failed", "title": "should preserve existing correlation header from response"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Request Path Extraction"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:363:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Request Path Extraction should extract path from request URL", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077527, "status": "failed", "title": "should extract path from request URL"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Request Path Extraction"], "duration": 6, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:385:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Request Path Extraction should handle requests without path", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077530, "status": "failed", "title": "should handle requests without path"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Request Path Extraction"], "duration": 2, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:402:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Request Path Extraction should fallback to unknown path when request info unavailable", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077537, "status": "failed", "title": "should fallback to unknown path when request info unavailable"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Status Code Handling"], "duration": 2, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:416:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Status Code Handling should extract status code from HttpException", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077541, "status": "failed", "title": "should extract status code from HttpException"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Status Code Handling"], "duration": 2, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:432:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Status Code Handling should default to 500 for non-HTTP exceptions", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077544, "status": "failed", "title": "should default to 500 for non-HTTP exceptions"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Status Code Handling"], "duration": 2, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:448:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Status Code Handling should handle custom status codes in error response", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077546, "status": "failed", "title": "should handle custom status codes in error response"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Error Response Validation"], "duration": 2, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:465:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Error Response Validation should ensure response has required structure", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077549, "status": "failed", "title": "should ensure response has required structure"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Error Response Validation"], "duration": 2, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:477:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Error Response Validation should handle null/undefined error responses", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077552, "status": "failed", "title": "should handle null/undefined error responses"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Performance and Efficiency"], "duration": 3, "failing": false, "failureDetails": [{}], "failureMessages": ["TypeError: this.correlationService.getCorrelationId is not a function\n    at CorrelationExceptionFilter.catch (/root/code/polyrepo/libs/error-handling/src/filters/correlation-exception.filter.ts:34:51)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:495:19)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Performance and Efficiency should process exceptions efficiently", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077555, "status": "failed", "title": "should process exceptions efficiently"}, {"ancestorTitles": ["CorrelationExceptionFilter Unit Tests", "Performance and Efficiency"], "duration": 34, "failing": false, "failureDetails": [{"matcherResult": {"message": "expect(received).not.toThrow()\n\nError name:    \"TypeError\"\nError message: \"this.correlationService.getCorrelationId is not a function\"\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at test/unit/correlation-exception.filter.spec.ts:510:21\n      at Object.<anonymous> (../http/node_modules/expect/build/index.js:1824:9)\n      at Object.throwingMatcher [as toThrow] (../http/node_modules/expect/build/index.js:2231:93)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:511:14)", "pass": true}}], "failureMessages": ["Error: expect(received).not.toThrow()\n\nError name:    \"<PERSON><PERSON>rror\"\nError message: \"this.correlationService.getCorrelationId is not a function\"\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at test/unit/correlation-exception.filter.spec.ts:510:21\n      at Object.<anonymous> (../http/node_modules/expect/build/index.js:1824:9)\n      at Object.throwingMatcher [as toThrow] (../http/node_modules/expect/build/index.js:2231:93)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:511:14)\n    at Object.<anonymous> (/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts:511:14)\n    at Promise.finally.completed (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1499:10)\n    at _callCircusTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:829:11)\n    at run (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/jestAdapterInit.js:1920:21)\n    at jestAdapter (/root/code/polyrepo/libs/http/node_modules/jest-circus/build/runner.js:101:19)\n    at runTestInternal (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:272:16)\n    at runTest (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:340:7)\n    at Object.worker (/root/code/polyrepo/libs/http/node_modules/jest-runner/build/testWorker.js:494:12)"], "fullName": "CorrelationExceptionFilter Unit Tests Performance and Efficiency should not throw errors during error handling", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "startAt": 1750368077558, "status": "failed", "title": "should not throw errors during error handling"}], "endTime": 1750368077616, "message": "  ● CorrelationExceptionFilter Unit Tests › HTTP Exception Handling › should handle standard HttpException\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:79:19)\n\n  ● CorrelationExceptionFilter Unit Tests › HTTP Exception Handling › should handle HttpException with custom response object\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:113:19)\n\n  ● CorrelationExceptionFilter Unit Tests › HTTP Exception Handling › should handle HttpException with array messages\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:137:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Generic Error Handling › should handle generic Error instances\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:163:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Generic Error Handling › should handle custom error objects\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:197:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Generic Error Handling › should handle string exceptions\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:221:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Correlation Context Integration › should use correlation context when available\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:260:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Correlation Context Integration › should handle missing correlation context gracefully\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:285:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Response Headers › should set correlation ID in response header\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:306:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Response Headers › should set content type header\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:320:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Response Headers › should preserve existing correlation header from response\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:336:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Request Path Extraction › should extract path from request URL\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:363:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Request Path Extraction › should handle requests without path\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:385:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Request Path Extraction › should fallback to unknown path when request info unavailable\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:402:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Status Code Handling › should extract status code from HttpException\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:416:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Status Code Handling › should default to 500 for non-HTTP exceptions\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:432:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Status Code Handling › should handle custom status codes in error response\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:448:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Error Response Validation › should ensure response has required structure\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:465:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Error Response Validation › should handle null/undefined error responses\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:477:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Performance and Efficiency › should process exceptions efficiently\n\n    TypeError: this.correlationService.getCorrelationId is not a function\n\n      32 |     const response = ctx.getResponse<Response>();\n      33 |\n    > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n         |                                                   ^\n      35 |                           (request as any).correlationId ||\n      36 |                           this.correlationService.generateCorrelationId();\n      37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:495:19)\n\n  ● CorrelationExceptionFilter Unit Tests › Performance and Efficiency › should not throw errors during error handling\n\n    expect(received).not.toThrow()\n\n    Error name:    \"TypeError\"\n    Error message: \"this.correlationService.getCorrelationId is not a function\"\n\n          32 |     const response = ctx.getResponse<Response>();\n          33 |\n        > 34 |     const correlationId = this.correlationService.getCorrelationId() || \n             |                                                   ^\n          35 |                           (request as any).correlationId ||\n          36 |                           this.correlationService.generateCorrelationId();\n          37 |\n\n      at CorrelationExceptionFilter.catch (src/filters/correlation-exception.filter.ts:34:51)\n      at test/unit/correlation-exception.filter.spec.ts:510:21\n      at Object.<anonymous> (../http/node_modules/expect/build/index.js:1824:9)\n      at Object.throwingMatcher [as toThrow] (../http/node_modules/expect/build/index.js:2231:93)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:511:14)\n      at Object.<anonymous> (test/unit/correlation-exception.filter.spec.ts:511:14)\n", "name": "/root/code/polyrepo/libs/error-handling/test/unit/correlation-exception.filter.spec.ts", "startTime": 1750368072107, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750368077954, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'buildServiceCorrelationQuery' does not exist on type 'LokiQueryBuilder'. Did you mean 'buildCorrelationQuery'?\n\n    \u001b[7m45\u001b[0m       const query = queryBuilder.buildServiceCorrelationQuery('user-service', 'correlation-456');\n    \u001b[7m  \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/utils/loki-query-builder.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m41\u001b[0m   buildCorrelationQuery(correlationId: string, options?: {\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n        'buildCorrelationQuery' is declared here.\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m53\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m53\u001b[0m       const query = queryBuilder.buildServiceQuery('auth-service');\n    \u001b[7m  \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m61\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildErrorQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m61\u001b[0m       const query = queryBuilder.buildErrorQuery('payment-service', 'error-correlation-789');\n    \u001b[7m  \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m83\u001b[0m:\u001b[93m70\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'timeRangeHours' does not exist in type '{ serviceName?: string | undefined; timeRangeMinutes?: number | undefined; logLevel?: \"error\" | \"warn\" | \"info\" | \"debug\" | undefined; }'.\n\n    \u001b[7m83\u001b[0m       const query = queryBuilder.buildCorrelationQuery('test-123', { timeRangeHours: 2 });\n    \u001b[7m  \u001b[0m \u001b[91m                                                                     ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m91\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'timeRangeHours' does not exist in type '{ serviceName?: string | undefined; timeRangeMinutes?: number | undefined; logLevel?: \"error\" | \"warn\" | \"info\" | \"debug\" | undefined; }'.\n\n    \u001b[7m91\u001b[0m         timeRangeHours: 1\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m107\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m107\u001b[0m       const query = queryBuilder.buildServiceQuery('api-service', {\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m116\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m116\u001b[0m       const query = queryBuilder.buildServiceQuery('order-service', {\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m131\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m131\u001b[0m       const query = queryBuilder.buildServiceQuery('test-service', {\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m150\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m150\u001b[0m       const query = queryBuilder.buildServiceQuery(serviceName);\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m156\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'buildServiceCorrelationQuery' does not exist on type 'LokiQueryBuilder'. Did you mean 'buildCorrelationQuery'?\n\n    \u001b[7m156\u001b[0m       const query = queryBuilder.buildServiceCorrelationQuery(\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/utils/loki-query-builder.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m41\u001b[0m   buildCorrelationQuery(correlationId: string, options?: {\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n        'buildCorrelationQuery' is declared here.\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m170\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'buildServiceCorrelationQuery' does not exist on type 'LokiQueryBuilder'. Did you mean 'buildCorrelationQuery'?\n\n    \u001b[7m170\u001b[0m       const query = queryBuilder.buildServiceCorrelationQuery('test-service', 'test-correlation');\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/utils/loki-query-builder.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m41\u001b[0m   buildCorrelationQuery(correlationId: string, options?: {\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n        'buildCorrelationQuery' is declared here.\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m179\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m179\u001b[0m       const query = queryBuilder.buildServiceQuery('multi-label-service', {\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m194\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m194\u001b[0m       const query = queryBuilder.buildServiceQuery('single-service');\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m209\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m209\u001b[0m       const query = queryBuilder.buildServiceQuery(undefined as any);\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m215\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'buildServiceCorrelationQuery' does not exist on type 'LokiQueryBuilder'. Did you mean 'buildCorrelationQuery'?\n\n    \u001b[7m215\u001b[0m       const query = queryBuilder.buildServiceCorrelationQuery('', '');\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/utils/loki-query-builder.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m41\u001b[0m   buildCorrelationQuery(correlationId: string, options?: {\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n        'buildCorrelationQuery' is declared here.\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m239\u001b[0m:\u001b[93m35\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'buildServiceCorrelationQuery' does not exist on type 'LokiQueryBuilder'. Did you mean 'buildCorrelationQuery'?\n\n    \u001b[7m239\u001b[0m       const query = customBuilder.buildServiceCorrelationQuery('my-app', 'trace-123');\n    \u001b[7m   \u001b[0m \u001b[91m                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/utils/loki-query-builder.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m41\u001b[0m   buildCorrelationQuery(correlationId: string, options?: {\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n        'buildCorrelationQuery' is declared here.\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m271\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m271\u001b[0m       const query = queryBuilder.buildServiceQuery('param-test', {\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m292\u001b[0m:\u001b[93m22\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'buildServiceCorrelationQuery' does not exist on type 'LokiQueryBuilder'. Did you mean 'buildCorrelationQuery'?\n\n    \u001b[7m292\u001b[0m         queryBuilder.buildServiceCorrelationQuery(`service-${i}`, `correlation-${i}`);\n    \u001b[7m   \u001b[0m \u001b[91m                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/utils/loki-query-builder.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m41\u001b[0m   buildCorrelationQuery(correlationId: string, options?: {\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n        'buildCorrelationQuery' is declared here.\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m313\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildErrorQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m313\u001b[0m       const query = queryBuilder.buildErrorQuery('modern-service', 'modern-correlation');\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/loki-query-builder.spec.ts\u001b[0m:\u001b[93m323\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'buildServiceQuery' does not exist on type 'LokiQueryBuilder'.\n\n    \u001b[7m323\u001b[0m       const query = queryBuilder.buildServiceQuery('service-with-quotes', {\n    \u001b[7m   \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/error-handling/test/unit/loki-query-builder.spec.ts", "startTime": 1750368077954, "status": "failed", "summary": ""}], "wasInterrupted": false}