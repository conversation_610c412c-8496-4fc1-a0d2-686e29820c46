import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CorrelationService } from '../services/correlation.service';

/**
 * Correlation ID middleware for API Gateway
 * Generates or extracts correlation IDs and sets up context for the request
 */
@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  constructor(private readonly correlationService: CorrelationService) {}

  use(req: Request, res: Response, next: NextFunction): void {
    // Extract existing correlation ID from headers or generate new one
    const existingCorrelationId = this.correlationService.extractCorrelationId(req.headers);
    
    // Create correlation context
    const context = this.correlationService.createContext({
      correlationId: existingCorrelationId,
      request: {
        method: req.method,
        path: req.path,
        url: req.url,
      },
      user: this.extractUserFromRequest(req),
      service: {
        name: process.env.SERVICE_NAME || 'api-gateway',
        version: process.env.SERVICE_VERSION || '1.0.0',
      },
      metadata: {
        userAgent: req.get('user-agent'),
        remoteAddress: req.ip || req.connection.remoteAddress,
        host: req.get('host'),
      },
    });

    // Set correlation ID in response headers for client reference
    res.setHeader('x-correlation-id', context.correlationId);
    res.setHeader('x-request-id', context.correlationId);

    // Store correlation ID in request for easy access
    (req as any).correlationId = context.correlationId;
    (req as any).correlationContext = context;

    // Run the request within correlation context
    this.correlationService.runWithContext(context, () => {
      next();
    });
  }

  /**
   * Extract user information from request if available
   */
  private extractUserFromRequest(req: Request): { userId: string; roles?: string[] } | undefined {
    // Check for user from JWT guard or auth middleware
    const user = (req as any).user;
    if (user) {
      return {
        userId: user.userId || user.sub || user.id,
        roles: user.roles || user.realm_access?.roles,
      };
    }

    return undefined;
  }
}