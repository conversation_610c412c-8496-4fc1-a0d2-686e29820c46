import { 
  ExceptionFilter, 
  Catch, 
  ArgumentsHost, 
  HttpException, 
  HttpStatus,
  Injectable,
  Logger
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ErrorResponseBuilderService } from '../services/error-response-builder.service';
import { CorrelationService } from '../services/correlation.service';
import { ErrorResponse, ErrorResponseConfig } from '../interfaces/error-response.interface';

/**
 * Global exception filter with correlation ID support
 * Catches all exceptions and returns standardized error responses
 */
@Catch()
@Injectable()
export class CorrelationExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(CorrelationExceptionFilter.name);

  constructor(
    private readonly errorBuilder: ErrorResponseBuilderService,
    private readonly correlationService: CorrelationService
  ) {}

  catch(exception: any, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    const correlationId = this.correlationService.getCorrelationId() || 
                          (request as any).correlationId ||
                          this.correlationService.generateCorrelationId();

    // Log the exception with correlation context
    this.logException(exception, request, correlationId);

    // Build standardized error response
    const errorResponse = this.errorBuilder.buildFromException(
      exception,
      request.url
    );

    // Ensure correlation ID is set in response headers
    response.setHeader('x-correlation-id', correlationId);
    response.setHeader('x-request-id', correlationId);

    // Send error response
    response
      .status(errorResponse.statusCode)
      .json(errorResponse);
  }

  /**
   * Log exception with correlation context
   */
  private logException(exception: any, request: Request, correlationId: string): void {
    const context = this.correlationService.getContext();
    const duration = this.correlationService.getRequestDuration();

    const logContext = {
      correlationId,
      method: request.method,
      url: request.url,
      userAgent: request.get('user-agent'),
      remoteAddress: request.ip || request.connection.remoteAddress,
      duration: duration ? `${duration}ms` : 'unknown',
      user: context?.user,
      service: context?.service,
    };

    if (exception instanceof HttpException) {
      const status = exception.getStatus();
      
      if (status >= 500) {
        this.logger.error(
          `HTTP ${status} Error: ${exception.message}`,
          {
            ...logContext,
            stack: exception.stack,
            response: exception.getResponse(),
          }
        );
      } else if (status >= 400) {
        this.logger.warn(
          `HTTP ${status} Warning: ${exception.message}`,
          logContext
        );
      }
    } else {
      this.logger.error(
        `Unhandled Exception: ${exception.message || 'Unknown error'}`,
        {
          ...logContext,
          stack: exception.stack,
          name: exception.constructor?.name,
        }
      );
    }
  }
}

/**
 * Factory function to create correlation exception filter with config
 */
export function createCorrelationExceptionFilter(
  config?: Partial<ErrorResponseConfig>
) {
  return (errorBuilder: ErrorResponseBuilderService, correlationService: CorrelationService) => {
    return new CorrelationExceptionFilter(errorBuilder, correlationService);
  };
}