import { Injectable } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';
import { v4 as uuidv4 } from 'uuid';
import { CorrelationContext } from '../interfaces/correlation-context.interface';

/**
 * Correlation service for managing request correlation IDs
 * Uses AsyncLocalStorage for context propagation across async operations
 */
@Injectable()
export class CorrelationService {
  private static asyncLocalStorage = new AsyncLocalStorage<CorrelationContext>();

  /**
   * Generate a new correlation ID with timestamp
   */
  generateCorrelationId(): string {
    const timestamp = Date.now();
    const uuid = uuidv4().split('-')[0]; // First part of UUID for brevity
    return `req-${timestamp}-${uuid}`;
  }

  /**
   * Create a new correlation context
   */
  createContext(options: {
    correlationId?: string;
    request: {
      method: string;
      path: string;
      url: string;
    };
    user?: {
      userId: string;
      roles?: string[];
    };
    service: {
      name: string;
      version?: string;
    };
    metadata?: Record<string, any>;
  }): CorrelationContext {
    return {
      correlationId: options.correlationId || this.generateCorrelationId(),
      startTime: Date.now(),
      request: options.request,
      user: options.user,
      service: options.service,
      metadata: options.metadata || {},
    };
  }

  /**
   * Run callback with correlation context
   */
  runWithContext<T>(context: CorrelationContext, callback: () => T): T {
    return CorrelationService.asyncLocalStorage.run(context, callback);
  }

  /**
   * Get current correlation context
   */
  getContext(): CorrelationContext | undefined {
    return CorrelationService.asyncLocalStorage.getStore();
  }

  /**
   * Get current correlation ID
   */
  getCorrelationId(): string | undefined {
    const context = this.getContext();
    return context?.correlationId;
  }

  /**
   * Update current context metadata
   */
  updateMetadata(metadata: Record<string, any>): void {
    const context = this.getContext();
    if (context) {
      Object.assign(context.metadata, metadata);
    }
  }

  /**
   * Get request duration in milliseconds
   */
  getRequestDuration(): number | undefined {
    const context = this.getContext();
    return context ? Date.now() - context.startTime : undefined;
  }

  /**
   * Check if we're currently in a correlation context
   */
  hasContext(): boolean {
    return CorrelationService.asyncLocalStorage.getStore() !== undefined;
  }

  /**
   * Extract correlation ID from headers
   */
  extractCorrelationId(headers: Record<string, any>): string | undefined {
    // Try multiple header formats
    const headerKeys = [
      'x-correlation-id',
      'x-request-id', 
      'correlation-id',
      'request-id',
      'trace-id'
    ];

    for (const key of headerKeys) {
      const value = headers[key] || headers[key.toLowerCase()];
      if (value) {
        return Array.isArray(value) ? value[0] : value;
      }
    }

    return undefined;
  }

  /**
   * Get correlation headers for outgoing requests
   */
  getCorrelationHeaders(): Record<string, string> {
    const correlationId = this.getCorrelationId();
    if (!correlationId) {
      return {};
    }

    return {
      'x-correlation-id': correlationId,
      'x-request-id': correlationId, // Alternative header name
    };
  }
}