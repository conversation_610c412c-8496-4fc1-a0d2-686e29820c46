import { Injectable } from '@nestjs/common';
import { HttpException, HttpStatus } from '@nestjs/common';
import { ErrorResponse, ErrorResponseConfig } from '../interfaces/error-response.interface';
import { CorrelationService } from './correlation.service';
import { LokiQueryBuilder } from '../utils/loki-query-builder';
import { ERROR_CODES, DEFAULT_ERROR_MESSAGES, ERROR_HTTP_STATUS } from '../constants/error-codes';

/**
 * Service for building standardized error responses
 */
@Injectable()
export class ErrorResponseBuilderService {
  private readonly config: ErrorResponseConfig;
  private readonly lokiQueryBuilder: LokiQueryBuilder;

  constructor(
    private readonly correlationService: CorrelationService,
    config?: Partial<ErrorResponseConfig>
  ) {
    this.config = {
      environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',
      includeSensitiveDetails: process.env.NODE_ENV !== 'production',
      includeLokiLinks: true,
      lokiBaseUrl: process.env.LOKI_BASE_URL || 'http://localhost:3100',
      serviceName: process.env.SERVICE_NAME || 'unknown-service',
      ...config,
    };

    this.lokiQueryBuilder = new LokiQueryBuilder({
      baseUrl: this.config.lokiBaseUrl!,
      defaultTimeRange: 60,
      serviceLabelName: 'service',
      correlationLabelName: 'correlationId',
    });
  }

  /**
   * Build error response from exception (supports Got HTTP errors)
   */
  buildFromException(
    exception: any,
    path: string,
    statusCode?: number
  ): ErrorResponse {
    const context = this.correlationService.getContext();
    const correlationId = context?.correlationId || this.correlationService.generateCorrelationId();

    // Determine error code and message
    let errorCode: string;
    let message: string;
    let finalStatusCode: number;

    if (exception instanceof HttpException) {
      finalStatusCode = exception.getStatus();
      const response = exception.getResponse();
      
      if (typeof response === 'object' && response !== null) {
        const responseObj = response as any;
        errorCode = responseObj.error || this.getErrorCodeForStatus(finalStatusCode);
        message = responseObj.message || exception.message;
      } else {
        errorCode = this.getErrorCodeForStatus(finalStatusCode);
        message = typeof response === 'string' ? response : exception.message;
      }
    } else if (this.isGotHttpError(exception)) {
      // Handle Got HTTP client errors
      const gotErrorResult = this.handleGotError(exception);
      finalStatusCode = gotErrorResult.statusCode;
      errorCode = gotErrorResult.errorCode;
      message = gotErrorResult.message;
    } else if (this.isCircuitBreakerError(exception)) {
      // Handle circuit breaker errors
      finalStatusCode = HttpStatus.SERVICE_UNAVAILABLE;
      errorCode = ERROR_CODES.SERVICE_UNAVAILABLE;
      message = `Service ${exception.serviceName || 'unknown'} is temporarily unavailable (circuit breaker open)`;
    } else {
      finalStatusCode = statusCode || HttpStatus.INTERNAL_SERVER_ERROR;
      errorCode = ERROR_CODES.INTERNAL_SERVER_ERROR;
      message = this.config.environment === 'production' 
        ? 'An internal server error occurred' 
        : exception.message || 'Unknown error';
    }

    // Build base error response
    const errorResponse: ErrorResponse = {
      statusCode: finalStatusCode,
      error: errorCode,
      message: message,
      correlationId: correlationId,
      timestamp: new Date().toISOString(),
      path: path,
    };

    // Add Loki query link if enabled
    if (this.config.includeLokiLinks) {
      errorResponse.logQuery = this.lokiQueryBuilder.buildCorrelationQuery(correlationId, {
        serviceName: this.config.serviceName,
        timeRangeMinutes: 30,
      });
    }

    // Add sensitive details in development
    if (this.config.includeSensitiveDetails) {
      errorResponse.details = {
        stack: exception.stack,
        originalError: exception,
        context: context,
        requestDuration: this.correlationService.getRequestDuration(),
      };
    }

    return errorResponse;
  }

  /**
   * Build error response from error code
   */
  buildFromErrorCode(
    errorCode: string,
    path: string,
    customMessage?: string,
    statusCode?: number
  ): ErrorResponse {
    const context = this.correlationService.getContext();
    const correlationId = context?.correlationId || this.correlationService.generateCorrelationId();

    const finalStatusCode = statusCode || ERROR_HTTP_STATUS[errorCode as keyof typeof ERROR_HTTP_STATUS] || HttpStatus.INTERNAL_SERVER_ERROR;
    const message = customMessage || DEFAULT_ERROR_MESSAGES[errorCode as keyof typeof DEFAULT_ERROR_MESSAGES] || 'An error occurred';

    const errorResponse: ErrorResponse = {
      statusCode: finalStatusCode,
      error: errorCode,
      message: message,
      correlationId: correlationId,
      timestamp: new Date().toISOString(),
      path: path,
    };

    // Add Loki query link if enabled
    if (this.config.includeLokiLinks) {
      errorResponse.logQuery = this.lokiQueryBuilder.buildCorrelationQuery(correlationId, {
        serviceName: this.config.serviceName,
        timeRangeMinutes: 30,
      });
    }

    return errorResponse;
  }

  /**
   * Create HTTP exception with correlation support and compile-time validation
   * 
   * @param errorCode - Compile-time validated error code from ERROR_CODES
   * @param customMessage - Optional custom message (defaults to DEFAULT_ERROR_MESSAGES)
   * @param statusCode - Optional status code (defaults to ERROR_HTTP_STATUS mapping)
   * 
   * @example
   * ```typescript
   * // ✅ Compile-time validated
   * this.errorBuilder.createHttpException(
   *   ERROR_CODES.USER_NOT_FOUND,
   *   'User with ID 123 not found'
   * );
   * 
   * // ❌ Compile error - invalid error code
   * this.errorBuilder.createHttpException('INVALID_CODE');
   * ```
   */
  createHttpException<T extends keyof typeof ERROR_CODES>(
    errorCode: T,
    customMessage?: string,
    statusCode?: typeof ERROR_HTTP_STATUS[T]
  ): HttpException {
    // TypeScript ensures errorCode is valid at compile time
    const validErrorCode = ERROR_CODES[errorCode];
    const finalStatusCode = statusCode || ERROR_HTTP_STATUS[validErrorCode] || HttpStatus.INTERNAL_SERVER_ERROR;
    const message = customMessage || DEFAULT_ERROR_MESSAGES[validErrorCode] || 'An error occurred';
    
    const correlationId = this.correlationService.getCorrelationId();
    
    const response: any = {
      error: validErrorCode,
      message: message,
    };

    if (correlationId) {
      response.correlationId = correlationId;
    }

    return new HttpException(response, finalStatusCode);
  }

  /**
   * Legacy method for backward compatibility (without type safety)
   * @deprecated Use the type-safe createHttpException method instead
   */
  createHttpExceptionLegacy(
    errorCode: string,
    customMessage?: string,
    statusCode?: number
  ): HttpException {
    const finalStatusCode = statusCode || ERROR_HTTP_STATUS[errorCode as keyof typeof ERROR_HTTP_STATUS] || HttpStatus.INTERNAL_SERVER_ERROR;
    const message = customMessage || DEFAULT_ERROR_MESSAGES[errorCode as keyof typeof DEFAULT_ERROR_MESSAGES] || 'An error occurred';
    
    const correlationId = this.correlationService.getCorrelationId();
    
    const response: any = {
      error: errorCode,
      message: message,
    };

    if (correlationId) {
      response.correlationId = correlationId;
    }

    return new HttpException(response, finalStatusCode);
  }

  /**
   * Get error code for HTTP status code
   */
  private getErrorCodeForStatus(statusCode: number): string {
    switch (statusCode) {
      case HttpStatus.BAD_REQUEST:
        return ERROR_CODES.VALIDATION_FAILED;
      case HttpStatus.UNAUTHORIZED:
        return ERROR_CODES.UNAUTHORIZED;
      case HttpStatus.FORBIDDEN:
        return ERROR_CODES.FORBIDDEN;
      case HttpStatus.NOT_FOUND:
        return ERROR_CODES.NOT_FOUND;
      case HttpStatus.CONFLICT:
        return ERROR_CODES.CONFLICT;
      case HttpStatus.TOO_MANY_REQUESTS:
        return ERROR_CODES.RATE_LIMIT_EXCEEDED;
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return ERROR_CODES.INTERNAL_SERVER_ERROR;
      case HttpStatus.BAD_GATEWAY:
        return ERROR_CODES.SERVICE_UNAVAILABLE;
      case HttpStatus.SERVICE_UNAVAILABLE:
        return ERROR_CODES.SERVICE_UNAVAILABLE;
      case HttpStatus.GATEWAY_TIMEOUT:
        return ERROR_CODES.GATEWAY_TIMEOUT;
      default:
        return ERROR_CODES.INTERNAL_SERVER_ERROR;
    }
  }

  /**
   * Check if error is from Got HTTP client
   */
  private isGotHttpError(error: any): boolean {
    return error.name === 'RequestError' || 
           error.name === 'HTTPError' || 
           error.name === 'TimeoutError' ||
           error.name === 'ParseError' ||
           error.name === 'MaxRedirectsError' ||
           error.requestId || // Our custom request ID
           (error.response && error.response.statusCode);
  }

  /**
   * Check if error is from circuit breaker
   */
  private isCircuitBreakerError(error: any): boolean {
    return error.code === 'CIRCUIT_BREAKER_OPEN' || 
           error.message?.includes('circuit breaker');
  }

  /**
   * Handle Got HTTP client errors
   */
  private handleGotError(error: any): { statusCode: number; errorCode: string; message: string } {
    const serviceName = error.serviceName || 'external-service';
    
    // Got HTTP response errors
    if (error.response) {
      const statusCode = error.response.statusCode;
      
      switch (statusCode) {
        case 400:
          return {
            statusCode: HttpStatus.BAD_REQUEST,
            errorCode: ERROR_CODES.VALIDATION_FAILED,
            message: `Bad request to ${serviceName}: ${error.response.body?.message || error.message}`
          };
        case 401:
          return {
            statusCode: HttpStatus.UNAUTHORIZED,
            errorCode: ERROR_CODES.UNAUTHORIZED,
            message: `Authentication failed for ${serviceName}`
          };
        case 403:
          return {
            statusCode: HttpStatus.FORBIDDEN,
            errorCode: ERROR_CODES.FORBIDDEN,
            message: `Access denied to ${serviceName}`
          };
        case 404:
          return {
            statusCode: HttpStatus.NOT_FOUND,
            errorCode: ERROR_CODES.NOT_FOUND,
            message: `Resource not found in ${serviceName}`
          };
        case 409:
          return {
            statusCode: HttpStatus.CONFLICT,
            errorCode: ERROR_CODES.CONFLICT,
            message: `Conflict in ${serviceName}: ${error.response.body?.message || error.message}`
          };
        case 429:
          return {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            errorCode: ERROR_CODES.RATE_LIMIT_EXCEEDED,
            message: `Rate limit exceeded for ${serviceName}`
          };
        case 502:
        case 503:
        case 504:
          return {
            statusCode: HttpStatus.BAD_GATEWAY,
            errorCode: ERROR_CODES.SERVICE_UNAVAILABLE,
            message: `Service ${serviceName} is temporarily unavailable`
          };
        default:
          if (statusCode >= 500) {
            return {
              statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
              errorCode: ERROR_CODES.INTERNAL_SERVER_ERROR,
              message: `Internal error in ${serviceName}`
            };
          } else {
            return {
              statusCode: statusCode,
              errorCode: ERROR_CODES.VALIDATION_FAILED,
              message: `Request failed for ${serviceName}: ${error.message}`
            };
          }
      }
    }

    // Network/connection errors
    if (this.isNetworkError(error)) {
      return {
        statusCode: HttpStatus.BAD_GATEWAY,
        errorCode: ERROR_CODES.SERVICE_UNAVAILABLE,
        message: `Unable to connect to ${serviceName}: ${this.getNetworkErrorMessage(error)}`
      };
    }

    // Timeout errors
    if (error.name === 'TimeoutError' || error.code === 'ETIMEDOUT') {
      return {
        statusCode: HttpStatus.GATEWAY_TIMEOUT,
        errorCode: ERROR_CODES.GATEWAY_TIMEOUT,
        message: `Request to ${serviceName} timed out`
      };
    }

    // Default Got error
    return {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      errorCode: ERROR_CODES.INTERNAL_SERVER_ERROR,
      message: `Unexpected error calling ${serviceName}: ${error.message}`
    };
  }

  /**
   * Check if error is a network/connection error
   */
  private isNetworkError(error: any): boolean {
    const networkCodes = [
      'ECONNREFUSED',
      'ENOTFOUND', 
      'ENETUNREACH',
      'EHOSTUNREACH',
      'ECONNRESET',
      'EPIPE',
      'EAI_AGAIN'
    ];
    
    return networkCodes.includes(error.code) || error.name === 'RequestError';
  }

  /**
   * Get user-friendly network error message
   */
  private getNetworkErrorMessage(error: any): string {
    switch (error.code) {
      case 'ECONNREFUSED':
        return 'Connection refused';
      case 'ENOTFOUND':
        return 'Service not found';
      case 'ENETUNREACH':
      case 'EHOSTUNREACH':
        return 'Network unreachable';
      case 'ECONNRESET':
        return 'Connection reset';
      case 'EPIPE':
        return 'Broken pipe';
      case 'EAI_AGAIN':
        return 'DNS lookup failed';
      default:
        return 'Network error';
    }
  }

  /**
   * Add correlation context to HTTP request options
   */
  addCorrelationToRequestOptions(options: any = {}): any {
    const correlationId = this.correlationService.getCorrelationId();
    const context = this.correlationService.getContext();
    
    if (!correlationId) {
      return options;
    }

    // Add correlation headers
    const headers = {
      'x-correlation-id': correlationId,
      'x-request-id': correlationId,
      ...options.headers,
    };

    // Add trace headers if available
    if (context?.traceId) {
      headers['x-trace-id'] = context.traceId;
    }
    
    if (context?.spanId) {
      headers['x-span-id'] = context.spanId;
    }

    return {
      ...options,
      headers,
      context: {
        ...options.context,
        correlationId,
        parentService: context?.service?.name,
      },
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorResponseConfig>): void {
    Object.assign(this.config, newConfig);
  }
}