import { Injectable } from '@nestjs/common';
import { ErrorResponseBuilderService } from './error-response-builder.service';
import { ERROR_CODES } from '../constants/error-codes';
import { AxiosError } from 'axios';

/**
 * Specialized error handler for Keycloak integration
 * Transforms Keycloak API errors into standardized application errors
 */
@Injectable()
export class KeycloakErrorHandlerService {
  constructor(
    private readonly errorBuilder: ErrorResponseBuilderService
  ) {}

  /**
   * Handle Keycloak authentication errors
   * 
   * @param error - Axios error from Keycloak API
   * @param context - Additional context for debugging
   * @returns Standardized HTTP exception with correlation tracking
   */
  handleAuthenticationError(error: any, context?: string) {
    const errorContext = context ? ` [${context}]` : '';
    
    if (this.isAxiosError(error)) {
      const status = error.response?.status;
      const data = error.response?.data as any;
      
      switch (status) {
        case 400:
          // Invalid credentials or malformed request
          if (data?.error === 'invalid_grant') {
            return this.errorBuilder.createHttpException(
              'INVALID_CREDENTIALS',
              'Invalid email or password'
            );
          }
          return this.errorBuilder.createHttpException(
            'VALIDATION_FAILED',
            `Invalid request to Keycloak${errorContext}`
          );
          
        case 401:
          // Unauthorized - invalid or expired credentials
          if (data?.error === 'invalid_token') {
            return this.errorBuilder.createHttpException(
              'TOKEN_EXPIRED',
              'Your session has expired, please log in again'
            );
          }
          return this.errorBuilder.createHttpException(
            'INVALID_CREDENTIALS',
            'Invalid email or password'
          );
          
        case 403:
          // Forbidden - insufficient permissions
          return this.errorBuilder.createHttpException(
            'INSUFFICIENT_PERMISSIONS',
            `Insufficient permissions for Keycloak operation${errorContext}`
          );
          
        case 404:
          // User or resource not found
          return this.errorBuilder.createHttpException(
            'USER_NOT_FOUND',
            `User not found in Keycloak${errorContext}`
          );
          
        case 409:
          // Conflict - user already exists
          return this.errorBuilder.createHttpException(
            'EMAIL_ALREADY_EXISTS',
            'An account with this email already exists'
          );
          
        case 429:
          // Rate limiting
          return this.errorBuilder.createHttpException(
            'RATE_LIMIT_EXCEEDED',
            'Too many authentication attempts, please try again later'
          );
          
        case 500:
        case 502:
        case 503:
        case 504:
          // Keycloak server errors
          return this.errorBuilder.createHttpException(
            'EXTERNAL_SERVICE_ERROR',
            `Keycloak service is temporarily unavailable${errorContext}`
          );
          
        default:
          return this.errorBuilder.createHttpException(
            'EXTERNAL_SERVICE_ERROR',
            `Unexpected error from Keycloak (${status})${errorContext}`
          );
      }
    }
    
    // Non-HTTP errors (network, timeout, etc.)
    if (error.code === 'ECONNREFUSED') {
      return this.errorBuilder.createHttpException(
        'SERVICE_UNAVAILABLE',
        'Authentication service is temporarily unavailable'
      );
    }
    
    if (error.code === 'ETIMEDOUT') {
      return this.errorBuilder.createHttpException(
        'TIMEOUT',
        'Authentication request timed out'
      );
    }
    
    // Generic error fallback
    return this.errorBuilder.createHttpException(
      'EXTERNAL_SERVICE_ERROR',
      `Authentication service error${errorContext}: ${error.message || 'Unknown error'}`
    );
  }

  /**
   * Handle Keycloak user management errors
   */
  handleUserManagementError(error: any, operation: string) {
    if (this.isAxiosError(error)) {
      const status = error.response?.status;
      const data = error.response?.data;
      
      switch (status) {
        case 400:
          return this.errorBuilder.createHttpException(
            'VALIDATION_FAILED',
            `Invalid data for ${operation} operation`
          );
          
        case 404:
          return this.errorBuilder.createHttpException(
            'USER_NOT_FOUND',
            `User not found for ${operation} operation`
          );
          
        case 409:
          if (operation.includes('create')) {
            return this.errorBuilder.createHttpException(
              'EMAIL_ALREADY_EXISTS',
              'An account with this email already exists'
            );
          }
          return this.errorBuilder.createHttpException(
            'CONFLICT',
            `Conflict during ${operation} operation`
          );
          
        default:
          return this.handleAuthenticationError(error, operation);
      }
    }
    
    return this.handleAuthenticationError(error, operation);
  }

  /**
   * Handle Keycloak token operations
   */
  handleTokenError(error: any, tokenType: 'access' | 'refresh' | 'reset') {
    if (this.isAxiosError(error)) {
      const status = error.response?.status;
      const data = error.response?.data as any;
      
      if (status === 400 && data?.error === 'invalid_grant') {
        switch (tokenType) {
          case 'refresh':
            return this.errorBuilder.createHttpException(
              'TOKEN_EXPIRED',
              'Refresh token has expired, please log in again'
            );
          case 'reset':
            return this.errorBuilder.createHttpException(
              'TOKEN_INVALID',
              'Password reset token is invalid or expired'
            );
          default:
            return this.errorBuilder.createHttpException(
              'TOKEN_INVALID',
              'Token is invalid or expired'
            );
        }
      }
    }
    
    return this.handleAuthenticationError(error, `${tokenType} token`);
  }

  /**
   * Wrap Keycloak service calls with standardized error handling
   * 
   * @param operation - Async operation that may throw Keycloak errors
   * @param context - Context for error reporting
   * @returns Result of operation or throws standardized error
   */
  async wrapKeycloakOperation<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      throw this.handleAuthenticationError(error, context);
    }
  }

  /**
   * Type guard to check if error is an Axios error
   */
  private isAxiosError(error: any): error is AxiosError {
    return error && error.isAxiosError === true;
  }
}

/**
 * Decorator for automatic Keycloak error handling
 * 
 * @param context - Context string for error reporting
 * 
 * @example
 * ```typescript
 * @HandleKeycloakErrors('user authentication')
 * async authenticateUser(email: string, password: string) {
 *   // Keycloak API calls here
 * }
 * ```
 */
export function HandleKeycloakErrors(context: string): MethodDecorator {
  return function (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const errorHandler: KeycloakErrorHandlerService = 
        (this as any).keycloakErrorHandler || (this as any).errorHandler;
      
      if (!errorHandler) {
        throw new Error(
          `KeycloakErrorHandlerService not found. Make sure to inject it in ${target.constructor.name}`
        );
      }
      
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        throw errorHandler.handleAuthenticationError(error, context);
      }
    };
    
    return descriptor;
  };
}