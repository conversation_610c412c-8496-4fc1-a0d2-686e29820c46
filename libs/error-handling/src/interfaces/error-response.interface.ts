/**
 * Standardized error response interface for all microservices
 * Provides correlation tracking, Loki integration, and environment-aware details
 */
export interface ErrorResponse {
  /** HTTP status code */
  statusCode: number;
  
  /** Machine-readable error code for frontend handling */
  error: string;
  
  /** Human-readable error message */
  message: string;
  
  /** Unique correlation ID for request tracking across services */
  correlationId: string;
  
  /** ISO timestamp when error occurred */
  timestamp: string;
  
  /** Request path where error occurred */
  path: string;
  
  /** Optional direct link to Loki logs for this correlation ID */
  logQuery?: string;
  
  /** Optional trace ID for distributed tracing integration */
  traceId?: string;
  
  /** Additional error details (dev environment only) */
  details?: {
    /** Stack trace (dev only) */
    stack?: string;
    /** Validation errors (dev only) */
    validation?: any;
    /** Internal error data (dev only) */
    internal?: any;
    /** Original error object (dev only) */
    originalError?: any;
    /** Request context (dev only) */
    context?: any;
    /** Request duration (dev only) */
    requestDuration?: number;
  };
}

/**
 * Configuration for error response generation
 */
export interface ErrorResponseConfig {
  /** Environment mode affects detail level */
  environment: 'development' | 'production' | 'test';
  
  /** Base URL for Loki instance */
  lokiBaseUrl?: string;
  
  /** Whether to include Loki query links */
  includeLokiLinks: boolean;
  
  /** Whether to include sensitive details */
  includeSensitiveDetails: boolean;
  
  /** Default error message for unexpected errors */
  defaultErrorMessage?: string;
  
  /** Service name for error tracking */
  serviceName?: string;
}

/**
 * Error context passed through request lifecycle
 */
export interface ErrorContext {
  /** Request correlation ID */
  correlationId: string;
  
  /** Request path */
  path: string;
  
  /** HTTP method */
  method: string;
  
  /** User ID if authenticated */
  userId?: string;
  
  /** Service name where error occurred */
  serviceName: string;
  
  /** Additional context data */
  metadata?: Record<string, any>;
}