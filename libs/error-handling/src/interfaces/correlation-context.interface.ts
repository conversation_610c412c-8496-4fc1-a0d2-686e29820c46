/**
 * Correlation context stored in AsyncLocalStorage for request tracking
 */
export interface CorrelationContext {
  /** Unique correlation ID for this request */
  correlationId: string;
  
  /** Timestamp when request started */
  startTime: number;
  
  /** Request method and path */
  request: {
    method: string;
    path: string;
    url: string;
    userAgent?: string;
    ip?: string;
  };
  
  /** User context if authenticated */
  user?: {
    userId: string;
    email?: string;
    roles?: string[];
  };
  
  /** Service information */
  service: {
    name: string;
    version?: string;
  };

  /** Tracing information */
  traceId?: string;
  spanId?: string;
  
  /** Additional metadata */
  metadata: Record<string, any>;
}

/**
 * Headers used for correlation ID propagation
 */
export interface CorrelationHeaders {
  /** Primary correlation ID header */
  'x-correlation-id': string;
  
  /** Optional trace ID for distributed tracing integration */
  'x-trace-id'?: string;
  
  /** Request start timestamp */
  'x-request-start'?: string;
  
  /** Originating service */
  'x-origin-service'?: string;
}

/**
 * Configuration for correlation ID generation and propagation
 */
export interface CorrelationConfig {
  /** Header name for correlation ID */
  headerName: string;
  
  /** Format for generating correlation IDs */
  idFormat: 'uuid' | 'timestamp-uuid' | 'custom';
  
  /** Custom prefix for correlation IDs */
  prefix?: string;
  
  /** Whether to propagate to downstream services */
  propagateToDownstream: boolean;
  
  /** Services to include in correlation tracking */
  includedServices?: string[];
  
  /** Services to exclude from correlation tracking */
  excludedServices?: string[];
}