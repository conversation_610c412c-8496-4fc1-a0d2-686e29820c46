/**
 * Standardized error codes for consistent frontend handling
 * Following REST API error conventions with business-specific codes
 */

// Authentication & Authorization Errors (4xx)
export const AUTH_ERRORS = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  // Keycloak-specific errors
  KE<PERSON><PERSON><PERSON><PERSON>_CONNECTION_ERROR: 'KEY<PERSON>OAK_CONNECTION_ERROR',
  K<PERSON><PERSON><PERSON>OAK_INVALID_GRANT: 'KEYCLOAK_INVALID_GRANT',
  KEYCLOAK_USER_EXISTS: 'KEYCLOAK_USER_EXISTS',
  KEYCLOAK_REALM_ERROR: 'KEYCLOAK_REALM_ERROR',
} as const;

// Validation Errors (4xx)
export const VALIDATION_ERRORS = {
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_PASSWORD: 'INVALID_PASSWORD',
  PASSWORD_TOO_WEAK: 'PASSWORD_TOO_WEAK',
} as const;

// Resource Errors (4xx)
export const RESOURCE_ERRORS = {
  NOT_FOUND: 'NOT_FOUND',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  DUPLICATE_RESOURCE: 'DUPLICATE_RESOURCE',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  FORBIDDEN: 'FORBIDDEN',
  CONFLICT: 'CONFLICT',
} as const;

// Rate Limiting & Throttling (4xx)
export const RATE_LIMIT_ERRORS = {
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  TOO_MANY_REQUESTS: 'TOO_MANY_REQUESTS',
  LOGIN_ATTEMPTS_EXCEEDED: 'LOGIN_ATTEMPTS_EXCEEDED',
} as const;

// System Errors (5xx)
export const SYSTEM_ERRORS = {
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
  TIMEOUT: 'TIMEOUT',
  GATEWAY_TIMEOUT: 'GATEWAY_TIMEOUT',
} as const;

// Business Logic Errors (custom 4xx)
export const BUSINESS_ERRORS = {
  BUSINESS_RULE_VIOLATION: 'BUSINESS_RULE_VIOLATION',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  INVALID_STATE_TRANSITION: 'INVALID_STATE_TRANSITION',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
} as const;

// All error codes combined
export const ERROR_CODES = {
  ...AUTH_ERRORS,
  ...VALIDATION_ERRORS,
  ...RESOURCE_ERRORS,
  ...RATE_LIMIT_ERRORS,
  ...SYSTEM_ERRORS,
  ...BUSINESS_ERRORS,
} as const;

// Type for all error codes
export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * Default error messages for each error code
 * Can be overridden by specific services
 */
export const DEFAULT_ERROR_MESSAGES: Record<ErrorCode, string> = {
  // Auth errors
  UNAUTHORIZED: 'Authentication required',
  INVALID_CREDENTIALS: 'Invalid username or password',
  TOKEN_EXPIRED: 'Your session has expired, please log in again',
  TOKEN_INVALID: 'Invalid authentication token',
  INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action',
  ACCOUNT_LOCKED: 'Your account has been locked due to security reasons',
  ACCOUNT_DISABLED: 'Your account has been disabled',
  // Keycloak-specific error messages
  KEYCLOAK_CONNECTION_ERROR: 'Authentication service is temporarily unavailable',
  KEYCLOAK_INVALID_GRANT: 'Invalid credentials or expired session',
  KEYCLOAK_USER_EXISTS: 'An account with this email already exists',
  KEYCLOAK_REALM_ERROR: 'Authentication realm configuration error',
  
  // Validation errors
  VALIDATION_FAILED: 'The provided data is invalid',
  INVALID_INPUT: 'Invalid input provided',
  MISSING_REQUIRED_FIELD: 'Required field is missing',
  INVALID_FORMAT: 'Invalid format provided',
  INVALID_EMAIL: 'Invalid email address format',
  INVALID_PASSWORD: 'Invalid password format',
  PASSWORD_TOO_WEAK: 'Password does not meet security requirements',
  
  // Resource errors
  NOT_FOUND: 'The requested resource was not found',
  USER_NOT_FOUND: 'User not found',
  RESOURCE_NOT_FOUND: 'Resource not found',
  DUPLICATE_RESOURCE: 'Resource already exists',
  RESOURCE_CONFLICT: 'Resource conflict occurred',
  EMAIL_ALREADY_EXISTS: 'An account with this email already exists',
  FORBIDDEN: 'Access to this resource is forbidden',
  CONFLICT: 'A conflict occurred with the current state of the resource',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded, please try again later',
  TOO_MANY_REQUESTS: 'Too many requests, please slow down',
  LOGIN_ATTEMPTS_EXCEEDED: 'Too many login attempts, please try again later',
  
  // System errors
  INTERNAL_SERVER_ERROR: 'An unexpected error occurred',
  SERVICE_UNAVAILABLE: 'Service is temporarily unavailable',
  DATABASE_ERROR: 'Database operation failed',
  EXTERNAL_SERVICE_ERROR: 'External service is unavailable',
  CONFIGURATION_ERROR: 'Service configuration error',
  TIMEOUT: 'Request timed out',
  GATEWAY_TIMEOUT: 'Gateway timeout occurred',
  
  // Business errors
  BUSINESS_RULE_VIOLATION: 'Business rule violation',
  OPERATION_NOT_ALLOWED: 'Operation not allowed',
  INVALID_STATE_TRANSITION: 'Invalid state transition',
  QUOTA_EXCEEDED: 'Quota exceeded',
};

/**
 * HTTP status codes for each error code
 */
export const ERROR_HTTP_STATUS: Record<ErrorCode, number> = {
  // 401 Unauthorized
  UNAUTHORIZED: 401,
  INVALID_CREDENTIALS: 401,
  TOKEN_EXPIRED: 401,
  TOKEN_INVALID: 401,
  KEYCLOAK_INVALID_GRANT: 401,
  
  // 403 Forbidden
  INSUFFICIENT_PERMISSIONS: 403,
  ACCOUNT_LOCKED: 403,
  ACCOUNT_DISABLED: 403,
  
  // 400 Bad Request
  VALIDATION_FAILED: 400,
  INVALID_INPUT: 400,
  MISSING_REQUIRED_FIELD: 400,
  INVALID_FORMAT: 400,
  INVALID_EMAIL: 400,
  INVALID_PASSWORD: 400,
  PASSWORD_TOO_WEAK: 400,
  
  // 404 Not Found
  NOT_FOUND: 404,
  USER_NOT_FOUND: 404,
  RESOURCE_NOT_FOUND: 404,
  
  // 403 Forbidden
  FORBIDDEN: 403,
  
  // 409 Conflict
  DUPLICATE_RESOURCE: 409,
  RESOURCE_CONFLICT: 409,
  EMAIL_ALREADY_EXISTS: 409,
  CONFLICT: 409,
  KEYCLOAK_USER_EXISTS: 409,
  
  // 429 Too Many Requests
  RATE_LIMIT_EXCEEDED: 429,
  TOO_MANY_REQUESTS: 429,
  LOGIN_ATTEMPTS_EXCEEDED: 429,
  
  // 500 Internal Server Error
  INTERNAL_SERVER_ERROR: 500,
  DATABASE_ERROR: 500,
  CONFIGURATION_ERROR: 500,
  
  // 503 Service Unavailable
  SERVICE_UNAVAILABLE: 503,
  EXTERNAL_SERVICE_ERROR: 503,
  KEYCLOAK_CONNECTION_ERROR: 503,
  KEYCLOAK_REALM_ERROR: 503,
  
  // 408 Request Timeout
  TIMEOUT: 408,
  
  // 504 Gateway Timeout
  GATEWAY_TIMEOUT: 504,
  
  // 422 Unprocessable Entity (Business Logic)
  BUSINESS_RULE_VIOLATION: 422,
  OPERATION_NOT_ALLOWED: 422,
  INVALID_STATE_TRANSITION: 422,
  QUOTA_EXCEEDED: 422,
};