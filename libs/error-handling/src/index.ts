/**
 * @libs/error-handling
 * 
 * Comprehensive error handling library with correlation ID support
 * for microservices architecture following Netflix-style patterns.
 * 
 * Features:
 * - Correlation ID generation and propagation
 * - Standardized error responses across services
 * - Environment-aware error filtering (dev vs prod)
 * - Loki integration for log correlation
 * - Swagger documentation support
 * - Security-first approach (no information leakage)
 */

// Core interfaces
export * from './interfaces/error-response.interface';
export * from './interfaces/correlation-context.interface';

// Error constants and codes
export * from './constants/error-codes';

// Utilities
export * from './utils/loki-query-builder';

// Services
export * from './services/correlation.service';
export * from './services/error-response-builder.service';
export * from './services/keycloak-error-handler.service';

// Middleware and filters
export * from './middleware/correlation-id.middleware';
export * from './filters/correlation-exception.filter';

// Interceptors
export * from './interceptors/correlation-http.interceptor';

// Decorators
export { 
  CorrelationId, 
  CorrelationContext as CorrelationContextDecorator,
  RequestDuration 
} from './decorators/correlation-id.decorator';

// Swagger Error Response Decorators
export {
  ApiErrorResponse,
  ApiErrorResponses,
  CommonApiErrors,
  ErrorResponseSchema,
  type ApiErrorResponseOptions
} from './decorators/api-error-response.decorator';

// Module
export * from './error-handling.module';