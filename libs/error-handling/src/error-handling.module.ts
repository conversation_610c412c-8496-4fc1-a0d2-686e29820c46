import { Module, DynamicModule, Global, Provider } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { CorrelationService } from './services/correlation.service';
import { ErrorResponseBuilderService } from './services/error-response-builder.service';
import { KeycloakErrorHandlerService } from './services/keycloak-error-handler.service';
import { CorrelationExceptionFilter } from './filters/correlation-exception.filter';
import { CorrelationHttpInterceptor } from './interceptors/correlation-http.interceptor';
import { ErrorResponseConfig } from './interfaces/error-response.interface';

export interface ErrorHandlingModuleOptions {
  config?: Partial<ErrorResponseConfig>;
  /**
   * Whether to register the global exception filter
   * Default: true
   */
  useGlobalFilter?: boolean;
  
  /**
   * Whether to register the HTTP interceptor
   * Default: true
   */
  useHttpInterceptor?: boolean;
}

/**
 * Error handling module providing correlation ID tracking and standardized error responses
 */
@Global()
@Module({})
export class ErrorHandlingModule {
  /**
   * Register error handling module with configuration
   */
  static forRoot(options: ErrorHandlingModuleOptions = {}): DynamicModule {
    const {
      config = {},
      useGlobalFilter = true,
      useHttpInterceptor = true,
    } = options;

    const providers: Provider[] = [
      CorrelationService,
      {
        provide: ErrorResponseBuilderService,
        useFactory: (correlationService: CorrelationService) => {
          return new ErrorResponseBuilderService(correlationService, config);
        },
        inject: [CorrelationService],
      },
      {
        provide: KeycloakErrorHandlerService,
        useFactory: (errorBuilder: ErrorResponseBuilderService) => {
          return new KeycloakErrorHandlerService(errorBuilder);
        },
        inject: [ErrorResponseBuilderService],
      },
    ];

    // Add global filter if enabled
    if (useGlobalFilter) {
      providers.push({
        provide: APP_FILTER,
        useFactory: (errorBuilder: ErrorResponseBuilderService, correlationService: CorrelationService) => {
          return new CorrelationExceptionFilter(errorBuilder, correlationService);
        },
        inject: [ErrorResponseBuilderService, CorrelationService],
      });
    }

    // Add HTTP interceptor if enabled
    if (useHttpInterceptor) {
      providers.push({
        provide: APP_INTERCEPTOR,
        useFactory: (correlationService: CorrelationService) => {
          return new CorrelationHttpInterceptor(correlationService);
        },
        inject: [CorrelationService],
      });
    }

    return {
      module: ErrorHandlingModule,
      providers,
      exports: [
        CorrelationService,
        ErrorResponseBuilderService,
        KeycloakErrorHandlerService,
      ],
    };
  }

  /**
   * Register for feature modules (without global providers)
   */
  static forFeature(): DynamicModule {
    return {
      module: ErrorHandlingModule,
      providers: [
        CorrelationService,
        ErrorResponseBuilderService,
      ],
      exports: [
        CorrelationService,
        ErrorResponseBuilderService,
      ],
    };
  }
}