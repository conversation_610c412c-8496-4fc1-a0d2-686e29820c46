import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CorrelationService } from '../services/correlation.service';

/**
 * HTTP interceptor that adds correlation headers to outgoing HTTP requests
 * Works with @libs/nestjs-common HttpClientService for automatic correlation propagation
 */
@Injectable()
export class CorrelationHttpInterceptor implements NestInterceptor {
  constructor(private readonly correlationService: CorrelationService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    // Ensure correlation headers are set in response
    const correlationId = this.correlationService.getCorrelationId();
    if (correlationId && response) {
      response.setHeader('x-correlation-id', correlationId);
      response.setHeader('x-request-id', correlationId);
    }

    return next.handle().pipe(
      map((data) => {
        // Add correlation metadata to response if needed
        if (data && typeof data === 'object' && !Array.isArray(data)) {
          const context = this.correlationService.getContext();
          if (context) {
            // Only add in development mode for debugging
            if (process.env.NODE_ENV !== 'production') {
              data._correlation = {
                id: context.correlationId,
                duration: this.correlationService.getRequestDuration(),
                service: context.service.name,
              };
            }
          }
        }
        return data;
      })
    );
  }
}