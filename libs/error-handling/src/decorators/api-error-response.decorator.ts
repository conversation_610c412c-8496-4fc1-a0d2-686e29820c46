import { applyDecorators, Type } from '@nestjs/common';
import { ApiResponse, ApiResponseOptions } from '@nestjs/swagger';
import { ERROR_CODES, ERROR_HTTP_STATUS, DEFAULT_ERROR_MESSAGES, ErrorCode } from '../constants/error-codes';
import { ErrorResponse } from '../interfaces/error-response.interface';

/**
 * Configuration options for API error response decorators
 */
export interface ApiErrorResponseOptions {
  description?: string;
  statusCode?: number;
  example?: Partial<ErrorResponse>;
  isArray?: boolean;
}

/**
 * Enhanced decorator for documenting API error responses with compile-time validation
 * 
 * @param errorCode - Compile-time validated error code from ERROR_CODES
 * @param description - Optional custom description (defaults to DEFAULT_ERROR_MESSAGES)
 * @param options - Additional configuration options
 * 
 * @example
 * ```typescript
 * @ApiErrorResponse(ERROR_CODES.USER_NOT_FOUND, 'User with specified ID does not exist')
 * @ApiErrorResponse(ERROR_CODES.VALIDATION_FAILED)
 * @Get('users/:id')
 * async getUser(@Param('id') id: string): Promise<UserResponseDto> {
 *   // Implementation
 * }
 * ```
 */
export function ApiErrorResponse<T extends ErrorCode>(
  errorCode: T,
  description?: string,
  options?: Omit<ApiErrorResponseOptions, 'statusCode'>
): MethodDecorator {
  // Compile-time validation: errorCode must be from ERROR_CODES
  const statusCode = ERROR_HTTP_STATUS[errorCode];
  const defaultDescription = DEFAULT_ERROR_MESSAGES[errorCode];
  const finalDescription = description || defaultDescription;

  // Generate realistic example with proper correlation ID format
  const exampleCorrelationId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  
  const exampleResponse: ErrorResponse = {
    statusCode,
    error: errorCode,
    message: finalDescription,
    correlationId: exampleCorrelationId,
    timestamp: new Date().toISOString(),
    path: '/api/example/endpoint',
    logQuery: `http://localhost:3100/loki/api/v1/query_range?query=%7Bservice%3D%22example-service%22%2CcorrelationId%3D%22${exampleCorrelationId}%22%7D`,
    ...(options?.example || {})
  };

  const responseOptions: ApiResponseOptions = {
    status: statusCode,
    description: finalDescription,
    schema: {
      type: 'object',
      properties: {
        statusCode: {
          type: 'number',
          example: statusCode,
          description: 'HTTP status code'
        },
        error: {
          type: 'string',
          example: errorCode,
          description: 'Machine-readable error code for frontend handling',
          enum: Object.values(ERROR_CODES)
        },
        message: {
          type: 'string',
          example: finalDescription,
          description: 'Human-readable error message'
        },
        correlationId: {
          type: 'string',
          example: exampleCorrelationId,
          description: 'Unique request identifier for debugging and log correlation',
          pattern: '^req-\\d+-[a-z0-9]+$'
        },
        timestamp: {
          type: 'string',
          format: 'date-time',
          example: exampleResponse.timestamp,
          description: 'ISO 8601 timestamp when error occurred'
        },
        path: {
          type: 'string',
          example: '/api/example/endpoint',
          description: 'Request path where error occurred'
        },
        logQuery: {
          type: 'string',
          format: 'uri',
          example: exampleResponse.logQuery,
          description: 'Direct link to relevant logs in Loki for debugging',
          nullable: true
        },
        traceId: {
          type: 'string',
          description: 'Distributed trace ID (future enhancement)',
          nullable: true
        },
        details: {
          type: 'object',
          description: 'Additional error details (development mode only)',
          nullable: true,
          properties: {
            stack: {
              type: 'string',
              description: 'Stack trace (development mode only)'
            },
            context: {
              type: 'object',
              description: 'Request context information'
            }
          }
        }
      },
      required: ['statusCode', 'error', 'message', 'correlationId', 'timestamp', 'path'],
      example: exampleResponse
    }
  };

  return applyDecorators(
    ApiResponse(responseOptions)
  );
}

/**
 * Decorator for multiple error responses with compile-time validation
 * 
 * @param errorConfigs - Array of error configurations
 * 
 * @example
 * ```typescript
 * @ApiErrorResponses([
 *   { errorCode: ERROR_CODES.USER_NOT_FOUND, description: 'User not found' },
 *   { errorCode: ERROR_CODES.VALIDATION_FAILED, description: 'Invalid input' },
 *   { errorCode: ERROR_CODES.UNAUTHORIZED }
 * ])
 * @Post('users')
 * async createUser(@Body() userData: CreateUserDto): Promise<UserResponseDto> {
 *   // Implementation
 * }
 * ```
 */
export function ApiErrorResponses<T extends ErrorCode>(
  errorConfigs: Array<{
    errorCode: T;
    description?: string;
    options?: Omit<ApiErrorResponseOptions, 'statusCode'>;
  }>
): MethodDecorator {
  const decorators = errorConfigs.map(config => 
    ApiErrorResponse(config.errorCode, config.description, config.options)
  );
  
  return applyDecorators(...decorators);
}

/**
 * Common error response decorator combinations for typical REST endpoints
 */
export const CommonApiErrors = {
  /**
   * Standard errors for authenticated endpoints
   */
  Authenticated: () => ApiErrorResponses([
    { errorCode: ERROR_CODES.UNAUTHORIZED, description: 'Authentication required' },
    { errorCode: ERROR_CODES.FORBIDDEN, description: 'Insufficient permissions' }
  ]),

  /**
   * Standard errors for resource retrieval endpoints
   */
  ResourceRetrieval: () => ApiErrorResponses([
    { errorCode: ERROR_CODES.NOT_FOUND, description: 'Resource not found' },
    { errorCode: ERROR_CODES.UNAUTHORIZED },
    { errorCode: ERROR_CODES.FORBIDDEN }
  ]),

  /**
   * Standard errors for data validation endpoints
   */
  Validation: () => ApiErrorResponses([
    { errorCode: ERROR_CODES.VALIDATION_FAILED, description: 'Request validation failed' },
    { errorCode: ERROR_CODES.INVALID_INPUT, description: 'Invalid input provided' },
    { errorCode: ERROR_CODES.MISSING_REQUIRED_FIELD, description: 'Required field missing' }
  ]),

  /**
   * Standard errors for rate-limited endpoints
   */
  RateLimited: () => ApiErrorResponses([
    { errorCode: ERROR_CODES.RATE_LIMIT_EXCEEDED, description: 'Rate limit exceeded' },
    { errorCode: ERROR_CODES.TOO_MANY_REQUESTS, description: 'Too many requests' }
  ]),

  /**
   * Complete set of common errors for typical CRUD endpoints
   */
  CRUD: () => ApiErrorResponses([
    { errorCode: ERROR_CODES.VALIDATION_FAILED },
    { errorCode: ERROR_CODES.UNAUTHORIZED },
    { errorCode: ERROR_CODES.FORBIDDEN },
    { errorCode: ERROR_CODES.NOT_FOUND },
    { errorCode: ERROR_CODES.CONFLICT },
    { errorCode: ERROR_CODES.RATE_LIMIT_EXCEEDED },
    { errorCode: ERROR_CODES.INTERNAL_SERVER_ERROR }
  ])
};

/**
 * Type-safe error response schema for OpenAPI generation
 */
export const ErrorResponseSchema = {
  type: 'object' as const,
  properties: {
    statusCode: { type: 'number' as const },
    error: { 
      type: 'string' as const,
      enum: Object.values(ERROR_CODES)
    },
    message: { type: 'string' as const },
    correlationId: { 
      type: 'string' as const,
      pattern: '^req-\\d+-[a-z0-9]+$'
    },
    timestamp: { 
      type: 'string' as const,
      format: 'date-time' as const
    },
    path: { type: 'string' as const },
    logQuery: { 
      type: 'string' as const,
      format: 'uri' as const,
      nullable: true
    },
    traceId: { 
      type: 'string' as const,
      nullable: true
    },
    details: { 
      type: 'object' as const,
      nullable: true
    }
  },
  required: ['statusCode', 'error', 'message', 'correlationId', 'timestamp', 'path']
} as const;