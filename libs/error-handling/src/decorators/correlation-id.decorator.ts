import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { CorrelationService } from '../services/correlation.service';

/**
 * Parameter decorator to inject correlation ID into controller methods
 */
export const CorrelationId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest();
    
    // Try to get from correlation service first
    const correlationService = new CorrelationService();
    const correlationId = correlationService.getCorrelationId();
    
    if (correlationId) {
      return correlationId;
    }

    // Fallback to request object
    return (request as any).correlationId;
  },
);

/**
 * Parameter decorator to inject full correlation context into controller methods
 */
export const CorrelationContext = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const correlationService = new CorrelationService();
    return correlationService.getContext();
  },
);

/**
 * Parameter decorator to inject request duration in milliseconds
 */
export const RequestDuration = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): number | undefined => {
    const correlationService = new CorrelationService();
    return correlationService.getRequestDuration();
  },
);