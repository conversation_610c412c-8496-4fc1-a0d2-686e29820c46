/**
 * Utility for building Loki query URLs for error correlation
 */

/**
 * Configuration for Loki query generation
 */
export interface LokiQueryConfig {
  /** Base URL for Loki instance */
  baseUrl: string;
  
  /** Default time range for queries (in minutes) */
  defaultTimeRange: number;
  
  /** Service label name in Loki */
  serviceLabelName: string;
  
  /** Correlation ID label name in Loki */
  correlationLabelName: string;
}

/**
 * Default Loki configuration
 */
export const DEFAULT_LOKI_CONFIG: LokiQueryConfig = {
  baseUrl: 'http://localhost:3100',
  defaultTimeRange: 60, // 60 minutes
  serviceLabelName: 'service',
  correlationLabelName: 'correlationId',
};

/**
 * Build a Loki query URL for correlation ID tracking
 */
export class LokiQueryBuilder {
  constructor(private config: LokiQueryConfig = DEFAULT_LOKI_CONFIG) {}

  /**
   * Generate Loki query URL for a specific correlation ID
   */
  buildCorrelationQuery(correlationId: string, options?: {
    serviceName?: string;
    timeRangeMinutes?: number;
    logLevel?: 'error' | 'warn' | 'info' | 'debug';
  }): string {
    const timeRange = options?.timeRangeMinutes || this.config.defaultTimeRange;
    const endTime = Date.now();
    const startTime = endTime - (timeRange * 60 * 1000);

    // Build LogQL query
    let logQuery = `{${this.config.correlationLabelName}="${correlationId}"}`;
    
    if (options?.serviceName) {
      logQuery = `{${this.config.serviceLabelName}="${options.serviceName}",${this.config.correlationLabelName}="${correlationId}"}`;
    }
    
    if (options?.logLevel) {
      logQuery += ` |= "${options.logLevel.toUpperCase()}"`;
    }

    // Build complete URL
    const queryParams = new URLSearchParams({
      query: logQuery,
      start: (startTime * 1000000).toString(), // Loki expects nanoseconds
      end: (endTime * 1000000).toString(),
      limit: '1000',
      direction: 'backward', // Most recent first
    });

    return `${this.config.baseUrl}/loki/api/v1/query_range?${queryParams.toString()}`;
  }

  /**
   * Generate Grafana Explore URL for correlation ID
   */
  buildGrafanaExploreUrl(correlationId: string, options?: {
    serviceName?: string;
    timeRangeMinutes?: number;
    grafanaBaseUrl?: string;
  }): string {
    const grafanaUrl = options?.grafanaBaseUrl || 'http://localhost:3200';
    const timeRange = options?.timeRangeMinutes || this.config.defaultTimeRange;
    
    // Build LogQL query for Grafana
    let logQuery = `{${this.config.correlationLabelName}="${correlationId}"}`;
    
    if (options?.serviceName) {
      logQuery = `{${this.config.serviceLabelName}="${options.serviceName}",${this.config.correlationLabelName}="${correlationId}"}`;
    }

    // Grafana explore URL format
    const exploreParams = {
      left: JSON.stringify({
        datasource: 'Loki',
        queries: [{
          expr: logQuery,
          refId: 'A',
        }],
        range: {
          from: `now-${timeRange}m`,
          to: 'now',
        },
      }),
    };

    const encodedParams = encodeURIComponent(JSON.stringify(exploreParams));
    return `${grafanaUrl}/explore?left=${encodedParams}`;
  }

  /**
   * Generate simple Loki logs URL for error responses
   */
  buildSimpleLogsUrl(correlationId: string, serviceName?: string): string {
    const queryParams = new URLSearchParams({
      query: serviceName 
        ? `{${this.config.serviceLabelName}="${serviceName}",${this.config.correlationLabelName}="${correlationId}"}`
        : `{${this.config.correlationLabelName}="${correlationId}"}`,
      from: 'now-1h',
      to: 'now',
    });

    return `${this.config.baseUrl}/loki/api/v1/query_range?${queryParams.toString()}`;
  }

  /**
   * Build query for error pattern analysis
   */
  buildErrorPatternQuery(
    serviceName: string,
    timeRangeMinutes: number = 60,
    errorLevel: string = 'error'
  ): string {
    const queryParams = new URLSearchParams({
      query: `{${this.config.serviceLabelName}="${serviceName}"} |= "${errorLevel.toUpperCase()}" | json | line_format "{{.timestamp}} {{.level}} {{.message}}"`,
      from: `now-${timeRangeMinutes}m`,
      to: 'now',
      limit: '100',
    });

    return `${this.config.baseUrl}/loki/api/v1/query_range?${queryParams.toString()}`;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<LokiQueryConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

/**
 * Default Loki query builder instance
 */
export const lokiQueryBuilder = new LokiQueryBuilder();

/**
 * Utility function to quickly generate correlation query URL
 */
export function buildCorrelationQueryUrl(
  correlationId: string,
  serviceName?: string,
  config?: Partial<LokiQueryConfig>
): string {
  const builder = config ? new LokiQueryBuilder({ ...DEFAULT_LOKI_CONFIG, ...config }) : lokiQueryBuilder;
  return builder.buildCorrelationQuery(correlationId, { serviceName });
}

/**
 * Utility function to quickly generate Grafana explore URL
 */
export function buildGrafanaCorrelationUrl(
  correlationId: string,
  serviceName?: string,
  grafanaBaseUrl?: string
): string {
  return lokiQueryBuilder.buildGrafanaExploreUrl(correlationId, {
    serviceName,
    grafanaBaseUrl,
  });
}