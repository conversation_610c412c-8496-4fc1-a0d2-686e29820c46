/**
 * Example: Keycloak Service Integration with Enhanced Error Handling
 * 
 * This shows how to refactor the existing KeycloakService to use
 * our standardized error handling with correlation ID tracking.
 */

import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  KeycloakErrorHandlerService, 
  CorrelationId,
  HandleKeycloakErrors,
  ErrorResponseBuilderService,
  ERROR_CODES
} from '@libs/error-handling';
import { HttpClientService } from '@libs/http';
import { LOGGER_SERVICE, ObservabilityLogger } from '@libs/observability';

@Injectable()
export class KeycloakServiceEnhanced {
  constructor(
    private readonly configService: ConfigService,
    private readonly httpClient: HttpClientService,
    private readonly keycloakErrorHandler: KeycloakErrorHandlerService,
    private readonly errorBuilder: ErrorResponseBuilderService,
    @Inject(LOGGER_SERVICE) private readonly logger: ObservabilityLogger,
  ) {
    this.logger.setContext('KeycloakServiceEnhanced');
  }

  /**
   * Enhanced authentication with standardized error handling
   * 
   * BEFORE: Manual error mapping, no correlation tracking
   * AFTER: Automatic error standardization with correlation IDs
   */
  @HandleKeycloakErrors('user authentication')
  async authenticateUser(
    email: string, 
    password: string,
    @CorrelationId() correlationId?: string
  ): Promise<any> {
    this.logger.log(`Authenticating user: ${email} [${correlationId}]`);

    const tokenUrl = `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`;
    const payload = new URLSearchParams({
      grant_type: 'password',
      client_id: this.clientId,
      client_secret: this.clientSecret,
      username: email,
      password: password,
    });

    // The @HandleKeycloakErrors decorator automatically catches and transforms errors
    const response = await this.httpClient.makeRequest('POST', tokenUrl, payload.toString(), {
      serviceName: 'KeycloakService',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    this.logger.log(`Authentication successful for user: ${email} [${correlationId}]`);
    return response.data;
  }

  /**
   * Enhanced token refresh with specific error handling
   */
  async refreshToken(
    refreshToken: string,
    @CorrelationId() correlationId?: string
  ): Promise<any> {
    this.logger.log(`Refreshing access token [${correlationId}]`);

    try {
      const tokenUrl = `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`;
      const payload = new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        refresh_token: refreshToken,
      });

      const response = await this.httpClient.post(tokenUrl, payload.toString(), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      return response.data;
    } catch (error) {
      // Use specific token error handling
      throw this.keycloakErrorHandler.handleTokenError(error, 'refresh');
    }
  }

  /**
   * Enhanced user creation with conflict handling
   */
  async createUser(
    user: { email: string; password: string; firstName: string; lastName: string },
    @CorrelationId() correlationId?: string
  ): Promise<string> {
    this.logger.log(`Creating new user with email: ${user.email} [${correlationId}]`);

    try {
      const adminToken = await this.getAdminAccessToken();
      const usersUrl = `${this.baseUrl}/admin/realms/${this.realm}/users`;
      
      const userData = {
        email: user.email,
        username: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        enabled: true,
        credentials: [{
          type: 'password',
          value: user.password,
          temporary: false,
        }],
      };

      const response = await this.httpClient.post(usersUrl, userData, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
      });

      // Extract user ID from Location header
      const location = response.headers['location'];
      const userId = location.split('/').pop();

      this.logger.log(`User created successfully with ID: ${userId} [${correlationId}]`);
      return userId;
    } catch (error) {
      // Use specific user management error handling
      throw this.keycloakErrorHandler.handleUserManagementError(error, 'create user');
    }
  }

  /**
   * Enhanced password reset with token validation
   */
  async resetPassword(
    token: string, 
    newPassword: string,
    @CorrelationId() correlationId?: string
  ): Promise<void> {
    this.logger.log(`Resetting password with token [${correlationId}]`);

    try {
      // Validate and decode the reset token
      const userId = this.decodeResetToken(token);
      if (!userId) {
        throw this.errorBuilder.createHttpException(
          'TOKEN_INVALID',
          'Password reset token is invalid or expired'
        );
      }

      const adminToken = await this.getAdminAccessToken();
      const userUrl = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}`;
      
      const userData = {
        credentials: [{
          type: 'password',
          value: newPassword,
          temporary: false,
        }],
      };

      await this.httpClient.put(userUrl, userData, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json',
        },
      });

      this.logger.log(`Password reset successful for user ${userId} [${correlationId}]`);
    } catch (error) {
      throw this.keycloakErrorHandler.handleTokenError(error, 'reset');
    }
  }

  /**
   * Wrapper method using the generic operation wrapper
   */
  async logoutUser(
    refreshToken: string,
    @CorrelationId() correlationId?: string
  ): Promise<void> {
    return this.keycloakErrorHandler.wrapKeycloakOperation(
      async () => {
        this.logger.log(`Logging out user [${correlationId}]`);
        
        const logoutUrl = `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/logout`;
        const payload = new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          refresh_token: refreshToken,
        });

        await this.httpClient.post(logoutUrl, payload.toString(), {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        });

        this.logger.log(`User logout successful [${correlationId}]`);
      },
      'user logout'
    );
  }

  // Private helper methods (same as original)
  private async getAdminAccessToken(): Promise<string> {
    // Implementation same as original
    return 'admin-token';
  }

  private decodeResetToken(token: string): string | null {
    // Implementation same as original
    return 'user-id';
  }

  private get baseUrl(): string {
    return this.configService.get<string>('KEYCLOAK_BASE_URL') || '';
  }

  private get realm(): string {
    return this.configService.get<string>('KEYCLOAK_REALM_NAME') || '';
  }

  private get clientId(): string {
    return this.configService.get<string>('KEYCLOAK_CLIENT_ID') || '';
  }

  private get clientSecret(): string {
    return this.configService.get<string>('KEYCLOAK_CLIENT_SECRET') || '';
  }
}

/**
 * Migration Strategy:
 * 
 * 1. Add ErrorHandlingModule to auth-service app.module.ts
 * 2. Inject KeycloakErrorHandlerService into existing KeycloakService
 * 3. Replace manual error handling with standardized methods
 * 4. Add @CorrelationId decorators to methods
 * 5. Test error scenarios with correlation tracking
 * 
 * Benefits:
 * - Consistent error responses across all auth endpoints
 * - Automatic correlation ID tracking for debugging
 * - Frontend-friendly error codes
 * - Direct links to Loki logs from error responses
 * - Centralized Keycloak error transformation
 */