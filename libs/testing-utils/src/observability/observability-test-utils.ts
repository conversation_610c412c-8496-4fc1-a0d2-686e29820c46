import { LogLevel } from '@nestjs/common';

/**
 * In-Memory Log Capture for Testing
 * 
 * This utility captures logs in memory during tests without external dependencies.
 * Perfect for testing log structure, content, and formatting.
 */
export class TestLogCapture {
  private logs: Array<{
    level: LogLevel;
    message: string;
    context?: string;
    timestamp: Date;
    metadata?: any;
  }> = [];

  captureLog(level: LogLevel, message: string, context?: string, metadata?: any) {
    this.logs.push({
      level,
      message,
      context,
      timestamp: new Date(),
      metadata
    });
  }

  // Search methods for test verification
  findLogByMessage(message: string) {
    return this.logs.find(log => log.message.includes(message));
  }

  findLogsByLevel(level: LogLevel) {
    return this.logs.filter(log => log.level === level);
  }

  findLogsByContext(context: string) {
    return this.logs.filter(log => log.context === context);
  }

  // Test assertion helpers
  expectLogExists(message: string): boolean {
    return this.logs.some(log => log.message.includes(message));
  }

  expectBusinessEvent(eventType: string, action: string): boolean {
    return this.logs.some(log => 
      log.message.includes(eventType) && 
      log.message.includes(action)
    );
  }

  // Cleanup
  clear() {
    this.logs = [];
  }

  // Get all logs for debugging
  getAllLogs() {
    return [...this.logs];
  }

  getLogsCount() {
    return this.logs.length;
  }
}

/**
 * Direct Loki HTTP API Client for Tests
 * 
 * Query Loki directly without Grafana dependencies for real observability testing
 */
export class LokiTestClient {
  private readonly lokiUrl: string;

  constructor(lokiUrl: string = 'http://localhost:3100') {
    this.lokiUrl = lokiUrl;
  }

  async queryLogs(query: string, limit: number = 10, startTime?: Date, endTime?: Date): Promise<any> {
    // Use wider time range if not specified - last 10 minutes to account for delays
    const end = endTime ? endTime.getTime() * 1000000 : Date.now() * 1000000; // Convert to nanoseconds
    const start = startTime ? startTime.getTime() * 1000000 : (Date.now() - 600000) * 1000000; // 10 minutes ago

    const params = new URLSearchParams({
      query,
      limit: limit.toString(),
      start: start.toString(),
      end: end.toString()
    });

    console.log(`[LokiTestClient] Query: ${query}`);
    console.log(`[LokiTestClient] Time range: ${new Date(start / 1000000).toISOString()} to ${new Date(end / 1000000).toISOString()}`);
    console.log(`[LokiTestClient] URL: ${this.lokiUrl}/loki/api/v1/query_range?${params}`);

    const response = await fetch(`${this.lokiUrl}/loki/api/v1/query_range?${params}`);
    const data = await response.json();
    
    console.log(`[LokiTestClient] Response status: ${response.status}`);
    console.log(`[LokiTestClient] Results found: ${data.data?.result?.length || 0}`);
    if (data.data?.result?.length > 0) {
      console.log(`[LokiTestClient] Total entries: ${data.data.result.reduce((acc: number, stream: any) => acc + (stream.values?.length || 0), 0)}`);
    }

    return data;
  }

  async findLogsByService(service: string, limit: number = 10) {
    return this.queryLogs(`{service="${service}"}`, limit);
  }

  async findLogsByMarker(marker: string, limit: number = 10) {
    return this.queryLogs(`{service="auth-service"} |= "${marker}"`, limit);
  }

  async checkLokiHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.lokiUrl}/ready`);
      const text = await response.text();
      console.log(`[LokiTestClient] Health check: ${response.status} - ${text}`);
      return response.ok && text.trim() === 'ready';
    } catch (error) {
      console.log(`[LokiTestClient] Health check failed: ${(error as Error).message}`);
      return false;
    }
  }

  /**
   * Wait for a log with specific marker to appear in Loki within timeout
   * This ensures real integration testing - logs MUST reach Loki or test fails
   */
  async waitForLogInLoki(marker: string, timeoutMs: number = 5000): Promise<any[]> {
    const startTime = Date.now();
    const pollInterval = 500; // Check every 500ms
    let attempt = 0;
    
    console.log(`[LokiTestClient] Waiting for log with marker "${marker}" (timeout: ${timeoutMs}ms)`);
    
    while (Date.now() - startTime < timeoutMs) {
      attempt++;
      console.log(`[LokiTestClient] Attempt ${attempt}: Checking for marker "${marker}"`);
      
      try {
        const results = await this.findLogsByMarker(marker, 10);
        
        if (results && results.data && results.data.result && results.data.result.length > 0) {
          const foundLogs = [];
          
          // Parse Loki response format
          for (const stream of results.data.result) {
            if (stream.values && stream.values.length > 0) {
              for (const [timestamp, logLine] of stream.values) {
                if (logLine.includes(marker)) {
                  foundLogs.push({
                    timestamp,
                    logLine,
                    marker,
                    stream: stream.stream,
                    found: true
                  });
                }
              }
            }
          }
          
          if (foundLogs.length > 0) {
            const elapsed = Date.now() - startTime;
            console.log(`[LokiTestClient] ✅ Found ${foundLogs.length} logs with marker "${marker}" after ${elapsed}ms`);
            return foundLogs;
          }
        }
        
        console.log(`[LokiTestClient] Attempt ${attempt}: No logs found yet, waiting ${pollInterval}ms...`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        
      } catch (error) {
        console.log(`[LokiTestClient] Attempt ${attempt} failed: ${(error as Error).message}`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }
    
    const elapsed = Date.now() - startTime;
    throw new Error(`Timeout: Log with marker "${marker}" not found in Loki within ${timeoutMs}ms (elapsed: ${elapsed}ms). This indicates a real integration problem.`);
  }

  /**
   * Get all recent logs for debugging
   */
  async getRecentLogs(limit: number = 20): Promise<any> {
    console.log(`[LokiTestClient] Fetching ${limit} most recent auth-service logs...`);
    return this.queryLogs(`{service="auth-service"}`, limit);
  }
}

/**
 * Prometheus Test Client for Metrics Testing
 * 
 * Query Prometheus directly for metrics validation in tests
 */
export class PrometheusTestClient {
  private readonly prometheusUrl: string;

  constructor(prometheusUrl: string = 'http://localhost:9090') {
    this.prometheusUrl = prometheusUrl;
  }

  async queryMetric(query: string): Promise<any> {
    const params = new URLSearchParams({
      query,
      time: (Date.now() / 1000).toString()
    });

    console.log(`[PrometheusTestClient] Query: ${query}`);
    
    const response = await fetch(`${this.prometheusUrl}/api/v1/query?${params}`);
    const data = await response.json();
    
    console.log(`[PrometheusTestClient] Response status: ${response.status}`);
    console.log(`[PrometheusTestClient] Results: ${data.data?.result?.length || 0} metrics found`);
    
    return data;
  }

  async queryRange(query: string, start: Date, end: Date, step: string = '30s'): Promise<any> {
    const params = new URLSearchParams({
      query,
      start: (start.getTime() / 1000).toString(),
      end: (end.getTime() / 1000).toString(),
      step
    });

    const response = await fetch(`${this.prometheusUrl}/api/v1/query_range?${params}`);
    const data = await response.json();
    
    console.log(`[PrometheusTestClient] Range query results: ${data.data?.result?.length || 0} metrics found`);
    
    return data;
  }

  async checkPrometheusHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.prometheusUrl}/-/healthy`);
      const text = await response.text();
      console.log(`[PrometheusTestClient] Health check: ${response.status} - ${text}`);
      return response.ok;
    } catch (error) {
      console.log(`[PrometheusTestClient] Health check failed: ${(error as Error).message}`);
      return false;
    }
  }

  /**
   * Wait for a metric to appear or reach a specific value
   */
  async waitForMetric(query: string, expectedValue?: number, timeoutMs: number = 10000): Promise<any> {
    const startTime = Date.now();
    const pollInterval = 1000; // Check every 1 second
    let attempt = 0;
    
    console.log(`[PrometheusTestClient] Waiting for metric: ${query} ${expectedValue ? `= ${expectedValue}` : ''}`);
    
    while (Date.now() - startTime < timeoutMs) {
      attempt++;
      console.log(`[PrometheusTestClient] Attempt ${attempt}: Checking metric`);
      
      try {
        const result = await this.queryMetric(query);
        
        if (result && result.data && result.data.result && result.data.result.length > 0) {
          const metricValue = parseFloat(result.data.result[0].value[1]);
          
          if (expectedValue === undefined || metricValue === expectedValue) {
            const elapsed = Date.now() - startTime;
            console.log(`[PrometheusTestClient] ✅ Found metric after ${elapsed}ms: ${metricValue}`);
            return result;
          }
          
          console.log(`[PrometheusTestClient] Metric found but value ${metricValue} != expected ${expectedValue}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        
      } catch (error) {
        console.log(`[PrometheusTestClient] Attempt ${attempt} failed: ${(error as Error).message}`);
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }
    
    const elapsed = Date.now() - startTime;
    throw new Error(`Timeout: Metric "${query}" not found within ${timeoutMs}ms (elapsed: ${elapsed}ms)`);
  }
}

/**
 * Combined Observability Test Helper
 * 
 * Provides unified interface for testing logs, metrics, and traces
 */
export class ObservabilityTestHelper {
  public readonly loki: LokiTestClient;
  public readonly prometheus: PrometheusTestClient;
  public readonly logCapture: TestLogCapture;

  constructor(lokiUrl?: string, prometheusUrl?: string) {
    this.loki = new LokiTestClient(lokiUrl);
    this.prometheus = new PrometheusTestClient(prometheusUrl);
    this.logCapture = new TestLogCapture();
  }

  /**
   * Check if all observability services are available
   */
  async checkObservabilityHealth(): Promise<{
    loki: boolean;
    prometheus: boolean;
    overall: boolean;
  }> {
    const [lokiHealth, prometheusHealth] = await Promise.all([
      this.loki.checkLokiHealth(),
      this.prometheus.checkPrometheusHealth()
    ]);

    return {
      loki: lokiHealth,
      prometheus: prometheusHealth,
      overall: lokiHealth && prometheusHealth
    };
  }

  /**
   * Generate unique test marker for tracking in observability stack
   */
  generateTestMarker(testName: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `test-${testName}-${timestamp}-${random}`;
  }

  /**
   * Wait for business event to appear in both logs and metrics
   */
  async waitForBusinessEvent(
    eventType: string, 
    marker: string, 
    timeoutMs: number = 10000
  ): Promise<{
    logs: any[];
    metrics?: any;
  }> {
    console.log(`[ObservabilityTestHelper] Waiting for business event: ${eventType} with marker: ${marker}`);
    
    const [logs] = await Promise.all([
      // Wait for log entry
      this.loki.waitForLogInLoki(marker, timeoutMs),
      
      // Optionally wait for metric (if business events create metrics)
      // this.prometheus.waitForMetric(`business_events_total{event_type="${eventType}"}`, undefined, timeoutMs)
    ]);

    return { logs };
  }

  /**
   * Cleanup test markers and data
   */
  cleanup() {
    this.logCapture.clear();
    console.log('[ObservabilityTestHelper] Cleanup completed');
  }
}