import { KeycloakTestUtils, TestSession } from '../keycloak/keycloak-test-utils';
import { TestEnvironment, TestType } from '../utilities/test-environment';

/**
 * Service endpoint configuration
 */
export interface ServiceEndpoints {
  authService: string;
  userService: string;
  apiGateway: string;
  keycloak: string;
  database: string;
  redis: string;
}

/**
 * Test environment configuration
 */
export interface TestEnvironmentConfig {
  endpoints: ServiceEndpoints;
  cleanupOnFailure: boolean;
  testTimeout: number;
  maxRetries: number;
}

/**
 * Integration test context
 */
export interface IntegrationTestContext {
  sessionId: string;
  config: TestEnvironmentConfig;
  keycloakSession?: TestSession;
  cleanup: (() => Promise<void>)[];
  startTime: Date;
  testType: TestType;
}

/**
 * Service health status
 */
export interface ServiceHealth {
  service: string;
  healthy: boolean;
  responseTime: number;
  error?: string;
}

/**
 * Environment setup result
 */
export interface EnvironmentSetupResult {
  success: boolean;
  context?: IntegrationTestContext;
  healthChecks: ServiceHealth[];
  error?: string;
}

/**
 * Integration test environment manager for setting up and managing
 * test environments across multiple services with proper isolation
 * and cleanup capabilities.
 * 
 * This class automatically determines test type and only uses real services
 * for integration and e2e tests.
 */
export class IntegrationTestEnvironment {
  
  /**
   * Setup integration test environment with all required services
   * Automatically determines whether to use real services based on test type
   */
  static async setupEnvironment(config?: Partial<TestEnvironmentConfig>): Promise<EnvironmentSetupResult> {
    const testType = TestEnvironment.getTestType();
    const shouldUseReal = TestEnvironment.shouldUseRealServices();
    const fullConfig = this.getDefaultConfig(config);
    const healthChecks: ServiceHealth[] = [];
    
    try {
      // For unit tests, skip real service setup
      if (!shouldUseReal) {
        return {
          success: true,
          context: {
            sessionId: `unit_test_${Date.now()}`,
            config: fullConfig,
            cleanup: [],
            startTime: new Date(),
            testType
          },
          healthChecks: []
        };
      }

      // 1. Check service health for integration/e2e tests
      const healthResults = await this.checkServiceHealth(fullConfig.endpoints);
      healthChecks.push(...healthResults);
      
      const unhealthyServices = healthResults.filter(h => !h.healthy);
      if (unhealthyServices.length > 0) {
        return {
          success: false,
          healthChecks,
          error: `Integration/E2E tests require all services to be running. ` +
                 `Unhealthy services: ${unhealthyServices.map(s => s.service).join(', ')}. ` +
                 `Run 'yarn start:dev:bundled:watch' to start services.`
        };
      }

      // 2. Initialize Keycloak for test data management
      let keycloakSession: TestSession | undefined;
      await KeycloakTestUtils.initialize();
      keycloakSession = await KeycloakTestUtils.isolateTestRun(`${testType}_${Date.now()}`);

      // 3. Create test context
      const context: IntegrationTestContext = {
        sessionId: `${testType}_${Date.now()}_${Math.random().toString(36).substring(7)}`,
        config: fullConfig,
        keycloakSession,
        cleanup: [],
        startTime: new Date(),
        testType
      };

      // 4. Setup database isolation if needed
      await this.setupDatabaseIsolation(context);

      return {
        success: true,
        context,
        healthChecks
      };

    } catch (error) {
      return {
        success: false,
        healthChecks,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Cleanup integration test environment
   */
  static async cleanupEnvironment(context: IntegrationTestContext): Promise<void> {
    const errors: string[] = [];

    // Skip cleanup for unit tests
    if (context.testType === 'unit') {
      return;
    }

    // 1. Run custom cleanup functions
    for (const cleanup of context.cleanup) {
      try {
        await cleanup();
      } catch (error) {
        errors.push(`Cleanup function failed: ${error}`);
      }
    }

    // 2. Cleanup Keycloak test session
    if (context.keycloakSession) {
      try {
        await KeycloakTestUtils.cleanupTestSession(context.keycloakSession);
      } catch (error) {
        errors.push(`Keycloak cleanup failed: ${error}`);
      }
    }

    // 3. Database cleanup
    try {
      await this.cleanupDatabaseIsolation(context);
    } catch (error) {
      errors.push(`Database cleanup failed: ${error}`);
    }

    if (errors.length > 0) {
      console.warn('Some cleanup operations failed:', errors);
    }
  }

  /**
   * Wait for services to be ready
   */
  static async waitForServicesReady(endpoints: ServiceEndpoints, maxWaitTime: number = 30000): Promise<boolean> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const healthChecks = await this.checkServiceHealth(endpoints);
      const allHealthy = healthChecks.every(h => h.healthy);
      
      if (allHealthy) {
        return true;
      }
      
      // Wait 1 second before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return false;
  }

  /**
   * Check health of all services
   */
  static async checkServiceHealth(endpoints: ServiceEndpoints): Promise<ServiceHealth[]> {
    const healthChecks: Promise<ServiceHealth>[] = [
      this.checkServiceEndpoint('auth-service', `${endpoints.authService}/health`),
      this.checkServiceEndpoint('user-service', `${endpoints.userService}/health`),
      this.checkServiceEndpoint('api-gateway', `${endpoints.apiGateway}/health`),
      this.checkServiceEndpoint('keycloak', `${endpoints.keycloak}/realms/polyrepo-test/.well-known/openid_configuration`),
    ];

    return Promise.all(healthChecks);
  }

  /**
   * Check individual service endpoint
   */
  private static async checkServiceEndpoint(serviceName: string, url: string): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, { 
        method: 'GET',
        headers: { 'Accept': 'application/json' }
      });
      
      const responseTime = Date.now() - startTime;
      
      return {
        service: serviceName,
        healthy: response.ok,
        responseTime,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      };
    } catch (error) {
      return {
        service: serviceName,
        healthy: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get default configuration
   */
  private static getDefaultConfig(overrides?: Partial<TestEnvironmentConfig>): TestEnvironmentConfig {
    const defaults: TestEnvironmentConfig = {
      endpoints: {
        authService: process.env.AUTH_SERVICE_URL || 'http://localhost:3001',
        userService: process.env.USER_SERVICE_URL || 'http://localhost:3002', 
        apiGateway: process.env.API_GATEWAY_URL || 'http://localhost:3000',
        keycloak: process.env.KEYCLOAK_BASE_URL || 'http://localhost:8080',
        database: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/postgres',
        redis: process.env.REDIS_URL || 'redis://localhost:6379'
      },
      cleanupOnFailure: true,
      testTimeout: TestEnvironment.getTimeout(TestEnvironment.getTestType()),
      maxRetries: 3
    };

    return { ...defaults, ...overrides };
  }

  /**
   * Setup database isolation for tests
   */
  private static async setupDatabaseIsolation(context: IntegrationTestContext): Promise<void> {
    // TODO: Implement database isolation logic
    // This could involve:
    // - Creating a transaction that gets rolled back
    // - Using a separate test database
    // - Creating test-specific schemas
    
    context.cleanup.push(async () => {
      // Database cleanup will be implemented here
    });
  }

  /**
   * Cleanup database isolation
   */
  private static async cleanupDatabaseIsolation(context: IntegrationTestContext): Promise<void> {
    // TODO: Implement database cleanup logic
    // This should clean up any test data or rollback transactions
  }

  /**
   * Create test data across services
   */
  static async setupTestData(context: IntegrationTestContext): Promise<any> {
    // Unit tests don't need real test data
    if (context.testType === 'unit') {
      return {}; 
    }

    const testData: any = {};

    try {
      // 1. Create test users in Keycloak
      if (context.keycloakSession) {
        const testUser = await KeycloakTestUtils.createTestUser();
        testData.testUser = testUser;
        
        context.cleanup.push(async () => {
          await KeycloakTestUtils.cleanupTestUsers(testUser.email);
        });
      }

      // 2. TODO: Add other test data setup (database records, cache data, etc.)

      return testData;
    } catch (error) {
      throw new Error(`Failed to setup test data: ${error}`);
    }
  }

  /**
   * Verify test isolation between runs
   */
  static async verifyTestIsolation(context: IntegrationTestContext): Promise<boolean> {
    try {
      // TODO: Implement isolation verification
      // This should check that:
      // - No data from previous tests exists
      // - Services are in clean state
      // - No shared state between tests
      
      return true;
    } catch (error) {
      console.warn(`Test isolation verification failed: ${error}`);
      return false;
    }
  }

  /**
   * Get integration test configuration for Jest
   */
  static getJestConfig(): any {
    return {
      testEnvironment: 'node',
      testTimeout: 30000,
      setupFilesAfterEnv: ['<rootDir>/test/setup-integration.ts'],
      testMatch: ['**/*.integration.spec.ts'],
      globalSetup: '<rootDir>/test/global-setup.ts',
      globalTeardown: '<rootDir>/test/global-teardown.ts'
    };
  }
}