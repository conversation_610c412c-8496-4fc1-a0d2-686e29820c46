// Export all testing utilities for easy importing
export { MockFactory } from './mocks/mock-factory';
export { TestEnvironment, TestType } from './utilities/test-environment';
export { TestDataGenerator } from './utilities/test-data-generator';
export { TestModuleBuilder } from './helpers/test-module-builder';

// Export Keycloak testing utilities
export { KeycloakTestUtils, AppRole } from './keycloak/keycloak-test-utils';
export { KeycloakTestScenarios } from './keycloak/keycloak-test-scenarios';
export type { TestUserData, TokenResponse, TestSession } from './keycloak/keycloak-test-utils';
export type { ScenarioResult, ScenarioResultWithSteps } from './keycloak/keycloak-test-scenarios';

// Export integration testing utilities
export { IntegrationTestEnvironment } from './integration/test-environment';
export type { 
  ServiceEndpoints, 
  TestEnvironmentConfig, 
  IntegrationTestContext,
  ServiceHealth,
  EnvironmentSetupResult 
} from './integration/test-environment';

// Export auth testing helpers
export {
  createTestUser,
  cleanupTestUser,
  waitForUserCreation,
  extractUserId,
  validateJwtFormat,
  validateTokenResponse,
  createTestUserWithMarker
} from './helpers/auth-test-helpers';

// Export observability testing utilities
export { 
  TestLogCapture, 
  LokiTestClient, 
  PrometheusTestClient, 
  ObservabilityTestHelper 
} from './observability/observability-test-utils';

// Re-export common types for convenience
export type { TestingModule } from '@nestjs/testing';