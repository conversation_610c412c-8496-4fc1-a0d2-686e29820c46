import { CacheService } from '@libs/caching';
import { EventPublisher } from '@libs/messaging';
import { 
  ObservabilityLogger, 
  TracingService, 
  OBSERVABILITY_LOGGER,
  METRICS_SERVICE,
  TRACING_SERVICE 
} from '@libs/observability';
import { CircuitBreakerState, CircuitBreakerService } from '@libs/resilience';
import { CorrelationService, ErrorResponseBuilderService } from '@libs/error-handling';

/**
 * Standardized mock factory for creating consistent mocks across all services
 * This centralizes mock creation patterns and ensures consistency
 */
export class MockFactory {
  /**
   * Create a standardized mock for CacheService
   */
  static createCacheService(): any {
    return {
      onModuleInit: jest.fn().mockResolvedValue(undefined),
      onModuleDestroy: jest.fn().mockResolvedValue(undefined),
      get: jest.fn().mockResolvedValue({ hit: false, value: null }),
      set: jest.fn().mockResolvedValue({ success: true }),
      delete: jest.fn().mockResolvedValue({ success: true }),
      invalidate: jest.fn().mockResolvedValue({ success: true }),
      invalidatePattern: jest.fn().mockResolvedValue({ success: true }),
      getMetrics: jest.fn().mockReturnValue({
        totalOperations: 0,
        hits: 0,
        misses: 0,
        hitRatio: 0,
        averageResponseTime: 0,
        errors: 0,
        errorRate: 0
      }),
      getHealthStatus: jest.fn().mockReturnValue({
        status: 'connected',
        latency: 0,
        memoryUsage: '0MB'
      }),
      resetMetrics: jest.fn(),
    };
  }

  /**
   * Create a standardized mock for EventPublisher
   */
  static createEventPublisher(): any {
    return {
      publish: jest.fn().mockResolvedValue(undefined),
      publishBatch: jest.fn().mockResolvedValue(undefined),
      isHealthy: jest.fn().mockReturnValue(true),
    };
  }

  /**
   * Create a standardized mock for TracingService
   */
  static createTracingService(): any {
    return {
      onModuleInit: jest.fn(),
      onModuleDestroy: jest.fn().mockResolvedValue(undefined),
      initialize: jest.fn(),
      shutdown: jest.fn().mockResolvedValue(undefined),
      traceAsyncFunction: jest.fn(),
      getCurrentSpan: jest.fn(),
      addAttribute: jest.fn(),
      addEvent: jest.fn(),
      setStatus: jest.fn(),
    };
  }

  /**
   * Create a standardized mock business logger
   * This is a generic template that services can extend
   */
  static createBusinessLogger(): any {
    return {
      // Auth Service events
      logUserRegistrationEvent: jest.fn(),
      logUserLoginEvent: jest.fn(),
      logUserLogoutEvent: jest.fn(),
      logPasswordResetEvent: jest.fn(),
      logPasswordResetRequestEvent: jest.fn(),
      logEmailVerificationEvent: jest.fn(),
      logAccountLockEvent: jest.fn(),
      logAccountUnlockEvent: jest.fn(),
      logBusinessEvent: jest.fn(),
      
      // User Service events
      logUserCreationEvent: jest.fn(),
      logUserUpdateEvent: jest.fn(),
      logUserDeletionEvent: jest.fn(),
      logUserStatusChangeEvent: jest.fn(),
    };
  }

  /**
   * Create a standardized mock logger factory
   */
  static createLoggerFactory(): any {
    const mockLogger: any = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      setContext: jest.fn(),
      getContext: jest.fn(),
    };

    return {
      createLogger: jest.fn().mockReturnValue(mockLogger),
    };
  }

  /**
   * Create a standardized mock HTTP client service
   */
  static createHttpClientService(): any {
    return {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
      request: jest.fn(),
    };
  }

  /**
   * Create a standardized mock circuit breaker service
   */
  static createCircuitBreakerService(): any {
    return {
      execute: jest.fn(),
      getState: jest.fn().mockReturnValue('CLOSED'),
      isOpen: jest.fn().mockReturnValue(false),
      isHalfOpen: jest.fn().mockReturnValue(false),
      isClosed: jest.fn().mockReturnValue(true),
      open: jest.fn(),
      close: jest.fn(),
      halfOpen: jest.fn(),
    };
  }

  /**
   * Create a standardized mock ConfigService
   */
  static createConfigService(config: Record<string, any> = {}): any {
    return {
      get: jest.fn().mockImplementation((key: string) => config[key] || null),
      getOrThrow: jest.fn().mockImplementation((key: string) => {
        if (config[key] === undefined) {
          throw new Error(`Configuration key "${key}" is required`);
        }
        return config[key];
      }),
    };
  }

  /**
   * Create a standardized mock ObservabilityLogger
   */
  static createLogger(): any {
    return {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
      setContext: jest.fn(),
      getContext: jest.fn(),
      startTimer: jest.fn().mockReturnValue({ end: jest.fn() }),
      logMetric: jest.fn(),
      addTag: jest.fn(),
    };
  }

  /**
   * Create a standardized mock MetricsService
   */
  static createMetricsService(): any {
    return {
      onModuleInit: jest.fn(),
      onModuleDestroy: jest.fn().mockResolvedValue(undefined),
      initialize: jest.fn(),
      shutdown: jest.fn().mockResolvedValue(undefined),
      createCounter: jest.fn().mockReturnValue({ inc: jest.fn() }),
      createGauge: jest.fn().mockReturnValue({ set: jest.fn() }),
      createHistogram: jest.fn().mockReturnValue({ observe: jest.fn() }),
      incrementCounter: jest.fn(),
      setGauge: jest.fn(),
      observeHistogram: jest.fn(),
      getRegistry: jest.fn(),
    };
  }

  /**
   * Create a comprehensive mock for Error Handling services
   */
  static createErrorHandlingMocks() {
    return {
      correlationService: {
        generateCorrelationId: jest.fn().mockReturnValue('test-correlation-id-123'),
        getCurrentCorrelationId: jest.fn().mockReturnValue('test-correlation-id-123'),
        setCorrelationId: jest.fn(),
        getContext: jest.fn().mockReturnValue({
          correlationId: 'test-correlation-id-123',
          startTime: Date.now(),
          request: {
            method: 'GET',
            path: '/test',
            url: 'http://localhost/test',
          },
          service: {
            name: 'test-service',
          },
          metadata: {},
        }),
        runWithContext: jest.fn().mockImplementation(async (context, fn) => fn()),
      },
      errorResponseBuilder: {
        buildErrorResponse: jest.fn().mockReturnValue({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'An error occurred',
          correlationId: 'test-correlation-id-123',
          timestamp: new Date().toISOString(),
          path: '/test',
        }),
        enhanceError: jest.fn().mockImplementation((error) => ({
          ...error,
          correlationId: 'test-correlation-id-123',
          timestamp: new Date().toISOString(),
        })),
      },
    };
  }

  /**
   * Create a comprehensive mock for Messaging services
   */
  static createMessagingMocks() {
    return {
      eventPublisher: {
        publish: jest.fn().mockResolvedValue(undefined),
        publishBatch: jest.fn().mockResolvedValue(undefined),
        isConnected: jest.fn().mockReturnValue(true),
      },
      eventFactory: {
        createHttpRequestEvent: jest.fn().mockReturnValue({
          type: 'http.request.started',
          data: {
            method: 'GET',
            url: 'http://test.com',
            headers: {},
          },
          metadata: {
            correlationId: 'test-correlation-id-123',
            timestamp: new Date(),
            service: 'test-service',
          },
        }),
        createHttpResponseEvent: jest.fn().mockReturnValue({
          type: 'http.request.completed',
          data: {
            statusCode: 200,
            duration: 100,
            size: 1024,
          },
          metadata: {
            correlationId: 'test-correlation-id-123',
            timestamp: new Date(),
            service: 'test-service',
          },
        }),
        createHttpErrorEvent: jest.fn().mockReturnValue({
          type: 'http.request.failed',
          data: {
            error: 'Network Error',
            statusCode: 500,
          },
          metadata: {
            correlationId: 'test-correlation-id-123',
            timestamp: new Date(),
            service: 'test-service',
          },
        }),
        createCacheEvent: jest.fn().mockReturnValue({
          type: 'cache.operation',
          data: {
            operation: 'get',
            key: 'test-key',
            hit: true,
          },
          metadata: {
            correlationId: 'test-correlation-id-123',
            timestamp: new Date(),
            service: 'test-service',
          },
        }),
      },
    };
  }

  /**
   * Create enhanced circuit breaker mock with state management
   */
  static createCircuitBreakerMock() {
    const state = { current: CircuitBreakerState.CLOSED };
    
    return {
      execute: jest.fn().mockImplementation(async (fn, ...args) => {
        if (state.current === CircuitBreakerState.OPEN) {
          throw new Error('Circuit breaker is open');
        }
        return fn(...args);
      }),
      getState: jest.fn().mockImplementation(() => state.current),
      getStateEnum: jest.fn().mockImplementation(() => state.current),
      reset: jest.fn(),
      
      // Test helper methods
      mockOpen: () => { state.current = CircuitBreakerState.OPEN; },
      mockClosed: () => { state.current = CircuitBreakerState.CLOSED; },
      mockHalfOpen: () => { state.current = CircuitBreakerState.HALF_OPEN; },
    };
  }

  /**
   * Create enhanced circuit breaker service with multiple circuits
   */
  static createEnhancedCircuitBreakerService() {
    const circuits = new Map();
    
    return {
      createCircuit: jest.fn().mockImplementation((name: string) => {
        const circuit = this.createCircuitBreakerMock();
        circuits.set(name, circuit);
        return circuit;
      }),
      getCircuit: jest.fn().mockImplementation((name: string) => {
        return circuits.get(name) || this.createCircuitBreakerMock();
      }),
      resetCircuit: jest.fn(),
      getCircuitStatus: jest.fn().mockReturnValue({
        state: CircuitBreakerState.CLOSED,
        failureCount: 0,
        successCount: 0,
        lastFailureTime: null,
      }),
    };
  }

  /**
   * Create a mock Got HTTP client for testing
   */
  static createMockGot(): any {
    const mockResponse = {
      body: JSON.stringify({ success: true }),
      statusCode: 200,
      headers: { 'content-type': 'application/json' },
      requestUrl: 'http://test.com',
    };
    
    const mockGot: any = jest.fn().mockResolvedValue(mockResponse);
    
    // Add HTTP methods
    mockGot.get = jest.fn().mockResolvedValue(mockResponse);
    mockGot.post = jest.fn().mockResolvedValue(mockResponse);
    mockGot.put = jest.fn().mockResolvedValue(mockResponse);
    mockGot.patch = jest.fn().mockResolvedValue(mockResponse);
    mockGot.delete = jest.fn().mockResolvedValue(mockResponse);
    
    // Add extend method for creating instances
    mockGot.extend = jest.fn().mockReturnValue(mockGot);
    
    // Add create method for Got.create()
    mockGot.create = jest.fn().mockReturnValue(mockGot);
    
    // Add HTTPError class
    mockGot.HTTPError = class MockHTTPError extends Error {
      constructor(message: string, public response: any) {
        super(message);
        this.name = 'HTTPError';
      }
    };
    
    return mockGot;
  }

  /**
   * Create a comprehensive mock Redis client for caching tests
   * Includes ioredis-specific methods and enhanced testing capabilities
   */
  static createMockRedisClient(): any {
    const mockData = new Map<string, string>();
    
    return {
      // Connection management
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
      on: jest.fn(),
      
      // Basic operations
      get: jest.fn().mockImplementation((key: string) => 
        Promise.resolve(mockData.get(key) || null)
      ),
      set: jest.fn().mockImplementation((key: string, value: string) => {
        mockData.set(key, value);
        return Promise.resolve('OK');
      }),
      setex: jest.fn().mockImplementation((key: string, _ttl: number, value: string) => {
        mockData.set(key, value);
        return Promise.resolve('OK');
      }),
      del: jest.fn().mockImplementation((...keys: string[]) => {
        let deleted = 0;
        keys.forEach(key => {
          if (mockData.delete(key)) deleted++;
        });
        return Promise.resolve(deleted);
      }),
      exists: jest.fn().mockImplementation((key: string) => 
        Promise.resolve(mockData.has(key) ? 1 : 0)
      ),
      
      // Pattern operations
      keys: jest.fn().mockImplementation((pattern: string) => {
        const regex = new RegExp(pattern.replace('*', '.*'));
        const matchingKeys = Array.from(mockData.keys()).filter(key => regex.test(key));
        return Promise.resolve(matchingKeys);
      }),
      
      // TTL operations
      ttl: jest.fn().mockResolvedValue(-1),
      expire: jest.fn().mockResolvedValue(1),
      
      // Health check operations
      ping: jest.fn().mockResolvedValue('PONG'),
      info: jest.fn().mockResolvedValue('redis_version:7.0.0\r\nuptime_in_seconds:1000\r\ntotal_commands_processed:500'),
      
      // Test utility methods
      mockCacheHit: function(key: string, value: any) {
        mockData.set(key, JSON.stringify(value));
      },
      
      mockCacheMiss: function(key: string) {
        mockData.delete(key);
      },
      
      mockClear: function() {
        mockData.clear();
      },
      
      mockGetData: function() {
        return Array.from(mockData.entries());
      },
      
      mockSetData: function(entries: [string, string][]) {
        mockData.clear();
        entries.forEach(([key, value]) => mockData.set(key, value));
      },
    };
  }

  /**
   * Create a comprehensive NestJS test module factory
   */
  static async createTestModule(providers: any[] = []) {
    const { Test } = await import('@nestjs/testing');
    
    const errorHandling = this.createErrorHandlingMocks();
    const messaging = this.createMessagingMocks();
    
    return Test.createTestingModule({
      providers: [
        // Observability mocks - use correct symbols from @libs/observability
        {
          provide: OBSERVABILITY_LOGGER,
          useValue: this.createLogger(),
        },
        {
          provide: METRICS_SERVICE,
          useValue: this.createMetricsService(),
        },
        {
          provide: TRACING_SERVICE,
          useValue: this.createTracingService(),
        },
        {
          provide: 'BusinessLogger',
          useValue: this.createBusinessLogger(),
        },
        
        // Resilience mocks
        {
          provide: CircuitBreakerService,
          useValue: this.createEnhancedCircuitBreakerService(),
        },
        
        // Error handling mocks - use actual classes as tokens
        {
          provide: CorrelationService,
          useValue: errorHandling.correlationService,
        },
        {
          provide: ErrorResponseBuilderService,
          useValue: errorHandling.errorResponseBuilder,
        },
        
        // Service configuration for HttpClientService
        {
          provide: 'LOGGER_FACTORY',
          useValue: this.createLoggerFactory(),
        },
        
        // Messaging mocks
        {
          provide: 'EventPublisher',
          useValue: messaging.eventPublisher,
        },
        {
          provide: 'EventFactory',
          useValue: messaging.eventFactory,
        },
        
        // Caching mocks
        {
          provide: CacheService,
          useValue: this.createCacheService(),
        },
        
        // Service configuration parameters for HttpClientService
        {
          provide: 'ServiceClientConfig',
          useValue: {
            http2: true,
            cache: { enabled: true },
            timeout: { response: 30000 },
            retries: { limit: 3 },
          },
        },
        {
          provide: 'ServiceContext',
          useValue: 'test-http-client',
        },
        
        // Custom providers
        ...providers,
      ],
    }).compile();
  }

  /**
   * Create all commonly used mocks in a single call
   */
  static createCommonMocks(customConfig: Record<string, any> = {}) {
    return {
      cacheService: this.createCacheService(),
      eventPublisher: this.createEventPublisher(),
      tracingService: this.createTracingService(),
      businessLogger: this.createBusinessLogger(),
      loggerFactory: this.createLoggerFactory(),
      httpClientService: this.createHttpClientService(),
      circuitBreakerService: this.createCircuitBreakerService(),
      configService: this.createConfigService(customConfig),
      errorHandling: this.createErrorHandlingMocks(),
      messaging: this.createMessagingMocks(),
      redisClient: this.createMockRedisClient(),
      gotClient: this.createMockGot(),
    };
  }

  /**
   * Create HTTP client providers for bundle testing
   * Provides all necessary dependencies for HttpClientService
   */
  static createHttpClientProviders(): any[] {
    const errorHandling = this.createErrorHandlingMocks();
    const messaging = this.createMessagingMocks();
    
    return [
      // Observability mocks
      { provide: OBSERVABILITY_LOGGER, useValue: this.createLogger() },
      { provide: METRICS_SERVICE, useValue: this.createMetricsService() },
      { provide: TRACING_SERVICE, useValue: this.createTracingService() },
      
      // Resilience mocks
      { provide: CircuitBreakerService, useValue: this.createEnhancedCircuitBreakerService() },
      
      // Error handling mocks
      { provide: CorrelationService, useValue: errorHandling.correlationService },
      { provide: ErrorResponseBuilderService, useValue: errorHandling.errorResponseBuilder },
      
      // Messaging mocks
      { provide: 'EventPublisher', useValue: messaging.eventPublisher },
      { provide: 'EventFactory', useValue: messaging.eventFactory },
      
      // Caching mocks
      { provide: CacheService, useValue: this.createCacheService() },
      
      // Required factories and configuration
      { provide: 'LOGGER_FACTORY', useValue: this.createLoggerFactory() },
    ];
  }
}