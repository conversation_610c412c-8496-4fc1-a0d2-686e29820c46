import axios, { AxiosInstance } from 'axios';
import { KeycloakClientService, KeycloakTokenResponse, KeycloakUserCreateRequest } from '@libs/keycloak-client';

/**
 * Keycloak Test User data structure
 */
export interface TestUserData {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  roles: string[];
  enabled: boolean;
  emailVerified: boolean;
}

/**
 * Keycloak Test Session for isolation
 */
export interface TestSession {
  sessionId: string;
  realm: string;
  cleanupFunctions: (() => Promise<void>)[];
}

/**
 * Token response from Keycloak
 */
export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

/**
 * Application roles enum for type safety
 */
export enum AppRole {
  USER = 'user',
  ANALYTICS = 'analytics', 
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  TECH_ADMIN = 'tech-admin',
  SUPERADMIN = 'superadmin'
}

/**
 * Comprehensive Keycloak testing utilities for managing test users,
 * authentication flows, and test data isolation.
 */
export class KeycloakTestUtils {
  private static client: AxiosInstance;
  private static adminToken: string;
  private static realm: string = 'polyrepo-test';
  
  // Modern Keycloak client for HTTP/2 performance
  private static keycloakClient: KeycloakClientService;
  
  /**
   * Initialize the Keycloak client with admin credentials
   */
  static async initialize(): Promise<void> {
    const baseURL = process.env.KEYCLOAK_BASE_URL || 'http://localhost:8080';
    
    this.client = axios.create({
      baseURL,
      timeout: 10000,
    });

    // Get admin token for management operations
    await this.refreshAdminToken();
  }

  /**
   * Set modern Keycloak client for HTTP/2 performance
   * @param client KeycloakClientService instance
   */
  static setKeycloakClient(client: KeycloakClientService): void {
    this.keycloakClient = client;
  }

  /**
   * Get fresh admin token for Keycloak operations
   */
  private static async refreshAdminToken(): Promise<void> {
    const response = await this.client.post('/realms/master/protocol/openid-connect/token', 
      new URLSearchParams({
        grant_type: 'password',
        client_id: 'admin-cli',
        username: 'admin',
        password: 'admin'
      }), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      }
    );
    
    this.adminToken = response.data.access_token;
  }

  /**
   * Create a test user with specified role
   */
  static async createTestUser(role: AppRole = AppRole.USER): Promise<TestUserData> {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(7);
    
    const userData: TestUserData = {
      email: `test.${timestamp}.${randomSuffix}@example.com`,
      firstName: `Test`,
      lastName: `User${randomSuffix}`,
      password: 'Test@1234',
      roles: [role],
      enabled: true,
      emailVerified: true
    };

    await this.createKeycloakUser(userData);
    return userData;
  }

  /**
   * Create multiple test users with different roles
   */
  static async createBulkTestUsers(count: number, role: AppRole = AppRole.USER): Promise<TestUserData[]> {
    const users: TestUserData[] = [];
    
    for (let i = 0; i < count; i++) {
      const user = await this.createTestUser(role);
      users.push(user);
    }
    
    return users;
  }

  /**
   * Create a user hierarchy for complex testing scenarios
   */
  static async createUserHierarchy(): Promise<Record<AppRole, TestUserData>> {
    const hierarchy: Partial<Record<AppRole, TestUserData>> = {};
    
    for (const role of Object.values(AppRole)) {
      hierarchy[role] = await this.createTestUser(role);
    }
    
    return hierarchy as Record<AppRole, TestUserData>;
  }

  /**
   * Create a test user with specific attributes
   */
  static async createCustomTestUser(overrides: Partial<TestUserData>): Promise<TestUserData> {
    const baseUser = await this.createTestUser();
    const customUser = { ...baseUser, ...overrides };
    
    // If custom data provided, update the user in Keycloak
    if (Object.keys(overrides).length > 0) {
      await this.updateKeycloakUser(customUser);
    }
    
    return customUser;
  }

  /**
   * Authenticate a test user and get tokens
   */
  static async authenticateTestUser(email: string, password: string = 'Test@1234'): Promise<TokenResponse> {
    // Use modern HTTP/2 client for better performance if available
    if (this.keycloakClient) {
      const response = await this.keycloakClient.authenticateUser(email, password);
      // Convert to legacy format for backward compatibility
      return {
        access_token: response.access_token,
        refresh_token: response.refresh_token,
        expires_in: response.expires_in,
        token_type: response.token_type
      };
    }
    
    // Fallback to legacy axios implementation
    const response = await this.client.post(`/realms/${this.realm}/protocol/openid-connect/token`,
      new URLSearchParams({
        grant_type: 'password',
        client_id: 'auth-service-test-client',
        client_secret: 'TestClientSecretDev',
        username: email,
        password: password
      }), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      }
    );
    
    return response.data;
  }

  /**
   * Create an expired token for testing token validation
   */
  static async createExpiredToken(): Promise<string> {
    // Create a user, get token, then manually set expiration in the past
    const user = await this.createTestUser();
    const tokens = await this.authenticateTestUser(user.email);
    
    // This is a mock expired token - in real scenario you'd manipulate token exp claim
    // For testing purposes, we'll return a token that services can validate as expired
    return 'expired_' + tokens.access_token;
  }

  /**
   * Create an invalid/malformed token for testing
   */
  static createInvalidToken(): string {
    return 'invalid_token_' + Math.random().toString(36);
  }

  /**
   * Assign role to user
   */
  static async assignRole(userId: string, role: string): Promise<void> {
    await this.ensureAdminToken();
    
    // Get role representation
    const roleResponse = await this.client.get(
      `/admin/realms/${this.realm}/roles/${role}`,
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
    
    // Assign role to user
    await this.client.post(
      `/admin/realms/${this.realm}/users/${userId}/role-mappings/realm`,
      [roleResponse.data],
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
  }

  /**
   * Assign role hierarchy to user with proper business logic separation
   * 
   * Role Architecture:
   * - SUPERADMIN: System-wide admin (all permissions)
   * - TECH_ADMIN: Technical operations (user management, system config)
   * - ADMIN: Business operations (user moderation, content management)
   * - MODERATOR: Content moderation only
   * - ANALYTICS: Data analysis only (separate from admin hierarchy)
   * - USER: Basic access
   */
  private static async assignRoleHierarchy(userId: string, role: AppRole): Promise<void> {
    const roleHierarchy: Record<AppRole, AppRole[]> = {
      // Superadmin gets everything (system-wide access)
      [AppRole.SUPERADMIN]: [AppRole.SUPERADMIN, AppRole.TECH_ADMIN, AppRole.ADMIN, AppRole.MODERATOR, AppRole.ANALYTICS, AppRole.USER],
      
      // Tech admin gets technical operations + basic admin (no analytics unless explicitly granted)
      [AppRole.TECH_ADMIN]: [AppRole.TECH_ADMIN, AppRole.ADMIN, AppRole.MODERATOR, AppRole.USER],
      
      // Admin gets business operations (no analytics access unless explicitly granted)
      [AppRole.ADMIN]: [AppRole.ADMIN, AppRole.MODERATOR, AppRole.USER],
      
      // Moderator gets only moderation + user access (no analytics)
      [AppRole.MODERATOR]: [AppRole.MODERATOR, AppRole.USER],
      
      // Analytics is a separate permission track (not part of admin hierarchy)
      [AppRole.ANALYTICS]: [AppRole.ANALYTICS, AppRole.USER],
      
      // User gets basic access only
      [AppRole.USER]: [AppRole.USER]
    };

    const rolesToAssign = roleHierarchy[role] || [role];
    
    for (const roleToAssign of rolesToAssign) {
      try {
        await this.assignRole(userId, roleToAssign);
      } catch (error) {
        console.warn(`Failed to assign role ${roleToAssign} to user ${userId}:`, (error as Error).message);
      }
    }
  }

  /**
   * Remove role from user
   */
  static async removeRole(userId: string, role: string): Promise<void> {
    await this.ensureAdminToken();
    
    // Get role representation
    const roleResponse = await this.client.get(
      `/admin/realms/${this.realm}/roles/${role}`,
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
    
    // Remove role from user
    await this.client.delete(
      `/admin/realms/${this.realm}/users/${userId}/role-mappings/realm`,
      { 
        data: [roleResponse.data],
        headers: { Authorization: `Bearer ${this.adminToken}` } 
      }
    );
  }

  /**
   * Update user profile information
   */
  static async updateUserProfile(email: string, updates: Partial<TestUserData>): Promise<void> {
    await this.ensureAdminToken();
    
    const userId = await this.getUserIdByEmail(email);
    
    await this.client.put(
      `/admin/realms/${this.realm}/users/${userId}`,
      {
        firstName: updates.firstName,
        lastName: updates.lastName,
        enabled: updates.enabled,
        emailVerified: updates.emailVerified
      },
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
  }

  /**
   * Disable a user account
   */
  static async disableUser(email: string): Promise<void> {
    await this.updateUserProfile(email, { enabled: false });
  }

  /**
   * Enable a user account
   */
  static async enableUser(email: string): Promise<void> {
    await this.updateUserProfile(email, { enabled: true });
  }

  /**
   * Check if user exists by ID
   */
  static async userExists(userId: string): Promise<boolean> {
    try {
      await this.ensureAdminToken();
      
      await this.client.get(
        `/admin/realms/${this.realm}/users/${userId}`,
        { headers: { Authorization: `Bearer ${this.adminToken}` } }
      );
      
      return true;
    } catch (error) {
      // 404 means user doesn't exist
      if ((error as any)?.response?.status === 404) {
        return false;
      }
      // Re-throw other errors
      throw error;
    }
  }

  /**
   * Delete user by ID
   */
  static async deleteUser(userId: string): Promise<void> {
    await this.ensureAdminToken();
    
    await this.client.delete(
      `/admin/realms/${this.realm}/users/${userId}`,
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
  }

  /**
   * Clean up test users matching a pattern
   */
  static async cleanupTestUsers(emailPattern: string = 'test.'): Promise<number> {
    await this.ensureAdminToken();
    
    const users = await this.client.get(
      `/admin/realms/${this.realm}/users?briefRepresentation=true&max=1000`,
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
    
    let deletedCount = 0;
    
    for (const user of users.data) {
      if (user.email && user.email.includes(emailPattern)) {
        await this.deleteUser(user.id);
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  /**
   * Create an isolated test session for better test isolation
   */
  static async isolateTestRun(testName: string): Promise<TestSession> {
    const sessionId = `${testName}_${Date.now()}_${Math.random().toString(36).substring(7)}`;
    
    return {
      sessionId,
      realm: this.realm,
      cleanupFunctions: []
    };
  }

  /**
   * Cleanup a test session and all associated resources
   */
  static async cleanupTestSession(session: TestSession): Promise<void> {
    for (const cleanup of session.cleanupFunctions) {
      try {
        await cleanup();
      } catch (error) {
        console.warn(`Failed to cleanup test resource: ${error}`);
      }
    }
  }

  /**
   * Get user ID by email address
   */
  static async getUserIdByEmail(email: string): Promise<string> {
    await this.ensureAdminToken();
    
    const response = await this.client.get(
      `/admin/realms/${this.realm}/users?email=${encodeURIComponent(email)}`,
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
    
    if (response.data.length === 0) {
      throw new Error(`User not found: ${email}`);
    }
    
    return response.data[0].id;
  }

  /**
   * Create user in Keycloak
   */
  private static async createKeycloakUser(userData: TestUserData): Promise<string> {
    // Use modern HTTP/2 client for better performance if available
    if (this.keycloakClient) {
      const keycloakUserData: KeycloakUserCreateRequest = {
        email: userData.email,
        username: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        enabled: userData.enabled,
        credentials: [{
          type: 'password',
          value: userData.password,
          temporary: false
        }]
      };
      
      const userId = await this.keycloakClient.createUser(keycloakUserData);
      
      // Still need to assign roles using legacy method for now
      // TODO: Add role assignment to keycloak-client service
      await this.ensureAdminToken();
      for (const role of userData.roles) {
        await this.assignRoleHierarchy(userId, role as AppRole);
      }
      
      return userId;
    }
    
    // Fallback to legacy axios implementation
    await this.ensureAdminToken();
    
    const createResponse = await this.client.post(
      `/admin/realms/${this.realm}/users`,
      {
        username: userData.email,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        enabled: userData.enabled,
        emailVerified: userData.emailVerified,
        credentials: [{
          type: 'password',
          value: userData.password,
          temporary: false
        }]
      },
      { headers: { Authorization: `Bearer ${this.adminToken}` } }
    );
    
    // Extract user ID from location header
    const location = createResponse.headers.location;
    const userId = location.substring(location.lastIndexOf('/') + 1);
    
    // Assign roles with hierarchy
    for (const role of userData.roles) {
      await this.assignRoleHierarchy(userId, role as AppRole);
    }
    
    return userId;
  }

  /**
   * Update existing user in Keycloak
   */
  private static async updateKeycloakUser(userData: TestUserData): Promise<void> {
    const userId = await this.getUserIdByEmail(userData.email);
    await this.updateUserProfile(userData.email, userData);
  }

  /**
   * Ensure admin token is valid, refresh if needed
   */
  private static async ensureAdminToken(): Promise<void> {
    if (!this.adminToken) {
      await this.refreshAdminToken();
    }
    
    // TODO: Add token expiry check and refresh logic
  }

  /**
   * Validate if Keycloak is available
   */
  static async isKeycloakAvailable(): Promise<boolean> {
    try {
      await this.client.get(`/realms/${this.realm}/.well-known/openid_configuration`);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get predefined test users from the test realm
   */
  static getPredefinedTestUsers(): Record<AppRole, { email: string; password: string }> {
    return {
      [AppRole.USER]: { email: '<EMAIL>', password: 'Test@1234' },
      [AppRole.ANALYTICS]: { email: '<EMAIL>', password: 'Test@1234' },
      [AppRole.MODERATOR]: { email: '<EMAIL>', password: 'Test@1234' },
      [AppRole.ADMIN]: { email: '<EMAIL>', password: 'Test@1234' },
      [AppRole.TECH_ADMIN]: { email: '<EMAIL>', password: 'Test@1234' },
      [AppRole.SUPERADMIN]: { email: '<EMAIL>', password: 'Test@1234' }
    };
  }
}