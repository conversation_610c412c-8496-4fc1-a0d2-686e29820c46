import { Test, TestingModule } from '@nestjs/testing';
import { MockFactory } from '../mocks/mock-factory';

/**
 * Helper class for building test modules with common providers
 */
export class TestModuleBuilder {
  private providers: any[] = [];
  private imports: any[] = [];
  private controllers: any[] = [];

  /**
   * Add a service with automatic mock dependencies
   */
  addService(ServiceClass: any): TestModuleBuilder {
    this.providers.push(ServiceClass);
    return this;
  }

  /**
   * Add a controller
   */
  addController(ControllerClass: any): TestModuleBuilder {
    this.controllers.push(ControllerClass);
    return this;
  }

  /**
   * Add a module
   */
  addModule(ModuleClass: any): TestModuleBuilder {
    this.imports.push(ModuleClass);
    return this;
  }

  /**
   * Add a custom provider
   */
  addProvider(provider: any): TestModuleBuilder {
    this.providers.push(provider);
    return this;
  }

  /**
   * Add common mocks (cache, events, logging, etc.)
   */
  addCommonMocks(customConfig: Record<string, any> = {}): TestModuleBuilder {
    const mocks = MockFactory.createCommonMocks(customConfig);
    
    this.providers.push(
      {
        provide: 'CacheService',
        useValue: mocks.cacheService,
      },
      {
        provide: 'EVENT_PUBLISHER',
        useValue: mocks.eventPublisher,
      },
      {
        provide: 'TracingService',
        useValue: mocks.tracingService,
      },
      {
        provide: 'LOGGER_FACTORY',
        useValue: mocks.loggerFactory,
      },
      {
        provide: 'ConfigService',
        useValue: mocks.configService,
      }
    );
    
    return this;
  }

  /**
   * Add observability mocks
   */
  addObservabilityMocks(): TestModuleBuilder {
    const loggerFactory = MockFactory.createLoggerFactory();
    const tracingService = MockFactory.createTracingService();
    
    this.providers.push(
      {
        provide: 'LOGGER_FACTORY',
        useValue: loggerFactory,
      },
      {
        provide: 'TracingService',
        useValue: tracingService,
      }
    );
    
    return this;
  }

  /**
   * Add caching mocks
   */
  addCachingMocks(): TestModuleBuilder {
    const cacheService = MockFactory.createCacheService();
    
    this.providers.push({
      provide: 'CacheService',
      useValue: cacheService,
    });
    
    return this;
  }

  /**
   * Add messaging mocks
   */
  addMessagingMocks(): TestModuleBuilder {
    const eventPublisher = MockFactory.createEventPublisher();
    
    this.providers.push({
      provide: 'EVENT_PUBLISHER',
      useValue: eventPublisher,
    });
    
    return this;
  }

  /**
   * Add HTTP client mocks
   */
  addHttpMocks(): TestModuleBuilder {
    const httpClientService = MockFactory.createHttpClientService();
    
    this.providers.push({
      provide: 'HttpClientService',
      useValue: httpClientService,
    });
    
    return this;
  }

  /**
   * Add circuit breaker mocks
   */
  addCircuitBreakerMocks(): TestModuleBuilder {
    const circuitBreakerService = MockFactory.createCircuitBreakerService();
    
    this.providers.push({
      provide: 'CircuitBreakerService',
      useValue: circuitBreakerService,
    });
    
    return this;
  }

  /**
   * Build the testing module
   */
  async build(): Promise<TestingModule> {
    const moduleBuilder = Test.createTestingModule({
      imports: this.imports,
      controllers: this.controllers,
      providers: this.providers,
    });

    return moduleBuilder.compile();
  }

  /**
   * Build and get a specific service
   */
  async buildAndGet<T>(ServiceClass: new (...args: any[]) => T): Promise<T> {
    const module = await this.build();
    return module.get<T>(ServiceClass);
  }

  /**
   * Create a quick test module for a service with common dependencies
   */
  static async createServiceTestModule<T>(
    ServiceClass: new (...args: any[]) => T,
    additionalProviders: any[] = []
  ): Promise<{ module: TestingModule; service: T }> {
    const builder = new TestModuleBuilder()
      .addService(ServiceClass)
      .addCommonMocks();

    // Add any additional providers
    additionalProviders.forEach(provider => {
      builder.addProvider(provider);
    });

    const module = await builder.build();
    const service = module.get<T>(ServiceClass);

    return { module, service };
  }

  /**
   * Create a quick test module for a controller with common dependencies
   */
  static async createControllerTestModule<T>(
    ControllerClass: new (...args: any[]) => T,
    serviceMocks: any[] = []
  ): Promise<{ module: TestingModule; controller: T }> {
    const builder = new TestModuleBuilder()
      .addController(ControllerClass)
      .addCommonMocks();

    // Add service mocks
    serviceMocks.forEach(mock => {
      builder.addProvider(mock);
    });

    const module = await builder.build();
    const controller = module.get<T>(ControllerClass);

    return { module, controller };
  }
}