import { TestDataGenerator } from '../utilities/test-data-generator';
import { KeycloakTestUtils } from '../keycloak/keycloak-test-utils';

/**
 * Authentication-specific test helpers for E2E and integration testing
 */

/**
 * Create a test user with unique data for testing
 */
export function createTestUser(prefix: string = 'test') {
  return TestDataGenerator.createRegistrationData({
    email: TestDataGenerator.generateEmail(prefix),
  });
}

/**
 * Clean up test user from both Keycloak and User Service
 */
export async function cleanupTestUser(
  keycloakService: any,
  userServiceClient: any,
  keycloakId: string
): Promise<void> {
  const errors: Error[] = [];

  // Try to cleanup from Keycloak
  try {
    await KeycloakTestUtils.deleteUser(keycloakId);
    console.log(`✅ Cleaned up Keycloak user: ${keycloakId}`);
  } catch (error) {
    console.warn(`⚠️ Failed to cleanup Keycloak user ${keycloakId}:`, error);
    errors.push(error as Error);
  }

  // Try to cleanup from User Service (if needed)
  try {
    // Note: This depends on User Service having a cleanup/admin endpoint
    // For now, we'll just log the attempt
    console.log(`📝 Note: User Service cleanup for Keycloak ID ${keycloakId} should be handled by business logic`);
  } catch (error) {
    console.warn(`⚠️ Failed to cleanup User Service user for Keycloak ID ${keycloakId}:`, error);
    errors.push(error as Error);
  }

  // If we had errors but not complete failure, log them
  if (errors.length > 0 && errors.length < 2) {
    console.warn(`⚠️ Partial cleanup completed with ${errors.length} error(s)`);
  } else if (errors.length >= 2) {
    throw new Error(`Failed to cleanup test user: ${errors.map(e => e.message).join(', ')}`);
  }
}

/**
 * Wait for user to be fully created in both Keycloak and User Service
 */
export async function waitForUserCreation(
  keycloakId: string,
  timeoutMs: number = 5000
): Promise<boolean> {
  const startTime = Date.now();
  const pollInterval = 500;

  while (Date.now() - startTime < timeoutMs) {
    try {
      // Check if user exists in Keycloak
      const userExists = await KeycloakTestUtils.userExists(keycloakId);
      
      if (userExists) {
        console.log(`✅ User ${keycloakId} confirmed in Keycloak`);
        return true;
      }

      await new Promise(resolve => setTimeout(resolve, pollInterval));
    } catch (error) {
      console.log(`⚠️ Error checking user existence: ${(error as Error).message}`);
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
  }

  throw new Error(`Timeout: User ${keycloakId} not found within ${timeoutMs}ms`);
}

/**
 * Extract user ID from various response formats
 */
export function extractUserId(response: any): string {
  // Handle different response formats from different endpoints
  if (typeof response === 'string') {
    return response;
  }
  
  if (response?.id) {
    return response.id;
  }
  
  if (response?.keycloakId) {
    return response.keycloakId;
  }
  
  if (response?.body?.id) {
    return response.body.id;
  }
  
  if (response?.body?.keycloakId) {
    return response.body.keycloakId;
  }
  
  throw new Error(`Cannot extract user ID from response: ${JSON.stringify(response)}`);
}

/**
 * Validate JWT token format
 */
export function validateJwtFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  // JWT should have 3 parts separated by dots
  const parts = token.split('.');
  return parts.length === 3;
}

/**
 * Validate token response structure
 */
export function validateTokenResponse(response: any): boolean {
  return (
    response &&
    typeof response.access_token === 'string' &&
    typeof response.refresh_token === 'string' &&
    validateJwtFormat(response.access_token) &&
    validateJwtFormat(response.refresh_token)
  );
}

/**
 * Create test user with business event marker for observability testing
 */
export function createTestUserWithMarker(prefix: string = 'test') {
  const user = createTestUser(prefix);
  const marker = `test-user-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
  
  return {
    ...user,
    marker
  };
}