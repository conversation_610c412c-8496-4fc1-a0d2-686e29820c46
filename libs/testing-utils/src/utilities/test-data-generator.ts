/**
 * Test data generation utilities for creating consistent test data across services
 */
export class TestDataGenerator {
  /**
   * Generate a test user object
   */
  static createTestUser(overrides: Partial<any> = {}) {
    const defaultUser = {
      id: this.generateId(),
      email: this.generateEmail(),
      firstName: 'Test',
      lastName: 'User',
      keycloakId: this.generateKeycloakId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
      version: 1,
      ...overrides,
    };
    
    return defaultUser;
  }

  /**
   * Generate a test admin user
   */
  static createTestAdmin(overrides: Partial<any> = {}) {
    return this.createTestUser({
      email: this.generateEmail('admin'),
      firstName: 'Admin',
      lastName: 'User',
      ...overrides,
    });
  }

  /**
   * Generate test registration data
   */
  static createRegistrationData(overrides: Partial<any> = {}) {
    return {
      email: this.generateEmail(),
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User',
      ...overrides,
    };
  }

  /**
   * Generate test login data
   */
  static createLoginData(overrides: Partial<any> = {}) {
    return {
      email: this.generateEmail(),
      password: 'TestPassword123!',
      ...overrides,
    };
  }

  /**
   * Generate test JWT payload
   */
  static createJwtPayload(overrides: Partial<any> = {}) {
    return {
      sub: this.generateKeycloakId(),
      email: this.generateEmail(),
      realm_access: {
        roles: ['user'],
      },
      resource_access: {},
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
      ...overrides,
    };
  }

  /**
   * Generate test Keycloak token response
   */
  static createKeycloakTokenResponse(overrides: Partial<any> = {}) {
    return {
      access_token: this.generateJwtToken(),
      refresh_token: this.generateJwtToken(),
      expires_in: 3600,
      token_type: 'bearer',
      ...overrides,
    };
  }

  /**
   * Generate test business event data
   */
  static createBusinessEvent(eventType: string, overrides: Partial<any> = {}) {
    return {
      eventType,
      userId: this.generateKeycloakId(),
      timestamp: new Date(),
      correlationId: this.generateCorrelationId(),
      metadata: {},
      ...overrides,
    };
  }

  /**
   * Generate test error object
   */
  static createTestError(message: string = 'Test error', overrides: Partial<any> = {}) {
    const error = new Error(message);
    Object.assign(error, {
      status: 500,
      code: 'TEST_ERROR',
      ...overrides,
    });
    return error;
  }

  /**
   * Generate test HTTP response
   */
  static createHttpResponse(overrides: Partial<any> = {}) {
    return {
      status: 200,
      statusText: 'OK',
      data: {},
      headers: {},
      config: {},
      ...overrides,
    };
  }

  // Helper methods for generating specific types of data

  /**
   * Generate a unique test ID
   */
  static generateId(): number {
    return Math.floor(Math.random() * 1000000) + 1;
  }

  /**
   * Generate a test email address
   */
  static generateEmail(prefix: string = 'test'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}-${timestamp}-${random}@example.com`;
  }

  /**
   * Generate a Keycloak ID (UUID format)
   */
  static generateKeycloakId(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Generate a correlation ID
   */
  static generateCorrelationId(): string {
    return this.generateKeycloakId();
  }

  /**
   * Generate a test JWT token (mock format)
   */
  static generateJwtToken(): string {
    const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
    const payload = Buffer.from(JSON.stringify(this.createJwtPayload())).toString('base64');
    const signature = 'test-signature';
    return `${header}.${payload}.${signature}`;
  }

  /**
   * Generate test cache key
   */
  static generateCacheKey(prefix: string = 'test'): string {
    return `${prefix}:${this.generateKeycloakId()}`;
  }

  /**
   * Generate test event name
   */
  static generateEventName(service: string, action: string): string {
    return `${service}.${action}`;
  }

  /**
   * Create multiple test users
   */
  static createTestUsers(count: number): any[] {
    return Array.from({ length: count }, (_, index) => 
      this.createTestUser({ 
        email: this.generateEmail(`user${index}`) 
      })
    );
  }

  /**
   * Create test database seeding data
   */
  static createSeedData() {
    return {
      users: this.createTestUsers(5),
      admins: [this.createTestAdmin()],
    };
  }
}