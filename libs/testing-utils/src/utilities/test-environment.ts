import * as path from 'path';
import * as fs from 'fs';

export type TestType = 'unit' | 'integration' | 'e2e';

/**
 * Test environment utilities for consistent test setup across services
 */
export class TestEnvironment {
  /**
   * Setup test environment by loading appropriate environment variables
   */
  static setupEnvironment(testType: TestType, serviceName?: string): void {
    const envFileName = this.getEnvironmentFileName(testType);
    const envPath = this.resolveEnvironmentPath(envFileName, serviceName);
    
    if (fs.existsSync(envPath)) {
      console.log(`Loading environment from ${envPath}`);
      this.loadEnvironmentFile(envPath);
    } else {
      console.warn(`Environment file not found: ${envPath}`);
    }
  }

  /**
   * Get the appropriate environment file name for test type
   */
  private static getEnvironmentFileName(testType: TestType): string {
    switch (testType) {
      case 'unit':
        return '.env.test.unit';
      case 'integration':
        return '.env.test.integration';
      case 'e2e':
        return '.env.test.e2e';
      default:
        return '.env.test';
    }
  }

  /**
   * Resolve the environment file path based on service context
   */
  private static resolveEnvironmentPath(fileName: string, serviceName?: string): string {
    if (serviceName) {
      // For service-specific tests
      return path.join(process.cwd(), 'services', serviceName, 'test', fileName);
    } else {
      // For current directory (library tests)
      return path.join(process.cwd(), 'test', fileName);
    }
  }

  /**
   * Load environment variables from file
   */
  private static loadEnvironmentFile(filePath: string): void {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          process.env[key.trim()] = value;
        }
      }
    }
  }

  /**
   * Check if we're running in a CI environment
   */
  static isCI(): boolean {
    return !!(
      process.env.CI ||
      process.env.CONTINUOUS_INTEGRATION ||
      process.env.GITHUB_ACTIONS ||
      process.env.GITLAB_CI ||
      process.env.CIRCLECI
    );
  }

  /**
   * Determine test type based on test file path or Jest context
   */
  static getTestType(): TestType {
    // Try to get test file path from Jest context
    const testPath = (globalThis as any)?.__testPath || 
                     process.env.JEST_WORKER_ID ? 
                     ((globalThis as any)?.expect?.getState?.()?.testPath || '') : '';
    
    if (testPath.includes('/unit/') || testPath.includes('.spec.ts')) {
      return 'unit';
    }
    if (testPath.includes('/integration/') || testPath.includes('.integration.spec.ts')) {
      return 'integration';
    }
    if (testPath.includes('/e2e/') || testPath.includes('.e2e-spec.ts')) {
      return 'e2e';
    }
    
    // Fallback: check NODE_ENV for backwards compatibility
    if (process.env.NODE_ENV === 'test-integration') {
      return 'integration';
    }
    if (process.env.NODE_ENV === 'test-e2e') {
      return 'e2e';
    }
    
    // Default to unit tests for safety (always mocked)
    return 'unit';
  }

  /**
   * Check if real services should be used based on test category
   * - Unit tests: Never use real services (always mocked)
   * - Integration tests: Use real services for integration testing
   * - E2E tests: Use real services for full flow testing
   */
  static shouldUseRealServices(): boolean {
    const testType = this.getTestType();
    return testType === 'integration' || testType === 'e2e';
  }

  /**
   * Get test database URL for integration tests
   */
  static getTestDatabaseUrl(): string | undefined {
    return process.env.TEST_DATABASE_URL || process.env.DATABASE_URL;
  }

  /**
   * Get test Redis URL for integration tests
   */
  static getTestRedisUrl(): string | undefined {
    return process.env.TEST_REDIS_URL || process.env.REDIS_URL;
  }

  /**
   * Check if we should run integration tests
   */
  static shouldRunIntegrationTests(): boolean {
    if (this.isCI()) {
      return true; // Always run in CI
    }
    return process.env.RUN_INTEGRATION_TESTS === 'true';
  }

  /**
   * Check if we should run e2e tests
   */
  static shouldRunE2ETests(): boolean {
    if (this.isCI()) {
      return true; // Always run in CI
    }
    return process.env.RUN_E2E_TESTS === 'true';
  }

  /**
   * Get timeout configuration for different test types
   */
  static getTimeout(testType: TestType): number {
    switch (testType) {
      case 'unit':
        return 5000; // 5 seconds
      case 'integration':
        return 30000; // 30 seconds
      case 'e2e':
        return 60000; // 60 seconds
      default:
        return 10000; // 10 seconds
    }
  }

  /**
   * Create a unique test identifier for isolation
   */
  static createTestId(): string {
    return `test_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Wait for async operations to complete (useful for message processing)
   */
  static async waitForProcessing(milliseconds: number = 100): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, milliseconds));
  }
}