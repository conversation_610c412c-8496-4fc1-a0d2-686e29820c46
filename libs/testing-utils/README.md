# Testing Utilities Library (@libs/testing-utils)

## Purpose
Shared testing utilities for standardizing test patterns across all Polyrepo services. This library provides mock factories, test data generators, environment utilities, and test module builders to ensure consistent and reliable testing.

## Installation
Add to your service's package.json:
```json
{
  "dependencies": {
    "@libs/testing-utils": "file:../../libs/testing-utils"
  }
}
```

## Core Features

### 1. Mock Factory
Standardized mocks for common services and dependencies:

```typescript
import { MockFactory } from '@libs/testing-utils';

// Create individual mocks
const cacheService = MockFactory.createCacheService();
const eventPublisher = MockFactory.createEventPublisher();
const tracingService = MockFactory.createTracingService();

// Create all common mocks at once
const mocks = MockFactory.createCommonMocks({
  KEYCLOAK_REALM_NAME: 'test-realm'
});
```

### 2. Test Data Generator
Consistent test data creation:

```typescript
import { TestDataGenerator } from '@libs/testing-utils';

// Generate test users
const user = TestDataGenerator.createTestUser();
const admin = TestDataGenerator.createTestAdmin();

// Generate auth data
const registrationData = TestDataGenerator.createRegistrationData();
const jwtPayload = TestDataGenerator.createJwtPayload();

// Generate multiple users
const users = TestDataGenerator.createTestUsers(5);
```

### 3. Test Environment Utilities
Environment management and configuration:

```typescript
import { TestEnvironment } from '@libs/testing-utils';

// Setup test environment
TestEnvironment.setupEnvironment('unit', 'auth-service');

// Check environment conditions
if (TestEnvironment.shouldUseRealServices()) {
  // Use real database/cache
}

// Get test-specific URLs
const dbUrl = TestEnvironment.getTestDatabaseUrl();
const redisUrl = TestEnvironment.getTestRedisUrl();
```

### 4. Test Module Builder
Simplified test module creation:

```typescript
import { TestModuleBuilder } from '@libs/testing-utils';

// Quick service test setup
const { module, service } = await TestModuleBuilder.createServiceTestModule(
  AuthService,
  [
    { provide: KeycloakService, useValue: mockKeycloakService }
  ]
);

// Advanced module building
const module = await new TestModuleBuilder()
  .addService(AuthService)
  .addCommonMocks({ KEYCLOAK_REALM_NAME: 'test' })
  .addProvider({ provide: KeycloakService, useValue: mockKeycloak })
  .build();
```

## Usage Examples

### Service Unit Tests
```typescript
import { 
  MockFactory, 
  TestDataGenerator, 
  TestModuleBuilder 
} from '@libs/testing-utils';

describe('AuthService', () => {
  let service: AuthService;
  let cacheService: jest.Mocked<CacheService>;

  beforeEach(async () => {
    const { module, service: authService } = await TestModuleBuilder
      .createServiceTestModule(AuthService);
    
    service = authService;
    cacheService = module.get('CacheService');
  });

  it('should register user', async () => {
    const userData = TestDataGenerator.createRegistrationData();
    const result = await service.registerUser(userData);
    
    expect(result).toBeDefined();
    expect(cacheService.set).toHaveBeenCalled();
  });
});
```

### Controller Tests
```typescript
describe('AuthController', () => {
  let controller: AuthController;

  beforeEach(async () => {
    const mockAuthService = {
      registerUser: jest.fn(),
      loginUser: jest.fn(),
    };

    const { controller: authController } = await TestModuleBuilder
      .createControllerTestModule(AuthController, [
        { provide: AuthService, useValue: mockAuthService }
      ]);
    
    controller = authController;
  });

  // Test implementation...
});
```

## Available Mocks

### CacheService Mock
```typescript
const cacheService = MockFactory.createCacheService();
// Methods: get, set, del, invalidatePattern, mget, mset, exists, expire, ttl, keys, flushall
```

### EventPublisher Mock
```typescript
const eventPublisher = MockFactory.createEventPublisher();
// Methods: publish, publishBatch
```

### TracingService Mock
```typescript
const tracingService = MockFactory.createTracingService();
// Methods: createSpan, finishSpan, setTag, setError, getCurrentSpan, injectHeaders, extractFromHeaders
```

### Business Logger Mock
```typescript
const businessLogger = MockFactory.createBusinessLogger();
// Methods: logUserRegistrationEvent, logUserLoginEvent, logUserLogoutEvent, etc.
```

## Test Data Generation

### User Data
```typescript
// Basic user
const user = TestDataGenerator.createTestUser();

// User with overrides
const user = TestDataGenerator.createTestUser({
  email: '<EMAIL>',
  firstName: 'Custom'
});

// Admin user
const admin = TestDataGenerator.createTestAdmin();
```

### Authentication Data
```typescript
// Registration data
const regData = TestDataGenerator.createRegistrationData();

// Login data
const loginData = TestDataGenerator.createLoginData();

// JWT payload
const payload = TestDataGenerator.createJwtPayload({
  realm_access: { roles: ['admin'] }
});

// Keycloak token response
const tokenResponse = TestDataGenerator.createKeycloakTokenResponse();
```

## Environment Configuration

### Test Environment Files
The library expects environment files in the following locations:
- `test/.env.test.unit` - Unit test configuration
- `test/.env.test.integration` - Integration test configuration
- `test/.env.test.e2e` - E2E test configuration

### Environment Variables
```bash
# Test behavior control
USE_REAL_SERVICES=false
RUN_INTEGRATION_TESTS=true
RUN_E2E_TESTS=false

# Test database/cache URLs
TEST_DATABASE_URL=postgresql://test:test@localhost:5433/test_db
TEST_REDIS_URL=redis://localhost:6380

# Service configuration
KEYCLOAK_REALM_NAME=test-realm
USER_SERVICE_URL=http://localhost:3002
AUTH_SERVICE_URL=http://localhost:3001
```

## Integration with Existing Tests

### Migrating Auth Service Tests
Replace manual mock creation with MockFactory:

```typescript
// Before
const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  // ... more methods
};

// After
const mockCacheService = MockFactory.createCacheService();
```

### Updating Test Modules
Replace manual module creation with TestModuleBuilder:

```typescript
// Before
const module: TestingModule = await Test.createTestingModule({
  providers: [
    AuthService,
    { provide: CacheService, useValue: mockCacheService },
    { provide: 'EVENT_PUBLISHER', useValue: mockEventPublisher },
    // ... many more providers
  ],
}).compile();

// After
const { module, service } = await TestModuleBuilder
  .createServiceTestModule(AuthService);
```

## Best Practices

1. **Use MockFactory for all common services** - Ensures consistency across tests
2. **Use TestDataGenerator for test data** - Prevents hardcoded values and improves maintainability
3. **Use TestEnvironment for environment setup** - Standardizes test configuration
4. **Use TestModuleBuilder for complex modules** - Simplifies test setup and reduces boilerplate

## Template Customization

### Adding Custom Mocks
Extend MockFactory for service-specific mocks:

```typescript
// In your service test directory
export class CustomMockFactory extends MockFactory {
  static createCustomService(): jest.Mocked<CustomService> {
    return {
      customMethod: jest.fn(),
      // ... other methods
    };
  }
}
```

### Custom Test Data
Extend TestDataGenerator for domain-specific data:

```typescript
export class CustomTestDataGenerator extends TestDataGenerator {
  static createCustomEntity(overrides = {}) {
    return {
      id: this.generateId(),
      customField: 'default-value',
      ...overrides,
    };
  }
}
```

## API Reference

### MockFactory
- `createCacheService()` - Creates CacheService mock
- `createEventPublisher()` - Creates EventPublisher mock
- `createTracingService()` - Creates TracingService mock
- `createBusinessLogger()` - Creates business logger mock
- `createLoggerFactory()` - Creates logger factory mock
- `createHttpClientService()` - Creates HTTP client mock
- `createCircuitBreakerService()` - Creates circuit breaker mock
- `createConfigService(config)` - Creates ConfigService mock with custom config
- `createCommonMocks(config)` - Creates all common mocks

### TestDataGenerator
- `createTestUser(overrides)` - Generates test user
- `createTestAdmin(overrides)` - Generates admin user
- `createRegistrationData(overrides)` - Generates registration data
- `createLoginData(overrides)` - Generates login data
- `createJwtPayload(overrides)` - Generates JWT payload
- `createKeycloakTokenResponse(overrides)` - Generates token response
- `createBusinessEvent(type, overrides)` - Generates business event
- `generateId()` - Generates unique ID
- `generateEmail(prefix)` - Generates test email
- `generateKeycloakId()` - Generates UUID

### TestEnvironment
- `setupEnvironment(type, service)` - Loads environment configuration
- `isCI()` - Checks if running in CI
- `shouldUseRealServices()` - Checks if real services should be used
- `shouldRunIntegrationTests()` - Checks if integration tests should run
- `shouldRunE2ETests()` - Checks if E2E tests should run
- `getTimeout(type)` - Gets timeout for test type
- `createTestId()` - Creates unique test identifier
- `waitForProcessing(ms)` - Waits for async operations

### TestModuleBuilder
- `addService(ServiceClass)` - Adds service to module
- `addController(ControllerClass)` - Adds controller to module
- `addModule(ModuleClass)` - Adds module import
- `addProvider(provider)` - Adds custom provider
- `addCommonMocks(config)` - Adds all common mocks
- `addObservabilityMocks()` - Adds observability mocks
- `addCachingMocks()` - Adds caching mocks
- `addMessagingMocks()` - Adds messaging mocks
- `build()` - Builds the testing module
- `createServiceTestModule(ServiceClass, providers)` - Quick service test setup
- `createControllerTestModule(ControllerClass, mocks)` - Quick controller test setup