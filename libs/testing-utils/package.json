{"name": "@libs/testing-utils", "version": "1.0.0", "description": "Shared testing utilities for Polyrepo services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "test": "jest"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/testing": "^10.0.0", "@libs/caching": "file:../caching", "@libs/messaging": "file:../messaging", "@libs/observability": "file:../observability", "@libs/shared-types": "file:../shared-types", "@libs/keycloak-client": "file:../keycloak-client"}, "devDependencies": {"@types/jest": "^29.5.2", "jest": "^29.5.0", "typescript": "*"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}