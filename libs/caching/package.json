{"name": "@libs/caching", "version": "1.0.0", "description": "Caching patterns and Redis integration for microservices", "main": "dist/index.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "rimraf dist && rimraf tsconfig.build.tsbuildinfo && rimraf tsconfig.tsbuildinfo && tsc -p tsconfig.build.json --listEmittedFiles", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest --testPathIgnorePatterns=/integration/ --json --outputFile=test-results.json", "test:integration": "jest --testPathPattern=/integration/", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "dependencies": {"@libs/observability": "file:../observability", "@nestjs/common": "^10.0.0", "ioredis": "^5.4.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/testing": "^10.0.0", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "ioredis-mock": "^8.9.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"tsconfig": "test/tsconfig.json"}]}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "moduleNameMapper": {"^@libs/observability$": "<rootDir>/../observability/src", "^@libs/observability/(.*)$": "<rootDir>/../observability/src/$1", "^@libs/error-handling$": "<rootDir>/../error-handling/src", "^@libs/error-handling/(.*)$": "<rootDir>/../error-handling/src/$1", "^@libs/testing-utils$": "<rootDir>/../testing-utils/src", "^@libs/testing-utils/(.*)$": "<rootDir>/../testing-utils/src/$1", "^@libs/shared-types$": "<rootDir>/../shared-types/src", "^@libs/shared-types/(.*)$": "<rootDir>/../shared-types/src/$1"}}}