import { UseCache, InvalidateCache, generate<PERSON><PERSON><PERSON><PERSON>, evaluateKeyGenerator, CACHE_METADATA_KEY, CACHE_INVALIDATE_METADATA_KEY } from '../../src/redis/cache.decorators';
import { SetMetadata } from '@nestjs/common';

// Mock SetMetadata
jest.mock('@nestjs/common', () => ({
  SetMetadata: jest.fn(() => jest.fn()),
}));

describe('Cache Decorators Unit Tests', () => {
  let mockSetMetadata: jest.Mock;

  beforeEach(() => {
    mockSetMetadata = SetMetadata as jest.Mock;
    mockSetMetadata.mockClear();
  });

  describe('UseCache Decorator', () => {
    it('should apply metadata to method', () => {
      const options = { ttl: 300, namespace: 'test' };
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: jest.fn().mockResolvedValue('original result'),
      };

      UseCache(options)(target, propertyKey, descriptor);

      expect(mockSetMetadata).toHaveBeenCalledWith(CACHE_METADATA_KEY, options);
    });

    it('should wrap original method', async () => {
      const originalMethod = jest.fn().mockResolvedValue('original result');
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: originalMethod,
      };

      UseCache()(target, propertyKey, descriptor);

      // Execute the wrapped method
      const result = await descriptor.value('arg1', 'arg2');

      expect(result).toBe('original result');
      expect(originalMethod).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should work with empty options', () => {
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: jest.fn(),
      };

      UseCache()(target, propertyKey, descriptor);

      expect(mockSetMetadata).toHaveBeenCalledWith(CACHE_METADATA_KEY, {});
    });

    it('should preserve method context', async () => {
      const target = {
        name: 'TestClass',
        testMethod: function() {
          return this.name;
        }
      };
      
      const descriptor = {
        value: target.testMethod,
      };

      UseCache()(target, 'testMethod', descriptor);

      // Bind and execute the wrapped method
      const result = await descriptor.value.call(target);

      expect(result).toBe('TestClass');
    });
  });

  describe('InvalidateCache Decorator', () => {
    it('should apply metadata to method', () => {
      const options = { 
        keys: ['key1', 'key2'], 
        pattern: 'test:*',
        namespace: 'test'
      };
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: jest.fn().mockResolvedValue('result'),
      };

      InvalidateCache(options)(target, propertyKey, descriptor);

      expect(mockSetMetadata).toHaveBeenCalledWith(CACHE_INVALIDATE_METADATA_KEY, options);
    });

    it('should execute original method and return result', async () => {
      const originalMethod = jest.fn().mockResolvedValue('method result');
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: originalMethod,
      };

      InvalidateCache()(target, propertyKey, descriptor);

      const result = await descriptor.value('arg1', 'arg2');

      expect(result).toBe('method result');
      expect(originalMethod).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should work with empty options', async () => {
      const originalMethod = jest.fn().mockResolvedValue('result');
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: originalMethod,
      };

      InvalidateCache()(target, propertyKey, descriptor);

      expect(mockSetMetadata).toHaveBeenCalledWith(CACHE_INVALIDATE_METADATA_KEY, {});
      
      const result = await descriptor.value();
      expect(result).toBe('result');
    });

    it('should preserve method context', async () => {
      const target = {
        name: 'TestClass',
        testMethod: function() {
          return this.name;
        }
      };
      
      const descriptor = {
        value: target.testMethod,
      };

      InvalidateCache()(target, 'testMethod', descriptor);

      const result = await descriptor.value.call(target);

      expect(result).toBe('TestClass');
    });

    it('should handle method errors gracefully', async () => {
      const error = new Error('Method failed');
      const originalMethod = jest.fn().mockRejectedValue(error);
      const target = {};
      const propertyKey = 'testMethod';
      const descriptor = {
        value: originalMethod,
      };

      InvalidateCache()(target, propertyKey, descriptor);

      await expect(descriptor.value()).rejects.toThrow('Method failed');
    });
  });

  describe('generateCacheKey Helper', () => {
    it('should generate key without namespace', () => {
      const key = generateCacheKey('', 'getUserById', [123]);
      expect(key).toBe('getUserById:123');
    });

    it('should generate key with namespace', () => {
      const key = generateCacheKey('users', 'getUserById', [123]);
      expect(key).toBe('users:getUserById:123');
    });

    it('should handle complex arguments', () => {
      const args = [{ id: 123, filter: 'active' }, 'sort_desc'];
      const key = generateCacheKey('products', 'getProducts', args);
      
      expect(key).toContain('products:getProducts:');
      expect(key).toContain('id_123');
      expect(key).toContain('filter_active');
      expect(key).toContain('sort_desc');
    });

    it('should handle empty arguments', () => {
      const key = generateCacheKey('users', 'getAllUsers', []);
      expect(key).toBe('users:getAllUsers:no_args');
    });

    it('should clean special characters from arguments', () => {
      const args = [{ key: 'value', list: [1, 2, 3] }];
      const key = generateCacheKey('test', 'method', args);
      
      // Should not contain {, }, [, ], or quotes
      expect(key).not.toMatch(/[{}[\]"]/);
      expect(key).toContain('key_value');
      expect(key).toContain('list_1_2_3');
    });

    it('should handle nested objects', () => {
      const args = [{ user: { id: 123, profile: { name: 'John' } } }];
      const key = generateCacheKey('complex', 'method', args);
      
      expect(key).toContain('complex:method:');
      expect(key).toContain('user_id_123');
      expect(key).toContain('profile_name_John');
    });
  });

  describe('evaluateKeyGenerator Helper', () => {
    it('should return string keys as-is', () => {
      const result = evaluateKeyGenerator('static-key', ['arg1', 'arg2']);
      expect(result).toBe('static-key');
    });

    it('should execute function key generators', () => {
      const keyGenerator = jest.fn().mockReturnValue('generated-key');
      const args = ['arg1', 'arg2'];
      
      const result = evaluateKeyGenerator(keyGenerator, args);
      
      expect(result).toBe('generated-key');
      expect(keyGenerator).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should pass all arguments to key generator function', () => {
      const keyGenerator = (...args: any[]) => `key:${args.join(':')}`;
      const args = [123, 'user', { active: true }];
      
      const result = evaluateKeyGenerator(keyGenerator, args);
      
      expect(result).toBe('key:123:user:[object Object]');
    });

    it('should handle key generator functions that use arguments', () => {
      const keyGenerator = (userId: number, action: string) => `user:${userId}:${action}`;
      
      const result = evaluateKeyGenerator(keyGenerator, [123, 'profile']);
      
      expect(result).toBe('user:123:profile');
    });

    it('should handle complex key generation logic', () => {
      const keyGenerator = (filter: any) => {
        if (filter.category) {
          return `products:${filter.category}:${filter.sort || 'default'}`;
        }
        return 'products:all';
      };
      
      const result1 = evaluateKeyGenerator(keyGenerator, [{ category: 'electronics', sort: 'price' }]);
      expect(result1).toBe('products:electronics:price');
      
      const result2 = evaluateKeyGenerator(keyGenerator, [{ category: 'books' }]);
      expect(result2).toBe('products:books:default');
      
      const result3 = evaluateKeyGenerator(keyGenerator, [{}]);
      expect(result3).toBe('products:all');
    });
  });

  describe('Decorator Integration', () => {
    it('should work together for complex caching scenarios', () => {
      class TestService {
        @UseCache({ ttl: 600, namespace: 'users' })
        async getUser(id: number) {
          return { id, name: 'John' };
        }

        @InvalidateCache({ pattern: 'users:*' })
        async updateUser(id: number, data: any) {
          return { id, ...data };
        }
      }

      // Verify decorators were applied
      expect(mockSetMetadata).toHaveBeenCalledWith(
        CACHE_METADATA_KEY, 
        { ttl: 600, namespace: 'users' }
      );
      expect(mockSetMetadata).toHaveBeenCalledWith(
        CACHE_INVALIDATE_METADATA_KEY, 
        { pattern: 'users:*' }
      );
    });

    it('should support dynamic key generation in cache options', () => {
      const dynamicKeyGen = (id: number, filter: string) => `user:${id}:${filter}`;
      
      const options = {
        key: dynamicKeyGen,
        ttl: 300,
        namespace: 'api'
      };

      const target = {};
      const descriptor = { value: jest.fn() };

      UseCache(options)(target, 'method', descriptor);

      expect(mockSetMetadata).toHaveBeenCalledWith(CACHE_METADATA_KEY, options);
    });
  });
});