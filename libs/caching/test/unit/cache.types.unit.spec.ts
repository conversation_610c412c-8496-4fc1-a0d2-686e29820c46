import {
  CacheOptions,
  CacheDecoratorOptions,
  CacheInvalidationOptions,
  CacheMetrics,
  CacheOperationResult,
  CacheStrategy,
} from '../../src/redis/cache.types';

describe('Cache Types Unit Tests', () => {
  describe('CacheOptions Interface', () => {
    it('should allow minimal configuration', () => {
      const options: CacheOptions = {};
      
      expect(options).toBeDefined();
      expect(Object.keys(options)).toHaveLength(0);
    });

    it('should allow full configuration', () => {
      const options: CacheOptions = {
        url: 'redis://localhost:6379/0',
        host: 'localhost',
        port: 6379,
        db: 0,
        connectTimeout: 5000,
        commandTimeout: 5000,
        defaultTtl: 300,
        keyPrefix: 'cache:',
      };

      expect(options.url).toBe('redis://localhost:6379/0');
      expect(options.host).toBe('localhost');
      expect(options.port).toBe(6379);
      expect(options.db).toBe(0);
      expect(options.connectTimeout).toBe(5000);
      expect(options.commandTimeout).toBe(5000);
      expect(options.defaultTtl).toBe(300);
      expect(options.keyPrefix).toBe('cache:');
    });

    it('should allow partial configuration', () => {
      const options: CacheOptions = {
        host: 'redis-server',
        defaultTtl: 600,
      };

      expect(options.host).toBe('redis-server');
      expect(options.defaultTtl).toBe(600);
      expect(options.port).toBeUndefined();
      expect(options.db).toBeUndefined();
    });
  });

  describe('CacheDecoratorOptions Interface', () => {
    it('should allow empty options', () => {
      const options: CacheDecoratorOptions = {};
      
      expect(options).toBeDefined();
    });

    it('should allow string key', () => {
      const options: CacheDecoratorOptions = {
        key: 'static-key',
        ttl: 300,
      };

      expect(options.key).toBe('static-key');
      expect(options.ttl).toBe(300);
    });

    it('should allow function key generator', () => {
      const keyGenerator = (id: number) => `user:${id}`;
      const options: CacheDecoratorOptions = {
        key: keyGenerator,
        ttl: 600,
        namespace: 'users',
      };

      expect(typeof options.key).toBe('function');
      expect(options.ttl).toBe(600);
      expect(options.namespace).toBe('users');
    });

    it('should allow condition function', () => {
      const condition = (result: any) => result && result.success;
      const options: CacheDecoratorOptions = {
        condition,
        refreshAhead: true,
      };

      expect(typeof options.condition).toBe('function');
      expect(options.refreshAhead).toBe(true);
    });

    it('should support comprehensive configuration', () => {
      const keyGenerator = (...args: any[]) => `dynamic:${args.join(':')}`;
      const condition = (result: any) => result !== null && result !== undefined;
      
      const options: CacheDecoratorOptions = {
        key: keyGenerator,
        ttl: 1800,
        namespace: 'api',
        refreshAhead: true,
        condition,
      };

      expect(typeof options.key).toBe('function');
      expect(options.ttl).toBe(1800);
      expect(options.namespace).toBe('api');
      expect(options.refreshAhead).toBe(true);
      expect(typeof options.condition).toBe('function');
    });
  });

  describe('CacheInvalidationOptions Interface', () => {
    it('should allow pattern-based invalidation', () => {
      const options: CacheInvalidationOptions = {
        pattern: 'user:*',
      };

      expect(options.pattern).toBe('user:*');
    });

    it('should allow key-based invalidation', () => {
      const options: CacheInvalidationOptions = {
        keys: ['key1', 'key2', 'key3'],
      };

      expect(options.keys).toEqual(['key1', 'key2', 'key3']);
    });

    it('should allow namespace-based invalidation', () => {
      const options: CacheInvalidationOptions = {
        namespace: 'products',
      };

      expect(options.namespace).toBe('products');
    });

    it('should allow combined invalidation options', () => {
      const options: CacheInvalidationOptions = {
        pattern: 'temp:*',
        keys: ['specific-key'],
        namespace: 'session',
      };

      expect(options.pattern).toBe('temp:*');
      expect(options.keys).toEqual(['specific-key']);
      expect(options.namespace).toBe('session');
    });
  });

  describe('CacheMetrics Interface', () => {
    it('should define all required metrics properties', () => {
      const metrics: CacheMetrics = {
        totalOperations: 1000,
        hits: 800,
        misses: 200,
        hitRatio: 0.8,
        averageResponseTime: 15.5,
        errors: 5,
        errorRate: 0.005,
      };

      expect(metrics.totalOperations).toBe(1000);
      expect(metrics.hits).toBe(800);
      expect(metrics.misses).toBe(200);
      expect(metrics.hitRatio).toBe(0.8);
      expect(metrics.averageResponseTime).toBe(15.5);
      expect(metrics.errors).toBe(5);
      expect(metrics.errorRate).toBe(0.005);
    });

    it('should calculate hit ratio correctly', () => {
      const hits = 750;
      const misses = 250;
      const totalOperations = hits + misses;
      
      const metrics: CacheMetrics = {
        totalOperations,
        hits,
        misses,
        hitRatio: hits / totalOperations,
        averageResponseTime: 10,
        errors: 0,
        errorRate: 0,
      };

      expect(metrics.hitRatio).toBe(0.75);
      expect(metrics.totalOperations).toBe(1000);
    });

    it('should handle zero operations', () => {
      const metrics: CacheMetrics = {
        totalOperations: 0,
        hits: 0,
        misses: 0,
        hitRatio: 0,
        averageResponseTime: 0,
        errors: 0,
        errorRate: 0,
      };

      expect(metrics.hitRatio).toBe(0);
      expect(metrics.errorRate).toBe(0);
    });
  });

  describe('CacheOperationResult Interface', () => {
    it('should define successful cache hit result', () => {
      const result: CacheOperationResult<string> = {
        success: true,
        value: 'cached-value',
        hit: true,
        responseTime: 5.2,
      };

      expect(result.success).toBe(true);
      expect(result.value).toBe('cached-value');
      expect(result.hit).toBe(true);
      expect(result.responseTime).toBe(5.2);
    });

    it('should define successful cache miss result', () => {
      const result: CacheOperationResult<any> = {
        success: true,
        hit: false,
        responseTime: 3.1,
      };

      expect(result.success).toBe(true);
      expect(result.hit).toBe(false);
      expect(result.value).toBeUndefined();
      expect(result.responseTime).toBe(3.1);
    });

    it('should define error result with correlation', () => {
      const result: CacheOperationResult<any> = {
        success: false,
        hit: false,
        responseTime: 100.5,
        error: 'Redis connection failed',
        correlationId: 'req-123-cache-error',
      };

      expect(result.success).toBe(false);
      expect(result.hit).toBe(false);
      expect(result.error).toBe('Redis connection failed');
      expect(result.correlationId).toBe('req-123-cache-error');
    });

    it('should support complex value types', () => {
      interface UserData {
        id: number;
        name: string;
        roles: string[];
      }

      const userData: UserData = {
        id: 123,
        name: 'John Doe',
        roles: ['user', 'admin'],
      };

      const result: CacheOperationResult<UserData> = {
        success: true,
        value: userData,
        hit: true,
        responseTime: 2.3,
        correlationId: 'req-456',
      };

      expect(result.value?.id).toBe(123);
      expect(result.value?.name).toBe('John Doe');
      expect(result.value?.roles).toEqual(['user', 'admin']);
    });

    it('should support standardized error responses', () => {
      const standardizedError = {
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'Cache operation failed',
        timestamp: '2024-01-01T00:00:00.000Z',
        path: '/cache/get/user:123',
      };

      const result: CacheOperationResult<any> = {
        success: false,
        hit: false,
        responseTime: 50.0,
        error: 'Redis timeout',
        correlationId: 'req-789',
        standardizedError,
      };

      expect(result.standardizedError?.statusCode).toBe(500);
      expect(result.standardizedError?.message).toBe('Cache operation failed');
    });
  });

  describe('CacheStrategy Enum', () => {
    it('should define all cache strategy values', () => {
      expect(CacheStrategy.CACHE_ASIDE).toBe('CACHE_ASIDE');
      expect(CacheStrategy.WRITE_THROUGH).toBe('WRITE_THROUGH');
      expect(CacheStrategy.WRITE_BEHIND).toBe('WRITE_BEHIND');
      expect(CacheStrategy.REFRESH_AHEAD).toBe('REFRESH_AHEAD');
    });

    it('should allow using strategies in configuration', () => {
      const cacheConfig = {
        strategy: CacheStrategy.CACHE_ASIDE,
        fallbackStrategy: CacheStrategy.WRITE_THROUGH,
      };

      expect(cacheConfig.strategy).toBe('CACHE_ASIDE');
      expect(cacheConfig.fallbackStrategy).toBe('WRITE_THROUGH');
    });

    it('should support strategy comparison', () => {
      const currentStrategy = CacheStrategy.REFRESH_AHEAD;
      
      const isRefreshAhead = currentStrategy === CacheStrategy.REFRESH_AHEAD;
      const isCacheAside = currentStrategy === CacheStrategy.CACHE_ASIDE as CacheStrategy;

      expect(isRefreshAhead).toBe(true);
      expect(isCacheAside).toBe(false);
    });

    it('should allow iterating over strategy values', () => {
      const strategies = Object.values(CacheStrategy);
      
      expect(strategies).toHaveLength(4);
      expect(strategies).toContain('CACHE_ASIDE');
      expect(strategies).toContain('WRITE_THROUGH');
      expect(strategies).toContain('WRITE_BEHIND');
      expect(strategies).toContain('REFRESH_AHEAD');
    });
  });

  describe('Type Integration', () => {
    it('should work together in realistic scenarios', () => {
      // Cache configuration
      const cacheOptions: CacheOptions = {
        host: 'redis-cluster',
        port: 6379,
        defaultTtl: 300,
        keyPrefix: 'app:',
      };

      // Decorator configuration
      const decoratorOptions: CacheDecoratorOptions = {
        key: (userId: number) => `user:${userId}`,
        ttl: 600,
        namespace: 'users',
        condition: (result: any) => result && !result.error,
      };

      // Invalidation configuration
      const invalidationOptions: CacheInvalidationOptions = {
        pattern: 'users:*',
        namespace: 'session',
      };

      // Operation result
      const operationResult: CacheOperationResult<{ id: number; name: string }> = {
        success: true,
        value: { id: 123, name: 'John' },
        hit: true,
        responseTime: 5.5,
        correlationId: 'req-abc-123',
      };

      // All types should work together
      expect(cacheOptions.keyPrefix).toBe('app:');
      expect(decoratorOptions.namespace).toBe('users');
      expect(invalidationOptions.pattern).toBe('users:*');
      expect(operationResult.value?.name).toBe('John');
    });

    it('should support generic type parameters correctly', () => {
      // String result
      const stringResult: CacheOperationResult<string> = {
        success: true,
        value: 'test-string',
        hit: true,
        responseTime: 1.0,
      };

      // Array result
      const arrayResult: CacheOperationResult<number[]> = {
        success: true,
        value: [1, 2, 3, 4, 5],
        hit: true,
        responseTime: 2.5,
      };

      // Object result
      const objectResult: CacheOperationResult<{ status: string; data: any }> = {
        success: true,
        value: { status: 'active', data: { count: 42 } },
        hit: false,
        responseTime: 10.0,
      };

      expect(stringResult.value).toBe('test-string');
      expect(arrayResult.value).toHaveLength(5);
      expect(objectResult.value?.status).toBe('active');
    });
  });
});