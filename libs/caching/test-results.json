{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 2, "numPassedTests": 48, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 48, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750347305276, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Cache Types Unit Tests", "CacheOptions Interface"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOptions Interface should allow minimal configuration", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should allow minimal configuration"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOptions Interface"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOptions Interface should allow full configuration", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "status": "passed", "title": "should allow full configuration"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOptions Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOptions Interface should allow partial configuration", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should allow partial configuration"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheDecoratorOptions Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheDecoratorOptions Interface should allow empty options", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow empty options"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheDecoratorOptions Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheDecoratorOptions Interface should allow string key", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should allow string key"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheDecoratorOptions Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheDecoratorOptions Interface should allow function key generator", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should allow function key generator"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheDecoratorOptions Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheDecoratorOptions Interface should allow condition function", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should allow condition function"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheDecoratorOptions Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheDecoratorOptions Interface should support comprehensive configuration", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should support comprehensive configuration"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheInvalidationOptions Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheInvalidationOptions Interface should allow pattern-based invalidation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow pattern-based invalidation"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheInvalidationOptions Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheInvalidationOptions Interface should allow key-based invalidation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow key-based invalidation"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheInvalidationOptions Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheInvalidationOptions Interface should allow namespace-based invalidation", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow namespace-based invalidation"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheInvalidationOptions Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheInvalidationOptions Interface should allow combined invalidation options", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should allow combined invalidation options"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheMetrics Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheMetrics Interface should define all required metrics properties", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should define all required metrics properties"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheMetrics Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheMetrics Interface should calculate hit ratio correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should calculate hit ratio correctly"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheMetrics Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheMetrics Interface should handle zero operations", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle zero operations"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOperationResult Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOperationResult Interface should define successful cache hit result", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should define successful cache hit result"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOperationResult Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOperationResult Interface should define successful cache miss result", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should define successful cache miss result"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOperationResult Interface"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOperationResult Interface should define error result with correlation", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should define error result with correlation"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOperationResult Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOperationResult Interface should support complex value types", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should support complex value types"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheOperationResult Interface"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheOperationResult Interface should support standardized error responses", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should support standardized error responses"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheStrategy Enum"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheStrategy Enum should define all cache strategy values", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should define all cache strategy values"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheStrategy Enum"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheStrategy Enum should allow using strategies in configuration", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should allow using strategies in configuration"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheStrategy Enum"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheStrategy Enum should support strategy comparison", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should support strategy comparison"}, {"ancestorTitles": ["Cache Types Unit Tests", "CacheStrategy Enum"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests CacheStrategy Enum should allow iterating over strategy values", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should allow iterating over strategy values"}, {"ancestorTitles": ["Cache Types Unit Tests", "Type Integration"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests Type Integration should work together in realistic scenarios", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should work together in realistic scenarios"}, {"ancestorTitles": ["Cache Types Unit Tests", "Type Integration"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Types Unit Tests Type Integration should support generic type parameters correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should support generic type parameters correctly"}], "endTime": 1750347308703, "message": "", "name": "/root/code/polyrepo/libs/caching/test/unit/cache.types.unit.spec.ts", "startTime": 1750347305965, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Cache Decorators Unit Tests", "UseCache Decorator"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests UseCache Decorator should apply metadata to method", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should apply metadata to method"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "UseCache Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests UseCache Decorator should wrap original method", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should wrap original method"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "UseCache Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests UseCache Decorator should work with empty options", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should work with empty options"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "UseCache Decorator"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests UseCache Decorator should preserve method context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should preserve method context"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "InvalidateCache Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests InvalidateCache Decorator should apply metadata to method", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should apply metadata to method"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "InvalidateCache Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests InvalidateCache Decorator should execute original method and return result", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should execute original method and return result"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "InvalidateCache Decorator"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests InvalidateCache Decorator should work with empty options", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should work with empty options"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "InvalidateCache Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests InvalidateCache Decorator should preserve method context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should preserve method context"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "InvalidateCache Decorator"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests InvalidateCache Decorator should handle method errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle method errors gracefully"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests generateCacheKey Helper should generate key without namespace", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should generate key without namespace"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests generateCacheKey Helper should generate key with namespace", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should generate key with namespace"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests generate<PERSON>ache<PERSON>ey Helper should handle complex arguments", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle complex arguments"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests generate<PERSON>ache<PERSON><PERSON> Helper should handle empty arguments", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle empty arguments"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests generate<PERSON>ache<PERSON><PERSON> Helper should clean special characters from arguments", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should clean special characters from arguments"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests generateCache<PERSON>ey Helper should handle nested objects", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle nested objects"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "evaluateKeyGenerator Helper"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests evaluateKeyGenerator Helper should return string keys as-is", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return string keys as-is"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "evaluateKeyGenerator Helper"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests evaluateKeyGenerator Helper should execute function key generators", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should execute function key generators"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "evaluateKeyGenerator Helper"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests evaluateKeyGenerator Helper should pass all arguments to key generator function", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should pass all arguments to key generator function"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "evaluateKeyGenerator Helper"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests evaluateKeyGenerator Helper should handle key generator functions that use arguments", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle key generator functions that use arguments"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "evaluateKeyGenerator Helper"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests evaluateKeyGenerator Helper should handle complex key generation logic", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle complex key generation logic"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "Decorator Integration"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests Decorator Integration should work together for complex caching scenarios", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should work together for complex caching scenarios"}, {"ancestorTitles": ["Cache Decorators Unit Tests", "Decorator Integration"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Cache Decorators Unit Tests Decorator Integration should support dynamic key generation in cache options", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should support dynamic key generation in cache options"}], "endTime": 1750347309025, "message": "", "name": "/root/code/polyrepo/libs/caching/test/unit/cache.decorators.unit.spec.ts", "startTime": 1750347305982, "status": "passed", "summary": ""}], "wasInterrupted": false}