import { Injectable, SetMetadata } from '@nestjs/common';
import { CacheDecoratorOptions } from './cache.types';

/**
 * Metadata key for cache decorator
 */
export const CACHE_METADATA_KEY = 'cache:options';

/**
 * Metadata key for cache invalidation decorator
 */
export const CACHE_INVALIDATE_METADATA_KEY = 'cache:invalidate';

/**
 * Cache decorator for methods
 * Automatically caches method results based on arguments
 */
export function UseCache(options: CacheDecoratorOptions = {}): MethodDecorator {
  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    // Store metadata for potential interceptor use
    SetMetadata(CACHE_METADATA_KEY, options)(target, propertyKey, descriptor);
    
    descriptor.value = async function (...args: any[]) {
      // This implementation will be enhanced by the CacheInterceptor
      // For now, just call the original method
      return originalMethod.apply(this, args);
    };
    
    return descriptor;
  };
}

/**
 * Cache invalidation decorator for methods
 * Automatically invalidates cache entries when method is called
 */
export function InvalidateCache(options: {
  keys?: string[] | ((...args: any[]) => string[]);
  pattern?: string | ((...args: any[]) => string);
  namespace?: string;
} = {}): MethodDecorator {
  return (target: any, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    // Store metadata for potential interceptor use
    SetMetadata(CACHE_INVALIDATE_METADATA_KEY, options)(target, propertyKey, descriptor);
    
    descriptor.value = async function (...args: any[]) {
      // Execute the original method first
      const result = await originalMethod.apply(this, args);
      
      // This implementation will be enhanced by the CacheInvalidationInterceptor
      // For now, just return the result
      return result;
    };
    
    return descriptor;
  };
}

/**
 * Helper function to generate cache keys
 */
export function generateCacheKey(
  namespace: string = '',
  method: string,
  args: any[]
): string {
  // Create a stable key from method arguments
  const argsKey = args.length > 0 
    ? JSON.stringify(args).replace(/[{}[\]"]/g, '').replace(/[,:]/g, '_')
    : 'no_args';
    
  const key = namespace 
    ? `${namespace}:${method}:${argsKey}`
    : `${method}:${argsKey}`;
    
  return key;
}

/**
 * Helper function to evaluate key generator functions
 */
export function evaluateKeyGenerator(
  keyGenerator: string | ((...args: any[]) => string),
  args: any[]
): string {
  if (typeof keyGenerator === 'function') {
    return keyGenerator(...args);
  }
  return keyGenerator;
}
