﻿import { DynamicModule, Module, Provider } from '@nestjs/common';
import { CacheService } from './cache.service';
import { CacheOptions } from './cache.types';
import { LoggingModule } from '@libs/observability';
import { ErrorHandlingModule } from '@libs/error-handling';

/**
 * Token for cache options injection
 */
export const CACHE_OPTIONS = 'CACHE_OPTIONS';

/**
 * Cache module for Redis-based caching with observability integration
 */
@Module({})
export class CacheModule {
  /**
   * Register cache module with options
   */
  static register(options: CacheOptions): DynamicModule {
    const optionsProvider: Provider = {
      provide: CACHE_OPTIONS,
      useValue: options,
    };

    return {
      module: CacheModule,
      imports: [
        LoggingModule.forRoot(), // Provides LOGGER_FACTORY
        ErrorHandlingModule.forRoot(), // Provides CorrelationService, ErrorResponseBuilderService
      ],
      providers: [optionsProvider, CacheService],
      exports: [CacheService],
      global: true,
    };
  }

  /**
   * Register cache module asynchronously
   */
  static registerAsync(options: {
    useFactory: (...args: any[]) => Promise<CacheOptions> | CacheOptions;
    inject?: any[];
  }): DynamicModule {
    const optionsProvider: Provider = {
      provide: CACHE_OPTIONS,
      useFactory: options.useFactory,
      inject: options.inject || [],
    };

    return {
      module: CacheModule,
      imports: [
        LoggingModule.forRoot(), // Provides LOGGER_FACTORY
        ErrorHandlingModule.forRoot(), // Provides CorrelationService, ErrorResponseBuilderService
      ],
      providers: [optionsProvider, CacheService],
      exports: [CacheService],
      global: true,
    };
  }
}
