/**
 * Cache configuration options for Redis
 */
export interface CacheOptions {
  /**
   * Redis connection URL (e.g., 'redis://localhost:6379')
   */
  url?: string;
  
  /**
   * Redis host
   * @default 'localhost'
   */
  host?: string;
  
  /**
   * Redis port
   * @default 6379
   */
  port?: number;
  
  /**
   * Redis database number
   * @default 0
   */
  db?: number;
  
  /**
   * Connection timeout in milliseconds
   * @default 5000
   */
  connectTimeout?: number;
  
  /**
   * Command timeout in milliseconds
   * @default 5000
   */
  commandTimeout?: number;
  
  /**
   * Default TTL for cache entries in seconds
   * @default 300 (5 minutes)
   */
  defaultTtl?: number;
  
  /**
   * Key prefix for cache entries
   * @default 'cache:'
   */
  keyPrefix?: string;
}

/**
 * Cache decorator options
 */
export interface CacheDecoratorOptions {
  /**
   * Cache key or function to generate cache key
   */
  key?: string | ((...args: any[]) => string);
  
  /**
   * Time to live in seconds
   */
  ttl?: number;
  
  /**
   * Cache namespace/prefix
   */
  namespace?: string;
  
  /**
   * Whether to refresh cache asynchronously when accessed
   * @default false
   */
  refreshAhead?: boolean;
  
  /**
   * Condition function to determine if result should be cached
   */
  condition?: (result: any) => boolean;
}

/**
 * Cache invalidation options
 */
export interface CacheInvalidationOptions {
  /**
   * Pattern to match keys for invalidation (supports glob patterns)
   */
  pattern?: string;
  
  /**
   * Specific keys to invalidate
   */
  keys?: string[];
  
  /**
   * Namespace to invalidate
   */
  namespace?: string;
}

/**
 * Cache metrics for observability
 */
export interface CacheMetrics {
  /**
   * Total number of cache operations
   */
  totalOperations: number;
  
  /**
   * Number of cache hits
   */
  hits: number;
  
  /**
   * Number of cache misses
   */
  misses: number;
  
  /**
   * Hit ratio (hits / total operations)
   */
  hitRatio: number;
  
  /**
   * Average response time in milliseconds
   */
  averageResponseTime: number;
  
  /**
   * Number of errors
   */
  errors: number;
  
  /**
   * Error rate (errors / total operations)
   */
  errorRate: number;
}

/**
 * Cache operation result with enhanced error handling
 */
export interface CacheOperationResult<T = any> {
  /**
   * Whether the operation was successful
   */
  success: boolean;
  
  /**
   * The cached value (if found)
   */
  value?: T;
  
  /**
   * Whether this was a cache hit
   */
  hit: boolean;
  
  /**
   * Response time in milliseconds
   */
  responseTime: number;
  
  /**
   * Error message if operation failed
   */
  error?: string;
  
  /**
   * Correlation ID for request tracing
   */
  correlationId?: string;
  
  /**
   * Standardized error response (if error builder available)
   */
  standardizedError?: any;
}

/**
 * Cache strategy types
 */
export enum CacheStrategy {
  /**
   * Cache-aside: Application manages the cache
   */
  CACHE_ASIDE = 'CACHE_ASIDE',
  
  /**
   * Write-through: Write to cache and storage simultaneously
   */
  WRITE_THROUGH = 'WRITE_THROUGH',
  
  /**
   * Write-behind: Write to cache immediately, storage asynchronously
   */
  WRITE_BEHIND = 'WRITE_BEHIND',
  
  /**
   * Refresh-ahead: Proactively refresh cache before expiration
   */
  REFRESH_AHEAD = 'REFRESH_AHEAD'
}
