import { Injectable, OnM<PERSON>uleD<PERSON>roy, OnModuleInit, Inject, Optional } from '@nestjs/common';
import { Redis } from 'ioredis';
import { ObservabilityLogger } from '@libs/observability';
import { 
  ErrorResponseBuilderService, 
  CorrelationService 
} from '@libs/error-handling';
import { 
  CacheOptions, 
  CacheOperationResult, 
  CacheMetrics,
  CacheInvalidationOptions 
} from './cache.types';
import { CACHE_OPTIONS } from './cache.module';

/**
 * Redis-based cache service with observability and metrics
 */
@Injectable()
export class CacheService implements OnModuleInit, OnModuleDestroy {
  private readonly logger: ObservabilityLogger;
  private redis!: Redis;
  private metrics: CacheMetrics = {
    totalOperations: 0,
    hits: 0,
    misses: 0,
    hitRatio: 0,
    averageResponseTime: 0,
    errors: 0,
    errorRate: 0
  };
  private responseTimes: number[] = [];

  constructor(
    @Inject(CACHE_OPTIONS) private readonly options: CacheOptions,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    @Optional() private readonly correlationService?: CorrelationService,
    @Optional() private readonly errorResponseBuilder?: ErrorResponseBuilderService,
  ) {
    // Get a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger(CacheService.name);
  }

  /**
   * Enhanced error handling with correlation context and standardized responses
   */
  private handleCacheError(error: unknown, operation: string, key: string): {
    message: string;
    correlationId: string;
    logData: any;
  } {
    const correlationId = this.correlationService?.getContext()?.correlationId || 
                         this.correlationService?.generateCorrelationId() || 
                         `cache_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Enhanced logging with correlation context
    const logData = {
      message: `Cache ${operation} error`,
      error: errorMessage,
      key,
      operation,
      correlationId,
      service: 'cache-service',
      timestamp: new Date().toISOString(),
      ...(this.correlationService?.getContext() || {}),
    };

    // Log with correlation context
    this.logger.error(logData);

    return {
      message: errorMessage,
      correlationId,
      logData,
    };
  }

  /**
   * Build standardized cache error response
   */
  private buildCacheErrorResponse<T>(
    error: unknown, 
    operation: string, 
    key: string, 
    responseTime: number
  ): CacheOperationResult<T> {
    const errorDetails = this.handleCacheError(error, operation, key);
    
    // Build standardized error response if error builder is available
    let standardizedError: any = null;
    if (this.errorResponseBuilder) {
      try {
        standardizedError = this.errorResponseBuilder.buildFromException(
          error,
          `/cache/${operation}/${key}`
        );
      } catch (builderError) {
        this.logger.warn({
          message: 'Failed to build standardized error response',
          error: builderError instanceof Error ? builderError.message : String(builderError),
          correlationId: errorDetails.correlationId,
        });
      }
    }

    return {
      success: false,
      hit: false,
      responseTime,
      error: errorDetails.message,
      correlationId: errorDetails.correlationId,
      standardizedError,
    } as CacheOperationResult<T>;
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  /**
   * Connect to Redis
   */
  private async connect(): Promise<void> {
    try {
      this.redis = new Redis({
        host: this.options.host || 'localhost',
        port: this.options.port || 6379,
        db: this.options.db || 0,
        connectTimeout: this.options.connectTimeout || 5000,
        commandTimeout: this.options.commandTimeout || 5000,
        keyPrefix: this.options.keyPrefix || 'cache:',
        maxRetriesPerRequest: 3,
        lazyConnect: true
      });

      // Set up event listeners
      this.redis.on('connect', () => {
        this.logger.log('Connected to Redis');
      });

      this.redis.on('error', (error) => {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.error(`Redis connection error: ${errorMessage}`);
      });

      await this.redis.connect();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to connect to Redis: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Disconnect from Redis
   */
  private async disconnect(): Promise<void> {
    if (this.redis) {
      await this.redis.disconnect();
      this.logger.log('Disconnected from Redis');
    }
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<CacheOperationResult<T>> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalOperations++;
      
      const value = await this.redis.get(key);
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);

      if (value !== null) {
        this.metrics.hits++;
        const parsedValue = JSON.parse(value);
        
        this.logger.debug('Cache hit');
        
        return {
          success: true,
          value: parsedValue,
          hit: true,
          responseTime
        };
      } else {
        this.metrics.misses++;
        
        this.logger.debug('Cache miss');
        
        return {
          success: true,
          hit: false,
          responseTime
        };
      }
    } catch (error) {
      this.metrics.errors++;
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      return this.buildCacheErrorResponse<T>(error, 'get', key, responseTime);
    } finally {
      this.updateMetrics();
    }
  }

  /**
   * Set value in cache with TTL
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<CacheOperationResult<void>> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalOperations++;
      
      const serializedValue = JSON.stringify(value);
      const cacheTtl = ttl || this.options.defaultTtl || 300;
      
      await this.redis.setex(key, cacheTtl, serializedValue);
      
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      this.logger.debug('Cache set');
      
      return {
        success: true,
        hit: false, // Not applicable for set operations
        responseTime
      };
    } catch (error) {
      this.metrics.errors++;
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      return this.buildCacheErrorResponse<void>(error, 'set', key, responseTime);
    } finally {
      this.updateMetrics();
    }
  }

  /**
   * Delete specific key from cache
   */
  async delete(key: string): Promise<CacheOperationResult<void>> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalOperations++;
      
      await this.redis.del(key);
      
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      this.logger.debug('Cache delete');
      
      return {
        success: true,
        hit: false,
        responseTime
      };
    } catch (error) {
      this.metrics.errors++;
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      return this.buildCacheErrorResponse<void>(error, 'delete', key, responseTime);
    } finally {
      this.updateMetrics();
    }
  }

  /**
   * Invalidate cache entries based on pattern or keys
   */
  async invalidate(options: CacheInvalidationOptions): Promise<CacheOperationResult<number>> {
    const startTime = Date.now();
    
    try {
      this.metrics.totalOperations++;
      
      let deletedCount = 0;
      
      if (options.keys) {
        // Delete specific keys
        deletedCount = await this.redis.del(...options.keys);
      } else if (options.pattern) {
        // Delete keys matching pattern
        const keys = await this.redis.keys(options.pattern);
        if (keys.length > 0) {
          deletedCount = await this.redis.del(...keys);
        }
      } else if (options.namespace) {
        // Delete keys in namespace
        const pattern = `${options.namespace}:*`;
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          deletedCount = await this.redis.del(...keys);
        }
      }
      
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      this.logger.log('Cache invalidation');
      
      return {
        success: true,
        value: deletedCount,
        hit: false,
        responseTime
      };
    } catch (error) {
      this.metrics.errors++;
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      
      return this.buildCacheErrorResponse<number>(error, 'invalidate', 'pattern', responseTime);
    } finally {
      this.updateMetrics();
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      // Enhanced error handling for exists check
      this.handleCacheError(error, 'exists', key);
      return false;
    }
  }

  /**
   * Get cache metrics for observability
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset cache metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalOperations: 0,
      hits: 0,
      misses: 0,
      hitRatio: 0,
      averageResponseTime: 0,
      errors: 0,
      errorRate: 0
    };
    this.responseTimes = [];
  }

  /**
   * Get Redis client for advanced operations
   */
  getRedisClient(): Redis {
    return this.redis;
  }

  /**
   * Comprehensive health check for cache Redis instance
   */
  async getHealthStatus(): Promise<import('@libs/shared-types').RedisHealth> {
    const startTime = Date.now();
    
    try {
      // Test connection with ping
      await this.redis.ping();
      
      // Get Redis info for additional metrics
      const info = await this.redis.info();
      const memory = await this.redis.info('memory');
      const clients = await this.redis.info('clients');
      
      const responseTime = Date.now() - startTime;
      
      // Parse info for useful metrics
      const memoryUsage = this.parseRedisInfo(memory, 'used_memory');
      const connectedClients = this.parseRedisInfo(clients, 'connected_clients');
      const totalCommands = this.parseRedisInfo(info, 'total_commands_processed');
      
      return {
        status: 'ok',
        responseTime,
        memoryUsage: memoryUsage ? parseInt(memoryUsage) : undefined,
        connectedClients: connectedClients ? parseInt(connectedClients) : undefined,
        commandsProcessed: totalCommands ? parseInt(totalCommands) : undefined,
        details: {
          cacheMetrics: this.getMetrics(),
          redisVersion: this.parseRedisInfo(info, 'redis_version'),
          uptimeInSeconds: this.parseRedisInfo(info, 'uptime_in_seconds')
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // Enhanced error handling for health check
      const errorDetails = this.handleCacheError(error, 'health_check', 'redis');
      
      return {
        status: 'error',
        responseTime,
        error: errorDetails.message,
        details: {
          correlationId: errorDetails.correlationId
        }
      };
    }
  }

  /**
   * Parse Redis INFO command output for specific keys
   */
  private parseRedisInfo(info: string, key: string): string | undefined {
    const lines = info.split('\r\n');
    const line = lines.find(l => l.startsWith(`${key}:`));
    return line ? line.split(':')[1] : undefined;
  }

  /**
   * Update response time tracking
   */
  private updateResponseTime(responseTime: number): void {
    this.responseTimes.push(responseTime);
    
    // Keep only last 1000 response times to prevent memory growth
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000);
    }
  }

  /**
   * Update calculated metrics
   */
  private updateMetrics(): void {
    this.metrics.hitRatio = this.metrics.totalOperations > 0 
      ? this.metrics.hits / this.metrics.totalOperations 
      : 0;
      
    this.metrics.errorRate = this.metrics.totalOperations > 0 
      ? this.metrics.errors / this.metrics.totalOperations 
      : 0;
      
    this.metrics.averageResponseTime = this.responseTimes.length > 0
      ? this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length
      : 0;
  }
}
