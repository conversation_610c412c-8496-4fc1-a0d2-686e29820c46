# HTTP Testing and Mocking Strategy

## Reality Check: Custom Mocking is Always Needed

### Why Even Axios Needs Custom Mocking for Our Use Case

**Our Current HTTP Module is Already Complex:**
```typescript
// Our current HTTP client has:
- Custom observability interceptors
- Correlation ID injection  
- Circuit breaker integration
- Service-specific configurations
- Error transformation
- Retry logic coordination

// axios-mock-adapter can't mock all of this!
```

**Axios Mock Adapter Limitations:**
```typescript
// ❌ axios-mock-adapter only mocks basic requests
const mock = new MockAdapter(axios);
mock.onGet('/users').reply(200, { users: [] });

// ❌ It doesn't mock:
// - Our custom interceptors
// - Observability hooks
// - Circuit breaker behavior  
// - Service-specific configurations
// - Error transformation logic
// - Retry attempt tracking
// - Cache hit/miss behavior
```

### Got Mocking is Actually Easier Than Axios

**Why Got Mocking is Simpler:**
1. **Unified hooks system** - easier to mock consistently
2. **Better error information** - easier to mock error scenarios
3. **Built-in features** - less custom code to mock
4. **Undici MockAgent** - modern, powerful mocking

## Comprehensive Testing Strategy

### Level 1: Unit Testing (Mock HTTP Client)

```typescript
// /libs/http/src/testing/http-mock.service.ts
export class HttpMockService {
  private responses = new Map<string, MockResponse>();
  private requests: MockedRequest[] = [];

  // Simple mocking interface
  onGet(url: string | RegExp): MockRequestBuilder {
    return this.addMock('GET', url);
  }

  onPost(url: string | RegExp): MockRequestBuilder {
    return this.addMock('POST', url);
  }

  private addMock(method: string, url: string | RegExp): MockRequestBuilder {
    return {
      reply: (status: number, data?: any, headers?: Record<string, string>) => {
        this.responses.set(`${method}:${url}`, { status, data, headers });
        return this;
      },
      
      replyWithError: (error: Partial<HttpError>) => {
        this.responses.set(`${method}:${url}`, { error });
        return this;
      },

      // Mock advanced Got features
      replyWithCache: (status: number, data: any, cacheHeaders: CacheHeaders) => {
        this.responses.set(`${method}:${url}`, { 
          status, 
          data, 
          headers: cacheHeaders,
          fromCache: true 
        });
        return this;
      },

      replyWithRetry: (finalStatus: number, retryCount: number, data?: any) => {
        this.responses.set(`${method}:${url}`, { 
          status: finalStatus, 
          data,
          retryAttempts: retryCount
        });
        return this;
      }
    };
  }

  // Get mock for request
  getMockResponse(method: string, url: string): MockResponse | undefined {
    // Try exact match first
    const exactKey = `${method}:${url}`;
    if (this.responses.has(exactKey)) {
      return this.responses.get(exactKey);
    }

    // Try regex match
    for (const [key, response] of this.responses.entries()) {
      const [mockMethod, mockUrl] = key.split(':');
      if (mockMethod === method) {
        try {
          if (new RegExp(mockUrl).test(url)) {
            return response;
          }
        } catch {
          // Not a regex, skip
        }
      }
    }

    return undefined;
  }

  // Track requests for verification
  recordRequest(method: string, url: string, options: any) {
    this.requests.push({
      method,
      url, 
      options,
      timestamp: Date.now()
    });
  }

  // Verification helpers
  getRequestHistory(): MockedRequest[] {
    return [...this.requests];
  }

  getLastRequest(): MockedRequest | undefined {
    return this.requests[this.requests.length - 1];
  }

  expectRequest(method: string, url: string): jest.Matcher {
    const found = this.requests.find(req => req.method === method && req.url === url);
    return expect(found).toBeDefined();
  }

  reset() {
    this.responses.clear();
    this.requests.length = 0;
  }
}
```

### Level 2: Integration Testing (Undici MockAgent)

```typescript
// /libs/http/src/testing/undici-mock.setup.ts
export class UndiciMockSetup {
  private mockAgent: MockAgent;
  private pools = new Map<string, MockPool>();

  setup() {
    this.mockAgent = new MockAgent();
    setGlobalDispatcher(this.mockAgent);
    
    // Disable real network requests
    this.mockAgent.disableNetConnect();
  }

  teardown() {
    this.mockAgent?.close();
  }

  mockService(baseUrl: string) {
    const mockPool = this.mockAgent.get(baseUrl);
    this.pools.set(baseUrl, mockPool);
    
    return {
      get: (path: string) => ({
        reply: (status: number, data: any, headers?: Record<string, string>) => {
          mockPool.intercept({
            path,
            method: 'GET'
          }).reply(status, data, headers);
        },
        
        replyWithDelay: (status: number, data: any, delay: number) => {
          mockPool.intercept({
            path,
            method: 'GET'
          }).reply(() => {
            return new Promise(resolve => {
              setTimeout(() => resolve({ statusCode: status, data }), delay);
            });
          });
        }
      }),

      post: (path: string) => ({
        reply: (status: number, data: any) => {
          mockPool.intercept({
            path,
            method: 'POST'
          }).reply(status, data);
        }
      }),

      // Mock error scenarios
      networkError: (path: string) => {
        mockPool.intercept({
          path,
          method: 'GET'
        }).replyWithError(new Error('ECONNREFUSED'));
      },

      timeout: (path: string) => {
        mockPool.intercept({
          path,
          method: 'GET'  
        }).replyWithError(new Error('ETIMEDOUT'));
      }
    };
  }
}
```

### Level 3: End-to-End Testing (Test Module)

```typescript
// /libs/http/src/testing/http-test.module.ts
@Module({})
export class HttpTestModule {
  static forTesting(mockConfig: TestHttpConfig = {}): DynamicModule {
    return {
      module: HttpTestModule,
      providers: [
        {
          provide: HttpClientService,
          useClass: MockHttpClientService
        },
        {
          provide: 'HTTP_MOCK_CONFIG',
          useValue: mockConfig
        },
        HttpMockService
      ],
      exports: [HttpClientService, HttpMockService]
    };
  }

  static forIntegration(realUrls: string[] = []): DynamicModule {
    return {
      module: HttpTestModule,
      providers: [
        {
          provide: HttpClientService,
          useFactory: () => {
            const mockSetup = new UndiciMockSetup();
            mockSetup.setup();
            
            // Allow real requests to specified URLs
            realUrls.forEach(url => {
              mockSetup.mockAgent.enableNetConnect(url);
            });
            
            return new HttpClientService(/* real config */);
          }
        }
      ],
      exports: [HttpClientService]
    };
  }
}
```

### Testing Best Practices

```typescript
describe('UserService with HTTP Mocking', () => {
  let userService: UserService;
  let httpMock: HttpMockService;
  let undiciMock: UndiciMockSetup;

  beforeEach(async () => {
    // Unit test setup with mock service
    const module = await Test.createTestingModule({
      imports: [HttpTestModule.forTesting()],
      providers: [UserService]
    }).compile();

    userService = module.get<UserService>(UserService);
    httpMock = module.get<HttpMockService>(HttpMockService);
  });

  afterEach(() => {
    httpMock.reset();
  });

  describe('Unit Tests', () => {
    it('should fetch user with caching', async () => {
      // Mock user data with cache headers
      httpMock.onGet('/users/123')
        .replyWithCache(200, { id: 123, name: 'John' }, {
          'cache-control': 'max-age=300',
          'etag': '"user-123-v1"'
        });

      const user = await userService.getUser(123);
      
      expect(user.name).toBe('John');
      httpMock.expectRequest('GET', '/users/123');
    });

    it('should handle retry scenarios', async () => {
      // Mock retry behavior
      httpMock.onGet('/users/456')
        .replyWithRetry(200, 2, { id: 456, name: 'Jane' });

      const user = await userService.getUser(456);
      
      expect(user.name).toBe('Jane');
      // Verify retry attempts were made
      const history = httpMock.getRequestHistory();
      expect(history.filter(r => r.url.includes('456'))).toHaveLength(3); // 1 + 2 retries
    });

    it('should handle error scenarios', async () => {
      httpMock.onGet('/users/999')
        .replyWithError({ 
          statusCode: 404, 
          code: 'USER_NOT_FOUND',
          message: 'User not found' 
        });

      await expect(userService.getUser(999)).rejects.toThrow('User not found');
    });
  });

  describe('Integration Tests', () => {
    beforeEach(() => {
      // Integration test setup with Undici mocking
      undiciMock = new UndiciMockSetup();
      undiciMock.setup();
    });

    afterEach(() => {
      undiciMock.teardown();
    });

    it('should handle real HTTP scenarios', async () => {
      const userServiceMock = undiciMock.mockService('http://user-service:3000');
      
      userServiceMock.get('/users/123').reply(200, { id: 123, name: 'John' });
      userServiceMock.get('/users/123').reply(200, { id: 123, name: 'John' }); // Cache hit

      // First request hits service
      const user1 = await userService.getUser(123);
      expect(user1.name).toBe('John');

      // Second request should hit cache (if caching is enabled)
      const user2 = await userService.getUser(123);  
      expect(user2.name).toBe('John');
    });

    it('should handle network errors', async () => {
      const userServiceMock = undiciMock.mockService('http://user-service:3000');
      userServiceMock.networkError('/users/123');

      await expect(userService.getUser(123)).rejects.toThrow('ECONNREFUSED');
    });

    it('should handle timeouts', async () => {
      const userServiceMock = undiciMock.mockService('http://user-service:3000');
      userServiceMock.timeout('/users/123');

      await expect(userService.getUser(123)).rejects.toThrow('ETIMEDOUT');
    });
  });
});
```

### Mock Complexity Comparison

```typescript
// Current Axios Setup (Already Complex!)
describe('Current Auth Service', () => {
  // ❌ We already need custom mocking:
  - Mock circuit breaker behavior
  - Mock observability interceptors
  - Mock correlation ID injection
  - Mock retry coordination
  - Mock error transformation
  
  // axios-mock-adapter only handles basic HTTP!
});

// Got Setup (Cleaner!)
describe('Got Auth Service', () => {
  // ✅ Got's unified hooks make mocking easier:
  - Single hook system to mock
  - Better error information
  - Built-in retry/cache behavior
  - Cleaner error types
});
```

## Testing Strategy Summary

### For Simple HTTP Calls
```typescript
// Use basic mocking (similar complexity for Axios and Got)
httpMock.onGet('/simple').reply(200, { data: 'value' });
```

### For Complex Scenarios  
```typescript
// Both Axios and Got need custom mocking for our advanced features
// Got actually makes it easier due to:
// 1. Unified hooks system
// 2. Better error information  
// 3. Less custom code to mock
```

### Integration Testing
```typescript
// Undici MockAgent (Got ecosystem) is more powerful than Axios alternatives
// - Better error simulation
// - More realistic network conditions
// - Modern testing patterns
```

## Migration Impact on Tests

### Current Test Complexity: **High** (already need custom mocking)
### Got Test Complexity: **Medium** (simpler due to unified architecture)
### Migration Effort: **2-3 days** (similar patterns, cleaner implementation)

**Conclusion: Testing complexity is similar, but Got provides cleaner, more maintainable test infrastructure.**