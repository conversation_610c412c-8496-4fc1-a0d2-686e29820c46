# HTTP Error Handling Improvement Plan

## Executive Summary

This document outlines a comprehensive plan to improve error handling in the HTTP client library and its integration with other libraries. The current implementation has inconsistencies and loses valuable error context, making debugging difficult and reducing observability effectiveness.

## Current State Analysis

### ❌ **Critical Issues Identified**

1. **Dual Error Transformation Paths**
   - `transformError()` method creates rich error objects
   - `errorHandler.buildFromException()` creates ErrorResponse objects
   - **Result**: Inconsistent error handling across the codebase

2. **Lost Error Context**
   ```typescript
   // ❌ CURRENT: Loses rich error context
   const transformedError = this.errorHandler.buildFromException(error, url);
   throw new Error(`${transformedError.error}: ${transformedError.message}`);
   ```
   - Rich error objects converted to generic `Error` with string message
   - Debugging information lost (status codes, response bodies, retry counts)
   - Type safety compromised

3. **Incomplete Correlation Integration**
   ```typescript
   // ❌ CURRENT: Commented out correlation ID integration
   // this.correlationId = RequestContext.get('correlationId');
   ```

4. **Inconsistent Event Publishing**
   - Error events use `error.code || 'unknown'` and `error.message`
   - Generic Error objects provide limited event data

### ✅ **What's Working Well**

1. **Comprehensive Error Classes**: Well-designed hierarchy with `HttpError`, `NetworkError`, `TimeoutError`, etc.
2. **Duck Typing Pattern**: Services use property access, not `instanceof` checks
3. **Event System**: Complete HTTP lifecycle event system already implemented
4. **Cross-Library Integration**: Good foundation for observability, messaging, and caching

## Risk Assessment: **LOW RISK**

### **Why Changes Are Safe**

1. **Backward Compatibility**: All services use duck typing (`error.message`, `error.code`)
2. **Property Preservation**: New error classes maintain all expected properties
3. **Graceful Degradation**: Services that don't understand new error types still work
4. **No Breaking Changes**: Error transformation already happens, we're improving it

### **Current Problems That INCREASE Complexity**

- Duplicate error transformation logic
- Lost debugging context
- Inconsistent error formats
- Poor troubleshooting experience

## Implementation Plan

### **Phase 1: Core Error Propagation Fix** ⚡ *Immediate Impact*

**Goal**: Fix error propagation to preserve rich error context

**Changes**:
```typescript
// ❌ BEFORE: Generic Error with lost context
throw new Error(`${transformedError.error}: ${transformedError.message}`);

// ✅ AFTER: Rich error object with full context
const richError = this.transformError(error, serviceName, method, url, requestId);
richError.correlationId = correlationId;
throw richError;
```

**Benefits**:
- Immediate improvement in debugging experience
- Better error context for observability
- Enhanced event data for monitoring
- No breaking changes to existing services

**Effort**: 2-4 hours

### **Phase 2: Got Error Hooks Implementation** 🔧 *Architectural Improvement*

**Goal**: Implement centralized error transformation at Got level

**Changes**:
```typescript
// Add to Got instance configuration
hooks: {
  beforeError: [
    (error: any) => {
      return this.transformGotErrorToHttpError(error);
    }
  ]
}
```

**Benefits**:
- Centralized error transformation
- Consistent error handling across all requests
- Reduced code duplication
- Better error classification

**Effort**: 4-6 hours

### **Phase 3: Cross-Library Integration Enhancements** 🚀 *Advanced Features*

**Goal**: Enhance error handling integration with other libraries

**Observability Improvements**:
```typescript
// Enhanced error metrics with rich context
this.metricsService?.incrementCounter('http_errors_total', {
  method: method.toLowerCase(),
  service: serviceName,
  error_type: error.code,
  status_code: error.statusCode?.toString() || 'unknown',
  is_retryable: error.isRetryable?.toString() || 'false'
});
```

**Event Publishing Enhancements**:
```typescript
// Richer error events with detailed context
const errorEvent = EventFactory.httpError({
  // ... existing fields
  errorType: error.constructor.name, // NetworkError, TimeoutError, etc.
  isRetryable: error.isRetryable,
  originalErrorCode: error.originalError?.code,
  circuitBreakerTriggered: error.code === 'CIRCUIT_BREAKER_OPEN'
});
```

**Effort**: 6-8 hours

## Cross-Library Integration Analysis

### **Observability Library** ✅ *Compatible*

**Current Integration**:
- Metrics: Uses `error.code`, `error.message`, `error.response?.statusCode`
- Tracing: Adds error events with `error.message`, `error.code`
- Logging: Comprehensive error context logging

**Improvements**:
- Enhanced metrics with error type classification
- Better trace event data with retry information
- Structured error logging with correlation context

**Impact**: **Positive** - Better observability data, no breaking changes

### **Messaging Library** ✅ *Enhanced*

**Current Integration**:
- HTTP error events use `error.code || 'unknown'` and `error.message`
- Event publishing is fire-and-forget (doesn't block requests)

**Improvements**:
```typescript
// Enhanced error events with rich context
errorType: error.constructor.name,        // NetworkError, TimeoutError
isRetryable: error.isRetryable,          // Boolean flag
networkCode: error.networkCode,          // ECONNREFUSED, ETIMEDOUT
timeoutType: error.timeoutType,          // connect, response, socket
```

**Impact**: **Positive** - Richer event data for analytics and monitoring

### **Caching Library** ✅ *Unaffected*

**Current Integration**:
- Cache operations are independent of HTTP errors
- Cache events published separately
- Error handling doesn't affect cache functionality

**Impact**: **Neutral** - No changes needed, continues working as expected

### **Resilience Library** ✅ *Enhanced*

**Current Integration**:
- Circuit breakers use duck typing: `error.code === 'CIRCUIT_BREAKER_OPEN'`
- Error classification for circuit breaker triggers

**Improvements**:
```typescript
// Better error classification for circuit breaker decisions
private shouldTriggerCircuitBreaker(error: any): boolean {
  if (error instanceof HttpResponseError) {
    return error.statusCode >= 500;
  }
  return error instanceof NetworkError || error instanceof TimeoutError;
}
```

**Impact**: **Positive** - More accurate circuit breaker triggering

## Potential Flaws and Mitigation

### **Flaw 1: Memory Usage**
**Issue**: Rich error objects might use more memory than simple Error objects
**Mitigation**: 
- Error objects are short-lived (request lifecycle)
- Memory impact is negligible compared to benefits
- Can add memory monitoring if needed

### **Flaw 2: Serialization Issues**
**Issue**: Complex error objects might not serialize well for logging/events
**Mitigation**:
- Error classes implement proper `toJSON()` methods
- Event factory extracts relevant properties
- Logging already handles complex objects

### **Flaw 3: Type Confusion**
**Issue**: Services might not understand new error types
**Mitigation**:
- All services use duck typing (property access)
- New error classes extend `Error` and preserve expected properties
- Gradual adoption possible

## Success Metrics

### **Immediate (Phase 1)**
- [ ] Error objects preserve `statusCode`, `responseBody`, `isRetryable` properties
- [ ] Debugging sessions show rich error context
- [ ] No service disruptions or breaking changes

### **Short-term (Phase 2)**
- [ ] Centralized error transformation reduces code duplication
- [ ] Consistent error handling across all HTTP requests
- [ ] Improved error classification accuracy

### **Long-term (Phase 3)**
- [ ] Enhanced observability metrics with error type breakdown
- [ ] Richer event data for analytics and monitoring
- [ ] Better circuit breaker decision making
- [ ] Improved correlation tracking across services

## Implementation Timeline

| Phase | Duration | Dependencies | Risk Level |
|-------|----------|--------------|------------|
| Phase 1: Core Fix | 2-4 hours | None | Low |
| Phase 2: Got Hooks | 4-6 hours | Phase 1 | Low |
| Phase 3: Enhancements | 6-8 hours | Phase 2 | Medium |

**Total Effort**: 12-18 hours over 1-2 weeks

## Conclusion

The proposed error handling improvements will:
- ✅ **Reduce complexity** by consolidating error transformation logic
- ✅ **Improve debugging** with rich error context
- ✅ **Enhance observability** with better metrics and events
- ✅ **Maintain compatibility** with existing services
- ✅ **Follow established patterns** in the codebase

**Recommendation**: Proceed with implementation starting with Phase 1 for immediate benefits.

## Technical Implementation Details

### **Phase 1: Core Error Propagation Fix**

#### **File Changes Required**

1. **libs/http/src/client/http-client.service.ts**
   ```typescript
   // Lines 459-461 and 916-917: Replace error throwing

   // ❌ CURRENT
   const transformedError = this.errorHandler.buildFromException(error, url);
   throw new Error(`${transformedError.error}: ${transformedError.message}`);

   // ✅ NEW
   const richError = this.transformError(error, serviceName, method, url, requestId);
   richError.correlationId = correlationId;
   richError.requestId = requestId;
   throw richError;
   ```

2. **libs/http/src/errors/http-errors.ts**
   ```typescript
   // Complete correlation ID integration
   constructor(message: string, public readonly originalError?: Error) {
     super(message);
     this.name = this.constructor.name;
     this.timestamp = new Date().toISOString();
     this.correlationId = this.getCorrelationId(); // Implement this
   }

   private getCorrelationId(): string | undefined {
     // Integration with correlation service
     try {
       return require('@libs/error-handling').CorrelationService.getCorrelationId();
     } catch {
       return undefined;
     }
   }
   ```

#### **Testing Strategy**
- Unit tests for error propagation
- Integration tests with existing services
- Verify no breaking changes in error handling patterns

### **Phase 2: Got Error Hooks Implementation**

#### **Hook Implementation**
```typescript
// Add to Got instance configuration
hooks: {
  beforeError: [
    (error: any) => {
      // Transform Got errors to our error classes
      return this.transformGotError(error);
    }
  ]
}

private transformGotError(gotError: any): HttpError {
  const context = gotError.options?.context || {};

  if (gotError.response) {
    return new HttpResponseError(
      `HTTP ${gotError.response.statusCode}: ${gotError.response.statusMessage}`,
      gotError.response.statusCode,
      gotError.response.body,
      gotError.response.headers,
      gotError
    );
  }

  if (gotError.code === 'ETIMEDOUT') {
    return new TimeoutError(
      'Request timeout',
      this.determineTimeoutType(gotError),
      gotError
    );
  }

  if (['ECONNREFUSED', 'ENOTFOUND', 'ENETUNREACH'].includes(gotError.code)) {
    return new NetworkError(
      `Network error: ${gotError.code}`,
      gotError.code,
      gotError
    );
  }

  return new HttpClientError(
    gotError.message || 'HTTP request failed',
    gotError,
    context.serviceName,
    gotError.options?.url?.toString(),
    gotError.options?.method
  );
}
```

### **Phase 3: Cross-Library Integration Enhancements**

#### **Enhanced Metrics**
```typescript
// Improved error metrics with rich context
private recordErrorMetrics(error: HttpError, serviceName: string, method: string, url: string): void {
  const labels = {
    method: method.toLowerCase(),
    service: serviceName,
    error_type: error.code,
    error_class: error.constructor.name,
    status_code: error.statusCode?.toString() || 'unknown',
    is_retryable: error.isRetryable?.toString() || 'false'
  };

  this.metricsService?.incrementCounter('http_errors_total', labels);

  if (error instanceof NetworkError) {
    this.metricsService?.incrementCounter('http_network_errors_total', {
      ...labels,
      network_code: error.networkCode
    });
  }

  if (error instanceof TimeoutError) {
    this.metricsService?.incrementCounter('http_timeout_errors_total', {
      ...labels,
      timeout_type: error.timeoutType
    });
  }
}
```

#### **Enhanced Event Publishing**
```typescript
// Richer error events
private publishErrorEvent(error: HttpError, context: RequestContext): void {
  if (!this.eventPublisher) return;

  const errorEvent = EventFactory.httpError({
    method: context.method.toUpperCase(),
    url: context.url,
    serviceName: context.serviceName,
    operationName: context.operation,
    correlationId: context.correlationId,
    statusCode: error.statusCode,
    errorType: error.constructor.name,
    errorMessage: error.message,
    errorCode: error.code,
    responseTime: context.duration,
    retryAttempt: context.retryCount || 0,
    isRetryable: error.isRetryable,
    originalErrorCode: error.originalError?.code,
    networkCode: error instanceof NetworkError ? error.networkCode : undefined,
    timeoutType: error instanceof TimeoutError ? error.timeoutType : undefined,
    circuitBreakerState: error.code === 'CIRCUIT_BREAKER_OPEN' ? 'OPEN' : undefined
  });

  this.eventPublisher.publish(errorEvent).catch(publishError => {
    this.observabilityLogger.warn({
      message: 'Failed to publish enhanced HTTP error event',
      error: publishError.message,
      correlationId: context.correlationId,
      requestId: context.requestId,
    });
  });
}
```

## Migration Strategy

### **Backward Compatibility Approach**
1. **Phase 1**: Preserve all existing error properties
2. **Phase 2**: Add new properties without removing old ones
3. **Phase 3**: Gradual enhancement of error context

### **Service-by-Service Validation**
- Test each service individually
- Verify error handling patterns remain functional
- Monitor for any unexpected behavior

### **Rollback Plan**
- Keep original error transformation as fallback
- Feature flags for new error handling
- Quick revert capability if issues arise

## Monitoring and Validation

### **Error Handling Metrics**
- Error type distribution
- Error context richness
- Service error handling patterns
- Performance impact measurement

### **Observability Validation**
- Verify enhanced metrics collection
- Validate improved trace data
- Confirm better log correlation

### **Integration Testing**
- Cross-library compatibility tests
- End-to-end error flow validation
- Performance regression testing
