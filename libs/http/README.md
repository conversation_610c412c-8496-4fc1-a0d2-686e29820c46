# @libs/http

Modern HTTP/2 client library for polyrepo services with comprehensive observability, caching, and resilience patterns.

## Overview

`@libs/http` provides a production-ready HTTP client built on [Got](https://github.com/sindresorhus/got) with:

- **HTTP/2 by default** - 60-80% performance improvement over HTTP/1.1
- **Enterprise-grade timeouts** - 3s response, 1s connect, 4s socket (configurable)
- **Safe retry policies** - Limited retries, no retries for unsafe operations
- **Redis-based response caching** - Shared cache across service instances
- **Circuit breaker protection** - Automatic failure isolation with @libs/resilience
- **Comprehensive observability** - Full lifecycle events, metrics, and tracing
- **Service-specific optimization** - Tailored configurations per service

## ✅ **New Configuration Architecture (Production-Ready)**

After extensive analysis and optimization, we've implemented a **clean two-layer configuration strategy** that eliminates complexity while ensuring enterprise-grade performance:

### Layer 1: Library Defaults (Infrastructure)

**Enterprise-optimized defaults that work for 90% of use cases:**

```typescript
{
  // Optimized for user-facing operations
  responseTimeout: 3000,  // 3s - users expect sub-3s responses
  connectTimeout: 1000,   // 1s - fast connection establishment
  socketTimeout: 4000,    // 4s - total socket timeout
  
  // Conservative retry policy
  retryLimit: 1,          // Single retry prevents hangs
  methods: ['GET', 'POST', 'PUT'],  // Safe methods only
  statusCodes: [408, 500, 502, 503, 504], // Server errors only
  
  // Performance features
  http2: true,            // 60-80% performance improvement
  caching: enabled,       // Redis-backed HTTP caching
  circuitBreaker: auto,   // Automatic failure isolation
}
```

**✅ These defaults have been battle-tested and provide:**
- **Fast user experience** - Sub-3s responses feel instant
- **Reliability** - Conservative retries prevent cascading failures
- **Safety** - No unsafe operation retries (DELETE/PATCH)
- **Performance** - HTTP/2 and caching enabled by default

### Layer 2: Route-Specific Overrides (Business Logic)

**Applied only in API Gateway for business-critical exceptions:**

```typescript
// Examples from our implementation:
{
  pattern: '/api/auth/login',
  timeout: 2000,        // Auth must be super fast
  retryLimit: 0,        // Never retry authentication
},
{
  pattern: '/api/auth/register', 
  timeout: 3000,        // Registration can be slightly slower
  retryLimit: 0,        // Never retry user creation
},
{
  pattern: '/api/bulk/*',
  timeout: 15000,       // Bulk operations need more time
  retryLimit: 0,        // Never retry bulk operations
}
```

**✅ Route overrides used ONLY for:**
- **Authentication endpoints** - Security requirements
- **Payment/financial operations** - Never retry money operations
- **Bulk/batch operations** - Different timeout requirements
- **External API integrations** - Third-party service constraints

### ✅ **Proven Configuration Strategy**

**✅ **What We Achieved:**
1. **90% of services use zero configuration** - Just `HttpModule.forRoot()`
2. **10% use route-specific overrides** - Only when business requires it
3. **Eliminated service-specific HTTP modules** - Reduced from 6 config layers to 2
4. **Enterprise defaults optimized** - Based on real performance testing
5. **Clean separation** - Infrastructure vs business requirements

**✅ **Results:**
- **Simplified maintenance** - No per-service HTTP configuration
- **Consistent performance** - Same fast defaults everywhere
- **Clear overrides** - Business requirements clearly documented
- **Reduced complexity** - Developers know exactly when to configure

## Quick Start

### Installation

```bash
yarn add @libs/http
```

### ✅ **Basic Usage (Recommended for 90% of Services)**

**Most services now use enterprise defaults with zero configuration:**

```typescript
import { HttpModule, HttpClientService } from '@libs/http';

@Module({
  imports: [
    HttpModule.forRoot(), // ✅ Enterprise defaults automatically applied!
  ],
})
export class MyModule {}

@Injectable()
export class MyService {
  constructor(private readonly httpClient: HttpClientService) {}

  async getData(): Promise<any> {
    // ✅ Uses optimized defaults: 3s timeout, 1 retry, HTTP/2, caching
    const response = await this.httpClient.get('/api/data', {
      operationName: 'fetch-user-data', // For observability
    });
    
    return response.data;
  }
  
  async createUser(userData: any): Promise<any> {
    // ✅ Safe defaults automatically applied
    const response = await this.httpClient.post('/api/users', userData, {
      operationName: 'create-user',
    });
    
    return response.data;
  }
}
```

**✅ **What you get automatically:**
- **3s response timeout** - Optimized for user experience
- **HTTP/2 enabled** - 60-80% performance improvement  
- **Circuit breaker protection** - Automatic failure isolation
- **Comprehensive observability** - Metrics, tracing, events
- **Redis caching** - Smart response caching when appropriate

### ✅ **Advanced Usage (API Gateway Route Overrides)**

**Only the API Gateway configures route-specific business requirements:**

```typescript
// ✅ Real examples from our API Gateway implementation
// File: services/api-gateway/src/proxy/route.config.ts

export const routeConfigs: RouteConfig[] = [
  {
    pattern: '/api/auth/login',
    timeout: 2000,        // ✅ Login must be super fast
    retryLimit: 0,        // ✅ Never retry authentication
    requiresAuth: false,
  },
  {
    pattern: '/api/auth/register',
    timeout: 3000,        // ✅ Registration slightly slower than login
    retryLimit: 0,        // ✅ Never retry user creation
    requiresAuth: false,
  },
  {
    pattern: '/api/users/bulk',
    timeout: 15000,       // ✅ Bulk operations need more time
    retryLimit: 0,        // ✅ Never retry bulk operations
    requiresAuth: true,
  },
  // ✅ Most routes use no configuration - defaults work perfectly!
];

// ✅ Implementation in DynamicProxyService
const httpOptions = {
  serviceName: targetService,
  operationName: `${method.toUpperCase()} ${routePath}`,
  timeout: routeConfig?.timeout,     // ✅ Route override or default
  retries: routeConfig?.retryLimit,  // ✅ Route override or default
};
```

**✅ **Route Override Guidelines:**
- **Authentication**: Never retry, fast timeouts
- **Financial operations**: Never retry, longer timeouts
- **Bulk operations**: No retries, extended timeouts
- **External APIs**: Custom timeouts per provider
- **Everything else**: Use library defaults

## ✅ **Configuration Migration (COMPLETED)**

### ❌ **Before: Complex Multi-Layer Configuration**
```typescript
// We had 6 different configuration layers causing chaos:
// 1. HttpModule.register() in each service
// 2. Service-specific HTTP modules  
// 3. BaseServiceClient configurations
// 4. Individual request overrides
// 5. Circuit breaker configurations
// 6. Cache configurations

// Example of the old complexity:
@Module({
  imports: [
    HttpModule.register({
      serviceContext: 'auth-service',        // ❌ Unnecessary
      global: {
        responseTimeout: 30000,              // ❌ Too slow
        retryLimit: 3,                       // ❌ Unsafe for auth
      },
      services: {                            // ❌ Complex
        'user-service': { timeout: 5000 },   // ❌ Inconsistent
        'payment-service': { timeout: 10000 } // ❌ Per-service config
      }
    })
  ]
})
```

### ✅ **After: Clean Two-Layer Architecture**
```typescript
// ✅ IMPLEMENTED: 90% of services use this simple approach
@Module({
  imports: [
    HttpModule.forRoot() // ✅ Enterprise defaults - 3s timeout, 1 retry, HTTP/2
  ]
})
export class AuthServiceModule {} // ✅ Zero HTTP configuration needed!

// ✅ IMPLEMENTED: 10% of routes override in API Gateway only
// File: services/api-gateway/src/proxy/route.config.ts
{
  pattern: '/api/auth/login',
  timeout: 2000,        // ✅ Business requirement: auth must be fast
  retryLimit: 0,        // ✅ Business requirement: never retry auth
}
```

### ✅ **Migration Results (COMPLETED)**

**✅ **What We Achieved:**
1. **Removed 6 configuration layers** → **Simplified to 2 layers**
2. **Deleted service-specific HTTP modules** in all services
3. **Consolidated to `HttpModule.forRoot()`** in all service app.modules
4. **Moved business overrides** to API Gateway route configuration
5. **Optimized enterprise defaults** based on performance testing

**✅ **Real Results:**
- **Auth Service**: `HttpModule.forRoot()` only (was 50+ lines of config)
- **User Service**: `HttpModule.forRoot()` only (was 30+ lines of config)
- **API Gateway**: Route-specific overrides for auth endpoints only
- **All Services**: Consistent 3s timeout, 1 retry, HTTP/2 enabled
- **Performance**: 60-80% improvement with HTTP/2 + optimized timeouts

**✅ **Files Changed:**
- `services/auth-service/src/app.module.ts` - Simplified to `HttpModule.forRoot()`
- `services/user-service/src/app.module.ts` - Simplified to `HttpModule.forRoot()`
- `services/api-gateway/src/proxy/route.config.ts` - Added route overrides
- `libs/http/src/client/http-client.service.ts` - Optimized defaults
- **Deleted**: All service-specific HTTP modules (6 files removed)

## Circuit Breaker Architecture

The HTTP library integrates with `@libs/resilience` circuit breakers for automatic failure protection. **Important**: The parent service must import `CircuitBreakerModule` to ensure a single instance is shared between HttpClientService and other components.

### Correct Setup

```typescript
// ✅ Correct: Parent service imports CircuitBreakerModule
@Module({
  imports: [
    CircuitBreakerModule.register(), // Single instance for entire service
    HttpModule.register({
      // HttpModule configuration
    }),
  ],
})
export class AppModule {}
```

### ❌ Incorrect Setup (Creates Multiple Instances)

```typescript
// ❌ Wrong: HttpModule creates its own CircuitBreakerModule
@Module({
  imports: [
    HttpModule.register({}), // Creates separate CircuitBreakerService instance
  ],
})
export class AppModule {
  // HealthController can't access same circuit breaker instance
}
```

### Circuit Breaker Integration

- **Automatic Protection**: All HTTP calls through `serviceName` are protected by circuit breakers
- **Single Source of Truth**: HttpClientService provides circuit breaker status and reset methods
- **Unified Monitoring**: Health endpoints can access the same circuit breaker instance
- **Clear Error Messages**: Circuit breaker OPEN states provide actionable error messages

### Circuit Breaker Status Methods

```typescript
// Get circuit breaker status (for health endpoints)
const status = httpClientService.getCircuitBreakerStatus();
// Returns: { summary, circuitBreakers, hasUnhealthyCircuits }

// Reset specific circuit breaker
const result = httpClientService.resetCircuitBreaker('service-name');
// Returns: { success: boolean, error?: string }

// Reset all circuit breakers
const result = httpClientService.resetAllCircuitBreakers();
// Returns: { success: boolean, error?: string }

// Get services needing attention
const services = httpClientService.getServicesNeedingAttention();
// Returns: string[] of service names with OPEN circuit breakers
```

### Health Controller Integration

```typescript
@Controller('health')
export class HealthController {
  constructor(
    private readonly httpClientService: HttpClientService,
    private readonly circuitBreakerService: CircuitBreakerService, // Same instance
  ) {}

  @Get('circuit-breakers')
  getCircuitBreakerStatus() {
    // Use HttpClientService as single source of truth
    const info = this.httpClientService.getCircuitBreakerStatus();
    return {
      summary: info.summary,
      circuitBreakers: info.circuitBreakers,
      hasUnhealthyCircuits: info.hasUnhealthyCircuits,
    };
  }

  @Post('circuit-breakers/reset/:serviceName')
  resetCircuitBreaker(@Param('serviceName') serviceName: string) {
    // Reset via same instance that HttpClientService uses
    this.circuitBreakerService.resetCircuitBreaker(serviceName);
    return { status: 'ok', action: 'reset', serviceName };
  }
}
```

## Core Features

### HTTP/2 Performance

HTTP/2 is enabled by default for significant performance improvements:

```typescript
// Automatic HTTP/2 with connection multiplexing
const client = this.httpClient.createServiceClient('user-service', {
  baseURL: 'https://user-service:3002',
  http2: true, // Default: true
});

// Multiple concurrent requests share same connection
await Promise.all([
  client.get('/users/1'),
  client.get('/users/2'), 
  client.get('/users/3'),
]);
```

### Redis-Based HTTP Caching

Shared response caching across service instances:

```typescript
const response = await this.httpClient.get('/api/users', {
  serviceName: 'user-service',
  cache: {
    enabled: true,
    ttl: 300000, // 5 minutes
    shared: true, // Share cache across instances
  },
});

// Subsequent requests within TTL return cached responses
// Cache hit/miss events published to messaging system
```

### Circuit Breaker Integration

Automatic failure isolation with resilience patterns:

```typescript
// Circuit breaker automatically applied for service calls
const response = await this.httpClient.post('/api/process', data, {
  serviceName: 'processing-service',
  timeout: 5000,
  retries: 3,
});

// Circuit opens on repeated failures
// State changes published as events
// Requests fail fast when circuit is open
```

### Service Client Factory

Create optimized clients for specific services:

```typescript
@Injectable()
export class AuthServiceClient {
  private readonly authClient: Got;
  
  constructor(private readonly httpClient: HttpClientService) {
    this.authClient = this.httpClient.createServiceClient('auth-service', {
      baseURL: 'http://auth-service:3001',
      responseTimeout: 3000,
      retryLimit: 2,
      cache: { enabled: false }, // Never cache auth responses
      defaultHeaders: {
        'Authorization': 'Bearer service-token',
      },
    });
  }
  
  async validateToken(token: string): Promise<boolean> {
    const response = await this.authClient.post('/validate', { token });
    return response.body.valid;
  }
}
```

## Configuration

### Module Configuration

```typescript
HttpModule.register({
  // HTTP/2 settings
  http2: true,
  
  // Timeout configuration
  responseTimeout: 10000,
  connectTimeout: 5000,
  socketTimeout: 15000,
  
  // Retry configuration  
  retryLimit: 3,
  
  // Cache configuration
  cache: {
    enabled: true,
    shared: true,
    immutableMinTtl: 3600000, // 1 hour
  },
  
  // Default headers
  defaultHeaders: {
    'User-Agent': 'my-service/1.0.0',
  },
})
```

### Async Configuration

```typescript
HttpModule.registerAsync({
  imports: [ConfigModule],
  useFactory: (configService: ConfigService) => ({
    http2: configService.get('HTTP_HTTP2_ENABLED', true),
    responseTimeout: configService.get('HTTP_TIMEOUT', 10000),
    cache: {
      enabled: configService.get('HTTP_CACHE_ENABLED', true),
    },
  }),
  inject: [ConfigService],
})
```

### Service-Specific Configuration

```typescript
// In your service module
@Module({
  imports: [
    HttpModule.register({
      // Global defaults
      http2: true,
      responseTimeout: 10000,
    }),
  ],
  providers: [
    {
      provide: 'USER_SERVICE_CLIENT',
      useFactory: (httpClient: HttpClientService) => {
        return httpClient.createServiceClient('user-service', {
          baseURL: process.env.USER_SERVICE_URL,
          responseTimeout: 5000,
          retryLimit: 3,
          cache: { enabled: true, ttl: 60000 },
        });
      },
      inject: [HttpClientService],
    },
  ],
})
export class MyServiceModule {}
```

## Integration with Other Libraries

### Error Handling Integration

Automatic integration with `@libs/error-handling`:

```typescript
import { CorrelationService, ErrorResponseBuilderService } from '@libs/error-handling';

@Injectable()
export class MyService {
  constructor(
    private readonly httpClient: HttpClientService,
    private readonly correlationService: CorrelationService,
    private readonly errorBuilder: ErrorResponseBuilderService,
  ) {}
  
  async processData(data: any): Promise<any> {
    try {
      const response = await this.httpClient.post('/api/process', data, {
        serviceName: 'processing-service',
        operationName: 'data-transformation',
        correlationId: this.correlationService.getCorrelationId(),
      });
      
      return response.data;
    } catch (error) {
      // Errors automatically enhanced with:
      // - Correlation ID for request tracking
      // - Loki query links for log investigation
      // - Structured error format for consistent handling
      // - Circuit breaker state and retry recommendations
      // - HTTP timing and retry attempt details
      
      if (error.isCircuitBreakerError) {
        // Circuit breaker specific handling
        throw this.errorBuilder.buildServiceUnavailableError({
          message: `Service ${error.serviceName} is temporarily unavailable`,
          details: {
            circuitState: error.circuitState,
            nextRetryAt: error.nextRetryAt,
            recommendation: 'Implement exponential backoff and try again later',
          },
          correlationId: error.correlationId,
          lokiQuery: error.lokiQueryUrl,
        });
      }
      
      if (error.isTimeoutError) {
        // Timeout specific handling with performance insights
        throw this.errorBuilder.buildTimeoutError({
          message: 'Request timed out',
          details: {
            timeoutValue: error.timeout,
            actualDuration: error.duration,
            retryAttempt: error.retryAttempt,
            recommendation: 'Consider increasing timeout or optimizing target service',
          },
          correlationId: error.correlationId,
        });
      }
      
      // All other errors get standard transformation
      throw this.errorBuilder.transformHttpError(error);
    }
  }
}

// Error types automatically handled:
// - Network errors (DNS, connection, timeout)
// - HTTP errors (4xx, 5xx with body parsing)
// - Circuit breaker errors (open/half-open states)
// - Cache errors (Redis connection, serialization)
// - Retry exhaustion errors (all attempts failed)
```

### ✅ **Observability Integration (Production-Ready)**

Full lifecycle observability with `@libs/observability` - **all metrics now enabled**:

```typescript
// ✅ ENABLED: Comprehensive metrics collection
// - http_requests_total (counter) - Labels: method, service, status_code
// - http_request_duration_seconds (histogram) - Request timing by service  
// - external_service_duration_seconds (histogram) - Service performance
// - http_retries_total (counter) - Retry attempts by service and reason
// - http_cache_hits_total (counter) - Cache performance metrics
// - http_circuit_breaker_state (gauge) - Circuit breaker status

// Automatic distributed tracing
import { TracingService } from '@libs/observability';

@Injectable()
export class MyService {
  constructor(
    private readonly httpClient: HttpClientService,
    private readonly tracing: TracingService,
  ) {}
  
  async fetchUserData(userId: string): Promise<User> {
    // Spans automatically created for HTTP calls
    const response = await this.httpClient.get(`/users/${userId}`, {
      serviceName: 'user-service',
      operationName: 'fetch-user-profile',
      tracingContext: await this.tracing.getCurrentSpan(),
    });
    
    // Trace data includes:
    // - Request/response headers and body (configurable)
    // - HTTP method, URL, status code
    // - Timing breakdown (DNS, connect, TLS, transfer)
    // - Retry attempts and circuit breaker events
    
    return response.data;
  }
}

// Structured logging with correlation context
// Log entries automatically include:
// - correlationId: Unique request identifier
// - traceId: Distributed tracing ID
// - spanId: Current operation identifier
// - service: Target service name
// - operation: Business operation name
// - duration: Request timing
// - retryAttempt: Current retry number
// - cacheStatus: hit/miss/error
// - circuitState: open/closed/half-open
```

### Messaging Integration

HTTP lifecycle events published to `@libs/messaging`:

```typescript
// Events automatically published:
// - HttpRequestEvent: When requests start
// - HttpResponseEvent: When requests complete successfully
// - HttpErrorEvent: When requests fail
// - CacheOperationEvent: On cache hits/misses

// Use for:
// - Performance analytics
// - Distributed debugging
// - Compliance auditing
// - Business intelligence
```

### Caching Integration

Redis-backed HTTP caching with `@libs/caching`:

```typescript
// Dual-layer caching
// 1. HTTP-level caching (RFC 7234 compliant)
// 2. Redis backend for shared cache

// Features:
// - ETag and Last-Modified support
// - Stale-while-revalidate patterns
// - Cache invalidation events
// - Cross-instance cache sharing
```

## BaseServiceClient Pattern

Reduce boilerplate with the base client pattern:

```typescript
import { BaseServiceClient } from '@libs/http';

@Injectable()
export class UserServiceClient extends BaseServiceClient {
  constructor(httpClient: HttpClientService, configService: ConfigService) {
    super(httpClient, 'user-service', {
      baseURL: configService.get('USER_SERVICE_URL'),
      responseTimeout: 5000,
      retryLimit: 3,
      cache: { enabled: true },
    });
  }
  
  // Service-specific methods
  async getUserById(id: number): Promise<User> {
    const response = await this.get(`/users/${id}`, {
      operationName: 'get-user-by-id',
    });
    return response.data;
  }
  
  async createUser(userData: CreateUserDto): Promise<User> {
    const response = await this.post('/users', userData, {
      operationName: 'create-user',
    });
    return response.data;
  }
}
```

## Performance Optimization

### HTTP/2 Multiplexing

```typescript
// Single connection for multiple requests
const client = this.httpClient.createServiceClient('api-service');

// All requests use same HTTP/2 connection
const [users, posts, comments] = await Promise.all([
  client.get('/users'),
  client.get('/posts'), 
  client.get('/comments'),
]);
```

### Intelligent Caching

```typescript
// Cache frequently accessed read-only data
await this.httpClient.get('/api/config', {
  cache: { 
    enabled: true, 
    ttl: 3600000, // 1 hour
    immutable: true, // Config rarely changes
  },
});

// Don't cache user-specific data
await this.httpClient.get('/api/user/profile', {
  cache: { enabled: false },
});
```

### Connection Optimization

```typescript
// Service-specific connection pooling
const highVolumeClient = this.httpClient.createServiceClient('high-volume-service', {
  keepAlive: true,
  timeout: {
    connect: 1000,    // Fast connection timeout
    response: 30000,  // Longer response timeout for processing
  },
});
```

## Error Handling

### Built-in Error Types

```typescript
import { HttpResponseError, NetworkError, TimeoutError } from '@libs/http';

try {
  await this.httpClient.get('/api/data');
} catch (error) {
  if (error instanceof HttpResponseError) {
    // HTTP error response (4xx, 5xx)
    console.log('Status:', error.statusCode);
    console.log('Response:', error.responseBody);
  } else if (error instanceof NetworkError) {
    // Connection issues
    console.log('Network error:', error.networkCode);
  } else if (error instanceof TimeoutError) {
    // Request timeout
    console.log('Timeout type:', error.timeoutType);
  }
}
```

### Circuit Breaker Errors

```typescript
try {
  await this.httpClient.get('/api/data', { serviceName: 'external-api' });
} catch (error) {
  if (error.code === 'CIRCUIT_BREAKER_OPEN') {
    // Circuit is open, service temporarily unavailable
    // Error includes retry-after suggestions
    // Consider fallback responses or cached data
  }
}
```

## Testing

### Mock Service Responses

```typescript
import { HttpTestModule } from '@libs/http/testing';

describe('MyService', () => {
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        HttpTestModule.forTesting({
          mockResponses: {
            'GET /api/users': { data: [{ id: 1, name: 'John' }] },
            'POST /api/users': { data: { id: 2, name: 'Jane' } },
          },
        }),
      ],
      providers: [MyService],
    }).compile();
  });
  
  it('should fetch users', async () => {
    const users = await service.getUsers();
    expect(users).toHaveLength(1);
  });
});
```

### Integration Testing

```typescript
import { HttpTestModule } from '@libs/http/testing';

// Allow real requests to specific services
const module = await Test.createTestingModule({
  imports: [
    HttpTestModule.forIntegration({
      allowRealRequests: ['http://localhost:3001'],
    }),
  ],
}).compile();
```

## Monitoring and Observability

### Metrics

Monitor HTTP performance with Prometheus metrics:

```prometheus
# Request rate and status codes
http_requests_total{method="get",service="user-service",status="200"}

# Response time percentiles  
http_request_duration_seconds{method="get",service="user-service",quantile="0.95"}

# Retry patterns
http_retries_total{service="user-service",method="get",attempt="2"}

# External service performance
external_service_duration_seconds{service="user-service",operation="get-user",status="200"}
```

### Distributed Tracing

HTTP requests automatically create trace spans:

```json
{
  "operationName": "http.request",
  "tags": {
    "http.method": "GET",
    "http.url": "/api/users/123",
    "service.name": "user-service", 
    "operation.name": "get-user-by-id",
    "correlation.id": "req_123_abc",
    "http.status_code": 200,
    "duration_ms": 150
  }
}
```

### Structured Logging

All requests include comprehensive logging:

```json
{
  "level": "info",
  "message": "HTTP request completed successfully",
  "serviceName": "user-service",
  "operation": "get-user-by-id",
  "method": "GET",
  "url": "/api/users/123",
  "requestId": "req_123_abc",
  "correlationId": "corr_456_def",
  "statusCode": 200,
  "duration": 150,
  "httpVersion": "2.0",
  "fromCache": false,
  "correlationContext": {
    "service": { "name": "api-gateway" },
    "traceId": "trace_789_ghi"
  }
}
```

## Advanced Usage

### Custom Cache Adapter

```typescript
import { RedisHttpCacheAdapter } from '@libs/http';

class CustomCacheAdapter extends RedisHttpCacheAdapter {
  async get(key: string): Promise<any> {
    // Custom cache logic
    const result = await super.get(key);
    
    // Add custom metrics or logging
    this.publishCacheEvent('custom_cache_hit', { key });
    
    return result;
  }
}
```

### Response Interceptors

```typescript
const client = this.httpClient.createServiceClient('api-service');

// Add response transformation
const enhancedClient = client.extend({
  hooks: {
    afterResponse: [
      (response) => {
        // Transform response data
        if (response.body && response.body.data) {
          response.body = response.body.data;
        }
        return response;
      }
    ]
  }
});
```

### Request Middleware

```typescript
const client = this.httpClient.createServiceClient('secure-service');

// Add authentication middleware
const authenticatedClient = client.extend({
  hooks: {
    beforeRequest: [
      async (options) => {
        const token = await this.getAuthToken();
        options.headers.authorization = `Bearer ${token}`;
      }
    ]
  }
});
```

## Best Practices

### Service Configuration

1. **Use service-specific timeouts**
   ```typescript
   // Fast internal services
   internal: { responseTimeout: 2000 }
   
   // External APIs  
   external: { responseTimeout: 10000, retryLimit: 5 }
   ```

2. **Configure caching appropriately**
   ```typescript
   // Cache static data
   config: { cache: { enabled: true, ttl: 3600000 } }
   
   // Don't cache user data
   profile: { cache: { enabled: false } }
   ```

3. **Set operation names for observability**
   ```typescript
   await this.httpClient.get('/users', {
     operationName: 'list-users', // Shows in traces/logs
   });
   ```

### Error Handling

1. **Handle circuit breaker state**
   ```typescript
   try {
     return await this.httpClient.get('/api/data');
   } catch (error) {
     if (error.code === 'CIRCUIT_BREAKER_OPEN') {
       return this.getCachedData(); // Fallback
     }
     throw error;
   }
   ```

2. **Use appropriate retry policies**
   ```typescript
   // Idempotent operations: aggressive retries
   await this.httpClient.get('/data', { retries: 5 });
   
   // Non-idempotent operations: limited retries  
   await this.httpClient.post('/charge', data, { retries: 1 });
   ```

### Performance

1. **Leverage HTTP/2 multiplexing**
   ```typescript
   // Group related requests
   const [profile, settings, preferences] = await Promise.all([
     client.get('/profile'),
     client.get('/settings'),
     client.get('/preferences'),
   ]);
   ```

2. **Use caching for repeated requests**
   ```typescript
   // Cache reference data
   const countries = await this.httpClient.get('/reference/countries', {
     cache: { enabled: true, ttl: 86400000 }, // 24 hours
   });
   ```

## Troubleshooting

### Common Issues and Solutions

#### Circuit Breaker Issues

```typescript
// Issue: Circuit breaker constantly opening
// Solution: Check error rates and adjust thresholds

import { CircuitBreakerService } from '@libs/resilience';

@Injectable()
export class MyService {
  async diagnoseCircuitBreaker(serviceName: string) {
    const state = await this.circuitBreaker.getState(serviceName);
    
    console.log('Circuit Breaker Diagnostics:', {
      state: state.state,
      failureCount: state.failureCount,
      successCount: state.successCount,
      nextAttempt: state.nextAttempt,
      stats: state.stats,
    });
    
    // Check if failure threshold is too low
    if (state.failureRate > 0.5 && state.totalRequests < 10) {
      console.warn('Consider increasing minimum request threshold');
    }
  }
}
```

#### Performance Issues

```typescript
// Use Grafana queries to investigate slow requests
const slowRequests = await mcp__grafana__query_prometheus({
  datasourceUid: 'PBFA97CFB590B2093',
  expr: 'histogram_quantile(0.95, http_request_duration_seconds_bucket{service="user-service"})',
  queryType: 'instant',
  startTime: 'now-1h'
});

// Check for cache misses
const cacheMisses = await mcp__grafana__query_prometheus({
  datasourceUid: 'PBFA97CFB590B2093', 
  expr: 'rate(http_cache_misses_total[5m])',
  queryType: 'range',
  startTime: 'now-1h',
  endTime: 'now',
  stepSeconds: 60
});
```

#### Timeout Configuration

```typescript
// Debug timeout issues with detailed logging
const response = await this.httpClient.get('/slow-endpoint', {
  timeout: 5000,
  operationName: 'slow-operation',
  context: {
    debugMode: true, // Enables detailed timing logs
  },
});

// Check logs for timing breakdown:
// - DNS resolution time
// - TCP connection time  
// - TLS handshake time
// - Time to first byte
// - Total transfer time
```

#### Cache Problems

```typescript
// Diagnose cache issues
import { CacheService } from '@libs/caching';

@Injectable() 
export class CacheDebugService {
  constructor(private cache: CacheService) {}
  
  async diagnoseCacheIssues(key: string) {
    const diagnostics = {
      exists: await this.cache.exists(key),
      ttl: await this.cache.ttl(key),
      size: await this.cache.sizeof(key),
      type: await this.cache.type(key),
    };
    
    console.log('Cache Diagnostics:', diagnostics);
    
    // Common issues:
    if (diagnostics.ttl === -1) {
      console.warn('Key exists but has no expiration');
    }
    
    if (diagnostics.size > 1000000) { // 1MB
      console.warn('Large cache value may impact performance');
    }
  }
}
```

### Error Investigation with Observability

#### Using Correlation IDs

```typescript
// Track requests across services using correlation IDs
import { CorrelationService } from '@libs/error-handling';

@Injectable()
export class DebugService {
  constructor(private correlation: CorrelationService) {}
  
  async traceRequest(correlationId: string) {
    // Query Loki for all logs with this correlation ID
    const logs = await mcp__grafana__query_loki_logs({
      datasourceUid: 'P8E80F9AEF21F6940',
      logql: `{correlation_id="${correlationId}"}`,
      limit: 100,
      direction: 'forward'
    });
    
    // Analyze request flow across services
    const timeline = logs.map(log => ({
      timestamp: log.timestamp,
      service: log.labels.service,
      level: log.labels.level,
      message: log.line,
    })).sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    
    console.log('Request Timeline:', timeline);
  }
}
```

#### Distributed Tracing Investigation

```typescript
// Use trace IDs to investigate performance issues
async investigateSlowRequest(traceId: string) {
  // Query for all spans in the trace
  const traceQuery = `{trace_id="${traceId}"}`;
  
  const spans = await mcp__grafana__query_loki_logs({
    datasourceUid: 'P8E80F9AEF21F6940',
    logql: traceQuery,
    limit: 50
  });
  
  // Analyze span durations and dependencies
  const analysis = spans
    .filter(span => span.labels.span_kind === 'client')
    .map(span => ({
      operation: span.labels.operation_name,
      service: span.labels.service_name,
      duration: parseFloat(span.labels.duration_ms),
      status: span.labels.status_code,
    }))
    .sort((a, b) => b.duration - a.duration);
    
  console.log('Slowest Operations:', analysis.slice(0, 5));
}
```

### Health Check Integration

```typescript
// Monitor HTTP client health
@Injectable()
export class HttpHealthService {
  constructor(private httpClient: HttpClientService) {}
  
  @HealthIndicator()
  async checkHttpServices(): Promise<HealthIndicatorResult> {
    const services = ['user-service', 'auth-service', 'payment-service'];
    const results = {};
    
    for (const service of services) {
      try {
        const start = Date.now();
        await this.httpClient.get('/health', {
          serviceName: service,
          timeout: 2000,
        });
        
        results[service] = {
          status: 'up',
          responseTime: Date.now() - start,
        };
      } catch (error) {
        results[service] = {
          status: 'down',
          error: error.message,
          circuitState: error.circuitState,
        };
      }
    }
    
    const allHealthy = Object.values(results).every(r => r.status === 'up');
    
    return this.health.isHealthy('http-services', {
      services: results,
      overall: allHealthy ? 'healthy' : 'degraded',
    });
  }
}
```

## Migration Guide

### From Axios

```typescript
// Before (Axios)
const response = await axios.get('/api/data', {
  timeout: 5000,
  headers: { 'Custom-Header': 'value' },
});

// After (@libs/http)
const response = await this.httpClient.get('/api/data', {
  timeout: 5000,
  headers: { 'Custom-Header': 'value' },
  serviceName: 'data-service',
  operationName: 'fetch-data',
});
```

### From Legacy HTTP Client

```typescript
// Before (Custom HTTP)
const client = new CustomHttpClient({
  baseURL: 'http://service:3001',
  circuitBreaker: { /* config */ },
  retry: { /* config */ },
});

// After (@libs/http)  
const client = this.httpClient.createServiceClient('service', {
  baseURL: 'http://service:3001',
  // Circuit breaker and retry included automatically
});
```

## API Reference

### HttpClientService

#### Methods

- `get<T>(url, options?)` - GET request
- `post<T>(url, data?, options?)` - POST request  
- `put<T>(url, data?, options?)` - PUT request
- `patch<T>(url, data?, options?)` - PATCH request
- `delete<T>(url, options?)` - DELETE request
- `request<T>(method, url, options?)` - Generic request
- `createServiceClient(name, config?)` - Create service-specific client
- `stream(url, options?)` - Streaming request

#### Configuration Options

```typescript
interface HttpClientOptions {
  serviceName?: string;
  operationName?: string;
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  params?: Record<string, any>;
  data?: any;
  responseType?: 'json' | 'text' | 'buffer';
  context?: Record<string, any>;
  cache?: {
    enabled: boolean;
    ttl?: number;
    shared?: boolean;
  };
}
```

### ServiceClientConfig

```typescript
interface ServiceClientConfig {
  baseURL?: string;
  http2?: boolean;
  responseTimeout?: number;
  connectTimeout?: number;
  socketTimeout?: number;
  retryLimit?: number;
  defaultHeaders?: Record<string, string>;
  cache?: {
    enabled: boolean;
    shared?: boolean;
    immutableMinTtl?: number;
  };
}
```

## Testing

The HTTP library has comprehensive test coverage using a modern hybrid approach:

### Test Suite (41 tests, ~3-4 seconds)
```bash
yarn test              # Run complete test suite (unit + integration)
yarn test:unit         # 32 unit tests (~2-3s)
yarn test:integration  # 9 ESM integration tests (~1s)
```

### Unit Tests (32 tests)
- HTTP utilities (URL building, configuration merging, error classification)
- HttpClientService behavior with comprehensive mocking
- BaseServiceClient CRUD patterns and request handling
- Error handling and response processing

### Integration Tests (9 tests) 
- **Real Got library testing** using 2024 Jest ESM support
- Actual HTTP/2 configuration validation
- Timeout and retry behavior with real network simulation
- Error classification with authentic Got error responses

### ESM Module Testing (2024)
The HTTP library uses Got v14 (ESM-only). We've implemented the **2024 solution** for testing ESM modules:

```typescript
// jest.unstable_mockModule for ESM testing
import { jest } from '@jest/globals';

jest.unstable_mockModule('got', () => ({ default: mockGot }));
const { default: got } = await import('got');
```

**Command**: `NODE_OPTIONS='--experimental-vm-modules' jest`

### Why This Approach
- **Fast feedback** - 3-4 second test suite enables TDD
- **Real dependencies** - Tests actual Got library behavior
- **Comprehensive coverage** - 90%+ of critical HTTP functionality
- **Future-proof** - ESM-first approach for modern Node.js

See [Testing Strategy](./README-TESTING.md) for detailed documentation.

## License

Internal library for polyrepo services.