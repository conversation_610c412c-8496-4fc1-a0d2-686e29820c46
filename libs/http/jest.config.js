module.exports = {
  testEnvironment: 'node',
  roots: ['<rootDir>/test/unit'],
  testMatch: ['**/*.unit.spec.ts'],
  transform: {
    '^.+\\.(t|j)sx?$': ['@swc/jest', {
      jsc: {
        parser: {
          syntax: 'typescript',
          decorators: true
        },
        target: 'es2022',
        transform: {
          legacyDecorator: true,
          decoratorMetadata: true
        }
      },
      module: {
        type: 'commonjs'
      }
    }]
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  collectCoverage: false,
  setupFilesAfterEnv: ['<rootDir>/test/unit/setup.ts'],
  moduleNameMapper: {
    '^@libs/(.*)$': '<rootDir>/../$1/src',
    // Handle ESM modules by mapping to mocks
    '^got$': '<rootDir>/test/unit/__mocks__/got.ts',
  },
  // Skip type checking for faster execution
  globals: {},
};