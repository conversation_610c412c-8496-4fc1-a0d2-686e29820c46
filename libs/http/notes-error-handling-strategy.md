# Error Handling Strategy: Clean Rewrite vs Adapter

## Decision: Clean Rewrite (Recommended)

### Why Not Use Error Adapter?

**Adapter Approach Problems:**
```typescript
// ❌ BAD: Error adapter adds complexity
class HttpErrorAdapter {
  static adaptGotError(gotError: any): AxiosLikeError {
    // 50+ lines of complex mapping logic
    // Maintains technical debt
    // Confusing for developers
    // Lost opportunity for improvement
  }
}
```

**Problems:**
- Maintains legacy error structures
- Adds translation overhead
- Confuses error handling patterns
- Prevents leveraging Got's better error info

### Clean Rewrite Approach

**New Error Hierarchy:**
```typescript
// ✅ GOOD: Clean, modern error types
export abstract class HttpError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  readonly timestamp: string;
  readonly correlationId?: string;

  constructor(message: string, public readonly originalError?: Error) {
    super(message);
    this.timestamp = new Date().toISOString();
    this.correlationId = RequestContext.get('correlationId');
  }
}

export class NetworkError extends HttpError {
  readonly code = 'NETWORK_ERROR';
  readonly statusCode = 0;
  readonly isRetryable = true;

  constructor(message: string, public readonly details: NetworkErrorDetails) {
    super(message);
  }
}

export class TimeoutError extends HttpError {
  readonly code = 'TIMEOUT_ERROR';
  readonly statusCode = 408;
  readonly isRetryable = true;

  constructor(
    message: string, 
    public readonly timeoutType: 'connect' | 'response' | 'socket'
  ) {
    super(message);
  }
}

export class HttpResponseError extends HttpError {
  readonly code = 'HTTP_RESPONSE_ERROR';
  readonly isRetryable: boolean;

  constructor(
    message: string,
    public readonly statusCode: number,
    public readonly responseBody: any,
    public readonly responseHeaders: Record<string, string>
  ) {
    super(message);
    this.isRetryable = statusCode >= 500 && statusCode < 600;
  }
}

export class RateLimitError extends HttpResponseError {
  readonly code = 'RATE_LIMIT_ERROR';
  readonly isRetryable = true;

  constructor(
    message: string,
    public readonly retryAfter?: number
  ) {
    super(message, 429, null, {});
  }
}
```

**Error Transformation in Got Hooks:**
```typescript
// Transform Got errors to our clean hierarchy
const errorTransformHook = (error: any) => {
  // Network/connection errors
  if (!error.response) {
    if (error.code === 'ETIMEDOUT') {
      return new TimeoutError('Request timed out', 'response');
    }
    if (error.code === 'ECONNREFUSED') {
      return new NetworkError('Connection refused', { code: error.code });
    }
    return new NetworkError(error.message, { code: error.code });
  }

  // HTTP response errors
  const { statusCode, body, headers } = error.response;
  
  if (statusCode === 429) {
    const retryAfter = headers['retry-after'] ? parseInt(headers['retry-after']) : undefined;
    return new RateLimitError('Rate limit exceeded', retryAfter);
  }

  return new HttpResponseError(
    `HTTP ${statusCode}: ${error.message}`,
    statusCode,
    body,
    headers
  );
};
```

**Update Existing Error Handlers:**
```typescript
// Update Keycloak error handler to use new errors
export class KeycloakErrorHandlerService {
  handleAuthenticationError(error: HttpError): never {
    if (error instanceof HttpResponseError) {
      switch (error.statusCode) {
        case 401:
          if (error.responseBody?.error === 'invalid_token') {
            throw this.errorBuilder.createHttpException('TOKEN_EXPIRED', 'Session expired');
          }
          throw this.errorBuilder.createHttpException('INVALID_CREDENTIALS', 'Invalid credentials');
        
        case 403:
          throw this.errorBuilder.createHttpException('INSUFFICIENT_PERMISSIONS', 'Access denied');
        
        default:
          throw this.errorBuilder.createHttpException('KEYCLOAK_ERROR', error.message);
      }
    }

    if (error instanceof NetworkError || error instanceof TimeoutError) {
      throw this.errorBuilder.createHttpException('KEYCLOAK_CONNECTION_ERROR', 'Service unavailable');
    }

    throw this.errorBuilder.createHttpException('UNKNOWN_ERROR', error.message);
  }
}
```

## Migration Strategy for Error Handling

### Phase 1: Implement New Error Types
- Create clean error hierarchy
- Implement Got error transformation hooks
- Add comprehensive error context

### Phase 2: Update Error Handlers Service by Service
```typescript
// Before (Axios)
catch (error) {
  if (error.response?.status === 401) {
    throw new UnauthorizedException('Invalid credentials');
  }
  throw new ServiceUnavailableException('Service error');
}

// After (Got with clean errors)
catch (error) {
  if (error instanceof HttpResponseError && error.statusCode === 401) {
    throw new UnauthorizedException('Invalid credentials');
  }
  if (error instanceof NetworkError || error instanceof TimeoutError) {
    throw new ServiceUnavailableException('Service error');
  }
  throw error; // Preserve detailed error context
}
```

### Phase 3: Enhance Error Context
```typescript
// Enhanced error context with Got
export class ContextualHttpError extends HttpError {
  constructor(
    message: string,
    public readonly context: {
      service: string;
      operation: string;
      correlationId: string;
      retryAttempt: number;
      totalDuration: number;
    },
    originalError?: Error
  ) {
    super(message, originalError);
  }
}
```

## Benefits of Clean Rewrite

✅ **Better Error Information**: More detailed context from Got
✅ **Type Safety**: Strong typing for error handling
✅ **Consistent Patterns**: Same error handling across all services
✅ **Enhanced Debugging**: Better error correlation and tracing
✅ **Future Flexibility**: Easy to extend for new error types

## Implementation Effort

- **New error classes**: ~2 hours
- **Got error hooks**: ~2 hours  
- **Update existing handlers**: ~4 hours per service
- **Testing**: ~4 hours

**Total: ~2 days vs 1 day for adapter, but much better long-term**