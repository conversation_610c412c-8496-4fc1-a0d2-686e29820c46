import { Method } from 'got';

/**
 * Modern HTTP client request options
 */
export interface HttpClientOptions {
  /** Target service name for observability and client selection */
  serviceName?: string;
  /** Operation name for tracing and logging */
  operationName?: string;
  /** Request timeout in milliseconds */
  timeout?: number;
  /** Additional custom headers */
  headers?: Record<string, string>;
  /** Number of retry attempts */
  retries?: number;
  /** Query parameters */
  params?: Record<string, string>;
  /** Request body data */
  data?: any;
  /** Additional context for hooks and observability */
  context?: Record<string, any>;
  /** Response type parsing */
  responseType?: 'json' | 'text' | 'buffer';
}

/**
 * Enhanced HTTP response with comprehensive metadata
 */
export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  request: {
    url: string;
    method: Method;
    headers: Record<string, string>;
  };
  metadata: {
    requestId: string;
    duration: number;
    httpVersion: string;
    fromCache: boolean;
  };
}

/**
 * HTTP caching configuration
 */
export interface HttpCacheConfig {
  /** Enable HTTP response caching */
  enabled?: boolean;
  /** Default TTL for cached responses (seconds) */
  defaultTtl?: number;
  /** Maximum cache size (number of entries) */
  maxSize?: number;
  /** Cache shared across service instances */
  shared?: boolean;
  /** Minimum TTL for immutable responses (milliseconds) */
  immutableMinTtl?: number;
}

/**
 * Service-specific client configuration
 */
export interface ServiceClientConfig {
  /** Base URL for the service */
  baseURL?: string;
  /** Enable HTTP/2 (default: true) */
  http2?: boolean;
  /** Response timeout in milliseconds */
  responseTimeout?: number;
  /** Connection timeout in milliseconds */
  connectTimeout?: number;
  /** Socket timeout in milliseconds */
  socketTimeout?: number;
  /** Maximum retry attempts */
  retryLimit?: number;
  /** Default headers for all requests */
  defaultHeaders?: Record<string, string>;
  /** HTTP caching configuration */
  cache?: HttpCacheConfig;
}

/**
 * Module configuration options
 */
export interface HttpModuleOptions {
  /** Service context for observability (e.g., 'api-gateway', 'auth-service') */
  serviceContext?: string;
  /** Global client configuration */
  global?: ServiceClientConfig;
  /** Service-specific configurations */
  services?: Record<string, ServiceClientConfig>;
  /** Observability settings */
  observability?: {
    enabled?: boolean;
    includeHeaders?: boolean;
    includeRequestBody?: boolean;
    includeResponseBody?: boolean;
  };
}

/**
 * Async module options
 */
export interface HttpModuleAsyncOptions {
  imports?: any[];
  useFactory?: (...args: any[]) => Promise<HttpModuleOptions> | HttpModuleOptions;
  inject?: any[];
}

/**
 * Method type alias
 */
export type HttpMethod = Method;