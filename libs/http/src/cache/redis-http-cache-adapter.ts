import { Injectable, Optional } from '@nestjs/common';
import { CacheService } from '@libs/caching';
import { EventPublisher, EventFactory } from '@libs/messaging';
import { ObservabilityLogger } from '@libs/observability';
import { StorageAdapter } from 'got';
import { createHash } from 'crypto';

/**
 * Got cache entry interface (from Got internals)
 */
interface CacheEntry {
  url: string;
  statusCode: number;
  statusMessage: string;
  headers: Record<string, string>;
  body: Buffer;
  cachePolicy: any;
}

/**
 * Redis-based HTTP cache adapter for Got
 * Provides dual-layer caching: HTTP response caching + Redis backend
 * Includes observability and messaging integration
 */
@Injectable()
export class RedisHttpCacheAdapter implements StorageAdapter {
  private readonly logger: ObservabilityLogger;

  constructor(
    private readonly cacheService: CacheService,
    private readonly loggerFactory: any,
    @Optional() private readonly eventPublisher?: EventPublisher,
  ) {
    this.logger = this.loggerFactory.createLogger('RedisHttpCacheAdapter');
  }

  /**
   * Generate cache key for HTTP request
   */
  private generateCacheKey(key: string): string {
    // Create deterministic cache key from URL and headers
    const hash = createHash('sha256').update(key).digest('hex').slice(0, 16);
    return `http:${hash}`;
  }

  /**
   * Get cached HTTP response from Redis
   */
  async get(key: string): Promise<CacheEntry | undefined> {
    const cacheKey = this.generateCacheKey(key);
    const startTime = Date.now();

    try {
      const result = await this.cacheService.get<CacheEntry>(cacheKey);
      const responseTime = Date.now() - startTime;

      if (result.hit && result.value) {
        this.logger.debug({
          message: 'HTTP cache hit',
          cacheKey,
          originalKey: key,
          responseTime,
        });

        // Publish cache operation event
        this.publishCacheEvent('get', cacheKey, true, responseTime);

        return result.value;
      } else {
        this.logger.debug({
          message: 'HTTP cache miss',
          cacheKey,
          originalKey: key,
          responseTime,
        });

        // Publish cache operation event
        this.publishCacheEvent('get', cacheKey, false, responseTime);

        return undefined;
      }
    } catch (error) {
      this.logger.error({
        message: 'HTTP cache get error',
        error: error instanceof Error ? error.message : String(error),
        cacheKey,
        originalKey: key,
      });

      return undefined;
    }
  }

  /**
   * Store HTTP response in Redis cache
   */
  async set(key: string, value: CacheEntry): Promise<void> {
    const cacheKey = this.generateCacheKey(key);
    const startTime = Date.now();

    try {
      // Calculate TTL from cache policy or use default
      let ttl = 300; // Default 5 minutes
      
      if (value.cachePolicy && value.cachePolicy.timeToLive) {
        ttl = Math.max(value.cachePolicy.timeToLive() / 1000, 60); // Min 1 minute
      }

      // Store in Redis with calculated TTL
      const result = await this.cacheService.set(cacheKey, value, ttl);
      const responseTime = Date.now() - startTime;

      if (result.success) {
        this.logger.debug({
          message: 'HTTP response cached',
          cacheKey,
          originalKey: key,
          ttl,
          responseTime,
          statusCode: value.statusCode,
          bodySize: value.body?.length || 0,
        });

        // Publish cache operation event
        this.publishCacheEvent('set', cacheKey, true, responseTime, ttl, value.body?.length);
      } else {
        this.logger.warn({
          message: 'Failed to cache HTTP response',
          cacheKey,
          originalKey: key,
          error: result.error,
        });
      }
    } catch (error) {
      this.logger.error({
        message: 'HTTP cache set error',
        error: error instanceof Error ? error.message : String(error),
        cacheKey,
        originalKey: key,
      });
    }
  }

  /**
   * Delete cached HTTP response from Redis
   */
  async delete(key: string): Promise<boolean> {
    const cacheKey = this.generateCacheKey(key);
    const startTime = Date.now();

    try {
      const result = await this.cacheService.delete(cacheKey);
      const responseTime = Date.now() - startTime;

      if (result.success) {
        this.logger.debug({
          message: 'HTTP cache entry deleted',
          cacheKey,
          originalKey: key,
          responseTime,
        });

        // Publish cache operation event
        this.publishCacheEvent('delete', cacheKey, true, responseTime);

        return true;
      } else {
        this.logger.warn({
          message: 'Failed to delete HTTP cache entry',
          cacheKey,
          originalKey: key,
          error: result.error,
        });

        return false;
      }
    } catch (error) {
      this.logger.error({
        message: 'HTTP cache delete error',
        error: error instanceof Error ? error.message : String(error),
        cacheKey,
        originalKey: key,
      });

      return false;
    }
  }

  /**
   * Clear all HTTP cache entries (not implemented for safety)
   * This would require pattern-based deletion which is expensive in Redis
   */
  async clear(): Promise<void> {
    this.logger.warn('HTTP cache clear operation not implemented for performance reasons');
  }

  /**
   * Publish cache operation events for analytics and monitoring
   */
  private publishCacheEvent(
    operation: 'get' | 'set' | 'delete',
    key: string,
    success: boolean,
    responseTime: number,
    ttl?: number,
    size?: number
  ): void {
    if (!this.eventPublisher) return;

    const cacheEvent = EventFactory.cacheOperation({
      operation,
      key,
      hit: operation === 'get' ? success : false,
      ttl,
      size,
      serviceName: 'http-cache',
      responseTime,
    });

    // Fire-and-forget event publishing
    this.eventPublisher.publish(cacheEvent).catch(error => {
      this.logger.warn({
        message: 'Failed to publish cache operation event',
        error: error.message,
        operation,
        key,
      });
    });
  }

  /**
   * Health check for cache adapter
   */
  async isHealthy(): Promise<boolean> {
    try {
      // Test basic cache operations
      const testKey = 'health:check';
      const testValue = { test: 'data', timestamp: Date.now() };
      
      const setResult = await this.cacheService.set(testKey, testValue, 60);
      if (!setResult.success) return false;

      const getResult = await this.cacheService.get(testKey);
      if (!getResult.hit) return false;

      await this.cacheService.delete(testKey);
      
      return true;
    } catch (error) {
      this.logger.error({
        message: 'HTTP cache adapter health check failed',
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }
}