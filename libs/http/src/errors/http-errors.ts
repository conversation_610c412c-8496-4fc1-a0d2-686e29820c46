/**
 * Enhanced HTTP client error that preserves original context
 * Maintains compatibility with existing HttpClientError from @libs/nestjs-common
 */
export class HttpClientError extends Error {
  public readonly serviceName?: string;
  public readonly url?: string;
  public readonly method?: string;

  constructor(
    message: string,
    public readonly originalError: any,
    serviceName?: string,
    url?: string,
    method?: string
  ) {
    super(message);
    this.name = 'HttpClientError';
    this.serviceName = serviceName;
    this.url = url;
    this.method = method;
  }

  /** Get the HTTP status code from the response */
  get status(): number {
    return this.originalError.response?.statusCode || this.originalError.response?.status || 0;
  }

  /** Get the response data */
  get responseData(): any {
    return this.originalError.response?.body || this.originalError.response?.data;
  }

  /** Check if this is a network/connection error */
  get isNetworkError(): boolean {
    return !this.originalError.response && !!this.originalError.code;
  }

  /** Check if this is a timeout error */
  get isTimeoutError(): boolean {
    return this.originalError.code === 'ETIMEDOUT' || 
           this.originalError.name === 'TimeoutError' ||
           this.originalError.message?.includes('timeout');
  }
}

/**
 * Enhanced error types for Got integration
 */
export abstract class HttpError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;
  readonly timestamp: string;
  readonly correlationId?: string;
  readonly isRetryable: boolean = false;

  constructor(message: string, public readonly originalError?: Error) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date().toISOString();
    // In a real implementation, get correlation ID from request context
    // this.correlationId = RequestContext.get('correlationId');
  }
}

export class NetworkError extends HttpError {
  readonly code = 'NETWORK_ERROR';
  readonly statusCode = 0;
  readonly isRetryable = true;

  constructor(
    message: string, 
    public readonly networkCode: string,
    originalError?: Error
  ) {
    super(message, originalError);
  }
}

export class TimeoutError extends HttpError {
  readonly code = 'TIMEOUT_ERROR';
  readonly statusCode = 408;
  readonly isRetryable = true;

  constructor(
    message: string,
    public readonly timeoutType: 'connect' | 'response' | 'socket',
    originalError?: Error
  ) {
    super(message, originalError);
  }
}

export class HttpResponseError extends HttpError {
  readonly code = 'HTTP_RESPONSE_ERROR';
  readonly isRetryable: boolean;

  constructor(
    message: string,
    public readonly statusCode: number,
    public readonly responseBody: any,
    public readonly responseHeaders: Record<string, string>,
    originalError?: Error
  ) {
    super(message, originalError);
    this.isRetryable = statusCode >= 500 && statusCode < 600;
  }
}

export class RateLimitError extends HttpError {
  readonly code = 'RATE_LIMIT_ERROR';
  readonly statusCode = 429;
  readonly isRetryable = true;

  constructor(
    message: string,
    public readonly retryAfter?: number,
    public readonly responseBody: any = null,
    public readonly responseHeaders: Record<string, string> = {},
    originalError?: Error
  ) {
    super(message, originalError);
  }
}