import { Injectable, Logger, Optional, Inject } from '@nestjs/common';
import got, { Got, Method, Options as GotOptions, Response, BeforeRetryHook } from 'got';
import { 
  OBSERVABILITY_LOGGER, 
  METRICS_SERVICE, 
  TRACING_SERVICE,
  ObservabilityLogger,
  MetricsService,
  TracingService 
} from '@libs/observability';
import { CircuitBreakerService } from '@libs/resilience';
import { 
  ErrorResponseBuilderService, 
  CorrelationService 
} from '@libs/error-handling';
import { EventPublisher, EventFactory } from '@libs/messaging';
import { CacheService } from '@libs/caching';
import { HttpClientOptions, HttpResponse, ServiceClientConfig } from '../types/http-client.types';
import { HttpClientError, NetworkError, TimeoutError, HttpResponseError } from '../errors/http-errors';
import { RedisHttpCacheAdapter } from '../cache/redis-http-cache-adapter';

/**
 * Modern Got-based HTTP client service with enhanced observability and performance
 * Features: HTTP/2, smart retries, circuit breaker integration, comprehensive logging
 */
@Injectable()
export class HttpClientService {
  private readonly logger: Logger;
  private readonly gotInstance: Got;
  private readonly serviceClients: Map<string, Got> = new Map();

  constructor(
    @Inject(OBSERVABILITY_LOGGER) private readonly observabilityLogger: ObservabilityLogger,
    @Inject(METRICS_SERVICE) @Optional() private readonly metricsService: MetricsService | undefined,
    @Inject(TRACING_SERVICE) @Optional() private readonly tracingService: TracingService | undefined,
    private readonly correlationService: CorrelationService,
    private readonly errorHandler: ErrorResponseBuilderService,
    @Optional() private readonly circuitBreakerService: CircuitBreakerService | undefined,
    @Optional() private readonly eventPublisher: EventPublisher | undefined,
    @Optional() private readonly cacheService: CacheService | undefined,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly globalConfig: ServiceClientConfig = {},
    private readonly serviceContext: string = 'http-client',
  ) {
    // Initialize logger with service context for better observability
    this.logger = new Logger(`${this.serviceContext}-http-module`);
    
    this.gotInstance = got.extend({
      // Enable HTTP/2 by default for better performance
      http2: this.globalConfig.http2 ?? true,
      
      // Enterprise timeout configuration - fast and user-friendly
      timeout: {
        response: this.globalConfig.responseTimeout ?? 3000, // 3s enterprise default
        connect: this.globalConfig.connectTimeout ?? 1000,   // 1s enterprise default
        socket: this.globalConfig.socketTimeout ?? 4000,     // 4s enterprise default
      },
      
      // Enterprise-grade retry configuration - safe and conservative
      retry: {
        limit: this.globalConfig.retryLimit ?? 1, // Reduced from 3 to 1 retry
        methods: ['GET', 'POST', 'PUT'], // Removed DELETE & PATCH (unsafe to retry)
        statusCodes: [408, 500, 502, 503, 504], // Removed 413, 429 (client errors), 520-524 (rare)
        errorCodes: [
          'ETIMEDOUT', 'ECONNRESET', 'EADDRINUSE', 'ECONNREFUSED',
          'EPIPE', 'ENOTFOUND', 'ENETUNREACH', 'EAI_AGAIN', 'ECONNABORTED'
        ],
        calculateDelay: ({ attemptCount }: { attemptCount: number }) => {
          const baseDelay = 1000;
          const exponential = baseDelay * Math.pow(2, attemptCount - 1);
          const jitter = Math.random() * 0.3 * exponential;
          return Math.min(exponential + jitter, 30000);
        },
      },
      
      // Default headers
      headers: {
        'user-agent': 'polyrepo-http-client/2.0.0',
        'accept': 'application/json, text/plain, */*',
        ...this.globalConfig.defaultHeaders,
      },
      
      // Enhanced hooks for observability
      hooks: {
        beforeRequest: [this.beforeRequestHook.bind(this)],
        afterResponse: [this.afterResponseHook.bind(this)],
        beforeError: [this.beforeErrorHook.bind(this)],
        beforeRetry: [this.beforeRetryHook.bind(this)],
      },
      
      // Response parsing
      responseType: 'json',
      resolveBodyOnly: false,
      
      // Configure Redis-based HTTP caching if available
      ...(this.globalConfig.cache?.enabled && this.cacheService ? {
        cache: new RedisHttpCacheAdapter(
          this.cacheService,
          this.loggerFactory,
          this.eventPublisher
        ),
        cacheOptions: {
          shared: this.globalConfig.cache.shared ?? true,
          immutableMinTtl: this.globalConfig.cache.immutableMinTtl ?? 3600000, // 1 hour
        }
      } : {}),
    });
  }

  /**
   * Create or get service-specific HTTP client with custom configuration
   */
  createServiceClient(serviceName: string, config: ServiceClientConfig = {}): Got {
    this.observabilityLogger.log({
      message: `HttpClientService: createServiceClient called for ${serviceName}`,
      serviceName,
      config: { baseURL: config.baseURL, responseTimeout: config.responseTimeout },
      currentServiceClientsCount: this.serviceClients.size,
      existingServiceNames: Array.from(this.serviceClients.keys())
    });

    if (this.serviceClients.has(serviceName)) {
      this.observabilityLogger.log({
        message: `HttpClientService: Service client already exists for ${serviceName}`,
        serviceName
      });
      return this.serviceClients.get(serviceName)!;
    }

    // DEBUG: Log timeout configuration details
    this.observabilityLogger.debug({
      message: 'CREATE_SERVICE_CLIENT: Timeout configuration analysis',
      serviceName,
      config_responseTimeout: config.responseTimeout,
      config_connectTimeout: config.connectTimeout,
      config_socketTimeout: config.socketTimeout,
      config_retryLimit: config.retryLimit,
      responseTimeoutType: typeof config.responseTimeout,
      responseTimeoutIsUndefined: config.responseTimeout === undefined,
      responseTimeoutIsNull: config.responseTimeout === null,
    });

    const timeoutConfig = {
      response: config.responseTimeout || 3000, // Enterprise default: 3s
      connect: config.connectTimeout || 1000,   // Enterprise default: 1s  
      socket: config.socketTimeout || 4000,     // Enterprise default: 4s
    };

    this.observabilityLogger.debug({
      message: 'CREATE_SERVICE_CLIENT: Final timeout object',
      serviceName,
      timeoutConfig,
      timeoutConfigKeys: Object.keys(timeoutConfig),
      hasResponseTimeout: 'response' in timeoutConfig && timeoutConfig.response !== undefined,
    });

    const serviceClient = this.gotInstance.extend({
      prefixUrl: config.baseURL,
      timeout: timeoutConfig,
      retry: {
        limit: config.retryLimit || 1, // Enterprise default: 1 retry
        // Safe retry logic: only retry server errors and timeouts
        statusCodes: [408, 500, 502, 503, 504],
        errorCodes: [
          'ETIMEDOUT', 'ECONNRESET', 'EADDRINUSE', 'ECONNREFUSED',
          'EPIPE', 'ENOTFOUND', 'ENETUNREACH', 'EAI_AGAIN', 'ECONNABORTED'
        ],
      },
      headers: {
        ...config.defaultHeaders,
      },
      context: {
        serviceName,
      },
      
      // Configure service-specific caching if enabled
      ...(config.cache?.enabled && this.cacheService ? {
        cache: new RedisHttpCacheAdapter(
          this.cacheService,
          this.loggerFactory,
          this.eventPublisher
        ),
        cacheOptions: {
          shared: config.cache.shared ?? true,
          immutableMinTtl: config.cache.immutableMinTtl ?? 3600000, // 1 hour
        }
      } : {}),
    });

    this.serviceClients.set(serviceName, serviceClient);
    
    this.observabilityLogger.log({
      message: `HttpClientService: Successfully created and stored service client for ${serviceName}`,
      serviceName,
      totalServiceClientsAfter: this.serviceClients.size,
      allServiceNames: Array.from(this.serviceClients.keys())
    });
    
    this.logger.debug(`Created service client for ${serviceName}`);
    
    return serviceClient;
  }

  /**
   * Enhanced request method with full observability integration
   */
  async request<T = any>(
    method: Method,
    url: string,
    options: HttpClientOptions = {}
  ): Promise<HttpResponse<T>> {
    // Bundle optimization test - this change should appear in 3-5 seconds
    console.log(`[HttpClientService] BUNDLE_OPTIMIZED request() called with method=${method}, url=${url}, serviceName=${options.serviceName}`);
    
    const serviceName = options.serviceName || 'default';
    const operation = options.operationName || `${method.toUpperCase()} ${url}`;
    const requestId = this.generateRequestId();
    
    // Get correlation context
    const correlationContext = this.correlationService.getContext();
    const correlationId = correlationContext?.correlationId || this.correlationService.generateCorrelationId();

    // Get current distributed tracing span
    const span = this.tracingService?.getCurrentSpan();
    
    // Metrics will be recorded at the end - just track start time
    const metricsStartTime = Date.now();

    const startTime = Date.now();

    // DEBUG: Comprehensive request initiation logging
    this.observabilityLogger.debug({
      message: 'HttpClientService.request() initiated',
      method,
      url,
      serviceName,
      operation,
      requestId,
      correlationId,
      hasOptions: !!options,
      optionsKeys: Object.keys(options),
      hasCircuitBreaker: !!this.circuitBreakerService,
      isDefaultService: serviceName === 'default',
      serviceClientsCount: this.serviceClients.size,
      serviceClientsKeys: Array.from(this.serviceClients.keys()),
    });

    try {
      // Use circuit breaker for resilience
      if (this.circuitBreakerService && serviceName !== 'default') {
        this.observabilityLogger.debug({
          message: 'Using circuit breaker for request',
          serviceName,
          requestId,
          correlationId,
        });

        const circuit = this.circuitBreakerService.getCircuitBreaker(serviceName, {
          timeout: options.timeout || 10000,
          errorThresholdPercentage: 30,
          resetTimeout: 20000,
        });

        // Log circuit breaker state before execution
        this.observabilityLogger.debug({
          message: 'Circuit breaker state before execution',
          serviceName,
          requestId,
          correlationId,
          circuitState: (circuit as any).state,
          circuitStats: (circuit as any).stats,
          circuitOptions: (circuit as any).options,
          circuitToJSON: (circuit as any).toJSON ? (circuit as any).toJSON() : 'toJSON not available',
        });

        this.observabilityLogger.debug({
          message: 'About to execute circuit breaker',
          serviceName,
          requestId,
          correlationId,
        });

        let response;
        try {
          this.observabilityLogger.debug({
            message: 'About to call circuit.execute()',
            serviceName,
            requestId,
            correlationId,
          });
          
          response = await circuit.execute(async () => {
            try {
              this.observabilityLogger.debug({
                message: 'Inside circuit breaker callback - calling executeRequest',
                serviceName,
                requestId,
                correlationId,
              });
              
              const executeResult = await this.executeRequest<T>(method, url, options, serviceName, operation, requestId, correlationId, span);
              
              this.observabilityLogger.debug({
                message: 'executeRequest completed inside circuit breaker',
                serviceName,
                requestId,
                correlationId,
                hasResult: !!executeResult,
                resultType: typeof executeResult,
                resultKeys: executeResult ? Object.keys(executeResult) : [],
              });
              
              this.observabilityLogger.debug({
                message: 'ABOUT TO RETURN from circuit breaker callback',
                serviceName,
                requestId,
                correlationId,
                returnValue: executeResult,
                returnType: typeof executeResult,
              });
              
              return executeResult;
            } catch (callbackError: any) {
              this.observabilityLogger.error({
                message: 'ERROR INSIDE CIRCUIT BREAKER CALLBACK',
                serviceName,
                requestId,
                correlationId,
                error: callbackError.message,
                stack: callbackError.stack,
              });
              throw callbackError;
            }
          });
          
          this.observabilityLogger.debug({
            message: 'circuit.execute() completed successfully',
            serviceName,
            requestId,
            correlationId,
            hasResponse: !!response,
            responseType: typeof response,
          });
          
          // CRITICAL: Check for silent circuit breaker failures
          if (response === undefined || response === null) {
            const circuitState = (circuit as any).getState ? (circuit as any).getState() : 'unknown';
            const metrics = (circuit as any).getMetrics ? (circuit as any).getMetrics() : {};
            const errorMessage = `Circuit breaker for ${serviceName} is ${circuitState}`;
            
            this.observabilityLogger.error({
              message: 'CIRCUIT BREAKER BLOCKED REQUEST: Circuit is OPEN',
              serviceName,
              requestId,
              correlationId,
              circuitState,
              circuitMetrics: metrics,
              responseValue: response,
              responseType: typeof response,
              error: errorMessage,
              resetInstructions: `Reset via: curl -X POST http://localhost:3000/health/circuit-breakers/reset/${serviceName}`,
            });
            
            // Provide clear, actionable error message
            throw new Error(`Service ${serviceName} is temporarily unavailable (circuit breaker OPEN). This protects against cascading failures. Reset via: curl -X POST http://localhost:3000/health/circuit-breakers/reset/${serviceName} or check service health first.`);
          }
        } catch (circuitError: any) {
          this.observabilityLogger.error({
            message: 'CIRCUIT BREAKER ERROR: circuit.execute() failed',
            serviceName,
            requestId,
            correlationId,
            error: circuitError.message,
            errorType: typeof circuitError,
            stack: circuitError.stack,
          });
          throw circuitError;
        }

        this.observabilityLogger.debug({
          message: 'Circuit breaker execution completed',
          serviceName,
          requestId,
          correlationId,
          hasResponse: !!response,
          responseType: typeof response,
          responseKeys: response ? Object.keys(response) : [],
        });

        return response;
      }

      // Execute without circuit breaker
      this.observabilityLogger.debug({
        message: 'Executing request without circuit breaker',
        serviceName,
        requestId,
        correlationId,
      });

      const directResponse = await this.executeRequest<T>(method, url, options, serviceName, operation, requestId, correlationId, span);
      
      this.observabilityLogger.debug({
        message: 'Direct execution completed',
        serviceName,
        requestId,
        correlationId,
        hasResponse: !!directResponse,
        responseType: typeof directResponse,
        responseKeys: directResponse ? Object.keys(directResponse) : [],
      });

      return directResponse;

    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      // Record error metrics
      this.metricsService?.incrementCounter('http_requests_total', {
        method: method.toLowerCase(),
        route: url,
        status_code: 'error',
        service: serviceName,
      });

      // Add error details to current span
      if (span) {
        this.tracingService?.addEvent('http.request.error', {
          'error.message': error.message,
          'error.code': error.code || 'unknown',
          'http.status_code': error.response?.statusCode || 0,
        });
      }

      // Comprehensive error logging with correlation
      this.observabilityLogger.error({
        message: 'HTTP request failed with full context',
        error: error.message,
        serviceName,
        operation,
        method,
        url,
        requestId,
        correlationId,
        duration,
        errorCode: error.code,
        statusCode: error.response?.statusCode,
        httpVersion: error.response?.httpVersion,
        retryCount: error.retryCount || 0,
        circuitBreakerOpen: error.code === 'CIRCUIT_BREAKER_OPEN',
        correlationContext: correlationContext,
      });

      // Use comprehensive error handler
      error.serviceName = serviceName;
      error.requestId = requestId;
      error.correlationId = correlationId;
      
      const transformedError = this.errorHandler.buildFromException(error, url);
      console.log(`[HttpClientService] throwing error in request() catch block:`, transformedError);
      throw new Error(`${transformedError.error}: ${transformedError.message}`);

    } finally {
      // Record overall request duration
      const totalDuration = Date.now() - metricsStartTime;
      this.metricsService?.observeHistogram('http_request_duration_seconds', 
        totalDuration / 1000, // Convert to seconds
        { 
          method: method.toLowerCase(), 
          route: url,
          status_code: 'completed', // Mark as completed in finally block
          service: serviceName 
        }
      );
    }
  }

  /**
   * Internal request execution with full observability
   */
  private async executeRequest<T = any>(
    method: Method,
    url: string,
    options: HttpClientOptions,
    serviceName: string,
    operation: string,
    requestId: string,
    correlationId: string,
    span?: any
  ): Promise<HttpResponse<T>> {
    const startTime = Date.now();
    let client: Got;

    // DEBUG: Log executeRequest initiation
    this.observabilityLogger.debug({
      message: 'executeRequest() initiated',
      method,
      url,
      serviceName,
      operation,
      requestId,
      correlationId,
      optionsServiceName: options.serviceName,
      hasServiceName: !!options.serviceName,
      serviceClientExists: options.serviceName ? this.serviceClients.has(options.serviceName) : false,
    });

    try {
      // Get appropriate client
      if (options.serviceName && this.serviceClients.has(options.serviceName)) {
        client = this.serviceClients.get(options.serviceName)!;
        this.observabilityLogger.debug({
          message: 'Using service-specific client',
          serviceName: options.serviceName,
          requestId,
          correlationId,
          clientType: 'service-specific',
        });
      } else {
        client = this.gotInstance;
        this.observabilityLogger.debug({
          message: 'Using default Got instance',
          serviceName,
          requestId,
          correlationId,
          clientType: 'default',
          reason: !options.serviceName ? 'no service name' : 'service client not found',
        });
      }

      // Build Got options with full correlation context
      let gotOptions: GotOptions = {
        method,
        url,
        context: {
          serviceName,
          operation,
          requestId,
          correlationId,
          span,
          ...options.context,
        },
        headers: {
          'x-request-id': requestId,
          'x-correlation-id': correlationId,
          ...this.buildCorrelationHeaders(),
          ...options.headers,
        },
        searchParams: options.params,
        timeout: {
          response: options.timeout || 3000, // Enterprise-grade: 3s max for user-facing operations
          connect: 1000,  // 1s connect timeout
          socket: 4000    // 4s total socket timeout
        },
        retry: options.retries !== undefined ? { limit: options.retries } : undefined,
        responseType: options.responseType || 'json',
        ...this.buildRequestBody(method, options.data),
      };

      // Add correlation context from error handler
      gotOptions = this.errorHandler.addCorrelationToRequestOptions(gotOptions);

      // Record request metrics with error handling to catch metrics issues
      try {
        this.metricsService?.incrementCounter('http_requests_total', {
          method: method.toLowerCase(),
          route: url,
          status_code: 'started',
          service: serviceName,
        });
      } catch (metricsError: any) {
        this.observabilityLogger.error({
          message: 'METRICS ERROR: Failed to record http_requests_total metric',
          error: metricsError.message,
          stack: metricsError.stack,
          serviceName,
          requestId,
        });
      }

      // Add span attributes for tracing
      if (span) {
        this.tracingService?.addEvent('http.request.start', {
          'http.method': method,
          'http.url': url,
          'service.name': serviceName,
          'operation.name': operation,
          'correlation.id': correlationId,
        });
      }

      // Publish HTTP request lifecycle event
      if (this.eventPublisher) {
        const requestEvent = EventFactory.httpRequest({
          method: method.toUpperCase(),
          url,
          serviceName,
          operationName: operation,
          correlationId,
          hasBody: !!options.data,
          contentType: gotOptions.headers?.['content-type'] as string,
          headers: options.headers,
          queryParams: options.params,
        });
        
        // Fire-and-forget event publishing (don't block HTTP request)
        this.eventPublisher.publish(requestEvent).catch(error => {
          this.observabilityLogger.warn({
            message: 'Failed to publish HTTP request event',
            error: error.message,
            correlationId,
            requestId,
          });
        });
      }

      // DEBUG: Log just before Got execution
      this.observabilityLogger.debug({
        message: 'About to execute Got request',
        method,
        url,
        serviceName,
        requestId,
        correlationId,
        gotOptionsKeys: Object.keys(gotOptions),
        hasUrl: !!gotOptions.url,
        hasMethod: !!gotOptions.method,
        clientType: typeof client,
        clientName: client.constructor.name,
      });

      // Execute Got request with detailed observability logging
      this.observabilityLogger.debug({
        message: 'About to call Got client',
        serviceName,
        requestId,
        correlationId,
        gotOptionsUrl: gotOptions.url,
        gotOptionsMethod: gotOptions.method,
        hasHeaders: !!gotOptions.headers,
        clientType: typeof client,
        clientName: client.constructor?.name,
      });

      // COMPREHENSIVE GOT OPTIONS DEBUGGING
      this.observabilityLogger.debug({
        message: 'DETAILED Got options analysis',
        serviceName,
        requestId,
        correlationId,
        gotOptionsKeys: Object.keys(gotOptions),
        gotOptionsMethod: gotOptions.method,
        gotOptionsUrl: gotOptions.url,
        gotOptionsResponseType: gotOptions.responseType,
        gotOptionsTimeout: gotOptions.timeout,
        gotOptionsTimeoutType: typeof gotOptions.timeout,
        gotOptionsTimeoutResponse: gotOptions.timeout ? gotOptions.timeout.response : 'N/A',
        gotOptionsTimeoutConnect: gotOptions.timeout ? gotOptions.timeout.connect : 'N/A',
        gotOptionsTimeoutSocket: gotOptions.timeout ? gotOptions.timeout.socket : 'N/A',
        gotOptionsRetry: gotOptions.retry,
        gotOptionsRetryLimit: gotOptions.retry ? gotOptions.retry.limit : 'N/A',
        gotOptionsHasJson: 'json' in gotOptions,
        gotOptionsHasBody: 'body' in gotOptions,
        gotOptionsJsonType: typeof gotOptions.json,
        gotOptionsJsonSize: gotOptions.json ? JSON.stringify(gotOptions.json).length : 0,
        gotOptionsJsonPreview: gotOptions.json ? JSON.stringify(gotOptions.json).substring(0, 100) : 'N/A',
        gotOptionsBodyType: typeof gotOptions.body,
        gotOptionsBodySize: gotOptions.body ? gotOptions.body.length : 0,
        gotOptionsHeadersCount: gotOptions.headers ? Object.keys(gotOptions.headers).length : 0,
        gotOptionsHeadersContentType: gotOptions.headers ? gotOptions.headers['content-type'] : 'N/A',
        gotOptionsSearchParams: gotOptions.searchParams,
        clientIsHttp2: (client as any).defaults?.http2,
        clientDefaults: (client as any).defaults ? Object.keys((client as any).defaults) : [],
        clientTimeouts: (client as any).defaults?.timeout,
      });

      // Log the exact Got options that will be passed
      console.log('[HTTP CLIENT] About to call Got with options:', {
        method: gotOptions.method,
        url: gotOptions.url,
        hasJson: 'json' in gotOptions,
        hasBody: 'body' in gotOptions,
        jsonData: gotOptions.json,
        bodyData: gotOptions.body,
        timeout: gotOptions.timeout,
        responseType: gotOptions.responseType,
        http2: (client as any).defaults?.http2,
      });
      
      let response;
      try {
        console.log('[HTTP CLIENT] Calling client(gotOptions) now...');
        response = await client(gotOptions);
        console.log('[HTTP CLIENT] client(gotOptions) returned successfully!');
        
        this.observabilityLogger.debug({
          message: 'Got client execution SUCCESS',
          serviceName,
          requestId,
          correlationId,
          responseType: typeof response,
          hasResponse: !!response,
          responseKeys: response ? Object.keys(response) : [],
          isUndefined: response === undefined,
          isNull: response === null,
        });
      } catch (gotError: any) {
        this.observabilityLogger.error({
          message: 'Got client execution ERROR',
          serviceName,
          requestId,
          correlationId,
          errorType: typeof gotError,
          errorMessage: gotError?.message,
          errorCode: gotError?.code,
          errorStack: gotError?.stack?.slice(0, 500),
        });
        throw gotError; // Re-throw to be handled by outer catch
      }
      
      const duration = Date.now() - startTime;

      // DEBUG: Log immediately after Got execution
      this.observabilityLogger.debug({
        message: 'Got request execution completed',
        method,
        url,
        serviceName,
        requestId,
        correlationId,
        duration,
        hasResponse: !!response,
        responseType: typeof response,
        responseStatusCode: (response as any)?.statusCode,
        responseBodyType: typeof (response as any)?.body,
        responseKeys: response ? Object.keys(response) : [],
      });

      // Record success metrics
      this.metricsService?.incrementCounter('http_requests_total', {
        method: method.toLowerCase(),
        route: url,
        status_code: (response as any).statusCode.toString(),
        service: serviceName,
      });

      this.metricsService?.observeHistogram('external_service_duration_seconds', 
        duration / 1000, // Convert to seconds
        { 
          service: serviceName, 
          operation: operation, 
          method: method.toLowerCase(),
          status: (response as any).statusCode.toString() 
        }
      );

      // Add response data to span
      if (span) {
        this.tracingService?.addEvent('http.request.success', {
          'http.status_code': (response as any).statusCode,
          'http.response_size': (response as any).body ? JSON.stringify((response as any).body).length : 0,
          'http.version': (response as any).httpVersion,
          'duration_ms': duration,
        });
      }

      // Comprehensive success logging with correlation
      this.observabilityLogger.log({
        message: 'HTTP request completed successfully',
        serviceName,
        operation,
        method,
        url,
        requestId,
        correlationId,
        statusCode: (response as any).statusCode,
        duration,
        responseSize: (response as any).body ? JSON.stringify((response as any).body).length : 0,
        httpVersion: (response as any).httpVersion,
        fromCache: (response as any).isFromCache || false,
        correlationContext: this.correlationService.getContext(),
      });

      // Publish HTTP response lifecycle event
      if (this.eventPublisher) {
        const responseEvent = EventFactory.httpResponse({
          method: method.toUpperCase(),
          url,
          serviceName,
          operationName: operation,
          correlationId,
          statusCode: (response as any).statusCode,
          responseTime: duration,
          responseSize: (response as any).body ? JSON.stringify((response as any).body).length : 0,
          cacheHit: (response as any).isFromCache || false,
        });
        
        // Fire-and-forget event publishing (don't block HTTP request)
        this.eventPublisher.publish(responseEvent).catch(error => {
          this.observabilityLogger.warn({
            message: 'Failed to publish HTTP response event',
            error: error.message,
            correlationId,
            requestId,
          });
        });
      }

      const httpResponse = {
        data: (response as any).body as T,
        status: (response as any).statusCode,
        statusText: (response as any).statusMessage || 'OK',
        headers: (response as any).headers as Record<string, string>,
        request: {
          url: (response as any).url,
          method,
          headers: gotOptions.headers as Record<string, string>,
        },
        metadata: {
          requestId,
          duration,
          httpVersion: (response as any).httpVersion || '1.1',
          fromCache: (response as any).isFromCache || false,
        },
      };

      // DEBUG: Log the response we're about to return
      this.observabilityLogger.debug({
        message: 'Returning HttpResponse object from executeRequest',
        serviceName,
        requestId,
        correlationId,
        httpResponseKeys: Object.keys(httpResponse),
        hasData: httpResponse.data !== undefined && httpResponse.data !== null,
        dataType: typeof httpResponse.data,
        status: httpResponse.status,
        statusText: httpResponse.statusText,
        hasHeaders: !!httpResponse.headers,
        headerCount: httpResponse.headers ? Object.keys(httpResponse.headers).length : 0,
        duration: httpResponse.metadata.duration,
      });

      return httpResponse;

    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      // Record error metrics
      this.metricsService?.incrementCounter('http_requests_total', {
        method: method.toLowerCase(),
        route: url,
        status_code: 'error',
        service: serviceName,
      });

      // Add error to span
      if (span) {
        this.tracingService?.addEvent('http.request.error', {
          'error.message': error.message,
          'error.code': error.code || 'unknown',
          'http.status_code': error.response?.statusCode || 0,
          'duration_ms': duration,
        });
      }
      
      // Enhanced error logging with correlation
      this.observabilityLogger.error({
        message: 'HTTP request failed in executeRequest',
        error: error.message,
        serviceName,
        operation,
        method,
        url,
        requestId,
        correlationId,
        duration,
        errorCode: error.code,
        statusCode: error.response?.statusCode,
        httpVersion: error.response?.httpVersion,
        retryCount: error.retryCount || 0,
        correlationContext: this.correlationService.getContext(),
      });

      // Publish HTTP error lifecycle event
      if (this.eventPublisher) {
        const errorEvent = EventFactory.httpError({
          method: method.toUpperCase(),
          url,
          serviceName,
          operationName: operation,
          correlationId,
          statusCode: error.response?.statusCode,
          errorType: error.code || 'unknown',
          errorMessage: error.message,
          errorCode: error.code,
          responseTime: duration,
          retryAttempt: error.retryCount || 0,
        });
        
        // Fire-and-forget event publishing (don't block HTTP request)
        this.eventPublisher.publish(errorEvent).catch(publishError => {
          this.observabilityLogger.warn({
            message: 'Failed to publish HTTP error event',
            error: publishError.message,
            correlationId,
            requestId,
          });
        });
      }

      // Add service context to error
      error.serviceName = serviceName;
      error.requestId = requestId;
      error.correlationId = correlationId;
      
      const transformedError = this.errorHandler.buildFromException(error, url);
      throw new Error(`${transformedError.error}: ${transformedError.message}`);
    }
  }

  // Convenience methods
  async get<T = any>(url: string, options: Omit<HttpClientOptions, 'data'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>('GET', url, options);
  }

  async post<T = any>(url: string, data?: any, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
    return this.request<T>('POST', url, { ...options, data });
  }

  async put<T = any>(url: string, data?: any, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
    return this.request<T>('PUT', url, { ...options, data });
  }

  async patch<T = any>(url: string, data?: any, options: HttpClientOptions = {}): Promise<HttpResponse<T>> {
    return this.request<T>('PATCH', url, { ...options, data });
  }

  async delete<T = any>(url: string, options: Omit<HttpClientOptions, 'data'> = {}): Promise<HttpResponse<T>> {
    return this.request<T>('DELETE', url, options);
  }

  /**
   * Get circuit breaker status for all services
   * @returns Circuit breaker status summary
   */
  getCircuitBreakerStatus(): {
    summary: {
      total: number;
      open: number;
      halfOpen: number;
      closed: number;
      openServices: string[];
      healthyServices: string[];
    };
    circuitBreakers: Record<string, any>;
    hasUnhealthyCircuits: boolean;
  } {
    if (!this.circuitBreakerService) {
      return {
        summary: { total: 0, open: 0, halfOpen: 0, closed: 0, openServices: [], healthyServices: [] },
        circuitBreakers: {},
        hasUnhealthyCircuits: false,
      };
    }

    const status = this.circuitBreakerService.getStatus();
    
    // Manually calculate summary since getHealthSummary may not exist
    const openServices: string[] = [];
    const healthyServices: string[] = [];
    let open = 0, halfOpen = 0, closed = 0;
    
    Object.entries(status).forEach(([serviceName, serviceStatus]) => {
      const state = (serviceStatus as any).state;
      if (state === 'OPEN') {
        open++;
        openServices.push(serviceName);
      } else if (state === 'HALF_OPEN') {
        halfOpen++;
      } else {
        closed++;
        healthyServices.push(serviceName);
      }
    });
    
    const summary = {
      total: Object.keys(status).length,
      open,
      halfOpen,
      closed,
      openServices,
      healthyServices,
    };
    
    return {
      summary,
      circuitBreakers: status,
      hasUnhealthyCircuits: summary.open > 0,
    };
  }

  /**
   * Reset all circuit breakers
   */
  resetAllCircuitBreakers(): { success: boolean; error?: string } {
    if (!this.circuitBreakerService) {
      return { success: false, error: 'Circuit breaker service not available' };
    }

    try {
      this.circuitBreakerService.resetAllCircuitBreakers();
      this.observabilityLogger.log('All circuit breakers reset via HttpClientService');
      return { success: true };
    } catch (error: any) {
      this.observabilityLogger.error({
        message: 'Failed to reset all circuit breakers',
        error: error.message,
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Reset specific circuit breaker
   */
  resetCircuitBreaker(serviceName: string): { success: boolean; error?: string } {
    if (!this.circuitBreakerService) {
      return { success: false, error: 'Circuit breaker service not available' };
    }

    try {
      this.circuitBreakerService.resetCircuitBreaker(serviceName);
      this.observabilityLogger.log(`Circuit breaker reset for ${serviceName} via HttpClientService`);
      return { success: true };
    } catch (error: any) {
      this.observabilityLogger.error({
        message: `Failed to reset circuit breaker for ${serviceName}`,
        error: error.message,
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Get services with open circuit breakers that need attention
   */
  getServicesNeedingAttention(): string[] {
    if (!this.circuitBreakerService) {
      return [];
    }
    
    // Use our own calculation since getServicesNeedingAttention may not exist
    const circuitBreakerStatus = this.getCircuitBreakerStatus();
    return circuitBreakerStatus.summary.openServices;
  }

  /**
   * Stream support for large responses
   */
  stream(url: string, options: HttpClientOptions = {}) {
    const client = options.serviceName && this.serviceClients.has(options.serviceName) 
      ? this.serviceClients.get(options.serviceName)! 
      : this.gotInstance;
    
    return client.stream(url, {
      headers: options.headers,
      searchParams: options.params,
    });
  }

  private buildRequestBody(method: Method, data?: any): Partial<GotOptions> {
    this.observabilityLogger.debug({
      message: 'BUILD_REQUEST_BODY: Analyzing request data',
      method,
      hasData: !!data,
      dataType: typeof data,
      dataIsString: typeof data === 'string',
      dataIsURLSearchParams: data instanceof URLSearchParams,
      dataIsObject: typeof data === 'object' && data !== null,
      dataKeys: data && typeof data === 'object' ? Object.keys(data) : [],
      dataSize: data ? JSON.stringify(data).length : 0,
      shouldProcess: !!data && ['POST', 'PUT', 'PATCH'].includes(method),
    });

    if (!data || !['POST', 'PUT', 'PATCH'].includes(method)) {
      this.observabilityLogger.debug({
        message: 'BUILD_REQUEST_BODY: No body needed',
        reason: !data ? 'no data' : 'method not POST/PUT/PATCH',
        method,
        hasData: !!data,
      });
      return {};
    }

    if (typeof data === 'string') {
      this.observabilityLogger.debug({
        message: 'BUILD_REQUEST_BODY: Using string body',
        bodyLength: data.length,
        bodyPreview: data.substring(0, 100),
      });
      return { body: data };
    }

    if (data instanceof URLSearchParams) {
      const bodyString = data.toString();
      this.observabilityLogger.debug({
        message: 'BUILD_REQUEST_BODY: Using URLSearchParams body',
        bodyLength: bodyString.length,
        bodyPreview: bodyString.substring(0, 100),
      });
      return { body: bodyString };
    }

    // Default to JSON
    this.observabilityLogger.debug({
      message: 'BUILD_REQUEST_BODY: Using JSON body',
      jsonData: data,
      jsonSize: JSON.stringify(data).length,
      jsonPreview: JSON.stringify(data).substring(0, 100),
      willReturnJsonOption: true,
    });
    return { json: data };
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private buildCorrelationHeaders(): Record<string, string> {
    const context = this.correlationService.getContext();
    const headers: Record<string, string> = {};

    if (context?.correlationId) {
      headers['x-correlation-id'] = context.correlationId;
      headers['x-request-id'] = context.correlationId;
    }

    if (context?.traceId) {
      headers['x-trace-id'] = context.traceId;
    }

    if (context?.spanId) {
      headers['x-span-id'] = context.spanId;
    }

    if (context?.service?.name) {
      headers['x-origin-service'] = context.service.name;
    }

    return headers;
  }

  private transformError(error: any, serviceName: string, method: Method, url: string, requestId: string): Error {
    if (error.response) {
      const httpError = new HttpResponseError(
        `HTTP ${error.response.statusCode}: ${error.response.statusMessage || 'Request failed'}`,
        error.response.statusCode,
        error.response.body,
        error.response.headers || {},
        error
      );
      // Add context properties
      (httpError as any).serviceName = serviceName;
      (httpError as any).method = method;
      (httpError as any).url = url;
      (httpError as any).requestId = requestId;
      return httpError;
    }

    if (error.code === 'ETIMEDOUT' || error.name === 'TimeoutError') {
      const timeoutError = new TimeoutError(
        'Request timeout',
        'response', // timeout type
        error
      );
      (timeoutError as any).serviceName = serviceName;
      (timeoutError as any).method = method;
      (timeoutError as any).url = url;
      (timeoutError as any).requestId = requestId;
      return timeoutError;
    }

    if (error.code && ['ECONNREFUSED', 'ENOTFOUND', 'ENETUNREACH'].includes(error.code)) {
      const networkError = new NetworkError(
        `Network error: ${error.code}`,
        error.code,
        error
      );
      (networkError as any).serviceName = serviceName;
      (networkError as any).method = method;
      (networkError as any).url = url;
      (networkError as any).requestId = requestId;
      return networkError;
    }

    const httpError = new HttpClientError(
      error.message || 'HTTP request failed',
      error,
      serviceName,
      url,
      method
    );
    (httpError as any).requestId = requestId;
    return httpError;
  }

  // Hooks for observability and debugging
  private beforeRequestHook(options: GotOptions): void {
    const context = options.context as any;
    this.logger.debug(`[${context?.requestId}] Starting ${options.method} ${options.url}`, {
      serviceName: context?.serviceName,
      headers: options.headers,
    });
  }

  private afterResponseHook(response: Response): Response {
    const context = response.request.options.context as any;
    this.logger.debug(`[${context?.requestId}] Response received: ${response.statusCode}`, {
      serviceName: context?.serviceName,
      contentLength: response.headers['content-length'],
      httpVersion: response.httpVersion,
    });
    return response;
  }

  private beforeErrorHook(error: any): any {
    const context = error.options?.context as any;
    this.logger.debug(`[${context?.requestId}] Request error: ${error.message}`, {
      serviceName: context?.serviceName,
      code: error.code,
      statusCode: error.response?.statusCode,
    });
    return error;
  }

  private beforeRetryHook(error: any): void {
    const context = error.options?.context as any;
    const retryCount = error.retryCount || 0;
    
    // Record retry metrics
    this.metricsService?.incrementCounter('http_retries_total', {
      service: context?.serviceName || 'unknown',
      method: error.options?.method?.toLowerCase() || 'unknown',
      attempt: retryCount.toString(),
    });
    
    this.logger.warn(`[${context?.requestId}] HTTP request retry`, {
      serviceName: context?.serviceName,
      correlationId: context?.correlationId,
      retryAttempt: retryCount + 1,
      error: error.message,
      statusCode: error.response?.statusCode,
    });
  }
}