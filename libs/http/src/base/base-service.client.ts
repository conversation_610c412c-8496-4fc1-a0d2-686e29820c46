import { HttpClientService } from '../client/http-client.service';
import { HttpClientOptions, HttpResponse } from '../types/http-client.types';

/**
 * Base class for service-specific HTTP clients
 * Eliminates boilerplate while maintaining flexibility for custom use cases
 */
export abstract class BaseServiceClient {
  constructor(
    protected readonly httpClient: HttpClientService,
    protected readonly serviceName: string,
    protected readonly baseUrl: string
  ) {}

  /**
   * Standard GET request with automatic service configuration
   */
  protected async get<T = any>(path: string, options: Partial<HttpClientOptions> = {}): Promise<HttpResponse<T>> {
    return this.httpClient.get<T>(`${this.baseUrl}${path}`, {
      serviceName: this.serviceName,
      operationName: `get-${this.getOperationName(path)}`,
      ...options
    });
  }

  /**
   * Standard POST request with JSON body
   */
  protected async post<T = any>(path: string, data?: any, options: Partial<HttpClientOptions> = {}): Promise<HttpResponse<T>> {
    return this.httpClient.post<T>(`${this.baseUrl}${path}`, data, {
      serviceName: this.serviceName,
      operationName: `post-${this.getOperationName(path)}`,
      ...options
    });
  }

  /**
   * Standard PUT request with JSON body
   */
  protected async put<T = any>(path: string, data?: any, options: Partial<HttpClientOptions> = {}): Promise<HttpResponse<T>> {
    return this.httpClient.put<T>(`${this.baseUrl}${path}`, data, {
      serviceName: this.serviceName,
      operationName: `put-${this.getOperationName(path)}`,
      ...options
    });
  }

  /**
   * Standard PATCH request with JSON body
   */
  protected async patch<T = any>(path: string, data?: any, options: Partial<HttpClientOptions> = {}): Promise<HttpResponse<T>> {
    return this.httpClient.patch<T>(`${this.baseUrl}${path}`, data, {
      serviceName: this.serviceName,
      operationName: `patch-${this.getOperationName(path)}`,
      ...options
    });
  }

  /**
   * Standard DELETE request
   */
  protected async delete<T = any>(path: string, options: Partial<HttpClientOptions> = {}): Promise<HttpResponse<T>> {
    return this.httpClient.delete<T>(`${this.baseUrl}${path}`, {
      serviceName: this.serviceName,
      operationName: `delete-${this.getOperationName(path)}`,
      ...options
    });
  }

  /**
   * Form data POST request (for application/x-www-form-urlencoded)
   * Common for OAuth endpoints, webhooks, etc.
   */
  protected async postForm<T = any>(
    path: string, 
    formData: Record<string, string>, 
    options: Partial<HttpClientOptions> = {}
  ): Promise<HttpResponse<T>> {
    return this.httpClient.post<T>(`${this.baseUrl}${path}`, 
      new URLSearchParams(formData).toString(), {
        serviceName: this.serviceName,
        operationName: `post-form-${this.getOperationName(path)}`,
        headers: { 
          'Content-Type': 'application/x-www-form-urlencoded',
          ...options.headers 
        },
        ...options
      });
  }

  /**
   * Multipart form data POST request (for file uploads)
   */
  protected async postMultipart<T = any>(
    path: string, 
    formData: FormData, 
    options: Partial<HttpClientOptions> = {}
  ): Promise<HttpResponse<T>> {
    return this.httpClient.post<T>(`${this.baseUrl}${path}`, formData, {
      serviceName: this.serviceName,
      operationName: `post-multipart-${this.getOperationName(path)}`,
      headers: { 
        'Content-Type': 'multipart/form-data',
        ...options.headers 
      },
      ...options
    });
  }

  /**
   * Raw request method for complete customization
   * Use when standard methods don't cover your use case
   */
  protected async request<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    path: string, 
    options: Partial<HttpClientOptions> = {}
  ): Promise<HttpResponse<T>> {
    return this.httpClient.request<T>(method, `${this.baseUrl}${path}`, {
      serviceName: this.serviceName,
      operationName: `${method.toLowerCase()}-${this.getOperationName(path)}`,
      ...options
    });
  }

  /**
   * Health check helper - common pattern across all services
   */
  protected async checkHealth(healthPath: string = '/health'): Promise<{ 
    status: string; 
    responseTime: number; 
    details?: any 
  }> {
    try {
      const response = await this.get(healthPath, {
        timeout: 5000,
        operationName: 'health-check'
      });

      return {
        status: response.status === 200 ? 'ok' : 'degraded',
        responseTime: response.metadata.duration,
        details: response.data,
      };
    } catch (error: any) {
      return {
        status: 'error',
        responseTime: 0,
        details: {
          message: error?.message || 'Unknown error',
          code: error?.code || 'UNKNOWN',
        },
      };
    }
  }

  /**
   * Helper to build query parameters from complex objects
   * Handles arrays, dates, and null/undefined values automatically
   */
  protected buildQuery(params: Record<string, any>): Record<string, string> {
    const result: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(params)) {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          result[key] = value.join(',');
        } else if (value instanceof Date) {
          result[key] = value.toISOString();
        } else {
          result[key] = String(value);
        }
      }
    }
    
    return result;
  }

  /**
   * Helper to extract operation name from path for better observability
   * /users/123 -> users-123, /api/v1/auth/login -> auth-login
   */
  protected getOperationName(path: string): string {
    return path
      .replace(/^\//, '') // Remove leading slash
      .replace(/\/api\/v\d+\//, '') // Remove API version prefix
      .replace(/\//g, '-') // Replace slashes with dashes
      .replace(/[^a-zA-Z0-9\-]/g, '') // Remove special characters
      .toLowerCase();
  }

  /**
   * Get the configured base URL for this service
   */
  protected getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Get the service name for this client
   */
  protected getServiceName(): string {
    return this.serviceName;
  }
}