// Core HTTP module and service
export { HttpModule } from './http.module';
export { HttpClientService } from './client/http-client.service';

// Base client for service-specific implementations
export { BaseServiceClient } from './base/base-service.client';

// HTTP Cache adapter for Redis integration
export { RedisHttpCacheAdapter } from './cache/redis-http-cache-adapter';

// Types and interfaces
export {
  HttpClientOptions,
  HttpResponse,
  ServiceClientConfig,
  HttpModuleOptions,
  HttpModuleAsyncOptions,
  HttpMethod,
} from './types/http-client.types';

// Error classes
export {
  HttpClientError,
  HttpResponseError,
  NetworkError,
  TimeoutError,
} from './errors/http-errors';