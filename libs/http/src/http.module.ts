import { DynamicModule, Module, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { 
  OBSERVABILITY_LOGGER, 
  METRICS_SERVICE, 
  TRACING_SERVICE,
  ObservabilityLogger,
  MetricsService,
  TracingService 
} from '@libs/observability';
import { CircuitBreakerService } from '@libs/resilience';
import { 
  ErrorHandlingModule, 
  ErrorResponseBuilderService, 
  CorrelationService 
} from '@libs/error-handling';
import { EventPublisher } from '@libs/messaging';
import { CacheService } from '@libs/caching';
import { HttpClientService } from './client/http-client.service';
import { HttpModuleOptions, HttpModuleAsyncOptions } from './types/http-client.types';

@Module({})
export class HttpModule {
  /**
   * Register HTTP module with static configuration and full observability integration
   */
  static register(options: HttpModuleOptions = {}): DynamicModule {
    const providers = this.createProviders(options);
    
    return {
      module: HttpModule,
      imports: [
        // Don't create our own ObservabilityModule - use the one provided by the parent service
        // ObservabilityModule should be imported by the service that uses this HttpModule
        // CircuitBreakerModule should be imported by the parent service to ensure single instance
        ErrorHandlingModule
      ],
      providers,
      exports: [HttpClientService],
      global: true,
    };
  }

  /**
   * Register HTTP module with async configuration
   */
  static registerAsync(options: HttpModuleAsyncOptions): DynamicModule {
    const providers = this.createAsyncProviders(options);
    
    return {
      module: HttpModule,
      imports: [
        // Don't import ObservabilityModule here - use the one from the parent service
        // CircuitBreakerModule should be imported by the parent service to ensure single instance
        ErrorHandlingModule, 
        ConfigModule, 
        ...(options.imports || [])
      ],
      providers,
      exports: [HttpClientService],
      global: true,
    };
  }

  /**
   * Create synchronous providers with full observability integration
   */
  private static createProviders(options: HttpModuleOptions): Provider[] {
    return [
      {
        provide: 'HTTP_MODULE_OPTIONS',
        useValue: options,
      },
      {
        provide: HttpClientService,
        useFactory: (
          observabilityLogger: ObservabilityLogger,
          metricsService: MetricsService | undefined,
          tracingService: TracingService | undefined,
          correlationService: CorrelationService,
          errorHandler: ErrorResponseBuilderService,
          circuitBreakerService?: CircuitBreakerService,
          eventPublisher?: EventPublisher,
          cacheService?: CacheService,
          loggerFactory?: any
        ) => {
          const httpService = new HttpClientService(
            observabilityLogger,
            metricsService,
            tracingService,
            correlationService,
            errorHandler,
            circuitBreakerService,
            eventPublisher,  
            cacheService,
            loggerFactory,
            options.global,
            options.serviceContext
          );
          
          // Create service-specific clients if configured
          if (options.services) {
            observabilityLogger.log({
              message: 'HttpModule: Creating service clients during initialization',
              serviceCount: Object.keys(options.services).length,
              serviceNames: Object.keys(options.services)
            });
            
            Object.entries(options.services).forEach(([serviceName, config]) => {
              observabilityLogger.log({
                message: `HttpModule: Creating service client for ${serviceName}`,
                serviceName,
                config: { baseURL: config.baseURL, responseTimeout: config.responseTimeout }
              });
              
              try {
                httpService.createServiceClient(serviceName, config);
                observabilityLogger.log({
                  message: `HttpModule: Successfully created service client for ${serviceName}`,
                  serviceName
                });
              } catch (error: any) {
                observabilityLogger.error({
                  message: `HttpModule: Failed to create service client for ${serviceName}`,
                  serviceName,
                  error: error?.message || String(error)
                });
              }
            });
          } else {
            observabilityLogger.warn({
              message: 'HttpModule: No services configured - no service clients will be created',
              optionsKeys: Object.keys(options)
            });
          }
          
          return httpService;
        },
        inject: [
          OBSERVABILITY_LOGGER,
          { token: METRICS_SERVICE, optional: true },
          { token: TRACING_SERVICE, optional: true },
          CorrelationService,
          ErrorResponseBuilderService,
          { token: CircuitBreakerService, optional: true },
          { token: 'EVENT_PUBLISHER', optional: true },
          { token: CacheService, optional: true },
          { token: 'LOGGER_FACTORY', optional: true },
        ],
      },
    ];
  }

  /**
   * Create asynchronous providers
   */
  private static createAsyncProviders(options: HttpModuleAsyncOptions): Provider[] {
    return [
      {
        provide: 'HTTP_MODULE_OPTIONS',
        useFactory: options.useFactory!,
        inject: options.inject || [],
      },
      {
        provide: HttpClientService,
        useFactory: (
          moduleOptions: HttpModuleOptions, 
          observabilityLogger: ObservabilityLogger, 
          metricsService: MetricsService | undefined,
          tracingService: TracingService | undefined,
          correlationService: CorrelationService,
          errorHandler: ErrorResponseBuilderService,
          circuitBreakerService?: CircuitBreakerService,
          eventPublisher?: EventPublisher,
          cacheService?: CacheService,
          loggerFactory?: any
        ) => {
          const httpService = new HttpClientService(
            observabilityLogger,
            metricsService,
            tracingService,
            correlationService,
            errorHandler,
            circuitBreakerService,
            eventPublisher,
            cacheService,
            loggerFactory,
            moduleOptions.global,
            moduleOptions.serviceContext
          );
          
          // Create service-specific clients if configured
          if (moduleOptions.services) {
            Object.entries(moduleOptions.services).forEach(([serviceName, config]) => {
              httpService.createServiceClient(serviceName, config);
            });
          }
          
          return httpService;
        },
        inject: [
          'HTTP_MODULE_OPTIONS', 
          OBSERVABILITY_LOGGER, 
          { token: METRICS_SERVICE, optional: true },
          { token: TRACING_SERVICE, optional: true },
          CorrelationService,
          ErrorResponseBuilderService,
          { token: CircuitBreakerService, optional: true },
          { token: 'EVENT_PUBLISHER', optional: true },
          { token: CacheService, optional: true },
          { token: 'LOGGER_FACTORY', optional: true },
        ],
      },
    ];
  }

  /**
   * Default configuration factory for common setup
   */
  static forRoot(): DynamicModule {
    return this.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService): HttpModuleOptions => ({
        global: {
          http2: configService.get<boolean>('HTTP_CLIENT_HTTP2', true),
          responseTimeout: configService.get<number>('HTTP_CLIENT_TIMEOUT', 3000), // Enterprise default
          connectTimeout: configService.get<number>('HTTP_CLIENT_CONNECT_TIMEOUT', 1000), // Enterprise default
          retryLimit: configService.get<number>('HTTP_CLIENT_RETRY_LIMIT', 1), // Enterprise default
          defaultHeaders: {
            'x-client-version': configService.get<string>('APP_VERSION', '1.0.0'),
          },
        },
        observability: {
          enabled: configService.get<boolean>('HTTP_OBSERVABILITY_ENABLED', true),
          includeHeaders: configService.get<boolean>('HTTP_LOG_HEADERS', false),
          includeRequestBody: configService.get<boolean>('HTTP_LOG_REQUEST_BODY', false),
          includeResponseBody: configService.get<boolean>('HTTP_LOG_RESPONSE_BODY', false),
        },
      }),
      inject: [ConfigService],
    });
  }

}