/**
 * HTTP Utilities Unit Tests
 * 
 * Tests pure utility functions without complex dependencies
 */

describe('HTTP Utilities', () => {
  describe('URL Construction', () => {
    it('should build basic URLs correctly', () => {
      const baseUrl = 'https://api.example.com';
      const path = '/users';
      const result = `${baseUrl}${path}`;
      
      expect(result).toBe('https://api.example.com/users');
    });

    it('should handle trailing slashes in base URL', () => {
      const baseUrl = 'https://api.example.com/';
      const path = '/users';
      const result = baseUrl.replace(/\/$/, '') + path;
      
      expect(result).toBe('https://api.example.com/users');
    });

    it('should handle missing leading slash in path', () => {
      const baseUrl = 'https://api.example.com';
      const path = 'users';
      const result = `${baseUrl}/${path}`;
      
      expect(result).toBe('https://api.example.com/users');
    });
  });

  describe('Configuration Merging', () => {
    it('should merge configurations with correct precedence', () => {
      const enterprise = { timeout: 30000, retries: 3, headers: { 'User-Agent': 'Enterprise' } };
      const service = { timeout: 5000, headers: { 'X-Service': 'UserService' } };
      const request = { retries: 1, headers: { 'X-Request-ID': '123' } };
      
      const result = { ...enterprise, ...service, ...request };
      
      expect(result).toEqual({
        timeout: 5000,     // service overrides enterprise
        retries: 1,        // request overrides enterprise
        headers: {
          'X-Request-ID': '123'  // request headers completely override
        }
      });
    });

    it('should handle undefined configurations gracefully', () => {
      const enterprise = { timeout: 30000, retries: 3 };
      const service = undefined;
      const request = { retries: 1 };
      
      const result = { ...enterprise, ...(service || {}), ...request };
      
      expect(result).toEqual({
        timeout: 30000,
        retries: 1
      });
    });
  });

  describe('Request Option Processing', () => {
    it('should extract method from options', () => {
      const options = { method: 'POST', data: { test: true } };
      const method = options.method;
      
      expect(method).toBe('POST');
    });

    it('should provide default method', () => {
      const options = { data: { test: true } };
      const method = options.method || 'GET';
      
      expect(method).toBe('GET');
    });

    it('should handle headers correctly', () => {
      const options = {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token123'
        }
      };
      
      expect(options.headers['Content-Type']).toBe('application/json');
      expect(options.headers['Authorization']).toBe('Bearer token123');
    });
  });

  describe('Error Classification', () => {
    it('should classify HTTP errors correctly', () => {
      const classifyError = (statusCode: number) => {
        if (statusCode >= 400 && statusCode < 500) return 'client-error';
        if (statusCode >= 500) return 'server-error';
        return 'success';
      };

      expect(classifyError(404)).toBe('client-error');
      expect(classifyError(500)).toBe('server-error');
      expect(classifyError(200)).toBe('success');
    });

    it('should identify timeout errors', () => {
      const error = new Error('Timeout');
      error.name = 'TimeoutError';
      
      const isTimeout = error.name === 'TimeoutError';
      expect(isTimeout).toBe(true);
    });

    it('should identify network errors', () => {
      const error = new Error('ECONNREFUSED');
      const isNetworkError = error.message.includes('ECONNREFUSED') || 
                           error.message.includes('ENOTFOUND') ||
                           error.message.includes('EHOSTUNREACH');
      
      expect(isNetworkError).toBe(true);
    });
  });

  describe('Response Processing', () => {
    it('should parse JSON responses correctly', () => {
      const responseBody = '{"success": true, "data": {"id": 123}}';
      const parsed = JSON.parse(responseBody);
      
      expect(parsed.success).toBe(true);
      expect(parsed.data.id).toBe(123);
    });

    it('should handle non-JSON responses', () => {
      const responseBody = 'Plain text response';
      
      let parsed;
      try {
        parsed = JSON.parse(responseBody);
      } catch {
        parsed = responseBody;
      }
      
      expect(parsed).toBe('Plain text response');
    });

    it('should extract response metadata', () => {
      const response = {
        statusCode: 200,
        headers: { 'content-type': 'application/json' },
        body: '{"test": true}',
        requestUrl: 'https://api.example.com/test'
      };
      
      expect(response.statusCode).toBe(200);
      expect(response.headers['content-type']).toBe('application/json');
      expect(response.requestUrl).toBe('https://api.example.com/test');
    });
  });

  describe('Operation Name Generation', () => {
    it('should generate operation names from paths', () => {
      const generateOperationName = (path: string): string => {
        return path
          .replace(/^\//, '') // Remove leading slash
          .replace(/^api\/v\d+\//, '') // Remove API version prefix
          .replace(/\//g, '-') // Replace slashes with dashes
          .replace(/\?.*$/, ''); // Remove query parameters
      };

      expect(generateOperationName('/users/123')).toBe('users-123');
      expect(generateOperationName('/api/v1/auth/login')).toBe('auth-login');
      expect(generateOperationName('/users?page=1&limit=10')).toBe('users');
    });

    it('should handle empty or root paths', () => {
      const generateOperationName = (path: string): string => {
        const cleaned = path
          .replace(/^\//, '')
          .replace(/^api\/v\d+\//, '')
          .replace(/\//g, '-')
          .replace(/\?.*$/, '');
        
        return cleaned || 'root';
      };

      expect(generateOperationName('/')).toBe('root');
      expect(generateOperationName('')).toBe('root');
    });
  });
});