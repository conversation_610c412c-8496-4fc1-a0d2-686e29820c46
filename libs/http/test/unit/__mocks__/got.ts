/**
 * Got ESM Module Mock for Simple Jest Tests
 * 
 * Provides a comprehensive mock of the Got HTTP client for unit testing
 */

const mockResponse = {
  statusCode: 200,
  headers: { 'content-type': 'application/json' },
  body: '{"success": true}',
  requestUrl: 'http://test.com',
};

const mockGotInstance = {
  get: jest.fn().mockResolvedValue(mockResponse),
  post: jest.fn().mockResolvedValue(mockResponse),
  put: jest.fn().mockResolvedValue(mockResponse),
  patch: jest.fn().mockResolvedValue(mockResponse),
  delete: jest.fn().mockResolvedValue(mockResponse),
  extend: jest.fn().mockReturnThis(),
};

const mockGot = jest.fn().mockResolvedValue(mockResponse);
Object.assign(mockGot, mockGotInstance);

// Mock Got constructor and static methods
mockGot.extend = jest.fn().mockReturnValue(mockGotInstance);
mockGot.get = jest.fn().mockResolvedValue(mockResponse);
mockGot.post = jest.fn().mockResolvedValue(mockResponse);
mockGot.put = jest.fn().mockResolvedValue(mockResponse);
mockGot.patch = jest.fn().mockResolvedValue(mockResponse);
mockGot.delete = jest.fn().mockResolvedValue(mockResponse);

// Mock Got error classes
mockGot.HTTPError = class MockHTTPError extends Error {
  response: any;
  constructor(message: string, response: any) {
    super(message);
    this.response = response;
    this.name = 'HTTPError';
  }
};

mockGot.RequestError = class MockRequestError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'RequestError';
  }
};

mockGot.TimeoutError = class MockTimeoutError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TimeoutError';
  }
};

// Export as both default and named exports to handle different import styles
export default mockGot;
export const { HTTPError, RequestError, TimeoutError } = mockGot;