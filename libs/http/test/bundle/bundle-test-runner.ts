/**
 * Bundle Test Runner for HTTP Library
 * 
 * This test runner executes tests in a webpack bundled environment
 * identical to production, enabling real cross-library integration testing
 */

import { runBundleTests } from './suite/bundle-test-suite';

async function main() {
  console.log('🚀 Starting HTTP Library Bundle Tests');
  console.log('📦 Testing in webpack bundled environment (production-like)');
  
  try {
    const results = await runBundleTests();
    
    console.log('\n📊 Bundle Test Results:');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`⏱️  Total time: ${results.duration}ms`);
    
    if (results.failed > 0) {
      console.log('\n❌ Some bundle tests failed:');
      results.failures.forEach(failure => {
        console.log(`  - ${failure.test}: ${failure.error}`);
      });
      process.exit(1);
    } else {
      console.log('\n🎉 All bundle tests passed!');
      process.exit(0);
    }
  } catch (error) {
    console.error('💥 Bundle test runner failed:', error);
    process.exit(1);
  }
}

// Run if this is the main module
if (require.main === module) {
  main().catch(console.error);
}