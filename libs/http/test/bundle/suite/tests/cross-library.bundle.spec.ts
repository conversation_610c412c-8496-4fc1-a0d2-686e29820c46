/**
 * Cross-Library Integration Bundle Tests
 * 
 * Tests HTTP library integration with other libraries in bundled environment
 */

import { HttpClientService } from '../../../../src/client/http-client.service';
import { MockHttpServer } from '../../utils/mock-server';
import { createTestAssertions } from '../../utils/test-setup';

export async function validateCrossLibraryIntegration(httpClient: HttpClientService, mockServer: MockHttpServer): Promise<void> {
  const assert = createTestAssertions();

  // Test observability integration
  console.log('  📊 Testing observability integration...');
  const response = await httpClient.get(`${mockServer.url}/health`, {
    serviceName: 'test-service',
    operationName: 'health-check'
  });
  
  assert.assertEqual(response.statusCode, 200);
  // Note: In real environment, we would validate that metrics/logs/traces were created
  // For bundle tests, we verify the request completes successfully with observability context

  // Test circuit breaker integration  
  console.log('  🔌 Testing circuit breaker integration...');
  // First request should succeed
  const healthResponse = await httpClient.get(`${mockServer.url}/health`);
  assert.assertEqual(healthResponse.statusCode, 200);
  
  // Circuit breaker status should be available
  const circuitStatus = httpClient.getCircuitBreakerStatus();
  assert.assertDefined(circuitStatus, 'Circuit breaker status should be available');
  assert.assertDefined(circuitStatus.summary, 'Circuit breaker summary should be defined');

  // Test caching integration
  console.log('  🗃️ Testing caching integration...');
  // First request - cache miss
  const cacheTest1 = await httpClient.get(`${mockServer.url}/cache-test`, {
    enableCache: true,
    cacheTtl: 300
  });
  assert.assertEqual(cacheTest1.statusCode, 200);
  
  // Second request - should potentially hit cache (hard to test in bundle without real Redis)
  const cacheTest2 = await httpClient.get(`${mockServer.url}/cache-test`, {
    enableCache: true,
    cacheTtl: 300
  });
  assert.assertEqual(cacheTest2.statusCode, 200);

  // Test error handling integration
  console.log('  ⚠️ Testing error handling integration...');
  try {
    await httpClient.get(`${mockServer.url}/error/500`, {
      serviceName: 'test-service',
      operationName: 'error-test'
    });
    throw new Error('Expected error was not thrown');
  } catch (error) {
    // Verify error was properly handled and enhanced with context
    assert.assertTrue(error.message.includes('500'), 'Error should contain status code');
  }

  // Test messaging integration
  console.log('  📨 Testing messaging integration...');
  const messagingResponse = await httpClient.get(`${mockServer.url}/health`, {
    serviceName: 'test-service',
    operationName: 'messaging-test',
    publishEvents: true
  });
  assert.assertEqual(messagingResponse.statusCode, 200);
  // Note: In real environment, we would verify that HTTP lifecycle events were published

  console.log('  ✅ All cross-library integrations validated');
}