/**
 * HTTP Operations Bundle Tests
 * 
 * Tests basic HTTP operations with real Got client in bundled environment
 */

import { HttpClientService } from '../../../../src/client/http-client.service';
import { MockHttpServer } from '../../utils/mock-server';
import { createTestAssertions } from '../../utils/test-setup';

export async function validateHttpOperations(httpClient: HttpClientService, mockServer: MockHttpServer): Promise<void> {
  const assert = createTestAssertions();

  // Test GET request
  console.log('  📝 Testing GET request...');
  const getResponse = await httpClient.get(`${mockServer.url}/users/123`);
  assert.assertEqual(getResponse.statusCode, 200);
  assert.assertTrue(getResponse.body.includes('Test User'), 'GET response should contain user data');

  // Test POST request
  console.log('  📝 Testing POST request...');
  const postData = { name: 'New User', email: '<EMAIL>' };
  const postResponse = await httpClient.post(`${mockServer.url}/users`, postData);
  assert.assertEqual(postResponse.statusCode, 201);
  assert.assertTrue(postResponse.body.includes('New User'), 'POST response should contain created user data');

  // Test PUT request
  console.log('  📝 Testing PUT request...');
  const putData = { name: 'Updated User', email: '<EMAIL>' };
  const putResponse = await httpClient.put(`${mockServer.url}/users/123`, putData);
  assert.assertEqual(putResponse.statusCode, 200);
  assert.assertTrue(putResponse.body.includes('Updated User'), 'PUT response should contain updated user data');

  // Test PATCH request
  console.log('  📝 Testing PATCH request...');
  const patchData = { name: 'Patched User' };
  const patchResponse = await httpClient.patch(`${mockServer.url}/users/123`, patchData);
  assert.assertEqual(patchResponse.statusCode, 200);
  assert.assertTrue(patchResponse.body.includes('Patched User'), 'PATCH response should contain patched user data');

  // Test DELETE request
  console.log('  📝 Testing DELETE request...');
  const deleteResponse = await httpClient.delete(`${mockServer.url}/users/123`);
  assert.assertEqual(deleteResponse.statusCode, 204);

  // Test error handling
  console.log('  📝 Testing error handling...');
  await assert.assertThrows(
    () => httpClient.get(`${mockServer.url}/error/404`),
    '404'
  );

  await assert.assertThrows(
    () => httpClient.get(`${mockServer.url}/error/500`),
    '500'
  );

  console.log('  ✅ All HTTP operations validated');
}