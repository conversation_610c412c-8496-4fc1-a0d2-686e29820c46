/**
 * Service Client Patterns Bundle Tests
 * 
 * Tests BaseServiceClient abstract class and service patterns in bundled environment
 */

import { BaseServiceClient } from '../../../../src/base/base-service.client';
import { HttpClientService } from '../../../../src/client/http-client.service';
import { MockHttpServer } from '../../utils/mock-server';
import { createTestAssertions } from '../../utils/test-setup';

// Test implementation of BaseServiceClient
class TestServiceClient extends BaseServiceClient {
  constructor(httpClient: HttpClientService) {
    super(httpClient, 'test-service', 'http://localhost:3333');
  }

  async getUser(id: string) {
    return this.get(`/users/${id}`);
  }

  async createUser(userData: any) {
    return this.post('/users', userData);
  }

  async updateUser(id: string, userData: any) {
    return this.put(`/users/${id}`, userData);
  }

  async patchUser(id: string, partialData: any) {
    return this.patch(`/users/${id}`, partialData);
  }

  async deleteUser(id: string) {
    return this.delete(`/users/${id}`);
  }

  // Expose protected method for testing
  getOperationNameForTest(path: string) {
    return this.getOperationName(path);
  }
}

export async function validateServiceClientPatterns(httpClient: HttpClientService, mockServer: MockHttpServer): Promise<void> {
  const assert = createTestAssertions();

  // Create test service client
  const serviceClient = new TestServiceClient(httpClient);

  // Test CRUD operations through service client
  console.log('  👤 Testing service client CRUD operations...');
  
  // Test GET
  const user = await serviceClient.getUser('123');
  assert.assertDefined(user.body, 'User data should be returned');
  assert.assertTrue(user.body.includes('Test User'), 'Should return correct user data');

  // Test POST
  const newUserData = { name: 'New User', email: '<EMAIL>' };
  const newUser = await serviceClient.createUser(newUserData);
  assert.assertEqual(newUser.statusCode, 201);
  assert.assertTrue(newUser.body.includes('New User'), 'Should create new user');

  // Test PUT
  const updateData = { name: 'Updated User', email: '<EMAIL>' };
  const updatedUser = await serviceClient.updateUser('123', updateData);
  assert.assertEqual(updatedUser.statusCode, 200);
  assert.assertTrue(updatedUser.body.includes('Updated User'), 'Should update user');

  // Test PATCH
  const patchData = { name: 'Patched User' };
  const patchedUser = await serviceClient.patchUser('123', patchData);
  assert.assertEqual(patchedUser.statusCode, 200);
  assert.assertTrue(patchedUser.body.includes('Patched User'), 'Should patch user');

  // Test DELETE
  const deleteResult = await serviceClient.deleteUser('123');
  assert.assertEqual(deleteResult.statusCode, 204);

  // Test operation name generation
  console.log('  🏷️ Testing operation name generation...');
  const operationName1 = serviceClient.getOperationNameForTest('/users/123');
  assert.assertEqual(operationName1, 'users-123');

  const operationName2 = serviceClient.getOperationNameForTest('/users/123/posts/456');
  assert.assertEqual(operationName2, 'users-123-posts-456');

  const operationName3 = serviceClient.getOperationNameForTest('/health');
  assert.assertEqual(operationName3, 'health');

  // Test service configuration
  console.log('  ⚙️ Testing service configuration...');
  // The service client should have stored the service name and base URL
  assert.assertEqual(serviceClient['serviceName'], 'test-service');
  assert.assertEqual(serviceClient['baseUrl'], 'http://localhost:3333');

  console.log('  ✅ All service client patterns validated');
}