/**
 * Configuration Handling Bundle Tests
 * 
 * Tests HTTP client configuration precedence and validation in bundled environment
 */

import { HttpClientService } from '../../../../src/client/http-client.service';
import { MockHttpServer } from '../../utils/mock-server';
import { createTestAssertions } from '../../utils/test-setup';

export async function validateConfigurationHandling(httpClient: HttpClientService, mockServer: MockHttpServer): Promise<void> {
  const assert = createTestAssertions();

  // Test basic configuration
  console.log('  ⚙️ Testing basic HTTP client configuration...');
  
  // Test timeout configuration
  const startTime = Date.now();
  try {
    await httpClient.get(`${mockServer.url}/timeout`, {
      timeout: 1000 // 1 second timeout, server responds in 10 seconds
    });
    throw new Error('Request should have timed out');
  } catch (error) {
    const duration = Date.now() - startTime;
    assert.assertTrue(duration < 2000, 'Request should timeout quickly');
    assert.assertTrue(error.message.includes('timeout') || error.message.includes('ETIMEDOUT'), 'Should be a timeout error');
  }

  // Test retry configuration
  console.log('  🔄 Testing retry configuration...');
  let attemptCount = 0;
  const originalFetch = httpClient['gotInstance'];
  
  try {
    await httpClient.get(`${mockServer.url}/error/500`, {
      retries: 2,
      retryDelay: 100
    });
    throw new Error('Request should have failed after retries');
  } catch (error) {
    assert.assertTrue(error.message.includes('500'), 'Should be a 500 error after retries');
  }

  // Test HTTP/2 configuration
  console.log('  🌐 Testing HTTP/2 configuration...');
  const http2Response = await httpClient.get(`${mockServer.url}/health`, {
    http2: true
  });
  assert.assertEqual(http2Response.statusCode, 200);
  // Note: In real environment, we would verify HTTP/2 was actually used

  // Test custom headers
  console.log('  📋 Testing custom headers...');
  const customHeaderResponse = await httpClient.get(`${mockServer.url}/health`, {
    headers: {
      'X-Custom-Header': 'test-value',
      'X-Test-Suite': 'bundle-tests'
    }
  });
  assert.assertEqual(customHeaderResponse.statusCode, 200);

  // Test service context configuration
  console.log('  🏷️ Testing service context configuration...');
  const contextResponse = await httpClient.get(`${mockServer.url}/health`, {
    serviceName: 'configuration-test',
    operationName: 'context-validation'
  });
  assert.assertEqual(contextResponse.statusCode, 200);

  // Test cache configuration
  console.log('  🗃️ Testing cache configuration...');
  const cacheResponse1 = await httpClient.get(`${mockServer.url}/cache-test`, {
    enableCache: true,
    cacheTtl: 600
  });
  assert.assertEqual(cacheResponse1.statusCode, 200);

  const cacheResponse2 = await httpClient.get(`${mockServer.url}/cache-test`, {
    enableCache: false // Explicitly disable cache
  });
  assert.assertEqual(cacheResponse2.statusCode, 200);

  // Test circuit breaker configuration
  console.log('  🔌 Testing circuit breaker configuration...');
  const circuitResponse = await httpClient.get(`${mockServer.url}/health`, {
    enableCircuitBreaker: true,
    circuitBreakerOptions: {
      threshold: 5,
      timeout: 60000
    }
  });
  assert.assertEqual(circuitResponse.statusCode, 200);

  console.log('  ✅ All configuration handling validated');
}