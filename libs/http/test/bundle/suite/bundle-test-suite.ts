/**
 * Bundle Test Suite for HTTP Library
 * 
 * Tests HTTP library in a real webpack bundled environment with
 * actual dependencies and cross-library integrations
 */

import { createTestHttpClient } from '../utils/test-setup';
import { createMockHttpServer } from '../utils/mock-server';
import { validateHttpOperations } from './tests/http-operations.bundle.spec';
import { validateCrossLibraryIntegration } from './tests/cross-library.bundle.spec';
import { validateServiceClientPatterns } from './tests/service-client.bundle.spec';
import { validateConfigurationHandling } from './tests/configuration.bundle.spec';

export interface BundleTestResults {
  passed: number;
  failed: number;
  duration: number;
  failures: Array<{ test: string; error: string }>;
}

export async function runBundleTests(): Promise<BundleTestResults> {
  const startTime = Date.now();
  const results: BundleTestResults = {
    passed: 0,
    failed: 0,
    duration: 0,
    failures: []
  };

  // Setup test environment
  console.log('🔧 Setting up bundle test environment...');
  const mockServer = await createMockHttpServer(3333);
  const httpClient = await createTestHttpClient();

  try {
    // Test suites to run
    const testSuites = [
      { name: 'HTTP Operations', test: () => validateHttpOperations(httpClient, mockServer) },
      { name: 'Cross-Library Integration', test: () => validateCrossLibraryIntegration(httpClient, mockServer) },
      { name: 'Service Client Patterns', test: () => validateServiceClientPatterns(httpClient, mockServer) },
      { name: 'Configuration Handling', test: () => validateConfigurationHandling(httpClient, mockServer) },
    ];

    // Run each test suite
    for (const suite of testSuites) {
      console.log(`\n🧪 Running: ${suite.name}`);
      
      try {
        await suite.test();
        results.passed++;
        console.log(`✅ ${suite.name}: PASSED`);
      } catch (error) {
        results.failed++;
        results.failures.push({
          test: suite.name,
          error: error instanceof Error ? error.message : String(error)
        });
        console.log(`❌ ${suite.name}: FAILED - ${error instanceof Error ? error.message : error}`);
      }
    }

  } finally {
    // Cleanup
    await mockServer.close();
    results.duration = Date.now() - startTime;
  }

  return results;
}