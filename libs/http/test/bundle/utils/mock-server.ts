/**
 * Mock HTTP Server for Bundle Testing
 * 
 * Creates a real HTTP server for testing actual HTTP operations
 * with controlled responses and timing
 */

import * as http from 'http';
import { URL } from 'url';

export interface MockHttpServer {
  url: string;
  port: number;
  close(): Promise<void>;
}

interface MockRoute {
  method: string;
  path: string;
  response: any;
  statusCode: number;
  delay?: number;
}

/**
 * Create a mock HTTP server for testing
 */
export async function createMockHttpServer(port: number): Promise<MockHttpServer> {
  const routes: MockRoute[] = [];
  
  // Default routes for common test scenarios
  addRoute('GET', '/health', { status: 'ok', timestamp: Date.now() }, 200);
  addRoute('GET', '/users/123', { id: 123, name: 'Test User', email: '<EMAIL>' }, 200);
  addRoute('POST', '/users', { id: 456, name: 'New User', email: '<EMAIL>' }, 201);
  addRoute('PUT', '/users/123', { id: 123, name: 'Updated User', email: '<EMAIL>' }, 200);
  addRoute('PATCH', '/users/123', { id: 123, name: 'Patched User' }, 200);
  addRoute('DELETE', '/users/123', null, 204);
  
  // Error scenarios
  addRoute('GET', '/error/404', { error: 'Not Found' }, 404);
  addRoute('GET', '/error/500', { error: 'Internal Server Error' }, 500);
  addRoute('GET', '/timeout', { message: 'This endpoint times out' }, 200, 10000);
  
  // Cache testing endpoints
  addRoute('GET', '/cache-test', { data: 'cacheable', timestamp: Date.now() }, 200);
  
  // Circuit breaker testing endpoints
  addRoute('GET', '/circuit-breaker-test', { status: 'failing' }, 500);

  const server = http.createServer(async (req, res) => {
    try {
      const url = new URL(req.url || '', `http://localhost:${port}`);
      const route = findRoute(req.method || 'GET', url.pathname);
      
      if (!route) {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not Found', path: url.pathname }));
        return;
      }

      // Add delay if specified
      if (route.delay) {
        await sleep(route.delay);
      }

      // Set CORS headers for testing
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Correlation-ID');

      // Handle OPTIONS preflight
      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      // Send response
      res.writeHead(route.statusCode, { 'Content-Type': 'application/json' });
      
      if (route.response !== null) {
        res.end(JSON.stringify(route.response));
      } else {
        res.end();
      }
      
    } catch (error) {
      console.error('Mock server error:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Mock server internal error' }));
    }
  });

  // Helper functions
  function addRoute(method: string, path: string, response: any, statusCode: number, delay?: number) {
    routes.push({ method, path, response, statusCode, delay });
  }

  function findRoute(method: string, path: string): MockRoute | undefined {
    return routes.find(route => route.method === method && route.path === path);
  }

  function sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Start server
  await new Promise<void>((resolve, reject) => {
    server.listen(port, (error?: Error) => {
      if (error) {
        reject(error);
      } else {
        console.log(`🌐 Mock HTTP server started on port ${port}`);
        resolve();
      }
    });
  });

  return {
    url: `http://localhost:${port}`,
    port,
    async close() {
      await new Promise<void>((resolve) => {
        server.close(() => {
          console.log(`🔌 Mock HTTP server on port ${port} closed`);
          resolve();
        });
      });
    }
  };
}