/**
 * Bundle-specific mocks without Jest dependencies
 * 
 * Creates mocks for bundle testing environment that don't rely on Jest
 */

import { 
  OBSERVABILITY_LOGGER,
  METRICS_SERVICE,
  TRACING_SERVICE 
} from '@libs/observability';
import { CircuitBreakerService } from '@libs/resilience';
import { CorrelationService, ErrorResponseBuilderService } from '@libs/error-handling';
import { CacheService } from '@libs/caching';

/**
 * Create simple mocks for bundle testing (no Jest dependency)
 */
export function createBundleMocks(): any[] {
  // Create simple mock functions
  const mockFn = () => {
    const fn = () => {};
    fn.mockReturnValue = (value: any) => fn;
    fn.mockResolvedValue = (value: any) => fn;
    fn.mockImplementation = (impl: any) => fn;
    return fn;
  };

  return [
    // Observability mocks
    {
      provide: OBSERVABILITY_LOGGER,
      useValue: {
        log: mockFn(),
        error: mockFn(),
        warn: mockFn(),
        debug: mockFn(),
        verbose: mockFn(),
        setContext: mockFn(),
        getContext: mockFn(),
        startTimer: () => ({ end: mockFn() }),
        logMetric: mockFn(),
        addTag: mockFn(),
      }
    },
    {
      provide: METRICS_SERVICE,
      useValue: {
        onModuleInit: mockFn(),
        onModuleDestroy: () => Promise.resolve(),
        initialize: mockFn(),
        shutdown: () => Promise.resolve(),
        createCounter: () => ({ inc: mockFn() }),
        createGauge: () => ({ set: mockFn() }),
        createHistogram: () => ({ observe: mockFn() }),
        incrementCounter: mockFn(),
        setGauge: mockFn(),
        observeHistogram: mockFn(),
        getRegistry: mockFn(),
      }
    },
    {
      provide: TRACING_SERVICE,
      useValue: {
        onModuleInit: mockFn(),
        onModuleDestroy: () => Promise.resolve(),
        initialize: mockFn(),
        shutdown: () => Promise.resolve(),
        traceAsyncFunction: mockFn(),
        getCurrentSpan: mockFn(),
        addAttribute: mockFn(),
        addEvent: mockFn(),
        setStatus: mockFn(),
      }
    },

    // Resilience mocks
    {
      provide: CircuitBreakerService,
      useValue: {
        onModuleInit: mockFn(),
        onModuleDestroy: () => Promise.resolve(),
        getCircuit: () => ({
          getStateEnum: () => 'CLOSED',
          fire: (fn: any) => fn(),
          isOpen: () => false,
          isHalfOpen: () => false,
          isClosed: () => true,
        }),
        getCircuitBreaker: (name: string) => ({
          getStateEnum: () => 'CLOSED',
          fire: (fn: any) => fn(),
          stats: { requests: 0, successes: 0, failures: 0 },
        }),
        getStats: () => ({
          summary: { total: 0, closed: 1, open: 0, halfOpen: 0 },
          circuits: {}
        }),
      }
    },

    // Error handling mocks
    {
      provide: CorrelationService,
      useValue: {
        generateCorrelationId: () => 'bundle-test-correlation-id',
        getCurrentCorrelationId: () => 'bundle-test-correlation-id',
        setCorrelationId: mockFn(),
        getContext: () => ({
          correlationId: 'bundle-test-correlation-id',
          startTime: Date.now(),
          request: { method: 'GET', path: '/test', url: 'http://localhost/test' },
          service: { name: 'bundle-test-service' },
          metadata: {},
        }),
        runWithContext: async (context: any, fn: any) => fn(),
      }
    },
    {
      provide: ErrorResponseBuilderService,
      useValue: {
        buildErrorResponse: () => ({
          statusCode: 500,
          error: 'Internal Server Error',
          message: 'Bundle test error',
          correlationId: 'bundle-test-correlation-id',
          timestamp: new Date().toISOString(),
          path: '/test',
        }),
        enhanceError: (error: any) => ({
          ...error,
          correlationId: 'bundle-test-correlation-id',
          timestamp: new Date().toISOString(),
        }),
      }
    },

    // Messaging mocks
    {
      provide: 'EventPublisher',
      useValue: {
        publish: () => Promise.resolve(),
        publishBatch: () => Promise.resolve(),
        isConnected: () => true,
      }
    },
    {
      provide: 'EventFactory',
      useValue: {
        createHttpRequestEvent: () => ({
          type: 'http.request.started',
          timestamp: Date.now(),
          data: {},
        }),
        createHttpResponseEvent: () => ({
          type: 'http.response.completed',
          timestamp: Date.now(),
          data: {},
        }),
      }
    },

    // Caching mocks
    {
      provide: CacheService,
      useValue: {
        onModuleInit: () => Promise.resolve(),
        onModuleDestroy: () => Promise.resolve(),
        get: () => Promise.resolve({ hit: false, value: null }),
        set: () => Promise.resolve({ success: true }),
        delete: () => Promise.resolve({ success: true }),
        invalidate: () => Promise.resolve({ success: true }),
        invalidatePattern: () => Promise.resolve({ success: true }),
        getMetrics: () => ({
          totalOperations: 0,
          hits: 0,
          misses: 0,
          hitRatio: 0,
          averageResponseTime: 0,
          errors: 0,
          errorRate: 0
        }),
        getHealthStatus: () => ({
          status: 'connected',
          latency: 0,
          memoryUsage: '0MB'
        }),
        resetMetrics: mockFn(),
      }
    },

    // Required factories and configuration
    {
      provide: 'LOGGER_FACTORY',
      useValue: {
        createLogger: () => ({
          log: mockFn(),
          error: mockFn(),
          warn: mockFn(),
          debug: mockFn(),
        })
      }
    },
  ];
}