/**
 * Test Setup Utilities for Bundle Testing
 * 
 * Creates real HTTP client instances with actual dependencies
 * for testing in the webpack bundled environment
 */

import { HttpClientService } from '../../../src/client/http-client.service';
import { createBundleMocks } from './bundle-mocks';

/**
 * Create a test HTTP client with real dependencies
 * This uses minimal mocking to test actual integrations
 */
export async function createTestHttpClient(): Promise<HttpClientService> {
  // Create NestJS testing module with real services
  const { Test } = await import('@nestjs/testing');
  
  const module = await Test.createTestingModule({
    providers: [
      HttpClientService,
      // Use bundle-specific mocks (no Jest dependencies)
      ...createBundleMocks(),
      // HttpClientService optional constructor parameters 
      // These have default values but NestJS still tries to inject them
      {
        provide: 'ServiceClientConfig',
        useValue: {
          http2: true,
          timeout: { response: 5000, connect: 2000 },
          retries: { limit: 1, backoff: 100 },
          cache: { enabled: true, ttl: 300 },
        }
      },
      {
        provide: 'serviceContext',
        useValue: 'bundle-test-http-client'
      }
    ]
  }).compile();

  return module.get<HttpClientService>(HttpClientService);
}

/**
 * Create test assertion helpers
 */
export function createTestAssertions() {
  return {
    /**
     * Assert that a value is truthy with descriptive error
     */
    assertTrue(condition: any, message: string): void {
      if (!condition) {
        throw new Error(`Assertion failed: ${message}`);
      }
    },

    /**
     * Assert that two values are equal
     */
    assertEqual<T>(actual: T, expected: T, message?: string): void {
      if (actual !== expected) {
        throw new Error(
          `Assertion failed: ${message || 'Values not equal'}\n` +
          `  Expected: ${JSON.stringify(expected)}\n` +
          `  Actual: ${JSON.stringify(actual)}`
        );
      }
    },

    /**
     * Assert that an async function throws
     */
    async assertThrows(fn: () => Promise<any>, expectedError?: string): Promise<void> {
      try {
        await fn();
        throw new Error('Expected function to throw but it did not');
      } catch (error) {
        if (expectedError && !error.message.includes(expectedError)) {
          throw new Error(`Expected error containing "${expectedError}" but got: ${error.message}`);
        }
      }
    },

    /**
     * Assert that a value is defined
     */
    assertDefined<T>(value: T | undefined | null, message?: string): asserts value is T {
      if (value === undefined || value === null) {
        throw new Error(`Assertion failed: ${message || 'Value is undefined or null'}`);
      }
    }
  };
}