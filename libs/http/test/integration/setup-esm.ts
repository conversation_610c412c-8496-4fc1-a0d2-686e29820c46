/**
 * ESM Test Setup for Jest (2024)
 * 
 * Setup file for ESM testing with Jest experimental support
 */

// Import Jest globals for ESM
import { jest } from '@jest/globals';

// Set test environment
process.env.NODE_ENV = 'test';

// Global test configuration
global.console = {
  ...console,
  // Optionally reduce noise during tests
  // log: jest.fn(),
  // debug: jest.fn(),
};

// Longer timeout for integration tests
jest.setTimeout(30000);

// Global setup for ESM testing
beforeEach(() => {
  // Reset modules between tests (important for ESM)
  jest.resetModules();
});

afterEach(() => {
  // Clear all mocks after each test
  jest.clearAllMocks();
});