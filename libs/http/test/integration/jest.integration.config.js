module.exports = {
  rootDir: '../../',
  testEnvironment: 'node',
  roots: ['<rootDir>/test/integration'],
  testMatch: ['**/*.spec.ts'],
  transform: {
    '^.+\\.(t|j)sx?$': ['@swc/jest', {
      jsc: {
        parser: {
          syntax: 'typescript',
          decorators: true
        },
        target: 'es2022',
        transform: {
          legacyDecorator: true,
          decoratorMetadata: true
        }
      },
      module: {
        type: 'commonjs'
      }
    }]
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  collectCoverage: false,
  setupFilesAfterEnv: ['<rootDir>/test/integration/setup.ts'],
  moduleNameMapper: {
    '^@libs/(.*)$': '<rootDir>/../$1/src',
  },
  // Integration tests may take longer
  testTimeout: 30000,
  // Run integration tests sequentially to avoid port conflicts
  maxWorkers: 1,
  // Skip coverage for integration tests
  collectCoverageFrom: [],
};