/**
 * HTTP Got Library ESM Integration Tests (2024)
 * 
 * Tests using <PERSON><PERSON>'s experimental ESM support with jest.unstable_mockModule
 * Based on 2024 best practices for testing ESM modules like Got v14
 */

import { jest } from '@jest/globals';

// Step 1: Mock Got library BEFORE any imports using jest.unstable_mockModule
// This is the 2024 way to mock ESM modules in Jest
jest.unstable_mockModule('got', () => {
  const mockGot: any = jest.fn().mockImplementation((...args: any[]) => {
    const url = args[0];
    const options = args[1];
    // Mock different responses based on URL patterns
    if (url.includes('/health')) {
      return Promise.resolve({
        statusCode: 200,
        body: JSON.stringify({ status: 'ok', timestamp: Date.now() }),
        headers: { 'content-type': 'application/json' }
      });
    }
    
    if (url.includes('/error')) {
      const error = new Error('HTTP 500 Internal Server Error');
      (error as any).response = { statusCode: 500 };
      return Promise.reject(error);
    }
    
    if (url.includes('/timeout')) {
      const timeoutError = new Error('Timeout Error');
      (timeoutError as any).code = 'ETIMEDOUT';
      return Promise.reject(timeoutError);
    }
    
    if (url.includes('/retry')) {
      // For retry test, just return success
      return Promise.resolve({
        statusCode: 200,
        body: JSON.stringify({ success: true, retryTest: true }),
        headers: { 'content-type': 'application/json' }
      });
    }
    
    // Default successful response
    return Promise.resolve({
      statusCode: 200,
      body: JSON.stringify({ url, options }),
      headers: { 'content-type': 'application/json' }
    });
  });

  // Add HTTP method shortcuts
  mockGot.post = jest.fn().mockImplementation((...args: any[]) => {
    const url = args[0];
    const options = args[1];
    return Promise.resolve({
      statusCode: 201,
      body: JSON.stringify({ created: true, url, options }),
      headers: { 'content-type': 'application/json' }
    });
  });

  mockGot.get = mockGot;
  mockGot.put = mockGot;
  mockGot.delete = mockGot;

  return {
    default: mockGot
  };
});

// Step 2: Dynamic imports AFTER mocking (required for ESM)
const { default: got } = await import('got');

describe('HTTP Got Library ESM Integration Tests (2024)', () => {
  
  beforeEach(() => {
    jest.resetModules();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic HTTP Operations with Got ESM', () => {
    it('should make GET requests with Got library', async () => {
      const response = await got('http://test-service.local/health', {
        responseType: 'json',
        timeout: { response: 5000 }
      });

      expect(response.statusCode).toBe(200);
      expect(JSON.parse(response.body as string)).toEqual(
        expect.objectContaining({
          status: 'ok',
          timestamp: expect.any(Number)
        })
      );
    });

    it('should handle HTTP errors correctly', async () => {
      await expect(
        got('http://test-service.local/error', {
          responseType: 'json',
          timeout: { response: 5000 }
        })
      ).rejects.toThrow('HTTP 500 Internal Server Error');
    });

    it('should handle timeout errors', async () => {
      await expect(
        got('http://test-service.local/timeout', {
          timeout: { response: 1000 }
        })
      ).rejects.toThrow('Timeout Error');
    });

    it('should support HTTP/2 configuration', async () => {
      const response = await got('http://test-service.local/api/data', {
        http2: true,
        responseType: 'json',
        timeout: { response: 5000 }
      });

      expect(response.statusCode).toBe(200);
      
      // Verify Got was called with HTTP/2 configuration
      expect(got).toHaveBeenCalledWith(
        'http://test-service.local/api/data',
        expect.objectContaining({
          http2: true,
          responseType: 'json',
          timeout: { response: 5000 }
        })
      );
    });

    it('should handle POST requests with data', async () => {
      const postData = { name: 'test', value: 123 };
      
      const response = await got.post('http://test-service.local/create', {
        json: postData,
        responseType: 'json'
      });

      expect(response.statusCode).toBe(201);
      expect(got.post).toHaveBeenCalledWith(
        expect.stringContaining('/create'),
        expect.objectContaining({
          json: postData,
          responseType: 'json'
        })
      );
    });
  });

  describe('Advanced Got Features', () => {
    it('should verify retry configuration works', async () => {
      // This tests that Got's retry mechanism can be configured
      const response = await got('http://test-service.local/retry', {
        retry: {
          limit: 2,
          statusCodes: [500]
        },
        responseType: 'json'
      });

      expect(response.statusCode).toBe(200);
      expect(got).toHaveBeenCalledWith(
        expect.stringContaining('/retry'),
        expect.objectContaining({
          retry: {
            limit: 2,
            statusCodes: [500]
          }
        })
      );
    });

    it('should verify timeout configuration options', async () => {
      await got('http://test-service.local/slow', {
        timeout: {
          response: 30000,
          connect: 5000,
          request: 60000
        }
      });

      expect(got).toHaveBeenCalledWith(
        expect.stringContaining('/slow'),
        expect.objectContaining({
          timeout: {
            response: 30000,
            connect: 5000,
            request: 60000
          }
        })
      );
    });

    it('should verify custom headers are passed correctly', async () => {
      await got('http://test-service.local/auth', {
        headers: {
          'Authorization': 'Bearer token123',
          'X-Custom-Header': 'test-value',
          'Content-Type': 'application/json'
        }
      });

      expect(got).toHaveBeenCalledWith(
        expect.stringContaining('/auth'),
        expect.objectContaining({
          headers: {
            'Authorization': 'Bearer token123',
            'X-Custom-Header': 'test-value',
            'Content-Type': 'application/json'
          }
        })
      );
    });
  });

  describe('Got Library Configuration Verification', () => {
    it('should validate Got instance configuration options', async () => {
      // Test that Got can be configured with various enterprise-level options
      await got('http://enterprise-service.local/api/v1/data', {
        http2: true,
        timeout: { response: 5000, connect: 2000 },
        retry: { limit: 3, backoff: 'exponential' },
        headers: { 'User-Agent': 'Enterprise-HTTP-Client/1.0' },
        responseType: 'json',
        throwHttpErrors: true,
        allowGetBody: false,
        maxRedirects: 5
      });

      // Verify all configuration options are passed through
      expect(got).toHaveBeenCalledWith(
        'http://enterprise-service.local/api/v1/data',
        expect.objectContaining({
          http2: true,
          timeout: { response: 5000, connect: 2000 },
          retry: { limit: 3, backoff: 'exponential' },
          headers: { 'User-Agent': 'Enterprise-HTTP-Client/1.0' },
          responseType: 'json',
          throwHttpErrors: true,
          allowGetBody: false,
          maxRedirects: 5
        })
      );
    });
  });
});