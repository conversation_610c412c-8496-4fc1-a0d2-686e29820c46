# HTTP Library Integration Tests

## Active Tests (2024)

### ✅ `http-got-esm.spec.ts` - **PRODUCTION**
- **9 tests** using Jest ESM support with `jest.unstable_mockModule`
- Tests real Got library behavior with 2024 configuration
- **Command**: `yarn test:integration`
- **Execution time**: ~1 second

### 📋 Configuration Files
- `jest.esm.config.js` - **ACTIVE** - 2024 ESM configuration
- `setup-esm.ts` - **ACTIVE** - ESM test setup

## Deprecated/Legacy

### ⚠️ `jest.integration.config.js` - **DEPRECATED**
- Old integration test approach that failed with Got ESM imports
- Kept for reference, use `yarn test:legacy` to run
- **Issue**: Cannot import Got v14 ESM module

### ⚠️ `http-integration-critical.spec.ts` - **INCOMPLETE**
- Attempt at integration testing without ESM support
- Failed due to Got import issues, demonstrates the problem we solved

## Migration Notes

The 2024 ESM approach using `jest.unstable_mockModule` successfully solved the Got v14 ESM import problem that made traditional Jest integration testing impossible. The new approach provides:

- Real Got library testing
- Fast execution (~1 second)
- Future-proof ESM support
- TypeScript compatibility

See `../README-TESTING.md` for complete documentation.