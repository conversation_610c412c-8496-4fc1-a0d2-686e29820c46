/**
 * Jest ESM Configuration for 2024
 * 
 * Based on latest best practices for Jest ESM support
 * Requires Node.js --experimental-vm-modules flag
 */

export default {
  // Set Node.js environment for testing
  testEnvironment: 'node',
  
  // Set root directory correctly
  rootDir: '../../',
  
  // Look for ESM tests
  roots: ['<rootDir>/test/integration'],
  testMatch: ['**/test/integration/**/*esm*.spec.ts'],
  
  // ESM Configuration - Critical for Got library testing
  extensionsToTreatAsEsm: ['.ts'],
  globals: {
    'ts-jest': {
      useESM: true
    }
  },
  
  // Transform configuration for ESM + TypeScript
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      useESM: true,
      isolatedModules: true,
      tsconfig: {
        module: 'ES2022',
        target: 'ES2022',
        moduleResolution: 'node',
        allowSyntheticDefaultImports: true,
        esModuleInterop: true
      }
    }]
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node', 'mjs'],
  
  // Setup for ESM globals
  setupFilesAfterEnv: ['<rootDir>/test/integration/setup-esm.ts'],
  
  // Module name mapping for @libs paths
  moduleNameMapper: {
    '^@libs/(.*)$': '<rootDir>/../$1/src',
    // Handle ESM imports
    '^(\\.{1,2}/.*)\\.js$': '$1'
  },
  
  // ESM-specific configuration
  preset: undefined, // Don't use presets with ESM
  
  // Longer timeout for integration tests
  testTimeout: 30000,
  
  // Run tests sequentially to avoid conflicts
  maxWorkers: 1,
  
  // Don't collect coverage for integration tests
  collectCoverage: false,
  
  // Verbose output for debugging
  verbose: true,
  
  // Transform ignore patterns - allow transforming node_modules for ESM
  transformIgnorePatterns: [
    'node_modules/(?!(got|@sindresorhus|p-cancelable|@szmarczak|cacheable-lookup|responselike|lowercase-keys|mimic-response|normalize-url|@sindresorhus/is)/)'
  ]
};