/**
 * Critical HTTP Library Integration Tests
 * 
 * Tests ONLY the most critical integration scenarios that cannot be validated with mocks.
 * These tests use real HTTP calls but simplified setup to avoid complex DI issues.
 */

const nock = require('nock');

describe('HTTP Library Critical Integration Tests', () => {
  
  beforeAll(() => {
    // Enable network connections for nock
    nock.cleanAll();
  });

  afterAll(() => {
    nock.cleanAll();
  });

  beforeEach(() => {
    // Clean any existing nock interceptors
    nock.cleanAll();
  });

  describe('Basic HTTP Integration', () => {
    it('should make real HTTP requests with Got library', async () => {
      // Test that we can actually make HTTP requests with the real Got library
      // This is the most basic integration test - can we actually use Got?
      
      const scope = nock('http://test-service.local')
        .get('/health')
        .reply(200, { status: 'ok', timestamp: Date.now() });

      try {
        // Import Got directly to test basic functionality
        const got = require('got');
        
        const response = await got.get('http://test-service.local/health', {
          responseType: 'json',
          timeout: { response: 5000 }
        });

        expect(response.statusCode).toBe(200);
        expect(response.body).toEqual(expect.objectContaining({
          status: 'ok',
          timestamp: expect.any(Number)
        }));
        
        // Verify nock intercepted the request
        expect(scope.isDone()).toBe(true);
        
      } catch (error) {
        console.error('Basic Got integration failed:', error);
        throw error;
      }
    }, 10000);

    it('should handle HTTP errors correctly with Got', async () => {
      const scope = nock('http://test-service.local')
        .get('/error')
        .reply(500, { error: 'Internal Server Error' });

      try {
        const got = require('got');
        
        await expect(
          got.get('http://test-service.local/error', {
            responseType: 'json',
            timeout: { response: 5000 }
          })
        ).rejects.toThrow();
        
        expect(scope.isDone()).toBe(true);
        
      } catch (error) {
        // Expected behavior - should throw on 500 errors
      }
    });

    it('should work with HTTP/2 configuration', async () => {
      const scope = nock('http://test-service.local')
        .get('/http2-test')
        .reply(200, { http2: true });

      try {
        const got = require('got');
        
        const response = await got.get('http://test-service.local/http2-test', {
          http2: true,
          responseType: 'json',
          timeout: { response: 5000 }
        });

        expect(response.statusCode).toBe(200);
        expect(response.body.http2).toBe(true);
        expect(scope.isDone()).toBe(true);
        
      } catch (error) {
        console.error('HTTP/2 test failed:', error);
        throw error;
      }
    });
  });

  describe('Error Classification Integration', () => {
    it('should correctly classify different error types', async () => {
      // Test timeout errors
      const timeoutScope = nock('http://slow-service.local')
        .get('/slow')
        .delay(6000) // Longer than timeout
        .reply(200, { data: 'too slow' });

      try {
        const got = require('got');
        
        await expect(
          got.get('http://slow-service.local/slow', {
            timeout: { response: 1000 } // 1 second timeout
          })
        ).rejects.toThrow(/timeout/i);
        
      } catch (error) {
        // Expected timeout error
      }

      // Test network errors 
      const networkScope = nock('http://nonexistent-service.local')
        .get('/test')
        .replyWithError('ENOTFOUND');

      try {
        const got = require('got');
        
        await expect(
          got.get('http://nonexistent-service.local/test')
        ).rejects.toThrow();
        
      } catch (error) {
        // Expected network error
      }
    });
  });

  describe('Real HTTP Client Behavior', () => {
    it('should respect timeout configurations', async () => {
      const scope = nock('http://timeout-test.local')
        .get('/slow-endpoint')
        .delay(2000) // 2 second delay
        .reply(200, { data: 'finally' });

      const got = require('got');
      
      // Test that shorter timeout fails
      await expect(
        got.get('http://timeout-test.local/slow-endpoint', {
          timeout: { response: 500 } // 500ms timeout
        })
      ).rejects.toThrow(/timeout/i);

      // Clean up the scope
      nock.cleanAll();
    });

    it('should handle retry logic', async () => {
      let attemptCount = 0;
      
      const scope = nock('http://retry-test.local')
        .get('/flaky-endpoint')
        .times(3) // Allow 3 attempts
        .reply(() => {
          attemptCount++;
          if (attemptCount < 3) {
            return [500, { error: 'Server temporarily unavailable' }];
          }
          return [200, { success: true, attempts: attemptCount }];
        });

      const got = require('got');
      
      const response = await got.get('http://retry-test.local/flaky-endpoint', {
        responseType: 'json',
        retry: {
          limit: 2,
          statusCodes: [500]
        }
      });

      expect(response.statusCode).toBe(200);
      expect(response.body.success).toBe(true);
      expect(attemptCount).toBe(3); // Should have made 3 attempts total
    });
  });
});