{"name": "@libs/http", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rimraf dist && rimraf tsconfig.build.tsbuildinfo && rimraf tsconfig.tsbuildinfo && tsc -p tsconfig.build.json --listEmittedFiles", "lint": "echo 'Linting http...'", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest --config=jest.config.js", "test:integration": "NODE_OPTIONS='--experimental-vm-modules' jest --config=test/integration/jest.esm.config.js", "test:all": "npm run test:unit && npm run test:integration", "test:bundle": "webpack --config=webpack.test.js && node dist-test/bundle-tests.js", "test:legacy": "jest --config=test/integration/jest.integration.config.js", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:bundle:watch": "webpack --config=webpack.test.js --watch"}, "dependencies": {"@libs/observability": "file:../observability", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "got": "^14.4.2", "ioredis": "^5.4.1", "reflect-metadata": "^0.2.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0"}, "devDependencies": {"@nestjs/testing": "^10.2.10", "@swc/cli": "^0.1.62", "@swc/core": "^1.12.1", "@swc/jest": "^0.2.38", "@types/jest": "^30.0.0", "eslint": "*", "jest": "^30.0.0", "nock": "^14.0.5", "rimraf": "^3.0.2", "ts-jest": "^29.4.0", "typescript": "*", "undici": "^6.19.8"}}