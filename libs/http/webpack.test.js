const path = require('path');
const webpack = require('webpack');

module.exports = {
  // Target Node.js environment for testing
  target: 'node',
  mode: 'development',
  
  // Entry point for bundle tests
  entry: './test/bundle/bundle-test-runner.ts',
  
  // Output configuration
  output: {
    path: path.resolve(__dirname, 'dist-test'),
    filename: 'bundle-tests.js',
    libraryTarget: 'commonjs2',
  },
  
  // Module resolution - same as production services
  resolve: {
    extensions: ['.ts', '.js', '.mjs', '.json'],
    alias: {
      // BUNDLE OPTIMIZATION: Use src paths for 6x faster library change feedback
      '@libs/auth-common': path.resolve(__dirname, '../auth-common/src'),
      '@libs/http': path.resolve(__dirname, './src'),
      '@libs/observability': path.resolve(__dirname, '../observability/src'),
      '@libs/shared-types': path.resolve(__dirname, '../shared-types/src'),
      '@libs/caching': path.resolve(__dirname, '../caching/src'),
      '@libs/messaging': path.resolve(__dirname, '../messaging/src'),
      '@libs/resilience': path.resolve(__dirname, '../resilience/src'),
      '@libs/keycloak-client': path.resolve(__dirname, '../keycloak-client/src'),
      '@libs/error-handling': path.resolve(__dirname, '../error-handling/src'),
      '@libs/testing-utils': path.resolve(__dirname, '../testing-utils/src'),
    },
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },
  
  // Loader configuration
  resolveLoader: {
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },

  // Module rules
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        include: [
          path.resolve(__dirname, 'test'),
          path.resolve(__dirname, 'src'),
          path.resolve(__dirname, '../') // Include all library source
        ],
        use: [
          {
            loader: require.resolve('ts-loader'),
            options: {
              // Skip type checking for faster builds - focus on bundling
              transpileOnly: true,
              experimentalWatchApi: true,
              compilerOptions: {
                // Use consistent settings across all libraries
                module: 'CommonJS',
                moduleResolution: 'node',
                experimentalDecorators: true,
                emitDecoratorMetadata: true,
                sourceMap: true,
                skipLibCheck: true,
                target: 'ES2021',
              }
            }
          }
        ]
      },
      // Handle ESM modules
      {
        test: /\.mjs$/,
        type: 'javascript/esm',
      }
    ]
  },
  
  // External dependencies - bundle ESM modules like Got
  externals: [
    // Allow specific modules that need to be bundled
    function(context, request, callback) {
      // Bundle Got and related ESM modules
      if (/^(got|@sindresorhus|cacheable-request|keyv|responselike)/.test(request)) {
        return callback();
      }
      
      // Bundle all @libs/* modules
      if (/^@libs\//.test(request)) {
        return callback();
      }
      
      // External everything else
      if (/^[a-z@][a-z\-0-9@\/]*$/.test(request)) {
        return callback(null, 'commonjs ' + request);
      }
      
      callback();
    }
  ],

  // Plugins
  plugins: [
    // Ignore optional dependencies
    new webpack.IgnorePlugin({
      resourceRegExp: /^@nestjs\/(microservices|websockets)/,
    }),
    
    // Bundle test feedback
    new webpack.ProgressPlugin((percentage, message) => {
      if (percentage === 1) {
        console.log(`✅ HTTP Library: Bundle tests compiled`);
      }
    }),
  ],
  
  // Development settings
  devtool: 'source-map',
  
  // Stats configuration
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    warnings: true,
    errors: true,
    errorDetails: true,
    timings: true,
  },
  
  // Performance hints
  performance: {
    hints: false
  },
  
  // Node.js polyfills
  node: {
    __dirname: false,
    __filename: false,
  }
};