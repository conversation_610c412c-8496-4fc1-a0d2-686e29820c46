import { Test, TestingModule } from '@nestjs/testing';
import { Controller, Get, Post, UseGuards, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AuthCommonModule } from '../../src/auth.module';
import { JwtAuthGuard } from '../../src/guards/jwt-auth.guard';
import { RolesGuard } from '../../src/guards/roles.guard';
import { Roles } from '../../src/decorators/roles.decorator';
import { User } from '../../src/decorators/user.decorator';
import { UserContext } from '../../src/interfaces/user-context.interface';
import { MockFactory } from '@libs/testing-utils';

// Mock Keycloak client completely
jest.mock('@libs/keycloak-client', () => ({
  KeycloakClientModule: {
    forRoot: jest.fn().mockReturnValue({
      module: class MockKeycloakClientModule {},
      providers: [{
        provide: 'KeycloakClientService',
        useValue: {
          getSigningKey: jest.fn().mockResolvedValue({
            kid: 'test-kid',
            kty: 'RSA',
            n: 'test-modulus',
            e: 'AQAB'
          }),
          verifyToken: jest.fn().mockResolvedValue(true)
        }
      }],
      exports: ['KeycloakClientService']
    })
  },
  KeycloakClientService: jest.fn().mockImplementation(() => ({
    getSigningKey: jest.fn().mockResolvedValue({
      kid: 'test-kid',
      kty: 'RSA', 
      n: 'test-modulus',
      e: 'AQAB'
    }),
    verifyToken: jest.fn().mockResolvedValue(true)
  }))
}));

// Test controller to simulate real usage
@Controller('test')
class TestController {
  @Get('public')
  getPublicData() {
    return { message: 'Public data' };
  }

  @Get('protected')
  @UseGuards(JwtAuthGuard)
  getProtectedData(@User() user: UserContext) {
    return { message: 'Protected data', user: user };
  }

  @Get('admin-only')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  getAdminData(@User() user: UserContext) {
    return { message: 'Admin only data', user: user };
  }

  @Post('user-specific')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('user', 'admin')
  createUserData(@User('userId') userId: string, @User() fullUser: UserContext) {
    return { message: 'User specific data created', userId, fullUser };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [() => ({
        KEYCLOAK_BASE_URL: 'http://localhost:8080',
        KEYCLOAK_REALM_NAME: 'test-realm'
      })]
    }),
    AuthCommonModule.forRoot({
      loggerFactory: MockFactory.createLoggerFactory()
    })
  ],
  controllers: [TestController]
})
class TestAppModule {}

describe('Auth Common Integration Tests', () => {
  let app: TestingModule;
  let testController: TestController;

  beforeEach(async () => {
    // Set environment variables for the test
    process.env.KEYCLOAK_BASE_URL = 'http://localhost:8080';
    process.env.KEYCLOAK_REALM_NAME = 'test-realm';

    app = await Test.createTestingModule({
      imports: [TestAppModule]
    }).compile();

    testController = app.get<TestController>(TestController);
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
    delete process.env.KEYCLOAK_BASE_URL;
    delete process.env.KEYCLOAK_REALM_NAME;
  });

  describe('Module Registration', () => {
    it('should register AuthCommonModule successfully', () => {
      expect(app).toBeDefined();
      expect(testController).toBeDefined();
    });

    it('should provide all required guards', () => {
      const jwtAuthGuard = app.get(JwtAuthGuard);
      const rolesGuard = app.get(RolesGuard);

      expect(jwtAuthGuard).toBeDefined();
      expect(rolesGuard).toBeDefined();
    });
  });

  describe('Public Endpoints', () => {
    it('should allow access to public endpoints', () => {
      const result = testController.getPublicData();
      expect(result).toEqual({ message: 'Public data' });
    });
  });

  describe('Protected Endpoints with JWT Authentication', () => {
    it('should handle valid JWT authentication', async () => {
      // Mock request with valid user context
      const mockUser: UserContext = {
        userId: 'test-user-123',
        email: '<EMAIL>',
        username: 'testuser',
        roles: ['user'],
        resourceRoles: {},
        isEmailVerified: true
      };

      // In a real integration test, we would simulate the full request cycle
      // For now, we'll test the controller logic directly with a valid user
      const result = testController.getProtectedData(mockUser);

      expect(result).toEqual({
        message: 'Protected data',
        user: mockUser
      });
    });

    it('should extract specific user properties with decorators', () => {
      const mockUser: UserContext = {
        userId: 'specific-user-456',
        email: '<EMAIL>',
        username: 'specificuser',
        roles: ['user', 'admin'],
        resourceRoles: {
          'test-app': ['app-admin']
        },
        isEmailVerified: true
      };

      const result = testController.createUserData('specific-user-456', mockUser);

      expect(result).toEqual({
        message: 'User specific data created',
        userId: 'specific-user-456',
        fullUser: mockUser
      });
    });
  });

  describe('Role-Based Authorization', () => {
    it('should allow access for users with admin role', () => {
      const mockAdminUser: UserContext = {
        userId: 'admin-user-789',
        email: '<EMAIL>',
        username: 'admin',
        roles: ['admin', 'user'],
        resourceRoles: {},
        isEmailVerified: true
      };

      const result = testController.getAdminData(mockAdminUser);

      expect(result).toEqual({
        message: 'Admin only data',
        user: mockAdminUser
      });
    });

    it('should allow access for users with any required role', () => {
      const mockRegularUser: UserContext = {
        userId: 'regular-user-101',
        email: '<EMAIL>',
        username: 'regularuser',
        roles: ['user'], // Has 'user' role which is sufficient for @Roles('user', 'admin')
        resourceRoles: {},
        isEmailVerified: true
      };

      const result = testController.createUserData('regular-user-101', mockRegularUser);

      expect(result).toEqual({
        message: 'User specific data created',
        userId: 'regular-user-101',
        fullUser: mockRegularUser
      });
    });
  });

  describe('Complex User Context Scenarios', () => {
    it('should handle users with multiple resource roles', () => {
      const mockComplexUser: UserContext = {
        userId: 'complex-user-202',
        email: '<EMAIL>',
        username: 'complexuser',
        name: 'Complex User',
        roles: ['user', 'moderator'],
        resourceRoles: {
          'app-1': ['admin', 'editor'],
          'app-2': ['viewer'],
          'management': ['full-access']
        },
        isEmailVerified: true
      };

      const result = testController.getProtectedData(mockComplexUser);

      expect(result.user.resourceRoles).toEqual({
        'app-1': ['admin', 'editor'],
        'app-2': ['viewer'],
        'management': ['full-access']
      });
    });

    it('should handle users with minimal context', () => {
      const mockMinimalUser: UserContext = {
        userId: 'minimal-user-303',
        email: '<EMAIL>',
        roles: [],
        resourceRoles: {}
      };

      const result = testController.getProtectedData(mockMinimalUser);

      expect(result.user.roles).toEqual([]);
      expect(result.user.resourceRoles).toEqual({});
      expect(result.user.username).toBeUndefined();
    });
  });

  describe('Decorator Integration', () => {
    it('should support multiple user decorators in same endpoint', () => {
      const mockUser: UserContext = {
        userId: 'decorator-test-404',
        email: '<EMAIL>',
        username: 'decoratoruser',
        roles: ['admin'],
        resourceRoles: {
          'test': ['full-access']
        },
        isEmailVerified: true
      };

      const result = testController.createUserData('decorator-test-404', mockUser);

      // Verify both @User('userId') and @User() decorators work
      expect(result.userId).toBe('decorator-test-404');
      expect(result.fullUser).toEqual(mockUser);
    });
  });

  describe('Error Scenarios', () => {
    it('should handle invalid user context gracefully', () => {
      // Test with null user context
      expect(() => {
        testController.getProtectedData(null as any);
      }).not.toThrow();

      // In real scenario, guards would prevent reaching controller
      // but controller should handle gracefully if it does
    });

    it('should handle missing optional user properties', () => {
      const mockUserMissingProps: UserContext = {
        userId: 'missing-props-505',
        email: '<EMAIL>',
        roles: ['user'],
        resourceRoles: {}
        // Missing username, name, isEmailVerified
      };

      const result = testController.getProtectedData(mockUserMissingProps);

      expect(result.user.username).toBeUndefined();
      expect(result.user.name).toBeUndefined();
      expect(result.user.isEmailVerified).toBeUndefined();
    });
  });

  describe('Configuration Integration', () => {
    it('should work with environment variable configuration', () => {
      // This tests that the module correctly picks up environment variables
      // The fact that the module initializes successfully indicates this works
      expect(process.env.KEYCLOAK_BASE_URL).toBe('http://localhost:8080');
      expect(process.env.KEYCLOAK_REALM_NAME).toBe('test-realm');
    });

    it('should work with ConfigModule integration', async () => {
      // Test that ConfigModule values are properly injected
      const configService = app.get('ConfigService');
      expect(configService).toBeDefined();
    });
  });
});