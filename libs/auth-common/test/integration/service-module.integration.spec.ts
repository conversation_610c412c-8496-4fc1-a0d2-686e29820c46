import { Test, TestingModule } from '@nestjs/testing';
import { Controller, Get, UseGuards, Module, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthCommonModule } from '../../src/auth.module';
import { RolesGuard } from '../../src/guards/roles.guard';
import { UserContextGuard } from '../../src/guards/user-context.guard';
import { Roles } from '../../src/decorators/roles.decorator';
import { User } from '../../src/decorators/user.decorator';
import { UserContext } from '../../src/interfaces/user-context.interface';

// Test controller for service-only module
@Controller('service')
class ServiceTestController {
  @Get('user-data')
  @UseGuards(UserContextGuard)
  getUserData(@User() user: UserContext) {
    return { data: 'User specific data', user };
  }

  @Get('admin-data')
  @UseGuards(UserContextGuard, RolesGuard)
  @Roles('admin')
  getAdminData(@User() user: UserContext) {
    return { data: 'Admin only data', user };
  }

  @Get('multi-role-data')
  @UseGuards(UserContextGuard, RolesGuard)
  @Roles('manager', 'supervisor', 'admin')
  getMultiRoleData(@User('roles') userRoles: string[], @User('userId') userId: string) {
    return { data: 'Multi-role data', userRoles, userId };
  }
}

@Module({
  imports: [AuthCommonModule.forServices()],
  controllers: [ServiceTestController]
})
class ServiceTestAppModule {}

describe('Auth Common Service Module Integration Tests', () => {
  let app: TestingModule;
  let serviceController: ServiceTestController;
  let rolesGuard: RolesGuard;
  let userContextGuard: UserContextGuard;

  beforeEach(async () => {
    app = await Test.createTestingModule({
      imports: [ServiceTestAppModule]
    }).compile();

    serviceController = app.get<ServiceTestController>(ServiceTestController);
    rolesGuard = app.get<RolesGuard>(RolesGuard);
    userContextGuard = app.get<UserContextGuard>(UserContextGuard);
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  describe('Service Module Registration', () => {
    it('should register service-only module successfully', () => {
      expect(app).toBeDefined();
      expect(serviceController).toBeDefined();
    });

    it('should provide only service guards (not JWT components)', () => {
      expect(rolesGuard).toBeDefined();
      expect(userContextGuard).toBeDefined();

      // JWT components should not be available in forServices() mode
      expect(() => app.get('JwtStrategy')).toThrow();
    });
  });

  describe('User Context Guard Integration', () => {
    it('should work with valid user context in request', () => {
      const mockUser: UserContext = {
        userId: 'service-user-123',
        email: '<EMAIL>',
        username: 'serviceuser',
        roles: ['user'],
        resourceRoles: {}
      };

      // In real scenario, UserContextGuard would extract user from request
      const result = serviceController.getUserData(mockUser);

      expect(result).toEqual({
        data: 'User specific data',
        user: mockUser
      });
    });

    it('should handle user context extraction correctly', () => {
      const mockUser: UserContext = {
        userId: 'context-user-456',
        email: '<EMAIL>',
        username: 'contextuser',
        roles: ['manager', 'user'],
        resourceRoles: {
          'department-a': ['lead'],
          'project-x': ['contributor']
        }
      };

      const result = serviceController.getMultiRoleData(['manager', 'user'], 'context-user-456');

      expect(result).toEqual({
        data: 'Multi-role data',
        userRoles: ['manager', 'user'],
        userId: 'context-user-456'
      });
    });
  });

  describe('Roles Guard Integration in Service Context', () => {
    it('should enforce role requirements correctly', () => {
      const mockAdminUser: UserContext = {
        userId: 'admin-service-789',
        email: '<EMAIL>',
        username: 'adminuser',
        roles: ['admin', 'user'],
        resourceRoles: {}
      };

      const result = serviceController.getAdminData(mockAdminUser);

      expect(result).toEqual({
        data: 'Admin only data',
        user: mockAdminUser
      });
    });

    it('should handle multiple role requirements', () => {
      const mockManagerUser: UserContext = {
        userId: 'manager-service-101',
        email: '<EMAIL>',
        username: 'manageruser',
        roles: ['manager', 'user'], // Has one of the required roles
        resourceRoles: {}
      };

      const result = serviceController.getMultiRoleData(['manager', 'user'], 'manager-service-101');

      expect(result.userRoles).toContain('manager');
      expect(result.userId).toBe('manager-service-101');
    });
  });

  describe('Guard Interaction and Execution Order', () => {
    it('should execute UserContextGuard before RolesGuard', async () => {
      // Mock execution context
      const mockExecutionContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: {
              userId: 'test-user',
              email: '<EMAIL>',
              roles: ['admin'],
              resourceRoles: {}
            }
          })
        }),
        getType: jest.fn().mockReturnValue('http')
      } as any;

      // Mock Reflector for RolesGuard
      const mockReflector = app.get(Reflector);
      jest.spyOn(mockReflector, 'getAllAndOverride').mockReturnValue(['admin']);

      // Test that both guards can access user context
      const userContextResult = userContextGuard.canActivate(mockExecutionContext);
      const rolesGuardResult = rolesGuard.canActivate(mockExecutionContext);

      expect(userContextResult).toBe(true);
      expect(rolesGuardResult).toBe(true);
    });

    it('should handle guard execution with missing user context', async () => {
      const mockExecutionContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: null // No user context
          })
        }),
        getType: jest.fn().mockReturnValue('http')
      } as any;

      const mockReflector = app.get(Reflector);
      jest.spyOn(mockReflector, 'getAllAndOverride').mockReturnValue(['admin']);

      // UserContextGuard should handle missing user
      expect(() => {
        userContextGuard.canActivate(mockExecutionContext);
      }).toThrow('User context not found');

      // RolesGuard should also handle missing user
      expect(() => {
        rolesGuard.canActivate(mockExecutionContext);
      }).toThrow('User context not found');
    });
  });

  describe('Decorator Integration in Service Module', () => {
    it('should support @Roles decorator with RolesGuard', () => {
      const mockReflector = app.get(Reflector);
      
      // Test that @Roles decorator sets metadata correctly
      const mockExecutionContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: {
              userId: 'decorator-test',
              email: '<EMAIL>',
              roles: ['admin'],
              resourceRoles: {}
            }
          })
        })
      } as any;

      jest.spyOn(mockReflector, 'getAllAndOverride').mockReturnValue(['admin']);

      const result = rolesGuard.canActivate(mockExecutionContext);
      expect(result).toBe(true);
      expect(mockReflector.getAllAndOverride).toHaveBeenCalledWith('roles', [
        mockExecutionContext.getHandler(),
        mockExecutionContext.getClass()
      ]);
    });

    it('should support @User decorator parameter extraction', () => {
      const complexUser: UserContext = {
        userId: 'complex-decorator-test',
        email: '<EMAIL>',
        username: 'complexuser',
        name: 'Complex User',
        roles: ['supervisor', 'team-lead'],
        resourceRoles: {
          'team-alpha': ['manager'],
          'project-beta': ['coordinator'],
          'department-gamma': ['supervisor']
        },
        isEmailVerified: true
      };

      // Test multiple decorator extractions
      const userIdResult = serviceController.getMultiRoleData(['supervisor', 'team-lead'], 'complex-decorator-test');

      expect(userIdResult.userRoles).toEqual(['supervisor', 'team-lead']);
      expect(userIdResult.userId).toBe('complex-decorator-test');
    });
  });

  describe('Service Module Configuration', () => {
    it('should not include JWT-related providers', () => {
      // Verify that service module doesn't include JWT components
      const moduleProviders = app['container']['modules'];
      
      // Should not have JWT strategy or related components
      expect(() => app.get('JwtStrategy')).toThrow();
      expect(() => app.get('JwtModule')).toThrow();
      expect(() => app.get('PassportModule')).toThrow();
    });

    it('should work independently without Keycloak integration', () => {
      // Service module should work without external auth providers
      expect(rolesGuard).toBeDefined();
      expect(userContextGuard).toBeDefined();
      
      // Should not depend on KeycloakClientService
      expect(() => app.get('KeycloakClientService')).toThrow();
    });
  });

  describe('Cross-Service Communication Patterns', () => {
    it('should support service-to-service user context propagation', () => {
      // Simulate user context passed between services
      const propagatedUser: UserContext = {
        userId: 'propagated-user-505',
        email: '<EMAIL>',
        username: 'propagateduser',
        roles: ['service-account', 'api-client'],
        resourceRoles: {
          'service-a': ['read', 'write'],
          'service-b': ['read'],
          'admin-api': ['full-access']
        },
        isEmailVerified: true
      };

      const result = serviceController.getUserData(propagatedUser);

      expect(result.user.roles).toContain('service-account');
      expect(result.user.resourceRoles?.['service-a']).toContain('write');
    });

    it('should handle service accounts with resource-specific roles', () => {
      const serviceAccountUser: UserContext = {
        userId: 'service-account-606',
        email: '<EMAIL>',
        username: 'service-account',
        roles: ['service'],
        resourceRoles: {
          'user-management': ['create', 'read', 'update'],
          'billing-service': ['read'],
          'notification-service': ['send']
        }
      };

      // Test that service accounts work with role guards
      const mockExecutionContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: serviceAccountUser
          })
        })
      } as any;

      const mockReflector = app.get(Reflector);
      jest.spyOn(mockReflector, 'getAllAndOverride').mockReturnValue(['service']);

      const result = rolesGuard.canActivate(mockExecutionContext);
      expect(result).toBe(true);
    });
  });
});