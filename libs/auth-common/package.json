{"name": "@libs/auth-common", "version": "1.0.0", "description": "Shared authentication and authorization utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rimraf dist && tsc -p tsconfig.build.json", "build:watch": "tsc -p tsconfig.build.json --watch", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/passport": "^10.0.3", "@nestjs/jwt": "^10.2.0", "@libs/observability": "file:../observability", "@libs/keycloak-client": "file:../keycloak-client", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "reflect-metadata": "^0.2.0"}, "devDependencies": {"@nestjs/testing": "^10.0.0", "@types/passport-jwt": "^4.0.1", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@libs/testing-utils": "file:../testing-utils", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "*", "rimraf": "^4.4.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"tsconfig": "test/tsconfig.json"}]}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "moduleNameMapper": {"^@libs/observability$": "<rootDir>/../observability/src", "^@libs/observability/(.*)$": "<rootDir>/../observability/src/$1", "^@libs/keycloak-client$": "<rootDir>/../keycloak-client/src", "^@libs/keycloak-client/(.*)$": "<rootDir>/../keycloak-client/src/$1", "^@libs/http$": "<rootDir>/../http/src", "^@libs/http/(.*)$": "<rootDir>/../http/src/$1", "^@libs/testing-utils$": "<rootDir>/../testing-utils/src", "^@libs/testing-utils/(.*)$": "<rootDir>/../testing-utils/src/$1", "^got$": "<rootDir>/../../node_modules/got/dist/source/index.js"}, "transformIgnorePatterns": ["node_modules/(?!(got|@sindresorhus|@szmarczak|cacheable-request)/)"]}}