{"numFailedTestSuites": 3, "numFailedTests": 21, "numPassedTestSuites": 2, "numPassedTests": 54, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 1, "numTodoTests": 0, "numTotalTestSuites": 5, "numTotalTests": 75, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750370063526, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Roles Decorator Unit Tests", "ROLES_KEY"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests ROLES_KEY should have correct value", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should have correct value"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "ROLES_KEY"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests ROLES_KEY should be a string constant", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be a string constant"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should call SetMetadata with correct parameters for single role", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call SetMetadata with correct parameters for single role"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should call SetMetadata with correct parameters for multiple roles", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call SetMetadata with correct parameters for multiple roles"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle empty roles array", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle empty roles array"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle roles with special characters", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle roles with special characters"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle duplicate roles", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle duplicate roles"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle long role names", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle long role names"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle roles with numbers", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle roles with numbers"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should preserve role order", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should preserve role order"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle case-sensitive roles", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle case-sensitive roles"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Roles Decorator"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Roles Decorator should handle empty string roles", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle empty string roles"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Decorator Integration"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Decorator Integration should be usable as class decorator", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should be usable as class decorator"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Decorator Integration"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Decorator Integration should be usable as method decorator", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should be usable as method decorator"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Decorator Integration"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Decorator Integration should work with multiple decorator calls", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should work with multiple decorator calls"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Type Safety"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Type Safety should accept string parameters", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should accept string parameters"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Type Safety"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Type Safety should work with spread operator", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should work with spread operator"}, {"ancestorTitles": ["Roles Decorator Unit Tests", "Type Safety"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Roles Decorator Unit Tests Type Safety should handle mixed role sources", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle mixed role sources"}], "endTime": 1750370068685, "message": "", "name": "/root/code/polyrepo/libs/auth-common/test/unit/roles.decorator.spec.ts", "startTime": 1750370064325, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["User Decorator Unit Tests", "Full User Context Extraction"], "duration": 9, "failureDetails": [{"matcherResult": {"expected": {"userId": "user-123", "email": "<EMAIL>", "username": "testuser", "roles": ["admin", "user"], "resourceRoles": {"test-client": ["client-admin"]}, "isEmailVerified": true}, "message": "expect(received).toEqual(expected) // deep equality\n\nExpected: {\"email\": \"<EMAIL>\", \"isEmailVerified\": true, \"resourceRoles\": {\"test-client\": [\"client-admin\"]}, \"roles\": [\"admin\", \"user\"], \"userId\": \"user-123\", \"username\": \"testuser\"}\nReceived: [Function anonymous]", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"email\": \"<EMAIL>\", \"isEmailVerified\": true, \"resourceRoles\": {\"test-client\": [\"client-admin\"]}, \"roles\": [\"admin\", \"user\"], \"userId\": \"user-123\", \"username\": \"testuser\"}\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:50:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Full User Context Extraction should return full user context when no data parameter is provided", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return full user context when no data parameter is provided"}, {"ancestorTitles": ["User Decorator Unit Tests", "Full User Context Extraction"], "duration": 1, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeNull()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeNull()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:58:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Full User Context Extraction should return null when user context is not present", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return null when user context is not present"}, {"ancestorTitles": ["User Decorator Unit Tests", "Full User Context Extraction"], "duration": 1, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeUndefined()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeUndefined()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:66:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Full User Context Extraction should return undefined when user context is undefined", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return undefined when user context is undefined"}, {"ancestorTitles": ["User Decorator Unit Tests", "Specific Property Extraction"], "duration": 1, "failureDetails": [{"matcherResult": {"expected": "user-456", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"user-456\"\nReceived: [Function anonymous]", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"user-456\"\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:84:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Specific Property Extraction should return userId when data parameter is \"userId\"", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return userId when data parameter is \"userId\""}, {"ancestorTitles": ["User Decorator Unit Tests", "Specific Property Extraction"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": "<EMAIL>", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"<EMAIL>\"\nReceived: [Function anonymous]", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"<EMAIL>\"\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:100:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Specific Property Extraction should return email when data parameter is \"email\"", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return email when data parameter is \"email\""}, {"ancestorTitles": ["User Decorator Unit Tests", "Specific Property Extraction"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": "myusername", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"myusername\"\nReceived: [Function anonymous]", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"myusername\"\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:116:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Specific Property Extraction should return username when data parameter is \"username\"", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return username when data parameter is \"username\""}, {"ancestorTitles": ["User Decorator Unit Tests", "Specific Property Extraction"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": ["admin", "moderator", "user"], "message": "expect(received).toEqual(expected) // deep equality\n\nExpected: [\"admin\", \"moderator\", \"user\"]\nReceived: [Function anonymous]", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\nExpected: [\"admin\", \"moderator\", \"user\"]\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:131:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Specific Property Extraction should return roles when data parameter is \"roles\"", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return roles when data parameter is \"roles\""}, {"ancestorTitles": ["User Decorator Unit Tests", "Specific Property Extraction"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": {"app-client": ["app-admin"], "other-client": ["read-only"]}, "message": "expect(received).toEqual(expected) // deep equality\n\nExpected: {\"app-client\": [\"app-admin\"], \"other-client\": [\"read-only\"]}\nReceived: [Function anonymous]", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"app-client\": [\"app-admin\"], \"other-client\": [\"read-only\"]}\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:149:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Specific Property Extraction should return resourceRoles when data parameter is \"resourceRoles\"", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return resourceRoles when data parameter is \"resourceRoles\""}, {"ancestorTitles": ["User Decorator Unit Tests", "Specific Property Extraction"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": true, "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: true\nReceived: [Function anonymous]", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: true\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:168:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Specific Property Extraction should return isEmailVerified when data parameter is \"isEmailVerified\"", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return isEmailVerified when data parameter is \"isEmailVerified\""}, {"ancestorTitles": ["User Decorator Unit Tests", "Property Access with Missing User"], "duration": 0, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeUndefined()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeUndefined()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:178:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Property Access with Missing User should return undefined when accessing property on null user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return undefined when accessing property on null user"}, {"ancestorTitles": ["User Decorator Unit Tests", "Property Access with Missing User"], "duration": 0, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeUndefined()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeUndefined()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:186:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Property Access with Missing User should return undefined when accessing property on undefined user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return undefined when accessing property on undefined user"}, {"ancestorTitles": ["User Decorator Unit Tests", "Property Access with Missing Properties"], "duration": 1, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeUndefined()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeUndefined()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:203:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Property Access with Missing Properties should return undefined when property does not exist on user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return undefined when property does not exist on user"}, {"ancestorTitles": ["User Decorator Unit Tests", "Property Access with Missing Properties"], "duration": 0, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeUndefined()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeUndefined()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:220:33)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Property Access with Missing Properties should return undefined when accessing non-existent property", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return undefined when accessing non-existent property"}, {"ancestorTitles": ["User Decorator Unit Tests", "Complex User Context"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": {"userId": "complex-user-123", "email": "<EMAIL>", "name": "Complex User", "username": "complexuser", "roles": ["admin", "moderator", "user"], "resourceRoles": {"primary-app": ["admin", "editor"], "secondary-app": ["viewer"], "management-console": ["full-access"]}, "isEmailVerified": true}, "message": "expect(received).toEqual(expected) // deep equality\n\nExpected: {\"email\": \"<EMAIL>\", \"isEmailVerified\": true, \"name\": \"Complex User\", \"resourceRoles\": {\"management-console\": [\"full-access\"], \"primary-app\": [\"admin\", \"editor\"], \"secondary-app\": [\"viewer\"]}, \"roles\": [\"admin\", \"moderator\", \"user\"], \"userId\": \"complex-user-123\", \"username\": \"complexuser\"}\nReceived: [Function anonymous]", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"email\": \"<EMAIL>\", \"isEmailVerified\": true, \"name\": \"Complex User\", \"resourceRoles\": {\"management-console\": [\"full-access\"], \"primary-app\": [\"admin\", \"editor\"], \"secondary-app\": [\"viewer\"]}, \"roles\": [\"admin\", \"moderator\", \"user\"], \"userId\": \"complex-user-123\", \"username\": \"complexuser\"}\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:243:53)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Complex User Context should handle complex user context with all properties", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle complex user context with all properties"}, {"ancestorTitles": ["User Decorator Unit Tests", "Complex User Context"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": {"userId": "minimal-user", "email": "<EMAIL>", "roles": [], "resourceRoles": {}}, "message": "expect(received).toEqual(expected) // deep equality\n\nExpected: {\"email\": \"<EMAIL>\", \"resourceRoles\": {}, \"roles\": [], \"userId\": \"minimal-user\"}\nReceived: [Function anonymous]", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"email\": \"<EMAIL>\", \"resourceRoles\": {}, \"roles\": [], \"userId\": \"minimal-user\"}\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:269:53)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Complex User Context should handle minimal user context", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle minimal user context"}, {"ancestorTitles": ["User Decorator Unit Tests", "ExecutionContext Integration"], "duration": 0, "failureDetails": [{"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalled()\n\nExpected number of calls: >= 1\nReceived number of calls:    0", "pass": false}}], "failureMessages": ["Error: expect(jest.fn()).toHaveBeenCalled()\n\nExpected number of calls: >= 1\nReceived number of calls:    0\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:290:49)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests ExecutionContext Integration should call switchToHttp correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should call switchToHttp correctly"}, {"ancestorTitles": ["User Decorator Unit Tests", "ExecutionContext Integration"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": "ws-user", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"ws-user\"\nReceived: [Function anonymous]", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"ws-user\"\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:313:22)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests ExecutionContext Integration should work with different execution context types", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should work with different execution context types"}, {"ancestorTitles": ["User Decorator Unit Tests", "Edge Cases"], "duration": 1, "failureDetails": [{"matcherResult": {"message": "expect(received).toBeNull()\n\nReceived: [Function anonymous]", "pass": false}}], "failureMessages": ["Error: expect(received).toBeNull()\n\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:329:51)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Edge Cases should handle user context with null properties", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user context with null properties"}, {"ancestorTitles": ["User Decorator Unit Tests", "Edge Cases"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": [], "message": "expect(received).toEqual(expected) // deep equality\n\nExpected: []\nReceived: [Function anonymous]", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\nExpected: []\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:344:51)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Edge Cases should handle user context with empty arrays and objects", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user context with empty arrays and objects"}, {"ancestorTitles": ["User Decorator Unit Tests", "Edge Cases"], "duration": 0, "failureDetails": [{"matcherResult": {"expected": "", "message": "expect(received).toBe(expected) // Object.is equality\n\nExpected: \"\"\nReceived: [Function anonymous]", "name": "toBe", "pass": false}}], "failureMessages": ["Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: \"\"\nReceived: [Function anonymous]\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts:360:51)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "User Decorator Unit Tests Edge Cases should handle falsy values in user properties", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle falsy values in user properties"}], "endTime": 1750370069088, "message": "  ● User Decorator Unit Tests › Full User Context Extraction › should return full user context when no data parameter is provided\n\n    expect(received).toEqual(expected) // deep equality\n\n    Expected: {\"email\": \"<EMAIL>\", \"isEmailVerified\": true, \"resourceRoles\": {\"test-client\": [\"client-admin\"]}, \"roles\": [\"admin\", \"user\"], \"userId\": \"user-123\", \"username\": \"testuser\"}\n    Received: [Function anonymous]\n\n      48 |       const decoratorFunction = User(undefined, mockExecutionContext);\n      49 |\n    > 50 |       expect(decoratorFunction).toEqual(mockUser);\n         |                                 ^\n      51 |     });\n      52 |\n      53 |     it('should return null when user context is not present', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:50:33)\n\n  ● User Decorator Unit Tests › Full User Context Extraction › should return null when user context is not present\n\n    expect(received).toBeNull()\n\n    Received: [Function anonymous]\n\n      56 |       const decoratorFunction = User(undefined, mockExecutionContext);\n      57 |\n    > 58 |       expect(decoratorFunction).toBeNull();\n         |                                 ^\n      59 |     });\n      60 |\n      61 |     it('should return undefined when user context is undefined', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:58:33)\n\n  ● User Decorator Unit Tests › Full User Context Extraction › should return undefined when user context is undefined\n\n    expect(received).toBeUndefined()\n\n    Received: [Function anonymous]\n\n      64 |       const decoratorFunction = User(undefined, mockExecutionContext);\n      65 |\n    > 66 |       expect(decoratorFunction).toBeUndefined();\n         |                                 ^\n      67 |     });\n      68 |   });\n      69 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:66:33)\n\n  ● User Decorator Unit Tests › Specific Property Extraction › should return userId when data parameter is \"userId\"\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"user-456\"\n    Received: [Function anonymous]\n\n      82 |       const decoratorFunction = User('userId', mockExecutionContext);\n      83 |\n    > 84 |       expect(decoratorFunction).toBe('user-456');\n         |                                 ^\n      85 |     });\n      86 |\n      87 |     it('should return email when data parameter is \"email\"', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:84:33)\n\n  ● User Decorator Unit Tests › Specific Property Extraction › should return email when data parameter is \"email\"\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"<EMAIL>\"\n    Received: [Function anonymous]\n\n       98 |       const decoratorFunction = User('email', mockExecutionContext);\n       99 |\n    > 100 |       expect(decoratorFunction).toBe('<EMAIL>');\n          |                                 ^\n      101 |     });\n      102 |\n      103 |     it('should return username when data parameter is \"username\"', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:100:33)\n\n  ● User Decorator Unit Tests › Specific Property Extraction › should return username when data parameter is \"username\"\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"myusername\"\n    Received: [Function anonymous]\n\n      114 |       const decoratorFunction = User('username', mockExecutionContext);\n      115 |\n    > 116 |       expect(decoratorFunction).toBe('myusername');\n          |                                 ^\n      117 |     });\n      118 |\n      119 |     it('should return roles when data parameter is \"roles\"', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:116:33)\n\n  ● User Decorator Unit Tests › Specific Property Extraction › should return roles when data parameter is \"roles\"\n\n    expect(received).toEqual(expected) // deep equality\n\n    Expected: [\"admin\", \"moderator\", \"user\"]\n    Received: [Function anonymous]\n\n      129 |       const decoratorFunction = User('roles', mockExecutionContext);\n      130 |\n    > 131 |       expect(decoratorFunction).toEqual(['admin', 'moderator', 'user']);\n          |                                 ^\n      132 |     });\n      133 |\n      134 |     it('should return resourceRoles when data parameter is \"resourceRoles\"', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:131:33)\n\n  ● User Decorator Unit Tests › Specific Property Extraction › should return resourceRoles when data parameter is \"resourceRoles\"\n\n    expect(received).toEqual(expected) // deep equality\n\n    Expected: {\"app-client\": [\"app-admin\"], \"other-client\": [\"read-only\"]}\n    Received: [Function anonymous]\n\n      147 |       const decoratorFunction = User('resourceRoles', mockExecutionContext);\n      148 |\n    > 149 |       expect(decoratorFunction).toEqual({\n          |                                 ^\n      150 |         'app-client': ['app-admin'],\n      151 |         'other-client': ['read-only']\n      152 |       });\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:149:33)\n\n  ● User Decorator Unit Tests › Specific Property Extraction › should return isEmailVerified when data parameter is \"isEmailVerified\"\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: true\n    Received: [Function anonymous]\n\n      166 |       const decoratorFunction = User('isEmailVerified', mockExecutionContext);\n      167 |\n    > 168 |       expect(decoratorFunction).toBe(true);\n          |                                 ^\n      169 |     });\n      170 |   });\n      171 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:168:33)\n\n  ● User Decorator Unit Tests › Property Access with Missing User › should return undefined when accessing property on null user\n\n    expect(received).toBeUndefined()\n\n    Received: [Function anonymous]\n\n      176 |       const decoratorFunction = User('userId', mockExecutionContext);\n      177 |\n    > 178 |       expect(decoratorFunction).toBeUndefined();\n          |                                 ^\n      179 |     });\n      180 |\n      181 |     it('should return undefined when accessing property on undefined user', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:178:33)\n\n  ● User Decorator Unit Tests › Property Access with Missing User › should return undefined when accessing property on undefined user\n\n    expect(received).toBeUndefined()\n\n    Received: [Function anonymous]\n\n      184 |       const decoratorFunction = User('email', mockExecutionContext);\n      185 |\n    > 186 |       expect(decoratorFunction).toBeUndefined();\n          |                                 ^\n      187 |     });\n      188 |   });\n      189 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:186:33)\n\n  ● User Decorator Unit Tests › Property Access with Missing Properties › should return undefined when property does not exist on user\n\n    expect(received).toBeUndefined()\n\n    Received: [Function anonymous]\n\n      201 |       const decoratorFunction = User('email', mockExecutionContext);\n      202 |\n    > 203 |       expect(decoratorFunction).toBeUndefined();\n          |                                 ^\n      204 |     });\n      205 |\n      206 |     it('should return undefined when accessing non-existent property', () => {\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:203:33)\n\n  ● User Decorator Unit Tests › Property Access with Missing Properties › should return undefined when accessing non-existent property\n\n    expect(received).toBeUndefined()\n\n    Received: [Function anonymous]\n\n      218 |       const decoratorFunction = User('nonExistentProperty' as any, mockExecutionContext);\n      219 |\n    > 220 |       expect(decoratorFunction).toBeUndefined();\n          |                                 ^\n      221 |     });\n      222 |   });\n      223 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:220:33)\n\n  ● User Decorator Unit Tests › Complex User Context › should handle complex user context with all properties\n\n    expect(received).toEqual(expected) // deep equality\n\n    Expected: {\"email\": \"<EMAIL>\", \"isEmailVerified\": true, \"name\": \"Complex User\", \"resourceRoles\": {\"management-console\": [\"full-access\"], \"primary-app\": [\"admin\", \"editor\"], \"secondary-app\": [\"viewer\"]}, \"roles\": [\"admin\", \"moderator\", \"user\"], \"userId\": \"complex-user-123\", \"username\": \"complexuser\"}\n    Received: [Function anonymous]\n\n      241 |\n      242 |       // Test full context\n    > 243 |       expect(User(undefined, mockExecutionContext)).toEqual(complexUser);\n          |                                                     ^\n      244 |       \n      245 |       // Test individual properties\n      246 |       expect(User('userId', mockExecutionContext)).toBe('complex-user-123');\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:243:53)\n\n  ● User Decorator Unit Tests › Complex User Context › should handle minimal user context\n\n    expect(received).toEqual(expected) // deep equality\n\n    Expected: {\"email\": \"<EMAIL>\", \"resourceRoles\": {}, \"roles\": [], \"userId\": \"minimal-user\"}\n    Received: [Function anonymous]\n\n      267 |       mockRequest.user = minimalUser;\n      268 |\n    > 269 |       expect(User(undefined, mockExecutionContext)).toEqual(minimalUser);\n          |                                                     ^\n      270 |       expect(User('userId', mockExecutionContext)).toBe('minimal-user');\n      271 |       expect(User('email', mockExecutionContext)).toBeUndefined();\n      272 |       expect(User('roles', mockExecutionContext)).toEqual([]);\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:269:53)\n\n  ● User Decorator Unit Tests › ExecutionContext Integration › should call switchToHttp correctly\n\n    expect(jest.fn()).toHaveBeenCalled()\n\n    Expected number of calls: >= 1\n    Received number of calls:    0\n\n      288 |       User(undefined, mockExecutionContext);\n      289 |\n    > 290 |       expect(mockExecutionContext.switchToHttp).toHaveBeenCalled();\n          |                                                 ^\n      291 |       expect(mockExecutionContext.switchToHttp().getRequest).toHaveBeenCalled();\n      292 |     });\n      293 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:290:49)\n\n  ● User Decorator Unit Tests › ExecutionContext Integration › should work with different execution context types\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"ws-user\"\n    Received: [Function anonymous]\n\n      311 |       const result = User('userId', wsContext as any);\n      312 |\n    > 313 |       expect(result).toBe('ws-user');\n          |                      ^\n      314 |     });\n      315 |   });\n      316 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:313:22)\n\n  ● User Decorator Unit Tests › Edge Cases › should handle user context with null properties\n\n    expect(received).toBeNull()\n\n    Received: [Function anonymous]\n\n      327 |       mockRequest.user = userWithNulls;\n      328 |\n    > 329 |       expect(User('email', mockExecutionContext)).toBeNull();\n          |                                                   ^\n      330 |       expect(User('username', mockExecutionContext)).toBeNull();\n      331 |       expect(User('userId', mockExecutionContext)).toBe('null-props-user');\n      332 |     });\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:329:51)\n\n  ● User Decorator Unit Tests › Edge Cases › should handle user context with empty arrays and objects\n\n    expect(received).toEqual(expected) // deep equality\n\n    Expected: []\n    Received: [Function anonymous]\n\n      342 |       mockRequest.user = userWithEmpties;\n      343 |\n    > 344 |       expect(User('roles', mockExecutionContext)).toEqual([]);\n          |                                                   ^\n      345 |       expect(User('resourceRoles', mockExecutionContext)).toEqual({});\n      346 |     });\n      347 |\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:344:51)\n\n  ● User Decorator Unit Tests › Edge Cases › should handle falsy values in user properties\n\n    expect(received).toBe(expected) // Object.is equality\n\n    Expected: \"\"\n    Received: [Function anonymous]\n\n      358 |       mockRequest.user = userWithFalsyValues;\n      359 |\n    > 360 |       expect(User('email', mockExecutionContext)).toBe('');\n          |                                                   ^\n      361 |       expect(User('username', mockExecutionContext)).toBe('');\n      362 |       expect(User('isEmailVerified', mockExecutionContext)).toBe(false);\n      363 |     });\n\n      at Object.<anonymous> (test/unit/user.decorator.spec.ts:360:51)\n", "name": "/root/code/polyrepo/libs/auth-common/test/unit/user.decorator.spec.ts", "startTime": 1750370064322, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["JwtAuthGuard Unit Tests", "canActivate"], "duration": 26, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests canActivate should call parent canActivate method", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should call parent canActivate method"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "canActivate"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests canActivate should return Promise<boolean> when parent returns promise", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return Promise<boolean> when parent returns promise"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "canActivate"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests canActivate should handle parent returning false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle parent returning false"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should return user when authentication is successful", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return user when authentication is successful"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 37, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should throw error when error is provided", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw error when error is provided"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should throw error when user is null", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw error when user is null"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should throw error when user is undefined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw error when user is undefined"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should throw error when user is false", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw error when user is false"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should prioritize error over missing user", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should prioritize error over missing user"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should handle info parameter gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle info parameter gracefully"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "handleRequest"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests handleRequest should work with minimal user object", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should work with minimal user object"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "Integration with ExecutionContext"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests Integration with ExecutionContext should work with HTTP context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should work with HTTP context"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "Integration with ExecutionContext"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests Integration with ExecutionContext should work with WebSocket context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should work with WebSocket context"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "Integration with ExecutionContext"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests Integration with ExecutionContext should work with GraphQL context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should work with GraphQL context"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "Error Handling <PERSON>s"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests Error Handling Edge Cases should handle custom error objects", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle custom error objects"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "Error Handling <PERSON>s"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests Error Handling Edge Cases should handle string errors", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle string errors"}, {"ancestorTitles": ["JwtAuthGuard Unit Tests", "Error Handling <PERSON>s"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "JwtAuthGuard Unit Tests Error Handling Edge Cases should handle complex user objects", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle complex user objects"}], "endTime": 1750370069406, "message": "", "name": "/root/code/polyrepo/libs/auth-common/test/unit/jwt-auth.guard.spec.ts", "startTime": 1750370064290, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["RolesGuard Unit Tests", "Role Authorization"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Authorization should allow access when no roles are required", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should allow access when no roles are required"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Authorization"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Authorization should allow access when user has required role", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow access when user has required role"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Authorization"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Authorization should allow access when user has one of multiple required roles", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow access when user has one of multiple required roles"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Authorization"], "duration": 19, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Authorization should deny access when user does not have required role", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should deny access when user does not have required role"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Authorization"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Authorization should deny access when user has no roles but roles are required", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should deny access when user has no roles but roles are required"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Authorization"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Authorization should deny access when user roles is undefined but roles are required", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should deny access when user roles is undefined but roles are required"}, {"ancestorTitles": ["RolesGuard Unit Tests", "User Context Validation"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests User Context Validation should throw ForbiddenException when user context is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should throw ForbiddenException when user context is missing"}, {"ancestorTitles": ["RolesGuard Unit Tests", "User Context Validation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests User Context Validation should throw ForbiddenException when user context is undefined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw ForbiddenException when user context is undefined"}, {"ancestorTitles": ["RolesGuard Unit Tests", "User Context Validation"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests User Context Validation should work with minimal user context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should work with minimal user context"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Matching Logic"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Matching Logic should be case-sensitive for role matching", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be case-sensitive for role matching"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Matching Logic"], "duration": 2, "failureDetails": [{"response": {"message": "Insufficient permissions", "error": "Forbidden", "statusCode": 403}, "status": 403, "options": {}, "message": "Insufficient permissions", "name": "ForbiddenException"}], "failureMessages": ["ForbiddenException: Insufficient permissions\n    at RolesGuard.canActivate (/root/code/polyrepo/libs/auth-common/src/guards/roles.guard.ts:30:13)\n    at Object.<anonymous> (/root/code/polyrepo/libs/auth-common/test/unit/roles.guard.spec.ts:221:28)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "RolesGuard Unit Tests Role Matching Logic should handle empty required roles array", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle empty required roles array"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Matching Logic"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Matching Logic should handle special characters in role names", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle special characters in role names"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Matching Logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Matching Logic should handle duplicate roles in user context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle duplicate roles in user context"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Role Matching Logic"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Role Matching Logic should handle duplicate roles in required roles", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle duplicate roles in required roles"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Reflector Integration"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Reflector Integration should query reflector with correct parameters", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should query reflector with correct parameters"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Reflector Integration"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Reflector Integration should prioritize handler metadata over class metadata", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should prioritize handler metadata over class metadata"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Error Messages"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Error Messages should throw specific error message for missing user context", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw specific error message for missing user context"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Error Messages"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Error Messages should throw specific error message for insufficient permissions", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw specific error message for insufficient permissions"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Complex Role Scenarios"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Complex Role Scenarios should handle user with many roles against single required role", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle user with many roles against single required role"}, {"ancestorTitles": ["RolesGuard Unit Tests", "Complex Role Scenarios"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "RolesGuard Unit Tests Complex Role Scenarios should handle user with single role against many required roles", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle user with single role against many required roles"}], "endTime": 1750370069873, "message": "  ● RolesGuard Unit Tests › Role Matching Logic › should handle empty required roles array\n\n    ForbiddenException: Insufficient permissions\n\n      28 |     \n      29 |     if (!hasRole) {\n    > 30 |       throw new ForbiddenException('Insufficient permissions');\n         |             ^\n      31 |     }\n      32 |\n      33 |     return true;\n\n      at RolesGuard.canActivate (src/guards/roles.guard.ts:30:13)\n      at Object.<anonymous> (test/unit/roles.guard.spec.ts:221:28)\n", "name": "/root/code/polyrepo/libs/auth-common/test/unit/roles.guard.spec.ts", "startTime": 1750370068702, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750370073727, "message": "  ● Test suite failed to run\n\n    \u001b[96m../http/src/client/http-client.service.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m84\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module 'got' or its corresponding type declarations.\n\n    \u001b[7m2\u001b[0m import got, { Got, Method, Options as GotOptions, Response, BeforeRetryHook } from 'got';\n    \u001b[7m \u001b[0m \u001b[91m                                                                                   ~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/auth-common/test/unit/jwt.strategy.spec.ts", "startTime": 1750370073727, "status": "failed", "summary": ""}], "wasInterrupted": false}