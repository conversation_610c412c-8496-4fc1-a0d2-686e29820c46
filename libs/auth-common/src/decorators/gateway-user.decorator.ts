import { createParamDecorator, ExecutionContext, BadRequestException } from '@nestjs/common';
import { UserContext } from '../interfaces/user-context.interface';

/**
 * Parameter decorator that extracts user context from the 'x-user-context' header
 * set by the API Gateway after JWT validation.
 * 
 * This decorator is intended for use in downstream services that receive
 * pre-validated user context from the API Gateway in Netflix-style architecture.
 * 
 * @param data - Optional key from UserContext to extract specific field
 * @returns UserContext object or specific field if data parameter provided
 * 
 * @example
 * ```typescript
 * // Get full user context
 * async getCurrentUser(@GatewayUser() userContext: UserContext) { ... }
 * 
 * // Get specific field
 * async getCurrentUser(@GatewayUser('userId') userId: string) { ... }
 * ```
 */
export const GatewayUser = createParamDecorator(
  (data: keyof UserContext | undefined, ctx: ExecutionContext): UserContext | any => {
    const request = ctx.switchToHttp().getRequest();
    const userContextHeader = request.headers['x-user-context'];
    
    if (!userContextHeader) {
      throw new BadRequestException('Missing user context header from API Gateway');
    }
    
    let userContext: UserContext;
    try {
      userContext = JSON.parse(userContextHeader);
    } catch (error) {
      throw new BadRequestException('Invalid user context header format');
    }

    // Validate that we have essential user context fields
    if (!userContext.userId) {
      throw new BadRequestException('Invalid user context: missing userId');
    }
    
    return data ? userContext?.[data] : userContext;
  },
);