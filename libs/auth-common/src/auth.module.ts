import { Module, DynamicModule } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { KeycloakClientModule, KeycloakClientService } from '@libs/keycloak-client';
import { JwtStrategy } from './strategies/jwt.strategy';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { UserContextGuard } from './guards/user-context.guard';
import { RolesGuard } from './guards/roles.guard';

export interface AuthModuleOptions {
  loggerFactory?: any;
}

@Module({})
export class AuthCommonModule {
  static forRoot(options?: AuthModuleOptions): DynamicModule {
    return {
      module: AuthCommonModule,
      imports: [
        PassportModule.register({ defaultStrategy: 'jwt' }),
        JwtModule.registerAsync({
          imports: [ConfigModule],
          useFactory: async (configService: ConfigService) => ({
            secret: 'dummy-secret', // Not used for validation, only for module setup
            signOptions: { expiresIn: '1h' },
          }),
          inject: [ConfigService],
        }),
        KeycloakClientModule.forRoot({ loggerFactory: options?.loggerFactory }), // Centralized Keycloak client
      ],
      providers: [
        {
          provide: JwtStrategy,
          useFactory: (configService: ConfigService, keycloakClient: KeycloakClientService) => {
            return new JwtStrategy(configService, keycloakClient, options?.loggerFactory);
          },
          inject: [ConfigService, KeycloakClientService],
        },
        JwtAuthGuard,
        UserContextGuard,
        RolesGuard,
      ],
      exports: [
        JwtStrategy,
        JwtAuthGuard,
        UserContextGuard,
        RolesGuard,
        PassportModule
      ],
    };
  }

  // For services that only need guards and decorators, not JWT validation
  static forServices(): DynamicModule {
    return {
      module: AuthCommonModule,
      providers: [
        UserContextGuard,
        RolesGuard,
      ],
      exports: [
        UserContextGuard,
        RolesGuard,
      ],
    };
  }
}