import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { UserContext } from '../interfaces/user-context.interface';

@Injectable()
export class UserContextGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    
    // Check for user context in headers (passed from API Gateway)
    const userContextHeader = request.headers['x-user-context'];
    
    if (userContextHeader) {
      try {
        const userContext: UserContext = JSON.parse(userContextHeader);
        request.user = userContext;
        return true;
      } catch (error) {
        throw new UnauthorizedException('Invalid user context format');
      }
    }
    
    // Fallback to traditional JWT validation if user context not found
    if (request.user) {
      return true;
    }
    
    throw new UnauthorizedException('No user context or authentication found');
  }
}