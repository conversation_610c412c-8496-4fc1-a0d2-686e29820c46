import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RESOURCE_ROLES_KEY } from '../decorators/resource-roles.decorator';
import { UserContext } from '../interfaces/user-context.interface';

@Injectable()
export class ResourceRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredResourceRoles = this.reflector.getAllAndOverride<Record<string, string[]>>(RESOURCE_ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredResourceRoles) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;

    if (!user) {
      throw new ForbiddenException('User context not found');
    }

    if (!user.resourceRoles) {
      throw new ForbiddenException('User has no resource roles');
    }

    // Check if user has any of the required resource roles
    const hasRequiredResourceRole = Object.entries(requiredResourceRoles).some(([resource, requiredRoles]) => {
      const userResourceRoles = user.resourceRoles?.[resource];
      if (!userResourceRoles) {
        return false;
      }
      return requiredRoles.some(role => userResourceRoles.includes(role));
    });

    if (!hasRequiredResourceRole) {
      throw new ForbiddenException('Insufficient resource permissions');
    }

    return true;
  }
}