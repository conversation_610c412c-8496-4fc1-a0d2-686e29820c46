# @libs/auth-common

Shared authentication utilities for polyrepo services with JWT strategies, guards, decorators, and user context management.

## Overview

`@libs/auth-common` provides a comprehensive authentication framework built on:

- **Centralized JWT Strategy** - Uses `@libs/keycloak-client` with HTTP/2 performance
- **Role-based Access Control** - Guards and decorators for authorization
- **User Context Management** - Standardized user information handling
- **Type-safe Interfaces** - Complete TypeScript support for JWT payloads
- **Observability Integration** - Built-in logging and monitoring

## Quick Start

### Installation

```bash
yarn add @libs/auth-common
```

### Module Setup

```typescript
import { AuthCommonModule } from '@libs/auth-common';
import { KeycloakClientModule } from '@libs/keycloak-client';

@Module({
  imports: [
    KeycloakClientModule.register({
      baseUrl: process.env.KEYCLOAK_BASE_URL,
      realm: process.env.KEYCLOAK_REALM_NAME,
      clientId: process.env.KEYCLOAK_CLIENT_ID,
      clientSecret: process.env.KEYCLOAK_CLIENT_SECRET,
    }),
    AuthCommonModule,
  ],
})
export class AppModule {}
```

### Basic Usage

```typescript
import { JwtAuthGuard, RolesGuard, User, Roles } from '@libs/auth-common';

@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  
  @Get('profile')
  getProfile(@User() user: UserContext) {
    return {
      userId: user.userId,
      email: user.email,
      roles: user.roles,
    };
  }

  @Post('admin-action')
  @Roles('admin', 'super-admin')
  adminAction(@User('userId') userId: string) {
    // Only accessible to users with admin or super-admin roles
    return this.performAdminAction(userId);
  }
}
```

## Core Components

### JWT Strategy

High-performance JWT validation with automatic JWKS caching:

```typescript
// Automatically configured with module import
// Uses @libs/keycloak-client for 60-80% faster JWKS operations
// Handles key rotation and caching automatically

@Injectable()
export class MyService {
  // JWT strategy validates tokens and populates user context
  // No manual configuration needed
}
```

### Authentication Guards

#### JwtAuthGuard

Basic JWT authentication:

```typescript
import { JwtAuthGuard } from '@libs/auth-common';

@Controller('protected')
@UseGuards(JwtAuthGuard)
export class ProtectedController {
  @Get('data')
  getProtectedData(@User() user: UserContext) {
    // User automatically populated from validated JWT
    return { message: `Hello ${user.name}` };
  }
}
```

#### RolesGuard

Role-based authorization:

```typescript
import { JwtAuthGuard, RolesGuard, Roles } from '@libs/auth-common';

@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
export class AdminController {
  
  @Get('users')
  @Roles('admin')
  listUsers() {
    // Only accessible to users with 'admin' role
  }

  @Delete('user/:id')
  @Roles('super-admin')
  deleteUser(@Param('id') id: string) {
    // Only accessible to users with 'super-admin' role
  }

  @Post('critical-action')
  @Roles('admin', 'super-admin') // Multiple roles (OR logic)
  criticalAction() {
    // Accessible to users with either 'admin' OR 'super-admin' role
  }
}
```

#### UserContextGuard

Ensures user context is available:

```typescript
import { UserContextGuard } from '@libs/auth-common';

@Controller('profile')
@UseGuards(JwtAuthGuard, UserContextGuard)
export class ProfileController {
  @Get()
  getProfile(@User() user: UserContext) {
    // Guaranteed to have valid user context
    return user;
  }
}
```

### User Decorators

#### @User() Decorator

Extract user information from requests:

```typescript
import { User, UserContext } from '@libs/auth-common';

@Controller('api')
@UseGuards(JwtAuthGuard)
export class ApiController {
  
  // Get complete user context
  @Get('profile')
  getProfile(@User() user: UserContext) {
    return user;
  }

  // Get specific user property
  @Get('my-data')
  getMyData(@User('userId') userId: string) {
    return this.userService.getData(userId);
  }

  // Get multiple properties
  @Post('update')
  updateData(
    @User('userId') userId: string,
    @User('email') email: string,
    @Body() data: any,
  ) {
    return this.userService.update(userId, email, data);
  }
}
```

#### @GatewayUser() Decorator

Special decorator for API Gateway usage:

```typescript
import { GatewayUser } from '@libs/auth-common';

@Controller('gateway')
export class GatewayController {
  @Get('proxy')
  proxyRequest(@GatewayUser() user: UserContext) {
    // User context from API Gateway forwarding
    return this.proxyService.forward(user);
  }
}
```

### Type Interfaces

#### UserContext

Standardized user information:

```typescript
interface UserContext {
  userId: string;           // Keycloak user ID (sub claim)
  email?: string;           // User email
  name?: string;            // Full name
  username?: string;        // Username (preferred_username)
  roles: string[];          // Realm roles
  resourceRoles: Record<string, string[]>; // Client-specific roles
  isEmailVerified?: boolean; // Email verification status
}
```

#### JwtPayload

Complete JWT payload interface:

```typescript
interface JwtPayload {
  exp: number;              // Expiration time
  iat: number;              // Issued at
  jti: string;              // JWT ID
  iss: string;              // Issuer
  aud: string | string[];   // Audience
  sub: string;              // Subject (user ID)
  typ: string;              // Token type
  azp: string;              // Authorized party
  session_state: string;    // Session state
  scope: string;            // Scope
  email_verified: boolean;  // Email verification
  name: string;             // Full name
  preferred_username: string; // Username
  given_name: string;       // First name
  family_name: string;      // Last name
  email: string;            // Email address
  realm_access: {           // Realm roles
    roles: string[];
  };
  resource_access: Record<string, { // Client roles
    roles: string[];
  }>;
}
```

## Advanced Usage

### Custom Authorization Logic

```typescript
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { UserContext } from '@libs/auth-common';

@Injectable()
export class CustomAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    
    // Custom authorization logic
    if (user.roles.includes('admin')) {
      return true;
    }
    
    // Check resource-specific roles
    const clientRoles = user.resourceRoles['my-client'] || [];
    return clientRoles.includes('read-only');
  }
}

@Controller('custom')
@UseGuards(JwtAuthGuard, CustomAuthGuard)
export class CustomController {
  // Your endpoints here
}
```

### Role Hierarchies

```typescript
@Injectable()
export class RoleHierarchyGuard implements CanActivate {
  private readonly roleHierarchy = {
    'super-admin': ['admin', 'user'],
    'admin': ['user'],
    'user': [],
  };

  canActivate(context: ExecutionContext): boolean {
    const requiredRole = this.reflector.get<string>('role', context.getHandler());
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    
    return this.hasRequiredRole(user.roles, requiredRole);
  }

  private hasRequiredRole(userRoles: string[], requiredRole: string): boolean {
    // Check direct role
    if (userRoles.includes(requiredRole)) {
      return true;
    }
    
    // Check inherited roles
    for (const userRole of userRoles) {
      const inheritedRoles = this.roleHierarchy[userRole] || [];
      if (inheritedRoles.includes(requiredRole)) {
        return true;
      }
    }
    
    return false;
  }
}
```

### Resource-Based Authorization

```typescript
@Injectable()
export class ResourceGuard implements CanActivate {
  constructor(private readonly userService: UserService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user: UserContext = request.user;
    const resourceId = request.params.id;
    
    // Check if user owns the resource
    const resource = await this.userService.getResource(resourceId);
    if (resource.ownerId === user.userId) {
      return true;
    }
    
    // Check admin access
    return user.roles.includes('admin');
  }
}

@Controller('resources')
@UseGuards(JwtAuthGuard)
export class ResourceController {
  @Get(':id')
  @UseGuards(ResourceGuard)
  getResource(@Param('id') id: string, @User() user: UserContext) {
    // User has access to this specific resource
  }
}
```

## Integration with Services

### API Gateway Integration

```typescript
// In API Gateway
import { JwtAuthGuard, User, UserContext } from '@libs/auth-common';

@Controller('api')
export class ApiGatewayController {
  
  @Get('protected/*')
  @UseGuards(JwtAuthGuard)
  async proxyToService(
    @User() user: UserContext,
    @Req() request: Request,
  ) {
    // Add user context to downstream requests
    const headers = {
      ...request.headers,
      'x-user-id': user.userId,
      'x-user-email': user.email,
      'x-user-roles': JSON.stringify(user.roles),
    };
    
    return this.httpClient.request(request.method, request.url, {
      headers,
      data: request.body,
    });
  }
}
```

### Service-to-Service Authentication

```typescript
// In downstream services
import { GatewayUser, UserContext } from '@libs/auth-common';

@Controller('internal')
export class InternalController {
  
  @Post('process')
  processData(@GatewayUser() user: UserContext, @Body() data: any) {
    // User context forwarded from API Gateway
    this.auditService.log(`User ${user.userId} processed data`, data);
    return this.processService.handle(data, user);
  }
}
```

## Testing

### Unit Testing

```typescript
import { Test } from '@nestjs/testing';
import { JwtStrategy } from '@libs/auth-common';
import { KeycloakClientService } from '@libs/keycloak-client';

describe('JwtStrategy', () => {
  let strategy: JwtStrategy;
  let keycloakClient: jest.Mocked<KeycloakClientService>;

  beforeEach(async () => {
    const mockKeycloakClient = {
      getSigningKey: jest.fn(),
    };

    const module = await Test.createTestingModule({
      providers: [
        JwtStrategy,
        {
          provide: KeycloakClientService,
          useValue: mockKeycloakClient,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key) => {
              const config = {
                KEYCLOAK_BASE_URL: 'http://test-keycloak',
                KEYCLOAK_REALM_NAME: 'test-realm',
              };
              return config[key];
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<JwtStrategy>(JwtStrategy);
    keycloakClient = module.get(KeycloakClientService);
  });

  it('should validate JWT payload', async () => {
    const payload = {
      sub: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      preferred_username: 'testuser',
      realm_access: { roles: ['user'] },
      resource_access: {},
      email_verified: true,
    };

    const result = await strategy.validate(payload);

    expect(result).toEqual({
      userId: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      username: 'testuser',
      roles: ['user'],
      resourceRoles: {},
      isEmailVerified: true,
    });
  });
});
```

### Integration Testing

```typescript
import { Test } from '@nestjs/testing';
import { AuthCommonModule } from '@libs/auth-common';

describe('Auth Integration', () => {
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [AuthCommonModule],
    }).compile();
    
    const app = module.createNestApplication();
    await app.init();
  });

  it('should protect endpoints with JWT guard', async () => {
    const response = await request(app.getHttpServer())
      .get('/protected')
      .expect(401);
  });

  it('should allow access with valid token', async () => {
    const token = 'valid-jwt-token';
    
    const response = await request(app.getHttpServer())
      .get('/protected')
      .set('Authorization', `Bearer ${token}`)
      .expect(200);
  });
});
```

### Mock Authentication

```typescript
// For testing without real tokens
export const createMockUserContext = (overrides?: Partial<UserContext>): UserContext => ({
  userId: 'mock-user-123',
  email: '<EMAIL>',
  name: 'Mock User',
  username: 'mockuser',
  roles: ['user'],
  resourceRoles: {},
  isEmailVerified: true,
  ...overrides,
});

// In tests
const mockUser = createMockUserContext({
  roles: ['admin'],
});
```

## Performance Considerations

### JWT Validation Performance

The strategy leverages `@libs/keycloak-client` for optimal performance:

```typescript
// JWKS keys cached automatically
// HTTP/2 connections for 60-80% faster fetches
// Smart cache invalidation on key rotation
// No manual cache management needed
```

### Guard Optimization

```typescript
// Guards are stateless and fast
// Role checks use simple array operations
// No database queries in authorization logic
// Minimal memory footprint
```

## Best Practices

### Security

1. **Always use HTTPS in production**
   ```typescript
   // Verify JWT issuer matches HTTPS Keycloak URL
   issuer: 'https://keycloak.production.com/realms/my-realm'
   ```

2. **Validate audience claims**
   ```typescript
   // Configure expected audiences
   audience: ['account', 'my-service']
   ```

3. **Use role hierarchies appropriately**
   ```typescript
   // Don't over-engineer - keep roles simple
   @Roles('admin') // Single role when possible
   ```

### Performance

1. **Minimize guard chains**
   ```typescript
   // Good: Combine related guards
   @UseGuards(JwtAuthGuard, RolesGuard)
   
   // Avoid: Too many guards
   @UseGuards(Guard1, Guard2, Guard3, Guard4)
   ```

2. **Use specific user properties**
   ```typescript
   // Good: Extract only what you need
   @User('userId') userId: string
   
   // Less efficient: Extract full object when not needed
   @User() user: UserContext
   ```

### Maintainability

1. **Centralize role definitions**
   ```typescript
   // Create role constants
   export const ROLES = {
     ADMIN: 'admin',
     USER: 'user',
     MODERATOR: 'moderator',
   } as const;
   
   @Roles(ROLES.ADMIN)
   ```

2. **Document authorization requirements**
   ```typescript
   /**
    * Administrative endpoint - requires admin role
    * @roles admin
    */
   @Post('admin-action')
   @Roles('admin')
   adminAction() {}
   ```

## Troubleshooting

### Common Issues

1. **JWT validation failures**
   ```typescript
   // Check Keycloak configuration
   console.log('KEYCLOAK_BASE_URL:', process.env.KEYCLOAK_BASE_URL);
   console.log('KEYCLOAK_REALM_NAME:', process.env.KEYCLOAK_REALM_NAME);
   
   // Verify token format
   const header = jwt.decode(token, { complete: true })?.header;
   console.log('Token header:', header);
   ```

2. **Role authorization failures**
   ```typescript
   // Debug user roles
   @Get('debug-roles')
   @UseGuards(JwtAuthGuard)
   debugRoles(@User() user: UserContext) {
     console.log('User roles:', user.roles);
     console.log('Resource roles:', user.resourceRoles);
     return user;
   }
   ```

3. **Guard execution order**
   ```typescript
   // Ensure JwtAuthGuard runs before RolesGuard
   @UseGuards(JwtAuthGuard, RolesGuard) // Correct order
   ```

### Performance Issues

1. **Slow JWT validation**
   - Check Keycloak connectivity
   - Verify HTTP/2 is enabled
   - Monitor JWKS cache hit rates

2. **Memory leaks**
   - Ensure proper cleanup of request contexts
   - Monitor user context object creation

## Migration Guide

### From Custom JWT Implementation

```typescript
// Before: Manual JWT handling
@Injectable()
export class CustomJwtGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const token = this.extractToken(context);
    const user = await this.validateToken(token);
    context.switchToHttp().getRequest().user = user;
    return true;
  }
}

// After: @libs/auth-common
@UseGuards(JwtAuthGuard)
export class MyController {
  @Get()
  getData(@User() user: UserContext) {
    // User automatically validated and populated
  }
}
```

### From Basic Passport JWT

```typescript
// Before: Basic passport-jwt
@Injectable()
export class BasicJwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: 'hard-coded-secret', // Not secure
    });
  }
}

// After: @libs/auth-common with Keycloak
// Automatic JWKS resolution, key rotation, caching
// No manual configuration needed
```

## API Reference

### Guards

- `JwtAuthGuard` - JWT authentication
- `RolesGuard` - Role-based authorization  
- `UserContextGuard` - User context validation

### Decorators

- `@User()` - Extract user context
- `@User(property)` - Extract specific user property
- `@GatewayUser()` - Gateway-forwarded user context
- `@Roles(...roles)` - Specify required roles

### Interfaces

- `UserContext` - Standardized user information
- `JwtPayload` - Complete JWT payload structure

## License

Internal library for polyrepo services.