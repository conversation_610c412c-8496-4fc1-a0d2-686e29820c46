# Shared Types Library

## Purpose
Centralized type definitions and Data Transfer Objects (DTOs) for the Polyrepo microservices architecture. This library provides consistent interfaces, validation schemas, and type safety across all services within the template.

## Features

### Core Functionality
- **Standardized DTOs**: Consistent data transfer objects with validation decorators
- **Health Check Types**: Comprehensive health monitoring interfaces
- **User Management Types**: User-related DTOs and enums
- **Keycloak Integration**: Token parsing and authentication types
- **Type Safety**: TypeScript interfaces for cross-service communication

### Validation Support
- **Class Validator Integration**: Built-in validation decorators
- **Strong Typing**: Compile-time type checking across services
- **Consistent Patterns**: Standardized DTO structure and validation rules

## Type Categories

### User Management DTOs
```typescript
// User registration
export class RegisterUserDto {
  @IsEmail()
  email: string;
  
  @IsString()
  @MinLength(8)
  password: string;
  
  @IsString()
  @MinLength(2)
  firstName: string;
  
  @IsString()
  @MinLength(2)
  lastName: string;
}

// User login
export class LoginUserDto {
  @IsEmail()
  email: string;
  
  @IsString()
  password: string;
}

// Internal user creation
export class CreateUserInternalDto {
  @IsEmail()
  email: string;
  
  @IsOptional()
  @IsString()
  firstName?: string;
  
  @IsOptional()
  @IsString()
  lastName?: string;
  
  @IsOptional()
  @IsUUID()
  keycloakId?: string;
}
```

### Health Check Types
```typescript
// Basic health response
export interface BasicHealthResponse {
  status: HealthStatus;
  service: string;
  timestamp: string;
  environment: string;
}

// Comprehensive health check
export interface HealthCheckResponse {
  status: HealthStatus;
  service: string;
  timestamp: string;
  version?: string;
  uptime?: number;
  environment: string;
  observability?: ObservabilityHealth;
  dependencies?: Record<string, DependencyHealth>;
  infrastructure?: {
    database?: DatabaseHealth;
    cache?: RedisHealth;
    messaging?: RedisHealth;
    circuitBreakers?: Record<string, CircuitBreakerStatus>;
  };
  performance?: {
    responseTime: number;
    memoryUsage?: number;
    cpuUsage?: number;
  };
}
```

### Authentication Types
```typescript
// Keycloak token structure
export interface KeycloakTokenParsed {
  sub: string;
  email_verified?: boolean;
  realm_access?: { roles: string[] };
  resource_access?: { [key: string]: { roles: string[] } };
  preferred_username?: string;
  email?: string;
  name?: string;
  given_name?: string;
  family_name?: string;
}

// User roles enumeration
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}
```

## Template Customization

### Adding New DTOs
1. **Create DTO file** in `src/dto/`:
```typescript
// src/dto/custom-entity.dto.ts
import { IsString, IsNumber, IsOptional } from 'class-validator';

export class CreateCustomEntityDto {
  @IsString()
  name: string;
  
  @IsNumber()
  value: number;
  
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateCustomEntityDto {
  @IsOptional()
  @IsString()
  name?: string;
  
  @IsOptional()
  @IsNumber()
  value?: number;
  
  @IsOptional()
  @IsString()
  description?: string;
}
```

2. **Export from index.ts**:
```typescript
export { CreateCustomEntityDto, UpdateCustomEntityDto } from './dto/custom-entity.dto';
```

3. **Use in services**:
```typescript
import { CreateCustomEntityDto } from '@libs/shared-types';

@Controller('entities')
export class EntitiesController {
  @Post()
  async create(@Body() dto: CreateCustomEntityDto) {
    // Implementation
  }
}
```

### Adding Custom Health Types
```typescript
// src/types/custom-health.ts
export interface CustomServiceHealth extends DependencyHealth {
  customMetric?: number;
  serviceSpecificData?: {
    queueLength: number;
    processingRate: number;
  };
}

// Export in index.ts
export * from './types/custom-health';
```

### Adding Business Domain Types
```typescript
// src/types/business-domain.ts
export interface ProductDto {
  id: string;
  name: string;
  price: number;
  category: ProductCategory;
}

export enum ProductCategory {
  ELECTRONICS = 'electronics',
  CLOTHING = 'clothing',
  BOOKS = 'books',
}

export interface OrderDto {
  id: string;
  userId: string;
  products: ProductDto[];
  total: number;
  status: OrderStatus;
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
}
```

## Validation Patterns

### Common Validation Decorators
```typescript
import {
  IsEmail,
  IsString,
  IsNumber,
  IsOptional,
  IsEnum,
  IsUUID,
  IsArray,
  IsBoolean,
  MinLength,
  MaxLength,
  Min,
  Max,
  IsDateString,
} from 'class-validator';

export class ExampleDto {
  @IsEmail()
  email: string;
  
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;
  
  @IsNumber()
  @Min(0)
  @Max(100)
  percentage: number;
  
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
  
  @IsArray()
  @IsString({ each: true })
  tags: string[];
  
  @IsBoolean()
  isActive: boolean;
  
  @IsDateString()
  createdAt: string;
}
```

### Nested Validation
```typescript
import { ValidateNested, Type } from 'class-transformer';

export class AddressDto {
  @IsString()
  street: string;
  
  @IsString()
  city: string;
  
  @IsString()
  zipCode: string;
}

export class UserWithAddressDto {
  @IsString()
  name: string;
  
  @ValidateNested()
  @Type(() => AddressDto)
  address: AddressDto;
}
```

### Conditional Validation
```typescript
import { ValidateIf } from 'class-validator';

export class ConditionalDto {
  @IsString()
  type: string;
  
  @ValidateIf(o => o.type === 'premium')
  @IsNumber()
  premiumValue?: number;
  
  @ValidateIf(o => o.type === 'basic')
  @IsString()
  basicValue?: string;
}
```

## Integration with Services

### Service Import
```typescript
// In any service
import {
  RegisterUserDto,
  LoginUserDto,
  HealthCheckResponse,
  UserRole
} from '@libs/shared-types';

@Injectable()
export class UserService {
  async register(dto: RegisterUserDto): Promise<User> {
    // Implementation using validated DTO
  }
  
  getHealthCheck(): HealthCheckResponse {
    return {
      status: 'ok',
      service: 'user-service',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    };
  }
}
```

### Controller Usage
```typescript
import { RegisterUserDto, LoginUserDto } from '@libs/shared-types';

@Controller('auth')
export class AuthController {
  @Post('register')
  async register(@Body() dto: RegisterUserDto) {
    // Automatic validation via ValidationPipe
    return this.authService.register(dto);
  }
  
  @Post('login')
  async login(@Body() dto: LoginUserDto) {
    return this.authService.login(dto);
  }
}
```

## Type Safety Benefits

### Compile-Time Checking
```typescript
// Type safety across service boundaries
const userData: RegisterUserDto = {
  email: '<EMAIL>',
  password: 'securePassword123',
  firstName: 'John',
  lastName: 'Doe'
};

// TypeScript will catch type mismatches
const invalidData: RegisterUserDto = {
  email: '<EMAIL>',
  // Missing required fields - TypeScript error
};
```

### Interface Consistency
```typescript
// Services can depend on consistent interfaces
interface UserServiceClient {
  register(dto: RegisterUserDto): Promise<User>;
  login(dto: LoginUserDto): Promise<AuthResult>;
}

// Both HTTP and message-based implementations
// must conform to the same interface
class HttpUserServiceClient implements UserServiceClient {
  async register(dto: RegisterUserDto): Promise<User> {
    return this.httpClient.post('/users', dto);
  }
}

class MessageUserServiceClient implements UserServiceClient {
  async register(dto: RegisterUserDto): Promise<User> {
    return this.messageClient.send('user.register', dto);
  }
}
```

## Development

### Prerequisites
- Node.js 20+
- TypeScript 5+
- Yarn package manager

### Build Commands
```bash
# Build the library
cd libs/shared-types
yarn build

# Type checking
tsc --noEmit

# Linting
yarn lint
```

### Usage in Development
```bash
# Install in a service
cd services/your-service
yarn add @libs/shared-types

# Import in TypeScript
import { YourDto } from '@libs/shared-types';
```

## Testing

### DTO Validation Testing
```typescript
import { validate } from 'class-validator';
import { RegisterUserDto } from '@libs/shared-types';

describe('RegisterUserDto', () => {
  it('should validate valid user data', async () => {
    const dto = new RegisterUserDto();
    dto.email = '<EMAIL>';
    dto.password = 'securePassword123';
    dto.firstName = 'John';
    dto.lastName = 'Doe';
    
    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });
  
  it('should reject invalid email', async () => {
    const dto = new RegisterUserDto();
    dto.email = 'invalid-email';
    dto.password = 'securePassword123';
    dto.firstName = 'John';
    dto.lastName = 'Doe';
    
    const errors = await validate(dto);
    expect(errors).toHaveLength(1);
    expect(errors[0].property).toBe('email');
  });
});
```

### Type Compatibility Testing
```typescript
import { HealthCheckResponse } from '@libs/shared-types';

describe('Health Check Types', () => {
  it('should match expected response structure', () => {
    const response: HealthCheckResponse = {
      status: 'ok',
      service: 'test-service',
      timestamp: new Date().toISOString(),
      environment: 'test'
    };
    
    expect(response.status).toBe('ok');
    expect(typeof response.timestamp).toBe('string');
  });
});
```

## Best Practices

### DTO Design Guidelines
1. **Use validation decorators** for all input fields
2. **Separate creation and update DTOs** for different validation rules
3. **Include optional fields** with `@IsOptional()` decorator
4. **Use enums** for constrained string values
5. **Document complex validation logic** with comments

### Type Organization
1. **Group related types** in logical modules
2. **Use barrel exports** in index.ts for clean imports
3. **Avoid circular dependencies** between type definitions
4. **Use generic types** for reusable patterns
5. **Document type purposes** with JSDoc comments

### Version Management
1. **Use semantic versioning** for breaking changes
2. **Deprecate old types** before removal
3. **Document migration paths** for type changes
4. **Test type compatibility** across service versions

## Dependencies

### Core Dependencies
- `class-validator` - Validation decorators
- `class-transformer` - Object transformation
- `tslib` - TypeScript runtime library

### Development Dependencies
- `typescript` - TypeScript compiler
- `eslint` - Code linting

## Contributing

### Adding New Types
1. Create type files in appropriate directories
2. Add validation decorators where applicable
3. Export from index.ts
4. Add documentation and examples
5. Include unit tests for validation logic
6. Update this README with usage examples

### Type Modification Guidelines
1. Maintain backward compatibility when possible
2. Use deprecation warnings before removal
3. Update all consuming services
4. Add migration documentation
5. Version bump according to semantic versioning

## License
Private - Part of Polyrepo template project