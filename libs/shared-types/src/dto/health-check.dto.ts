/**
 * Standardized health check response interfaces for all services
 * Based on analysis of existing implementations with improvements
 */

export type HealthStatus = 'ok' | 'degraded' | 'error';

export interface DependencyHealth {
  status: HealthStatus;
  responseTime: number;
  details?: Record<string, any>;
  error?: string;
}

export interface DatabaseHealth extends DependencyHealth {
  connectionPool?: {
    active: number;
    idle: number;
    total: number;
  };
}

export interface RedisHealth extends DependencyHealth {
  memoryUsage?: number;
  connectedClients?: number;
  commandsProcessed?: number;
}

export interface CircuitBreakerStatus {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  successCount: number;
  lastFailureTime?: string;
  nextAttemptTime?: string;
}

export interface ObservabilityHealth {
  logging: HealthStatus;
  metrics: HealthStatus;
  tracing?: HealthStatus;
  details?: {
    logLevel?: string;
    metricsCollected?: number;
    tracesCollected?: number;
  };
}

export interface HealthCheckResponse {
  status: HealthStatus;
  service: string;
  timestamp: string;
  version?: string;
  uptime?: number;
  environment: string;
  observability?: ObservabilityHealth;
  dependencies?: Record<string, DependencyHealth>;
  infrastructure?: {
    database?: DatabaseHealth;
    cache?: RedisHealth;
    messaging?: RedisHealth;
    circuitBreakers?: Record<string, CircuitBreakerStatus>;
  };
  performance?: {
    responseTime: number;
    memoryUsage?: number;
    cpuUsage?: number;
  };
}

/**
 * Basic health check response for simple endpoints
 */
export interface BasicHealthResponse {
  status: HealthStatus;
  service: string;
  timestamp: string;
  environment: string;
}

/**
 * Health check request options for dependency checks
 */
export interface HealthCheckOptions {
  includeDependencies?: boolean;
  includeInfrastructure?: boolean;
  includePerformance?: boolean;
  timeoutMs?: number;
}