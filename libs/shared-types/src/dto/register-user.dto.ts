import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>S<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class RegisterUserDto {
  @IsNotEmpty()
  @IsEmail()
  email!: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  password!: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  firstName!: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  lastName!: string;
}
