// Export shared types from here

export { RegisterUserDto } from './dto/register-user.dto';
export { CreateUserInternalDto } from './dto/create-user-internal.dto';
export { LoginUserDto } from './dto/login-user.dto';
export * from './dto/health-check.dto';

export interface SampleUserDto {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}

// Placeholder for Keycloak-related types if needed centrally
export interface KeycloakTokenParsed {
  sub: string; 
  email_verified?: boolean;
  realm_access?: { roles: string[] };
  resource_access?: { [key: string]: { roles: string[] } };
  preferred_username?: string;
  email?: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  // Add other fields you expect from your token
}
