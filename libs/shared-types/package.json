{"name": "@libs/shared-types", "version": "1.0.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && rm -f tsconfig.build.tsbuildinfo && rm -f tsconfig.tsbuildinfo && tsc -p tsconfig.build.json --listEmittedFiles", "lint": "echo 'Linting shared-types...'"}, "dependencies": {"class-transformer": "^0.5.1", "class-validator": "^0.14.2", "tslib": "^2.8.1"}, "devDependencies": {"eslint": "*", "typescript": "*"}}