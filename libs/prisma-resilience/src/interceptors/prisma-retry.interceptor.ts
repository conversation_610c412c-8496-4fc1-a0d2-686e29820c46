import { CallHand<PERSON>, ExecutionContext, Injectable, NestInterceptor, Logger } from '@nestjs/common';
import { Observable, throwError, timer } from 'rxjs';
import { catchError, mergeMap } from 'rxjs/operators';
// Removed Prisma import - using property-based error detection instead of instanceof

// Configuration for retry behavior
const DEFAULT_RETRY_ATTEMPTS = 3;
const DEFAULT_RETRY_DELAY_MS = 1000; // Initial delay

// Prisma error codes that might be transient and worth retrying
const RETRYABLE_PRISMA_ERROR_CODES = [
  'P1001', // Can't reach database server
  'P1002', // Database server timed out
  'P1008', // Operations timed out
  'P1017', // Server has closed the connection
  'P2024', // Timed out fetching a new connection from the connection pool
  'P2034', // Transaction failed due to a write conflict or a deadlock
  'P2037', // Too many database connections opened
];

@Injectable()
export class PrismaRetryInterceptor implements NestInterceptor {
  private readonly logger = new Logger(PrismaRetryInterceptor.name);

  constructor(
    private readonly retryAttempts: number = DEFAULT_RETRY_ATTEMPTS,
    private readonly retryDelayMs: number = DEFAULT_RETRY_DELAY_MS,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError(error => {
        // Webpack-safe error checking: check error properties instead of instanceof
        // After bundling, instanceof PrismaClientKnownRequestError doesn't work properly
        if (
          error && error.code && typeof error.code === 'string' &&
          RETRYABLE_PRISMA_ERROR_CODES.includes(error.code)
        ) {
          // If retryable, delegate to the retryWithBackoff logic
          return this.retryWithBackoff(next, error, 0, context);
        }
        // If not a retryable Prisma error, rethrow it immediately
        return throwError(() => error);
      })
    );
  }

  private retryWithBackoff(
    next: CallHandler, // The next handler in the chain
    error: any,        // The error that occurred
    attempt: number,   // Current retry attempt number (0-indexed)
    context: ExecutionContext // Execution context for logging
  ): Observable<any> {
    // If we've exceeded the maximum number of attempts, log and rethrow the error
    if (attempt >= this.retryAttempts) {
      this.logger.error(
        `Prisma operation failed permanently after ${attempt} attempts. Error Code: ${error.code}. Handler: ${context.getClass().name}.${context.getHandler().name}()`,
        error.stack
      );
      return throwError(() => error);
    }

    // Calculate delay with exponential backoff (e.g., 1s, 2s, 4s, ...)
    const delay = this.retryDelayMs * Math.pow(2, attempt);
    this.logger.warn(
      `Prisma operation failed (Attempt ${attempt + 1}/${this.retryAttempts}). Retrying in ${delay}ms. Error Code: ${error.code}. Handler: ${context.getClass().name}.${context.getHandler().name}()`,
      error.stack
    );

    // Wait for the calculated delay, then try calling the next handler again
    return timer(delay).pipe(
      mergeMap(() => 
        next.handle().pipe(
          catchError(nextError => {
            // Webpack-safe error checking: check error properties instead of instanceof
            if (
              nextError && nextError.code && typeof nextError.code === 'string' &&
              RETRYABLE_PRISMA_ERROR_CODES.includes(nextError.code)
            ) {
              // If still retryable, recurse with an incremented attempt count
              return this.retryWithBackoff(next, nextError, attempt + 1, context);
            }
            // If not retryable, or a different error occurs, rethrow it
            return throwError(() => nextError);
          })
        )
      )
    );
  }
}
