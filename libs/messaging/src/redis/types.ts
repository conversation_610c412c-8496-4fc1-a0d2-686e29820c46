import { DomainEvent, EventPublisher, MessageQueueConfig, PublishOptions } from '../events/event.types';

/**
 * Redis Streams specific configuration
 */
export interface RedisStreamsConfig extends MessageQueueConfig {
  /**
   * Redis connection configuration
   */
  redis?: {
    host?: string;
    port?: number;
    password?: string;
    db?: number;
    maxRetriesPerRequest?: number;
    retryDelayOnFailover?: number;
    lazyConnect?: boolean;
  };

  /**
   * Default stream for publishing events
   */
  defaultStream?: string;

  /**
   * Consumer group configuration
   */
  consumerGroup?: {
    name: string;
    startFromLatest?: boolean;
  };

  /**
   * Message retention configuration
   */
  retention?: {
    maxLength?: number;
    approximateMaxLength?: boolean;
  };
}

/**
 * Redis Streams publishing options
 */
export interface RedisPublishOptions extends PublishOptions {
  /**
   * Stream name to publish to
   */
  stream?: string;

  /**
   * Maximum stream length after adding message
   */
  maxLength?: number;

  /**
   * Whether to use approximate trimming for performance
   */
  approximateMaxLength?: boolean;
}

/**
 * Event with Redis metadata
 */
export interface RedisStreamEvent extends DomainEvent {
  /**
   * Redis stream entry ID
   */
  streamId?: string;

  /**
   * Stream name where event was published
   */
  streamName?: string;
}

/**
 * Consumer configuration for Redis Streams
 */
export interface StreamConsumerConfig {
  /**
   * Stream name to consume from
   */
  stream: string;

  /**
   * Consumer group name
   */
  group: string;

  /**
   * Consumer name within the group
   */
  consumer: string;

  /**
   * Block time for reading messages (milliseconds)
   */
  blockTime?: number;

  /**
   * Number of messages to read per batch
   */
  count?: number;

  /**
   * Start reading from latest or beginning
   */
  startFrom?: 'latest' | 'beginning' | string;
}

/**
 * Stream message metadata
 */
export interface StreamMessage {
  /**
   * Message ID in the stream
   */
  id: string;

  /**
   * Message fields
   */
  fields: Record<string, string>;

  /**
   * Parsed event data
   */
  event?: DomainEvent;
}

/**
 * Consumer group information
 */
export interface ConsumerGroupInfo {
  name: string;
  consumers: number;
  pending: number;
  lastDeliveredId: string;
}

/**
 * Stream information
 */
export interface StreamInfo {
  length: number;
  radixTreeKeys: number;
  radixTreeNodes: number;
  groups: number;
  lastGeneratedId: string;
  firstEntry?: StreamMessage;
  lastEntry?: StreamMessage;
}
