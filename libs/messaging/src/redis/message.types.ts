import { Type } from 'class-transformer';
import { IsString, IsObject, IsOptional, IsDate, IsEnum } from 'class-validator';

/**
 * Message queue configuration options for Redis Streams
 */
export interface MessageQueueOptions {
  /**
   * Redis connection URL (e.g., 'redis://localhost:6379')
   */
  url?: string;
  
  /**
   * Redis host
   * @default 'localhost'
   */
  host?: string;
  
  /**
   * Redis port
   * @default 6379
   */
  port?: number;
  
  /**
   * Redis database number
   * @default 0
   */
  db?: number;
  
  /**
   * Connection timeout in milliseconds
   * @default 5000
   */
  connectTimeout?: number;
  
  /**
   * Command timeout in milliseconds
   * @default 5000
   */
  commandTimeout?: number;
  
  /**
   * Default stream prefix for message streams
   * @default 'events:'
   */
  streamPrefix?: string;
  
  /**
   * Maximum length of each stream (for memory management)
   * @default 10000
   */
  maxStreamLength?: number;
  
  /**
   * Consumer group name for this service
   * @default 'default'
   */
  consumerGroup?: string;
  
  /**
   * Consumer name for this instance
   * @default hostname-pid
   */
  consumerName?: string;
}