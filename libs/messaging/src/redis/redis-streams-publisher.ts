import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import { DomainEvent, EventPublisher } from '../events/event.types';
import { RedisStreamsConfig, RedisPublishOptions, RedisStreamEvent } from './types';
import { ObservabilityLogger, MetricsService } from '@libs/observability';

/**
 * Redis Streams event publisher implementation
 * Provides persistent event publishing with Redis Streams
 */
@Injectable()
export class RedisStreamsPublisher implements EventPublisher, OnModuleDestroy {
  private readonly logger = new Logger(RedisStreamsPublisher.name);
  private redis: Redis;
  private readonly defaultStream: string;

  constructor(
    private readonly config: RedisStreamsConfig,
    private readonly loggerService: ObservabilityLogger,
    private readonly metricsService: MetricsService
  ) {
    this.defaultStream = config.defaultStream || 'events';
    this.redis = this.createRedisClient();
  }

  /**
   * Create Redis client with configuration
   */
  private createRedisClient(): Redis {
    const redisConfig = this.config.redis || {};
    
    const client = new Redis({
      host: redisConfig.host || 'localhost',
      port: redisConfig.port || 6379,
      password: redisConfig.password,
      db: redisConfig.db || 0,
      maxRetriesPerRequest: redisConfig.maxRetriesPerRequest || 3,
      lazyConnect: redisConfig.lazyConnect || true,
      reconnectOnError: (err) => {
        this.logger.error(`Redis connection error: ${err.message}`);
        return true;
      }
    });

    client.on('connect', () => {
      this.logger.log('Connected to Redis for event publishing');
      this.metricsService.incrementCounter('messaging_redis_connections_total', { status: 'connected' });
    });

    client.on('error', (error) => {
      this.logger.error(`Redis error: ${error.message}`);
      this.metricsService.incrementCounter('messaging_redis_connections_total', { status: 'error' });
    });

    client.on('ready', () => {
      this.logger.log('Redis client ready for event publishing');
    });

    return client;
  }

  /**
   * Publish a single event to Redis Stream
   */
  async publish(event: DomainEvent, options?: RedisPublishOptions): Promise<void> {
    const streamName = options?.stream || this.defaultStream;
    const startTime = Date.now();

    try {
      this.logger.debug(`Publishing event ${event.type} to stream ${streamName}`);

      // Serialize event to Redis stream format
      const streamData = this.serializeEvent(event);

      // Convert streamData to flat array format for XADD
      const streamArgs = Object.entries(streamData).flat();

      // Execute XADD command with optional trimming
      let streamId: string;
      if (options?.maxLength) {
        if (options.approximateMaxLength) {
          streamId = await this.redis.xadd(streamName, 'MAXLEN', '~', options.maxLength, '*', ...streamArgs);
        } else {
          streamId = await this.redis.xadd(streamName, 'MAXLEN', options.maxLength, '*', ...streamArgs);
        }
      } else {
        streamId = await this.redis.xadd(streamName, '*', ...streamArgs);
      }

      const duration = Date.now() - startTime;
      
      this.loggerService.log({
        message: 'Event published to Redis Stream',
        eventId: event.id,
        eventType: event.type,
        streamName,
        streamId,
        duration
      });

      this.metricsService.incrementCounter('messaging_events_published_total', {
        stream: streamName,
        eventType: event.type,
        status: 'success'
      });

      this.metricsService.observeHistogram('messaging_publish_duration_ms', duration, {
        stream: streamName,
        eventType: event.type
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.error(`Failed to publish event ${event.type}: ${errorMessage}`);
      
      this.loggerService.error({
        message: 'Event publishing failed',
        eventId: event.id,
        eventType: event.type,
        streamName,
        error: errorMessage,
        duration
      });

      this.metricsService.incrementCounter('messaging_events_published_total', {
        stream: streamName,
        eventType: event.type,
        status: 'error'
      });

      throw error;
    }
  }

  /**
   * Publish multiple events in batch
   */
  async publishBatch(events: DomainEvent[], options?: RedisPublishOptions): Promise<void> {
    const streamName = options?.stream || this.defaultStream;
    const startTime = Date.now();

    try {
      this.logger.debug(`Publishing ${events.length} events to stream ${streamName}`);

      // Use pipeline for batch publishing
      const pipeline = this.redis.pipeline();

      for (const event of events) {
        const streamData = this.serializeEvent(event);
        const streamArgs = Object.entries(streamData).flat();
        
        if (options?.maxLength) {
          if (options.approximateMaxLength) {
            pipeline.xadd(streamName, 'MAXLEN', '~', options.maxLength, '*', ...streamArgs);
          } else {
            pipeline.xadd(streamName, 'MAXLEN', options.maxLength, '*', ...streamArgs);
          }
        } else {
          pipeline.xadd(streamName, '*', ...streamArgs);
        }
      }

      await pipeline.exec();

      const duration = Date.now() - startTime;
      
      this.loggerService.log({
        message: 'Batch events published to Redis Stream',
        eventCount: events.length,
        streamName,
        duration
      });

      this.metricsService.incrementCounter('messaging_batch_events_published_total', {
        stream: streamName,
        status: 'success'
      }, events.length);

      this.metricsService.observeHistogram('messaging_batch_publish_duration_ms', duration, {
        stream: streamName,
        eventCount: events.length.toString()
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.error(`Failed to publish batch events: ${errorMessage}`);
      
      this.loggerService.error({
        message: 'Batch event publishing failed',
        eventCount: events.length,
        streamName,
        error: errorMessage,
        duration
      });

      this.metricsService.incrementCounter('messaging_batch_events_published_total', {
        stream: streamName,
        status: 'error'
      }, events.length);

      throw error;
    }
  }

  /**
   * Serialize event for Redis stream storage
   */
  private serializeEvent(event: DomainEvent): Record<string, string> {
    return {
      id: event.id,
      type: event.type,
      timestamp: event.timestamp.toISOString(),
      version: event.version,
      source: event.source,
      correlationId: event.correlationId || '',
      data: JSON.stringify(event.data),
      metadata: JSON.stringify(event.metadata || {})
    };
  }

  /**
   * Check if publisher is healthy
   */
  async isHealthy(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Health check failed: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Comprehensive health check for messaging Redis instance
   */
  async getHealthStatus(): Promise<import('@libs/shared-types').RedisHealth> {
    const startTime = Date.now();
    
    try {
      // Test connection with ping
      await this.redis.ping();
      
      // Get Redis info for additional metrics
      const info = await this.redis.info();
      const memory = await this.redis.info('memory');
      const clients = await this.redis.info('clients');
      
      const responseTime = Date.now() - startTime;
      
      // Parse info for useful metrics
      const memoryUsage = this.parseRedisInfo(memory, 'used_memory');
      const connectedClients = this.parseRedisInfo(clients, 'connected_clients');
      const totalCommands = this.parseRedisInfo(info, 'total_commands_processed');
      
      // Check stream health by getting info about default stream
      let streamInfo: any = null;
      try {
        streamInfo = await this.redis.xinfo('STREAM', this.defaultStream);
      } catch (error) {
        // Stream might not exist yet, which is okay
        streamInfo = null;
      }
      
      return {
        status: 'ok',
        responseTime,
        memoryUsage: memoryUsage ? parseInt(memoryUsage) : undefined,
        connectedClients: connectedClients ? parseInt(connectedClients) : undefined,
        commandsProcessed: totalCommands ? parseInt(totalCommands) : undefined,
        details: {
          defaultStream: this.defaultStream,
          streamExists: streamInfo !== null,
          streamLength: streamInfo && Array.isArray(streamInfo) ? streamInfo[1] : 0,
          redisVersion: this.parseRedisInfo(info, 'redis_version'),
          uptimeInSeconds: this.parseRedisInfo(info, 'uptime_in_seconds')
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.error(`Messaging health check failed: ${errorMessage}`);
      this.loggerService.error({
        message: 'Messaging health check failed',
        error: errorMessage,
        responseTime
      });
      
      return {
        status: 'error',
        responseTime,
        error: errorMessage
      };
    }
  }

  /**
   * Parse Redis INFO command output for specific keys
   */
  private parseRedisInfo(info: string, key: string): string | undefined {
    const lines = info.split('\r\n');
    const line = lines.find(l => l.startsWith(`${key}:`));
    return line ? line.split(':')[1] : undefined;
  }

  /**
   * Cleanup on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    try {
      this.logger.log('Closing Redis connection for event publisher');
      await this.redis.quit();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error closing Redis connection: ${errorMessage}`);
    }
  }
}
