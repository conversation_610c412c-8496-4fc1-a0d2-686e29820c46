import { Module, DynamicModule, Global } from '@nestjs/common';
import { InMemoryEventBus } from './events/in-memory-event-bus';
import { RedisStreamsPublisher } from './redis/redis-streams-publisher';
import { RedisStreamsConfig } from './redis/types';
import { ObservabilityModule, ObservabilityLogger, MetricsService, MetricsModule } from '@libs/observability';

/**
 * Configuration for the messaging module
 */
export interface MessagingModuleOptions {
  /**
   * Use in-memory event bus for development/testing
   */
  useInMemory?: boolean;

  /**
   * Redis Streams configuration
   */
  redis?: RedisStreamsConfig;

  /**
   * Global module registration
   */
  isGlobal?: boolean;
}

/**
 * Messaging module providing event bus and persistent messaging capabilities
 */
@Module({})
export class MessagingModule {
  /**
   * Register messaging module with configuration
   */
  static forRoot(options: MessagingModuleOptions = {}): DynamicModule {
    const providers: any[] = [
      {
        provide: 'MESSAGING_CONFIG',
        useValue: options
      }
    ];

    // Add observability and metrics modules
    const imports = [ObservabilityModule, MetricsModule.forRoot()];

    // Configure event bus based on options
    if (options.useInMemory) {
      providers.push({
        provide: 'EVENT_BUS',
        useFactory: (loggerFactory: any) => {
          return new InMemoryEventBus(loggerFactory);
        },
        inject: ['LOGGER_FACTORY']
      });
    }

    // Configure Redis Streams publisher if config provided
    if (options.redis) {
      providers.push(
        {
          provide: 'REDIS_STREAMS_CONFIG',
          useValue: options.redis
        },
        {
          provide: RedisStreamsPublisher,
          useFactory: (config: RedisStreamsConfig, loggerFactory: any, metricsService: MetricsService) => {
            const logger = loggerFactory.createLogger('RedisStreamsPublisher');
            return new RedisStreamsPublisher(config, logger, metricsService);
          },
          inject: ['REDIS_STREAMS_CONFIG', 'LOGGER_FACTORY', MetricsService]
        },
        {
          provide: 'EVENT_PUBLISHER',
          useExisting: RedisStreamsPublisher
        }
      );
    }

    return {
      module: MessagingModule,
      imports,
      providers,
      exports: providers.map(p => typeof p === 'object' && 'provide' in p ? p.provide : p),
      global: options.isGlobal || false
    };
  }

  /**
   * Register messaging module with async configuration factory
   */
  static forRootAsync(options: {
    useFactory: (...args: any[]) => MessagingModuleOptions | Promise<MessagingModuleOptions>;
    inject?: any[];
    imports?: any[];
  }): DynamicModule {
    const asyncProvider = {
      provide: 'MESSAGING_CONFIG',
      useFactory: options.useFactory,
      inject: options.inject || []
    };

    return {
      module: MessagingModule,
      imports: [
        ...(options.imports || []),
        ObservabilityModule, 
        MetricsModule.forRoot()
      ],
      providers: [
        asyncProvider,
        {
          provide: 'EVENT_BUS',
          useFactory: (config: MessagingModuleOptions, loggerFactory) => {
            if (config.useInMemory) {
              return new InMemoryEventBus(loggerFactory);
            }
            return null; // Will be handled by Redis publisher if configured
          },
          inject: ['MESSAGING_CONFIG', 'LOGGER_FACTORY']
        },
        {
          provide: RedisStreamsPublisher,
          useFactory: (config: MessagingModuleOptions, loggerFactory, metricsService) => {
            if (config.redis) {
              const logger = loggerFactory.createLogger('RedisStreamsPublisher');
              return new RedisStreamsPublisher(config.redis, logger, metricsService);
            }
            return null;
          },
          inject: ['MESSAGING_CONFIG', 'LOGGER_FACTORY', MetricsService]
        },
        {
          provide: 'EVENT_PUBLISHER',
          useFactory: (redisPublisher) => redisPublisher,
          inject: [RedisStreamsPublisher]
        }
      ],
      exports: ['MESSAGING_CONFIG', 'EVENT_BUS', RedisStreamsPublisher, 'EVENT_PUBLISHER'],
      global: true // Make async modules global by default
    };
  }
}
