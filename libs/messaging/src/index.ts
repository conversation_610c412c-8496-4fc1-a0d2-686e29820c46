// Event types and interfaces
export * from './events/event.types';
export * from './events/http-events.types';

// Event factory for creating domain events
export { EventFactory } from './events/event-factory';

// In-memory event bus implementation
export { InMemoryEventBus } from './events/in-memory-event-bus';

// Redis Streams implementation
export * from './redis';

// NestJS module
export { MessagingModule, MessagingModuleOptions } from './messaging.module';

// Re-export commonly used types for convenience
export type { 
  DomainEvent, 
  EventPublisher, 
  EventHandler, 
  EventBus,
  EventSubscriberConfig,
  MessageQueueConfig,
  PublishOptions 
} from './events/event.types';

export { SerializationStrategy, DeliveryGuarantee } from './events/event.types';

// Health check types
export type { RedisHealth } from '@libs/shared-types';
