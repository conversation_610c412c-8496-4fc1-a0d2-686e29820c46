import { DomainEvent } from './event.types';

/**
 * HTTP Request Event
 * Published when an HTTP request is initiated
 */
export interface HttpRequestEvent extends DomainEvent {
  type: 'http.request';
  data: {
    method: string;
    url: string;
    serviceName: string;
    operationName?: string;
    correlationId: string;
    userAgent?: string;
    ipAddress?: string;
    headers?: Record<string, string>;
    queryParams?: Record<string, any>;
    hasBody: boolean;
    contentType?: string;
  };
}

/**
 * HTTP Response Event
 * Published when an HTTP request completes successfully
 */
export interface HttpResponseEvent extends DomainEvent {
  type: 'http.response';
  data: {
    method: string;
    url: string;
    serviceName: string;
    operationName?: string;
    correlationId: string;
    statusCode: number;
    responseTime: number;
    responseSize?: number;
    cacheHit?: boolean;
    retryAttempt?: number;
  };
}

/**
 * HTTP Error Event
 * Published when an HTTP request fails or errors
 */
export interface HttpErrorEvent extends DomainEvent {
  type: 'http.error';
  data: {
    method: string;
    url: string;
    serviceName: string;
    operationName?: string;
    correlationId: string;
    statusCode?: number;
    errorType: string;
    errorMessage: string;
    errorCode?: string;
    responseTime: number;
    retryAttempt?: number;
    circuitBreakerState?: string;
  };
}

/**
 * Circuit Breaker State Change Event
 * Published when circuit breaker state changes
 */
export interface CircuitBreakerStateChangedEvent extends DomainEvent {
  type: 'circuit-breaker.state-changed';
  data: {
    serviceName: string;
    operationName?: string;
    previousState: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
    newState: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
    errorRate: number;
    requestCount: number;
    failureCount: number;
    lastFailureReason?: string;
    resetTime?: Date;
  };
}

/**
 * Cache Operation Event
 * Published for cache hits/misses and operations
 */
export interface CacheOperationEvent extends DomainEvent {
  type: 'cache.operation';
  data: {
    operation: 'get' | 'set' | 'delete' | 'invalidate';
    key: string;
    hit: boolean;
    ttl?: number;
    size?: number;
    correlationId?: string;
    serviceName?: string;
    responseTime: number;
  };
}

/**
 * Union type for all HTTP-related events
 */
export type HttpLifecycleEvent = 
  | HttpRequestEvent 
  | HttpResponseEvent 
  | HttpErrorEvent 
  | CircuitBreakerStateChangedEvent
  | CacheOperationEvent;