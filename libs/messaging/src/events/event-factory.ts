import { DomainEvent } from './event.types';
import { 
  HttpRequestEvent, 
  HttpResponseEvent, 
  HttpErrorEvent, 
  CircuitBreakerStateChangedEvent,
  CacheOperationEvent 
} from './http-events.types';
import { randomUUID } from 'crypto';

/**
 * Factory for creating domain events with consistent structure
 */
export class EventFactory {
  /**
   * Create a new domain event
   */
  static create<T = any>(
    type: string,
    data: T,
    options: {
      source: string;
      version?: string;
      correlationId?: string;
      metadata?: Record<string, any>;
      timestamp?: Date;
    }
  ): DomainEvent {
    return {
      id: randomUUID(),
      type,
      data,
      timestamp: options.timestamp || new Date(),
      version: options.version || '1.0',
      source: options.source,
      correlationId: options.correlationId,
      metadata: options.metadata || {}
    };
  }

  /**
   * Create user-related events
   */
  static createUserEvent<T = any>(
    eventType: string,
    userId: string,
    data: T,
    options?: {
      version?: string;
      correlationId?: string;
      metadata?: Record<string, any>;
    }
  ): DomainEvent {
    return this.create(`user.${eventType}`, data, {
      source: 'user-service',
      version: options?.version || '1.0',
      correlationId: options?.correlationId,
      metadata: {
        userId,
        ...options?.metadata
      }
    });
  }

  /**
   * Predefined user events
   */
  static userCreated(userId: string, userData: any, correlationId?: string): DomainEvent {
    return this.createUserEvent('created', userId, userData, { correlationId });
  }

  static userUpdated(userId: string, userData: any, correlationId?: string): DomainEvent {
    return this.createUserEvent('updated', userId, userData, { correlationId });
  }

  static userDeleted(userId: string, correlationId?: string): DomainEvent {
    return this.createUserEvent('deleted', userId, { userId }, { correlationId });
  }

  static userRestored(userId: string, correlationId?: string): DomainEvent {
    return this.createUserEvent('restored', userId, { userId }, { correlationId });
  }

  /**
   * HTTP Lifecycle Events
   */
  static httpRequest(data: {
    method: string;
    url: string;
    serviceName: string;
    operationName?: string;
    correlationId: string;
    userAgent?: string;
    ipAddress?: string;
    headers?: Record<string, string>;
    queryParams?: Record<string, any>;
    hasBody: boolean;
    contentType?: string;
  }): HttpRequestEvent {
    return this.create('http.request', data, {
      source: data.serviceName,
      correlationId: data.correlationId,
      metadata: {
        httpMethod: data.method,
        targetService: data.serviceName
      }
    }) as HttpRequestEvent;
  }

  static httpResponse(data: {
    method: string;
    url: string;
    serviceName: string;
    operationName?: string;
    correlationId: string;
    statusCode: number;
    responseTime: number;
    responseSize?: number;
    cacheHit?: boolean;
    retryAttempt?: number;
  }): HttpResponseEvent {
    return this.create('http.response', data, {
      source: data.serviceName,
      correlationId: data.correlationId,
      metadata: {
        httpMethod: data.method,
        targetService: data.serviceName,
        success: data.statusCode >= 200 && data.statusCode < 400
      }
    }) as HttpResponseEvent;
  }

  static httpError(data: {
    method: string;
    url: string;
    serviceName: string;
    operationName?: string;
    correlationId: string;
    statusCode?: number;
    errorType: string;
    errorMessage: string;
    errorCode?: string;
    responseTime: number;
    retryAttempt?: number;
    circuitBreakerState?: string;
  }): HttpErrorEvent {
    return this.create('http.error', data, {
      source: data.serviceName,
      correlationId: data.correlationId,
      metadata: {
        httpMethod: data.method,
        targetService: data.serviceName,
        errorSeverity: data.statusCode && data.statusCode >= 500 ? 'high' : 'medium'
      }
    }) as HttpErrorEvent;
  }

  static circuitBreakerStateChanged(data: {
    serviceName: string;
    operationName?: string;
    previousState: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
    newState: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
    errorRate: number;
    requestCount: number;
    failureCount: number;
    lastFailureReason?: string;
    resetTime?: Date;
  }): CircuitBreakerStateChangedEvent {
    return this.create('circuit-breaker.state-changed', data, {
      source: data.serviceName,
      metadata: {
        reliability: data.errorRate,
        targetService: data.serviceName,
        severity: data.newState === 'OPEN' ? 'critical' : 'info'
      }
    }) as CircuitBreakerStateChangedEvent;
  }

  static cacheOperation(data: {
    operation: 'get' | 'set' | 'delete' | 'invalidate';
    key: string;
    hit: boolean;
    ttl?: number;
    size?: number;
    correlationId?: string;
    serviceName?: string;
    responseTime: number;
  }): CacheOperationEvent {
    return this.create('cache.operation', data, {
      source: data.serviceName || 'cache-service',
      correlationId: data.correlationId,
      metadata: {
        cacheEfficiency: data.hit ? 'hit' : 'miss',
        targetService: data.serviceName
      }
    }) as CacheOperationEvent;
  }
}
