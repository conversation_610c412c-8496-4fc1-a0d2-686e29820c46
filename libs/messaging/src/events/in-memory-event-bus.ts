import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventEmitter } from 'events';
import { DomainEvent, EventBus, EventHandler } from './event.types';
import { ObservabilityLogger } from '@libs/observability';

/**
 * In-memory event bus implementation using Node.js EventEmitter
 * Suitable for development, testing, and single-service scenarios
 */
@Injectable()
export class InMemoryEventBus implements EventBus {
  private readonly eventEmitter = new EventEmitter();
  private readonly handlers = new Map<string, Set<EventHandler>>();
  private readonly logger: ObservabilityLogger;

  constructor(@Inject('LOGGER_FACTORY') private readonly loggerFactory: any) {
    // Create a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger(InMemoryEventBus.name);
    
    // Increase max listeners to handle many event types
    this.eventEmitter.setMaxListeners(100);
  }

  /**
   * Publish event to the bus
   */
  async publish(event: DomainEvent): Promise<void> {
    try {
      this.logger.debug(`Publishing event: ${event.type} (${event.id})`);
      
      // Log structured event publication data
      this.logger.log({
        message: `Event published: ${event.type}`,
        eventId: event.id,
        eventType: event.type,
        source: event.source,
        correlationId: event.correlationId,
        timestamp: event.timestamp
      });

      // Emit the event asynchronously
      setImmediate(() => {
        this.eventEmitter.emit(event.type, event);
        this.eventEmitter.emit('*', event); // Wildcard for global listeners
      });

      this.logger.debug(`Successfully published event: ${event.type}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error({
        message: `Failed to publish event: ${event.type}`,
        eventId: event.id,
        eventType: event.type,
        source: event.source,
        correlationId: event.correlationId,
        error: errorMessage,
        stack: errorStack
      });
      throw error;
    }
  }

  /**
   * Subscribe to events of a specific type
   */
  subscribe<T extends DomainEvent>(
    eventType: string,
    handler: EventHandler<T>
  ): void {
    try {
      this.logger.debug(`Subscribing handler to event type: ${eventType}`);
      
      // Add handler to our registry
      if (!this.handlers.has(eventType)) {
        this.handlers.set(eventType, new Set());
      }
      this.handlers.get(eventType)!.add(handler);

      // Create event listener wrapper
      const eventListener = async (event: T) => {
        try {
          this.logger.debug(`Handling event: ${event.type} (${event.id})`);
          
          this.logger.debug({
            message: `Event handling started: ${event.type}`,
            eventId: event.id,
            eventType: event.type,
            handlerName: handler.constructor.name,
            source: event.source,
            correlationId: event.correlationId
          });

          await handler.handle(event);
          
          this.logger.debug({
            message: `Event handled successfully: ${event.type}`,
            eventId: event.id,
            eventType: event.type,
            handlerName: handler.constructor.name,
            source: event.source,
            correlationId: event.correlationId,
            processingTime: Date.now() - event.timestamp.getTime()
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          const errorStack = error instanceof Error ? error.stack : undefined;
          this.logger.error({
            message: `Event handler failed: ${event.type}`,
            eventId: event.id,
            eventType: event.type,
            handlerName: handler.constructor.name,
            source: event.source,
            correlationId: event.correlationId,
            error: errorMessage,
            stack: errorStack,
            processingTime: Date.now() - event.timestamp.getTime()
          });
          
          // In a production system, you might want to:
          // - Send to dead letter queue
          // - Implement retry logic
          // - Send alerts
          throw error;
        }
      };

      // Store the listener on the handler for later removal
      (handler as any)._eventListener = eventListener;

      // Subscribe to the event
      this.eventEmitter.on(eventType, eventListener);
      
      this.logger.log({
        message: `Event handler subscribed: ${eventType}`,
        eventType,
        handlerName: handler.constructor.name,
        totalHandlers: (this.handlers.get(eventType)?.size || 0)
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error({
        message: `Failed to subscribe handler: ${eventType}`,
        eventType,
        handlerName: handler.constructor.name,
        error: errorMessage,
        stack: errorStack
      });
      throw error;
    }
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(eventType: string, handler: EventHandler): void {
    try {
      this.logger.debug(`Unsubscribing handler from event type: ${eventType}`);
      
      // Remove from our registry
      const handlers = this.handlers.get(eventType);
      if (handlers) {
        handlers.delete(handler);
        if (handlers.size === 0) {
          this.handlers.delete(eventType);
        }
      }

      // Remove the event listener
      const eventListener = (handler as any)._eventListener;
      if (eventListener) {
        this.eventEmitter.off(eventType, eventListener);
        delete (handler as any)._eventListener;
      }
      
      this.logger.log({
        message: `Event handler unsubscribed: ${eventType}`,
        eventType,
        handlerName: handler.constructor.name,
        remainingHandlers: (this.handlers.get(eventType)?.size || 0)
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error({
        message: `Failed to unsubscribe handler: ${eventType}`,
        eventType,
        handlerName: handler.constructor.name,
        error: errorMessage,
        stack: errorStack
      });
      throw error;
    }
  }

  /**
   * Clear all subscribers
   */
  clear(): void {
    try {
      this.logger.debug('Clearing all event subscribers');
      
      this.eventEmitter.removeAllListeners();
      this.handlers.clear();
      
      this.logger.log({
        message: 'All event subscribers cleared',
        totalEventTypes: this.handlers.size,
        totalHandlers: Array.from(this.handlers.values()).reduce((sum, set) => sum + set.size, 0)
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error({
        message: 'Failed to clear event subscribers',
        error: errorMessage,
        stack: errorStack
      });
      throw error;
    }
  }

  /**
   * Get subscriber count for an event type
   */
  getSubscriberCount(eventType: string): number {
    return this.handlers.get(eventType)?.size || 0;
  }

  /**
   * Get all registered event types
   */
  getRegisteredEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Get health status
   */
  isHealthy(): boolean {
    return true; // In-memory bus is always healthy
  }
}
