/**
 * Base interface for all domain events
 */
export interface DomainEvent {
  /**
   * Unique identifier for this event instance
   */
  id: string;
  
  /**
   * Event type identifier
   */
  type: string;
  
  /**
   * Timestamp when the event occurred
   */
  timestamp: Date;
  
  /**
   * Version of the event schema
   */
  version: string;
  
  /**
   * Service that published the event
   */
  source: string;
  
  /**
   * Event payload data
   */
  data: any;
  
  /**
   * Correlation ID for tracing related events
   */
  correlationId?: string;
  
  /**
   * Additional metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Event publisher interface
 */
export interface EventPublisher {
  /**
   * Publish a single event
   */
  publish(event: DomainEvent): Promise<void>;
  
  /**
   * Publish multiple events
   */
  publishBatch(events: DomainEvent[]): Promise<void>;
  
  /**
   * Check if publisher is healthy
   */
  isHealthy(): Promise<boolean>;
}

/**
 * Event handler interface
 */
export interface EventHandler<T extends DomainEvent = DomainEvent> {
  /**
   * Handle the event
   */
  handle(event: T): Promise<void>;
  
  /**
   * Event types this handler supports
   */
  supportedEvents: string[];
}

/**
 * Event subscriber configuration
 */
export interface EventSubscriberConfig {
  /**
   * Topic or queue name to subscribe to
   */
  topic: string;
  
  /**
   * Consumer group name (for group consumption)
   */
  group?: string;
  
  /**
   * Event types to filter for
   */
  eventTypes?: string[];
  
  /**
   * Maximum retry attempts
   */
  maxRetries?: number;
  
  /**
   * Dead letter queue configuration
   */
  deadLetterQueue?: string;
}

/**
 * Event bus interface for internal event handling
 */
export interface EventBus {
  /**
   * Publish event to the bus
   */
  publish(event: DomainEvent): Promise<void>;
  
  /**
   * Subscribe to events
   */
  subscribe<T extends DomainEvent>(
    eventType: string,
    handler: EventHandler<T>
  ): void;
  
  /**
   * Unsubscribe from events
   */
  unsubscribe(eventType: string, handler: EventHandler): void;
  
  /**
   * Clear all subscribers
   */
  clear(): void;
}

/**
 * Message queue configuration
 */
export interface MessageQueueConfig {
  /**
   * Queue connection URL
   */
  url?: string;
  
  /**
   * Connection options
   */
  connection?: {
    host?: string;
    port?: number;
    username?: string;
    password?: string;
    vhost?: string;
  };
  
  /**
   * Publisher configuration
   */
  publisher?: {
    exchange?: string;
    routingKey?: string;
    persistent?: boolean;
    confirmMode?: boolean;
  };
  
  /**
   * Consumer configuration
   */
  consumer?: {
    queue?: string;
    prefetch?: number;
    autoAck?: boolean;
    exclusive?: boolean;
  };
  
  /**
   * Retry configuration
   */
  retry?: {
    attempts?: number;
    delay?: number;
    backoff?: 'fixed' | 'exponential';
  };
}

/**
 * Event serialization strategies
 */
export enum SerializationStrategy {
  JSON = 'json',
  AVRO = 'avro',
  PROTOBUF = 'protobuf'
}

/**
 * Event delivery guarantees
 */
export enum DeliveryGuarantee {
  AT_MOST_ONCE = 'at-most-once',
  AT_LEAST_ONCE = 'at-least-once',
  EXACTLY_ONCE = 'exactly-once'
}

/**
 * Event publishing options
 */
export interface PublishOptions {
  /**
   * Topic to publish to
   */
  topic?: string;
  
  /**
   * Routing key for message routing
   */
  routingKey?: string;
  
  /**
   * Message persistence
   */
  persistent?: boolean;
  
  /**
   * Delivery guarantee level
   */
  deliveryGuarantee?: DeliveryGuarantee;
  
  /**
   * Message TTL in milliseconds
   */
  ttl?: number;
  
  /**
   * Delay before delivery in milliseconds
   */
  delay?: number;
}
