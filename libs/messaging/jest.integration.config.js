const baseConfig = require('./jest.config');

module.exports = {
  ...baseConfig,
  displayName: 'messaging-integration',
  testMatch: ['<rootDir>/test/integration/**/*.spec.ts'],
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/test/integration/setup.ts'],
  testTimeout: 30000, // Longer timeout for integration tests
  // Run integration tests sequentially to avoid Redis conflicts
  maxWorkers: 1,
  // Integration tests require real Redis
  globalSetup: '<rootDir>/test/integration/global-setup.ts',
  globalTeardown: '<rootDir>/test/integration/global-teardown.ts'
};