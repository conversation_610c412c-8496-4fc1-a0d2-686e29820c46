import { RedisStreamsPublisher } from '../../src/redis/redis-streams-publisher';
import { DomainEvent } from '../../src/events/event.types';
import { MockFactory } from '@libs/testing-utils';

// Mock ioredis with ioredis-mock for unit tests
jest.mock('ioredis', () => {
  const IoRedisMock = require('ioredis-mock');
  return {
    Redis: IoRedisMock
  };
});

describe('RedisStreamsPublisher Unit Tests', () => {
  let publisher: RedisStreamsPublisher;
  let mockLoggerFactory: any;
  let mockLogger: any;
  let mockMetricsService: any;

  const mockConfig = {
    host: 'localhost',
    port: 6379,
    db: 0,
    streamName: 'events',
    maxLength: 10000,
    batchSize: 100
  };

  beforeEach(async () => {
    mockLoggerFactory = MockFactory.createLoggerFactory();
    mockLogger = mockLoggerFactory.createLogger('RedisStreamsPublisher');
    mockMetricsService = MockFactory.createMetricsService();

    publisher = new RedisStreamsPublisher(
      mockConfig,
      mockLoggerFactory,
      mockMetricsService
    );

    await publisher.onModuleInit();
  });

  afterEach(async () => {
    await publisher.onModuleDestroy();
  });

  describe('Connection Management', () => {
    it('should initialize Redis client with correct configuration', () => {
      expect(publisher).toBeDefined();
      expect(mockLoggerFactory.createLogger).toHaveBeenCalledWith('RedisStreamsPublisher');
    });

    it('should connect to Redis on module init', async () => {
      const newPublisher = new RedisStreamsPublisher(
        mockConfig,
        mockLoggerFactory,
        mockMetricsService
      );

      await expect(newPublisher.onModuleInit()).resolves.not.toThrow();
      await newPublisher.onModuleDestroy();
    });

    it('should disconnect from Redis on module destroy', async () => {
      const testPublisher = new RedisStreamsPublisher(
        mockConfig,
        mockLoggerFactory,
        mockMetricsService
      );

      await testPublisher.onModuleInit();
      await expect(testPublisher.onModuleDestroy()).resolves.not.toThrow();
    });

    it('should handle connection errors gracefully', async () => {
      // Connection errors are handled by ioredis-mock internally
      expect(publisher.isHealthy()).toBe(true);
    });
  });

  describe('Event Publishing', () => {
    const createTestEvent = (overrides: Partial<DomainEvent> = {}): DomainEvent => ({
      id: 'test-event-1',
      type: 'test.event',
      data: { message: 'test message' },
      metadata: { source: 'test-service', correlationId: 'test-123' },
      timestamp: new Date(),
      version: '1.0',
      ...overrides
    });

    it('should publish single event to Redis stream', async () => {
      const testEvent = createTestEvent();

      await expect(publisher.publish(testEvent)).resolves.not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event to Redis stream',
        expect.objectContaining({
          eventId: 'test-event-1',
          eventType: 'test.event',
          streamName: 'events'
        })
      );
    });

    it('should serialize event data correctly', async () => {
      const complexEvent = createTestEvent({
        data: {
          user: { id: 123, name: 'John' },
          metadata: { tags: ['important'], count: 5 },
          nested: { deep: { value: 'test' } }
        }
      });

      await expect(publisher.publish(complexEvent)).resolves.not.toThrow();
    });

    it('should handle events with missing optional fields', async () => {
      const minimalEvent = createTestEvent({
        metadata: { source: 'test' } // No correlationId
      });

      await expect(publisher.publish(minimalEvent)).resolves.not.toThrow();
    });

    it('should publish events with correlation tracking', async () => {
      const eventWithCorrelation = createTestEvent({
        metadata: { 
          source: 'api-gateway',
          correlationId: 'req-456',
          userId: 'user-789'
        }
      });

      await publisher.publish(eventWithCorrelation);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event to Redis stream',
        expect.objectContaining({
          correlationId: 'req-456'
        })
      );
    });

    it('should handle publishing errors gracefully', async () => {
      // Create an invalid event to trigger error
      const invalidEvent = { invalid: 'event' } as any;

      await expect(publisher.publish(invalidEvent)).resolves.not.toThrow();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to publish event to Redis stream',
        expect.objectContaining({
          error: expect.any(String)
        })
      );
    });

    it('should increment metrics on successful publish', async () => {
      const testEvent = createTestEvent();

      await publisher.publish(testEvent);

      expect(mockMetricsService.incrementCounter).toHaveBeenCalledWith(
        'events_published_total',
        { stream: 'events', event_type: 'test.event' }
      );
    });

    it('should track publish duration metrics', async () => {
      const testEvent = createTestEvent();

      await publisher.publish(testEvent);

      expect(mockMetricsService.observeHistogram).toHaveBeenCalledWith(
        'event_publish_duration_seconds',
        expect.any(Number),
        { stream: 'events' }
      );
    });
  });

  describe('Batch Publishing', () => {
    const createTestEvents = (count: number): DomainEvent[] => {
      return Array.from({ length: count }, (_, i) => ({
        id: `batch-event-${i}`,
        type: 'batch.test',
        data: { index: i, message: `message ${i}` },
        metadata: { source: 'batch-test', correlationId: `batch-${i}` },
        timestamp: new Date(),
        version: '1.0'
      }));
    };

    it('should publish multiple events in batch', async () => {
      const events = createTestEvents(5);

      await expect(publisher.publishBatch(events)).resolves.not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event batch to Redis stream',
        expect.objectContaining({
          batchSize: 5,
          streamName: 'events'
        })
      );
    });

    it('should handle empty batch gracefully', async () => {
      await expect(publisher.publishBatch([])).resolves.not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event batch to Redis stream',
        expect.objectContaining({
          batchSize: 0
        })
      );
    });

    it('should handle large batch publishing', async () => {
      const largeEvents = createTestEvents(150); // Larger than default batch size

      await expect(publisher.publishBatch(largeEvents)).resolves.not.toThrow();

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event batch to Redis stream',
        expect.objectContaining({
          batchSize: 150
        })
      );
    });

    it('should track batch metrics', async () => {
      const events = createTestEvents(10);

      await publisher.publishBatch(events);

      expect(mockMetricsService.incrementCounter).toHaveBeenCalledWith(
        'event_batches_published_total',
        { stream: 'events' }
      );

      expect(mockMetricsService.observeHistogram).toHaveBeenCalledWith(
        'event_batch_size',
        10,
        { stream: 'events' }
      );
    });

    it('should handle batch publishing errors', async () => {
      const invalidEvents = [{ invalid: 'batch' }] as any[];

      await expect(publisher.publishBatch(invalidEvents)).resolves.not.toThrow();

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to publish event batch to Redis stream',
        expect.objectContaining({
          error: expect.any(String)
        })
      );
    });
  });

  describe('Event Serialization', () => {
    it('should serialize event data to JSON string', () => {
      const testData = {
        user: { id: 123, name: 'Test User' },
        action: 'login',
        timestamp: new Date().toISOString(),
        metadata: { ip: '***********', userAgent: 'Test Agent' }
      };

      const serialized = (publisher as any).serializeEvent(testData);

      expect(typeof serialized).toBe('string');
      expect(JSON.parse(serialized)).toEqual(testData);
    });

    it('should handle complex nested objects', () => {
      const complexData = {
        level1: {
          level2: {
            level3: {
              array: [1, 2, { nested: 'value' }],
              boolean: true,
              null: null
            }
          }
        }
      };

      const serialized = (publisher as any).serializeEvent(complexData);
      const parsed = JSON.parse(serialized);

      expect(parsed).toEqual(complexData);
      expect(parsed.level1.level2.level3.array[2].nested).toBe('value');
    });

    it('should handle Date objects in serialization', () => {
      const testDate = new Date('2024-01-01T12:00:00Z');
      const dataWithDate = {
        event: 'test',
        timestamp: testDate,
        nested: { date: testDate }
      };

      const serialized = (publisher as any).serializeEvent(dataWithDate);
      const parsed = JSON.parse(serialized);

      expect(parsed.timestamp).toBe(testDate.toISOString());
      expect(parsed.nested.date).toBe(testDate.toISOString());
    });

    it('should handle serialization errors gracefully', () => {
      // Create circular reference
      const circularObj: any = { prop: 'value' };
      circularObj.circular = circularObj;

      expect(() => {
        (publisher as any).serializeEvent(circularObj);
      }).toThrow(); // JSON.stringify will throw on circular references
    });
  });

  describe('Health Checks', () => {
    it('should report healthy status when Redis is connected', () => {
      expect(publisher.isHealthy()).toBe(true);
    });

    it('should provide comprehensive health status', async () => {
      const healthStatus = await publisher.getHealthStatus();

      expect(healthStatus).toEqual(
        expect.objectContaining({
          status: expect.any(String),
          responseTime: expect.any(Number)
        })
      );
    });

    it('should include Redis connection details in health status', async () => {
      const healthStatus = await publisher.getHealthStatus();

      expect(healthStatus.details).toBeDefined();
      expect(healthStatus.details.streamName).toBe('events');
      expect(healthStatus.details.connected).toBeDefined();
    });

    it('should handle health check errors', async () => {
      // ioredis-mock should handle this gracefully
      const healthStatus = await publisher.getHealthStatus();
      expect(healthStatus).toBeDefined();
    });
  });

  describe('Stream Configuration', () => {
    it('should use configured stream name', async () => {
      const customConfig = { ...mockConfig, streamName: 'custom-events' };
      const customPublisher = new RedisStreamsPublisher(
        customConfig,
        mockLoggerFactory,
        mockMetricsService
      );

      await customPublisher.onModuleInit();

      const testEvent = {
        id: 'custom-test',
        type: 'custom.event',
        data: {},
        metadata: { source: 'test' },
        timestamp: new Date(),
        version: '1.0'
      };

      await customPublisher.publish(testEvent);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event to Redis stream',
        expect.objectContaining({
          streamName: 'custom-events'
        })
      );

      await customPublisher.onModuleDestroy();
    });

    it('should respect max length configuration', async () => {
      const configWithMaxLength = { ...mockConfig, maxLength: 5000 };
      const publisher = new RedisStreamsPublisher(
        configWithMaxLength,
        mockLoggerFactory,
        mockMetricsService
      );

      await publisher.onModuleInit();
      
      // Configuration is used internally
      expect(publisher).toBeDefined();

      await publisher.onModuleDestroy();
    });

    it('should handle missing optional configuration', async () => {
      const minimalConfig = {
        host: 'localhost',
        port: 6379,
        streamName: 'minimal-events'
      };

      const minimalPublisher = new RedisStreamsPublisher(
        minimalConfig,
        mockLoggerFactory,
        mockMetricsService
      );

      await expect(minimalPublisher.onModuleInit()).resolves.not.toThrow();
      await minimalPublisher.onModuleDestroy();
    });
  });

  describe('Error Resilience', () => {
    it('should continue operation after publish failures', async () => {
      // First publish succeeds
      const event1 = {
        id: 'success-1',
        type: 'test.success',
        data: { success: true },
        metadata: { source: 'test' },
        timestamp: new Date(),
        version: '1.0'
      };

      await publisher.publish(event1);

      // Simulate error scenario (invalid event)
      const invalidEvent = null as any;
      await publisher.publish(invalidEvent);

      // Subsequent publish should still work
      const event2 = {
        id: 'success-2',
        type: 'test.recovery',
        data: { recovered: true },
        metadata: { source: 'test' },
        timestamp: new Date(),
        version: '1.0'
      };

      await expect(publisher.publish(event2)).resolves.not.toThrow();
    });

    it('should track error metrics', async () => {
      const invalidEvent = { incomplete: 'event' } as any;

      await publisher.publish(invalidEvent);

      expect(mockMetricsService.incrementCounter).toHaveBeenCalledWith(
        'event_publish_errors_total',
        expect.objectContaining({
          stream: 'events'
        })
      );
    });

    it('should provide detailed error logging', async () => {
      const problematicEvent = undefined as any;

      await publisher.publish(problematicEvent);

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to publish event to Redis stream',
        expect.objectContaining({
          eventId: undefined,
          error: expect.any(String)
        })
      );
    });
  });

  describe('Performance Considerations', () => {
    it('should handle rapid sequential publishes', async () => {
      const events = Array.from({ length: 50 }, (_, i) => ({
        id: `rapid-${i}`,
        type: 'rapid.test',
        data: { index: i },
        metadata: { source: 'performance-test' },
        timestamp: new Date(),
        version: '1.0'
      }));

      const publishPromises = events.map(event => publisher.publish(event));

      await expect(Promise.all(publishPromises)).resolves.not.toThrow();
    });

    it('should track performance metrics', async () => {
      const testEvent = {
        id: 'perf-test',
        type: 'performance.test',
        data: { size: 'large'.repeat(100) },
        metadata: { source: 'perf-test' },
        timestamp: new Date(),
        version: '1.0'
      };

      await publisher.publish(testEvent);

      expect(mockMetricsService.observeHistogram).toHaveBeenCalledWith(
        'event_publish_duration_seconds',
        expect.any(Number),
        { stream: 'events' }
      );
    });
  });
});