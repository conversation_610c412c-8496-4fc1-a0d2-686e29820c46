import { InMemoryEventBus } from '../../src/events/in-memory-event-bus';
import { DomainEvent, EventHandler } from '../../src/events/event.types';
import { MockFactory } from '@libs/testing-utils';

describe('InMemoryEventBus Unit Tests', () => {
  let eventBus: InMemoryEventBus;
  let mockLoggerFactory: any;
  let mockLogger: any;

  beforeEach(() => {
    mockLoggerFactory = MockFactory.createLoggerFactory();
    mockLogger = mockLoggerFactory.createLogger('InMemoryEventBus');
    eventBus = new InMemoryEventBus(mockLoggerFactory);
  });

  afterEach(() => {
    eventBus.clear();
  });

  describe('Event Publishing', () => {
    it('should publish single event successfully', async () => {
      const testEvent: DomainEvent = {
        id: 'test-1',
        type: 'test.event',
        data: { message: 'test' },
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      eventBus.subscribe('test.event', handler);

      await eventBus.publish(testEvent);

      // Allow async emission to complete
      await new Promise(resolve => setImmediate(resolve));

      expect(handler.handle).toHaveBeenCalledWith(testEvent);
      expect(handler.handle).toHaveBeenCalledTimes(1);
    });

    it('should emit wildcard events for all published events', async () => {
      const testEvent: DomainEvent = {
        id: 'test-2',
        type: 'user.created',
        data: { userId: 123 },
        source: 'user-service',
        timestamp: new Date(),
        version: '1.0'
      };

      const wildcardHandler = {
        handle: jest.fn(),
        supportedEvents: ['*']
      };
      const specificHandler = {
        handle: jest.fn(),
        supportedEvents: ['user.created']
      };
      
      eventBus.subscribe('*', wildcardHandler);
      eventBus.subscribe('user.created', specificHandler);

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      expect(wildcardHandler.handle).toHaveBeenCalledWith(testEvent);
      expect(specificHandler.handle).toHaveBeenCalledWith(testEvent);
    });

    it('should log published events', async () => {
      const testEvent: DomainEvent = {
        id: 'test-3',
        type: 'cache.hit',
        data: { key: 'user:123' },
        source: 'cache-service',
        correlationId: 'req-123',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Publishing event',
        expect.objectContaining({
          eventId: 'test-3',
          eventType: 'cache.hit',
          correlationId: 'req-123'
        })
      );
    });

    it('should handle publishing errors gracefully', async () => {
      const invalidEvent = null as any;

      await expect(eventBus.publish(invalidEvent)).resolves.not.toThrow();
      
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to publish event',
        expect.objectContaining({
          error: expect.any(String)
        })
      );
    });

    it('should publish events asynchronously', async () => {
      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const testEvent: DomainEvent = {
        id: 'async-test',
        type: 'test.async',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      eventBus.subscribe('test.async', handler);

      // Publish and check immediately - handler should not be called yet
      await eventBus.publish(testEvent);
      expect(handler.handle).not.toHaveBeenCalled();

      // Wait for async emission
      await new Promise(resolve => setImmediate(resolve));
      expect(handler.handle).toHaveBeenCalled();
    });
  });

  describe('Event Subscription', () => {
    it('should register event handlers correctly', () => {
      const handler1 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const handler2 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('user.created', handler1);
      eventBus.subscribe('user.updated', handler2);

      expect(eventBus.getSubscriberCount('user.created')).toBe(1);
      expect(eventBus.getSubscriberCount('user.updated')).toBe(1);
    });

    it('should support multiple handlers for same event type', () => {
      const handler1 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const handler2 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const handler3 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('order.placed', handler1);
      eventBus.subscribe('order.placed', handler2);
      eventBus.subscribe('order.placed', handler3);

      expect(eventBus.getSubscriberCount('order.placed')).toBe(3);
    });

    it('should track registered event types', () => {
      eventBus.subscribe('user.created', { handle: jest.fn(), supportedEvents: ['user.created'] });
      eventBus.subscribe('user.updated', { handle: jest.fn(), supportedEvents: ['user.updated'] });
      eventBus.subscribe('order.placed', { handle: jest.fn(), supportedEvents: ['order.placed'] });

      const eventTypes = eventBus.getRegisteredEventTypes();

      expect(eventTypes).toContain('user.created');
      expect(eventTypes).toContain('user.updated');
      expect(eventTypes).toContain('order.placed');
      expect(eventTypes).toHaveLength(3);
    });

    it('should wrap handlers with error isolation', async () => {
      const errorHandler = {
        handle: jest.fn().mockImplementation(() => {
          throw new Error('Handler error');
        }),
        supportedEvents: ['error.test']
      };
      const successHandler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('error.test', errorHandler);
      eventBus.subscribe('error.test', successHandler);

      const testEvent: DomainEvent = {
        id: 'error-test',
        type: 'error.test',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      // Both handlers should be called despite one throwing
      expect(errorHandler.handle).toHaveBeenCalled();
      expect(successHandler.handle).toHaveBeenCalled();

      // Error should be logged
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error in event handler',
        expect.objectContaining({
          eventType: 'error.test',
          error: 'Handler error'
        })
      );
    });

    it('should track handler performance metrics', async () => {
      const slowHandler = {
        handle: jest.fn().mockImplementation(() => {
          // Simulate processing time
          const start = Date.now();
          while (Date.now() - start < 10) {
            // Busy wait
          }
        }),
        supportedEvents: ['performance.test']
      };

      eventBus.subscribe('performance.test', slowHandler);

      const testEvent: DomainEvent = {
        id: 'perf-test',
        type: 'performance.test',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Event handler executed',
        expect.objectContaining({
          eventType: 'performance.test',
          handlerIndex: 0,
          processingTime: expect.any(Number)
        })
      );
    });
  });

  describe('Event Unsubscription', () => {
    it('should remove specific handler from event type', () => {
      const handler1 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const handler2 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('test.unsubscribe', handler1);
      eventBus.subscribe('test.unsubscribe', handler2);

      expect(eventBus.getSubscriberCount('test.unsubscribe')).toBe(2);

      eventBus.unsubscribe('test.unsubscribe', handler1);

      expect(eventBus.getSubscriberCount('test.unsubscribe')).toBe(1);
    });

    it('should remove handler from EventEmitter listeners', async () => {
      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('cleanup.test', handler);
      eventBus.unsubscribe('cleanup.test', handler);

      const testEvent: DomainEvent = {
        id: 'cleanup-test',
        type: 'cleanup.test',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      expect(handler.handle).not.toHaveBeenCalled();
    });

    it('should handle unsubscribing non-existent handler gracefully', () => {
      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      expect(() => {
        eventBus.unsubscribe('non.existent', handler);
      }).not.toThrow();

      expect(eventBus.getSubscriberCount('non.existent')).toBe(0);
    });

    it('should clean up empty event type registrations', () => {
      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('temp.event', handler);
      expect(eventBus.getRegisteredEventTypes()).toContain('temp.event');

      eventBus.unsubscribe('temp.event', handler);
      expect(eventBus.getRegisteredEventTypes()).not.toContain('temp.event');
    });
  });

  describe('Bus Management', () => {
    it('should clear all subscribers', () => {
      eventBus.subscribe('event1', { handle: jest.fn(), supportedEvents: ['event1'] });
      eventBus.subscribe('event2', { handle: jest.fn(), supportedEvents: ['event2'] });
      eventBus.subscribe('event3', { handle: jest.fn(), supportedEvents: ['event3'] });

      expect(eventBus.getRegisteredEventTypes()).toHaveLength(3);

      eventBus.clear();

      expect(eventBus.getRegisteredEventTypes()).toHaveLength(0);
      expect(eventBus.getSubscriberCount('event1')).toBe(0);
      expect(eventBus.getSubscriberCount('event2')).toBe(0);
      expect(eventBus.getSubscriberCount('event3')).toBe(0);
    });

    it('should report correct subscriber counts', () => {
      const handler1 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const handler2 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      const handler3 = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      expect(eventBus.getSubscriberCount('count.test')).toBe(0);

      eventBus.subscribe('count.test', handler1);
      expect(eventBus.getSubscriberCount('count.test')).toBe(1);

      eventBus.subscribe('count.test', handler2);
      eventBus.subscribe('count.test', handler3);
      expect(eventBus.getSubscriberCount('count.test')).toBe(3);
    });

    it('should report health status correctly', () => {
      expect(eventBus.isHealthy()).toBe(true);

      // Add some subscribers
      eventBus.subscribe('health.test', { handle: jest.fn(), supportedEvents: ['health.test'] });
      expect(eventBus.isHealthy()).toBe(true);

      // Clear should maintain health
      eventBus.clear();
      expect(eventBus.isHealthy()).toBe(true);
    });

    it('should enumerate registered event types correctly', () => {
      expect(eventBus.getRegisteredEventTypes()).toEqual([]);

      eventBus.subscribe('alpha', { handle: jest.fn(), supportedEvents: ['alpha'] });
      eventBus.subscribe('beta', { handle: jest.fn(), supportedEvents: ['beta'] });
      eventBus.subscribe('gamma', { handle: jest.fn(), supportedEvents: ['gamma'] });

      const eventTypes = eventBus.getRegisteredEventTypes();
      expect(eventTypes).toContain('alpha');
      expect(eventTypes).toContain('beta');
      expect(eventTypes).toContain('gamma');
      expect(eventTypes).toHaveLength(3);
    });
  });

  describe('Correlation Context Tracking', () => {
    it('should track correlation IDs in event handling', async () => {
      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      eventBus.subscribe('correlation.test', handler);

      const testEvent: DomainEvent = {
        id: 'corr-test',
        type: 'correlation.test',
        data: {},
        source: 'test',
        correlationId: 'test-correlation-456',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Event handler executed',
        expect.objectContaining({
          correlationId: 'test-correlation-456'
        })
      );
    });

    it('should handle events without correlation IDs', async () => {
      const handler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };
      eventBus.subscribe('no-correlation.test', handler);

      const testEvent: DomainEvent = {
        id: 'no-corr-test',
        type: 'no-correlation.test',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      expect(handler.handle).toHaveBeenCalled();
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Event handler executed',
        expect.objectContaining({
          eventType: 'no-correlation.test'
        })
      );
    });
  });

  describe('Error Scenarios', () => {
    it('should handle handler registration errors', () => {
      const invalidHandler = null as any;

      expect(() => {
        eventBus.subscribe('invalid.handler', invalidHandler);
      }).not.toThrow();

      // Should not register invalid handler
      expect(eventBus.getSubscriberCount('invalid.handler')).toBe(0);
    });

    it('should isolate errors between different handlers', async () => {
      const errorHandler1 = {
        handle: jest.fn().mockImplementation(() => {
          throw new Error('Handler 1 error');
        }),
        supportedEvents: ['multi.error']
      };
      const errorHandler2 = {
        handle: jest.fn().mockImplementation(() => {
          throw new Error('Handler 2 error');
        }),
        supportedEvents: ['multi.error']
      };
      const successHandler = {
        handle: jest.fn(),
        supportedEvents: ['test.event']
      };

      eventBus.subscribe('multi.error', errorHandler1);
      eventBus.subscribe('multi.error', errorHandler2);
      eventBus.subscribe('multi.error', successHandler);

      const testEvent: DomainEvent = {
        id: 'multi-error-test',
        type: 'multi.error',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));

      // All handlers should be called
      expect(errorHandler1.handle).toHaveBeenCalled();
      expect(errorHandler2.handle).toHaveBeenCalled();
      expect(successHandler.handle).toHaveBeenCalled();

      // Both errors should be logged
      expect(mockLogger.error).toHaveBeenCalledTimes(2);
    });

    it('should handle async handler errors', async () => {
      const asyncErrorHandler = {
        handle: jest.fn().mockImplementation(async () => {
          await new Promise(resolve => setTimeout(resolve, 1));
          throw new Error('Async handler error');
        }),
        supportedEvents: ['async.error']
      };

      eventBus.subscribe('async.error', asyncErrorHandler);

      const testEvent: DomainEvent = {
        id: 'async-error-test',
        type: 'async.error',
        data: {},
        source: 'test',
        timestamp: new Date(),
        version: '1.0'
      };

      await eventBus.publish(testEvent);
      await new Promise(resolve => setImmediate(resolve));
      
      // Allow async handler to complete
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(asyncErrorHandler.handle).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error in event handler',
        expect.objectContaining({
          error: 'Async handler error'
        })
      );
    });
  });

  describe('Module Lifecycle Integration', () => {
    it('should integrate with NestJS module lifecycle', () => {
      // InMemoryEventBus should implement OnModuleDestroy if needed
      expect(eventBus.clear).toBeDefined();
      expect(eventBus.isHealthy).toBeDefined();
    });

    it('should clean up resources on clear', () => {
      eventBus.subscribe('cleanup1', { handle: jest.fn(), supportedEvents: ['cleanup1'] });
      eventBus.subscribe('cleanup2', { handle: jest.fn(), supportedEvents: ['cleanup2'] });

      const eventTypesBeforeClear = eventBus.getRegisteredEventTypes();
      expect(eventTypesBeforeClear.length).toBeGreaterThan(0);

      eventBus.clear();

      expect(eventBus.getRegisteredEventTypes()).toHaveLength(0);
      expect(eventBus.isHealthy()).toBe(true);
    });
  });
});