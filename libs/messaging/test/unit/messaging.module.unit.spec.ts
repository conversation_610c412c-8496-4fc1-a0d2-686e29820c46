import { Test, TestingModule } from '@nestjs/testing';
import { MessagingModule } from '../../src/messaging.module';
import { InMemoryEventBus } from '../../src/events/in-memory-event-bus';
import { RedisStreamsPublisher } from '../../src/redis/redis-streams-publisher';
import { MockFactory } from '@libs/testing-utils';

// Mock ioredis for Redis tests
jest.mock('ioredis', () => {
  const IoRedisMock = require('ioredis-mock');
  return {
    Redis: IoRedisMock
  };
});

describe('MessagingModule Unit Tests', () => {
  describe('Static Configuration', () => {
    it('should register with in-memory event bus configuration', async () => {
      const config = {
        useInMemory: true
      };

      const dynamicModule = MessagingModule.forRoot(config);

      expect(dynamicModule.module).toBe(MessagingModule);
      expect(dynamicModule.global).toBe(true);
      expect(dynamicModule.providers).toBeDefined();
      expect(dynamicModule.exports).toContain('EventPublisher');
    });

    it('should register with Redis streams configuration', async () => {
      const config = {
        useInMemory: false,
        redis: {
        redis: { host: 'localhost', port: 6379 },
        defaultStream: 'events'
      }
      };

      const dynamicModule = MessagingModule.forRoot(config);

      expect(dynamicModule.module).toBe(MessagingModule);
      expect(dynamicModule.providers).toBeDefined();
      expect(dynamicModule.exports).toContain('EventPublisher');
    });

    it('should include required dependencies', () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      const providerTokens = dynamicModule.providers?.map((p: any) => 
        typeof p === 'object' ? p.provide : p
      );

      expect(providerTokens).toContain('MESSAGING_OPTIONS');
      expect(providerTokens).toContain('EventPublisher');
    });

    it('should be marked as global module', () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      expect(dynamicModule.global).toBe(true);
    });
  });

  describe('Async Configuration', () => {
    it('should register with async configuration factory', () => {
      const configFactory = jest.fn().mockReturnValue({
        useInMemory: true
      });

      const dynamicModule = MessagingModule.forRootAsync({
        useFactory: configFactory,
        inject: []
      });

      expect(dynamicModule.module).toBe(MessagingModule);
      expect(dynamicModule.providers).toBeDefined();

      const optionsProvider = dynamicModule.providers?.find(
        (p: any) => p.provide === 'MESSAGING_OPTIONS'
      );
      expect(optionsProvider).toBeDefined();
      expect((optionsProvider as any).useFactory).toBe(configFactory);
    });

    it('should handle async factory with dependencies', () => {
      const mockConfigService = { get: jest.fn() };
      const configFactory = jest.fn().mockReturnValue({
        useInMemory: false,
        redis: {
        redis: { host: 'redis-server', port: 6379 },
        defaultStream: 'events'
      }
      });

      const dynamicModule = MessagingModule.forRootAsync({
        useFactory: configFactory,
        inject: [mockConfigService]
      });

      const optionsProvider = dynamicModule.providers?.find(
        (p: any) => p.provide === 'MESSAGING_OPTIONS'
      );
      expect((optionsProvider as any).inject).toEqual([mockConfigService]);
    });

    it('should handle async factory returning Promise', () => {
      const asyncFactory = jest.fn().mockResolvedValue({
        useInMemory: true
      });

      const dynamicModule = MessagingModule.forRootAsync({
        useFactory: asyncFactory
      });

      expect(dynamicModule.providers).toBeDefined();
      expect(dynamicModule.module).toBe(MessagingModule);
    });
  });

  describe('Provider Registration', () => {
    it('should create module with in-memory event bus', async () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      const module: TestingModule = await Test.createTestingModule({
        imports: [dynamicModule],
        providers: [
          {
            provide: 'LOGGER_FACTORY',
            useValue: MockFactory.createLoggerFactory()
          }
        ]
      }).compile();

      const eventPublisher = module.get('EventPublisher');
      expect(eventPublisher).toBeDefined();
      expect(eventPublisher).toBeInstanceOf(InMemoryEventBus);

      await module.close();
    });

    it('should create module with Redis streams publisher', async () => {
      const config = {
        useInMemory: false,
        redis: {
        redis: { host: 'localhost', port: 6379 },
        defaultStream: 'test-events'
      }
      };
      const dynamicModule = MessagingModule.forRoot(config);

      const module: TestingModule = await Test.createTestingModule({
        imports: [dynamicModule],
        providers: [
          {
            provide: 'LOGGER_FACTORY',
            useValue: MockFactory.createLoggerFactory()
          },
          {
            provide: 'METRICS_SERVICE',
            useValue: MockFactory.createMetricsService()
          }
        ]
      }).compile();

      const eventPublisher = module.get('EventPublisher');
      expect(eventPublisher).toBeDefined();
      expect(eventPublisher).toBeInstanceOf(RedisStreamsPublisher);

      await module.close();
    });

    it('should inject logger factory correctly', async () => {
      const mockLoggerFactory = MockFactory.createLoggerFactory();
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      const module: TestingModule = await Test.createTestingModule({
        imports: [dynamicModule],
        providers: [
          {
            provide: 'LOGGER_FACTORY',
            useValue: mockLoggerFactory
          }
        ]
      }).compile();

      await module.init();

      expect(mockLoggerFactory.createLogger).toHaveBeenCalledWith('InMemoryEventBus');

      await module.close();
    });

    it('should inject metrics service for Redis publisher', async () => {
      const mockMetricsService = MockFactory.createMetricsService();
      const config = {
        useInMemory: false,
        redis: {
        redis: { host: 'localhost', port: 6379 },
        defaultStream: 'metrics-test'
      }
      };
      const dynamicModule = MessagingModule.forRoot(config);

      const module: TestingModule = await Test.createTestingModule({
        imports: [dynamicModule],
        providers: [
          {
            provide: 'LOGGER_FACTORY',
            useValue: MockFactory.createLoggerFactory()
          },
          {
            provide: 'METRICS_SERVICE',
            useValue: mockMetricsService
          }
        ]
      }).compile();

      const eventPublisher = module.get('EventPublisher');
      expect(eventPublisher).toBeInstanceOf(RedisStreamsPublisher);

      await module.close();
    });
  });

  describe('Configuration Validation', () => {
    it('should handle minimal in-memory configuration', () => {
      const minimalConfig = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(minimalConfig);

      expect(dynamicModule.module).toBe(MessagingModule);
      expect(dynamicModule.providers).toBeDefined();
    });

    it('should handle comprehensive Redis configuration', () => {
      const comprehensiveConfig = {
        useInMemory: false,
        redis: {
        redis: { 
          host: 'redis-cluster.example.com', 
          port: 6380, 
          db: 2 
        },
        defaultStream: 'production-events',
        retention: { maxLength: 50000 }
      }
      };

      const dynamicModule = MessagingModule.forRoot(comprehensiveConfig);

      expect(dynamicModule.module).toBe(MessagingModule);
      
      const optionsProvider = dynamicModule.providers?.find(
        (p: any) => p.provide === 'MESSAGING_OPTIONS'
      );
      expect((optionsProvider as any).useValue).toEqual(comprehensiveConfig);
    });

    it('should default to in-memory when Redis config is incomplete', () => {
      const incompleteConfig = {
        useInMemory: false
        // Missing redis configuration
      };

      expect(() => {
        MessagingModule.forRoot(incompleteConfig);
      }).not.toThrow(); // Module should handle gracefully
    });
  });

  describe('Module Integration', () => {
    it('should export EventPublisher for injection', async () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      expect(dynamicModule.exports).toContain('EventPublisher');
    });

    it('should work with NestJS dependency injection', async () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      const module: TestingModule = await Test.createTestingModule({
        imports: [dynamicModule],
        providers: [
          {
            provide: 'LOGGER_FACTORY',
            useValue: MockFactory.createLoggerFactory()
          },
          {
            provide: 'TestService',
            useFactory: (eventPublisher: any) => {
              return { eventPublisher };
            },
            inject: ['EventPublisher']
          }
        ]
      }).compile();

      const testService = module.get('TestService');
      expect(testService.eventPublisher).toBeDefined();
      expect(testService.eventPublisher).toBeInstanceOf(InMemoryEventBus);

      await module.close();
    });

    it('should support multiple module instances with different configs', async () => {
      // This tests that the module can be configured differently in different contexts
      const inMemoryModule = MessagingModule.forRoot({ useInMemory: true });
      const redisModule = MessagingModule.forRoot({
        useInMemory: false,
        redis: {
        redis: { host: 'localhost', port: 6379 },
        defaultStream: 'test'
      }
      });

      expect(inMemoryModule.module).toBe(MessagingModule);
      expect(redisModule.module).toBe(MessagingModule);
      expect(inMemoryModule).not.toBe(redisModule);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid configuration gracefully', () => {
      const invalidConfig = null as any;

      expect(() => {
        MessagingModule.forRoot(invalidConfig);
      }).not.toThrow();
    });

    it('should handle missing dependencies in test environment', async () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      // Test without providing LOGGER_FACTORY dependency
      await expect(
        Test.createTestingModule({
          imports: [dynamicModule]
        }).compile()
      ).rejects.toThrow(); // Should fail due to missing dependency
    });

    it('should handle async factory errors', () => {
      const errorFactory = jest.fn().mockImplementation(() => {
        throw new Error('Configuration error');
      });

      const dynamicModule = MessagingModule.forRootAsync({
        useFactory: errorFactory
      });

      expect(dynamicModule.module).toBe(MessagingModule);
      // Error will be thrown during module compilation, not registration
    });
  });

  describe('Publisher Factory Logic', () => {
    it('should select correct publisher based on configuration', () => {
      const inMemoryConfig = { useInMemory: true };
      const redisConfig = { 
        useInMemory: false, 
        redis: {
        redis: { host: 'localhost', port: 6379 },
        defaultStream: 'events'
      } 
      };

      const inMemoryModule = MessagingModule.forRoot(inMemoryConfig);
      const redisModule = MessagingModule.forRoot(redisConfig);

      // Both should create valid modules
      expect(inMemoryModule.providers).toBeDefined();
      expect(redisModule.providers).toBeDefined();

      // Provider logic differs based on configuration
      const inMemoryProviders = inMemoryModule.providers as any[];
      const redisProviders = redisModule.providers as any[];

      expect(inMemoryProviders.length).toBeGreaterThan(0);
      expect(redisProviders.length).toBeGreaterThan(0);
    });

    it('should handle publisher factory with conditional logic', () => {
      const config = {
        useInMemory: false,
        redis: {
        redis: { host: 'localhost', port: 6379 },
        defaultStream: 'conditional-test'
      }
      };

      const dynamicModule = MessagingModule.forRoot(config);
      
      const eventPublisherProvider = dynamicModule.providers?.find(
        (p: any) => p.provide === 'EventPublisher'
      ) as any;

      expect(eventPublisherProvider).toBeDefined();
      expect(eventPublisherProvider.useFactory).toBeDefined();
      expect(eventPublisherProvider.inject).toContain('MESSAGING_OPTIONS');
    });
  });

  describe('Module Lifecycle', () => {
    it('should initialize and cleanup properly', async () => {
      const config = { useInMemory: true };
      const dynamicModule = MessagingModule.forRoot(config);

      const module: TestingModule = await Test.createTestingModule({
        imports: [dynamicModule],
        providers: [
          {
            provide: 'LOGGER_FACTORY',
            useValue: MockFactory.createLoggerFactory()
          }
        ]
      }).compile();

      await module.init();
      const eventPublisher = module.get('EventPublisher');
      expect(eventPublisher).toBeDefined();

      await module.close();
      // Module should clean up without errors
    });
  });
});