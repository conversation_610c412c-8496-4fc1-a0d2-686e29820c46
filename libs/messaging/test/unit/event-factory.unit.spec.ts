import { EventFactory } from '../../src/events/event-factory';
import { DomainEvent } from '../../src/events/event.types';
// Types are inferred from EventFactory methods

describe('EventFactory Unit Tests', () => {
  describe('Generic Event Creation', () => {
    it('should create event with all required fields', () => {
      const eventType = 'test.event';
      const data = { testField: 'testValue' };
      const metadata = { source: 'test-service' };

      const event = EventFactory.create(eventType, data, metadata);

      expect(event.type).toBe(eventType);
      expect(event.data).toEqual(data);
      expect(event.metadata).toEqual(expect.objectContaining(metadata));
      expect(event.id).toBeDefined();
      expect(event.timestamp).toBeInstanceOf(Date);
      expect(event.version).toBe('1.0');
    });

    it('should generate unique event IDs', () => {
      const event1 = EventFactory.create('test', {}, { source: 'test' });
      const event2 = EventFactory.create('test', {}, { source: 'test' });
      
      expect(event1.id).not.toBe(event2.id);
      expect(event1.id).toBeTruthy();
      expect(event2.id).toBeTruthy();
    });

    it('should set timestamp close to current time', () => {
      const beforeCreation = new Date();
      const event = EventFactory.create('test', {}, { source: 'test' });
      const afterCreation = new Date();

      expect(event.timestamp.getTime()).toBeGreaterThanOrEqual(beforeCreation.getTime());
      expect(event.timestamp.getTime()).toBeLessThanOrEqual(afterCreation.getTime());
    });

    it('should preserve metadata properties', () => {
      const metadata = {
        source: 'api-gateway',
        correlationId: 'test-correlation-123',
        userId: 'user-456',
        sessionId: 'session-789',
        customProperty: 'custom-value'
      };

      const event = EventFactory.create('test', {}, metadata);

      expect(event.metadata).toEqual(expect.objectContaining(metadata));
      expect(event.metadata.source).toBe('api-gateway');
      expect(event.correlationId).toBe('test-correlation-123');
      expect(event.metadata.userId).toBe('user-456');
      expect(event.metadata.sessionId).toBe('session-789');
      expect(event.metadata.customProperty).toBe('custom-value');
    });

    it('should handle empty data object', () => {
      const event = EventFactory.create('empty.test', {}, { source: 'test' });

      expect(event.data).toEqual({});
      expect(event.type).toBe('empty.test');
    });

    it('should handle complex nested data', () => {
      const complexData = {
        user: {
          id: 123,
          profile: {
            name: 'John Doe',
            settings: {
              theme: 'dark',
              notifications: true
            }
          }
        },
        metadata: {
          tags: ['important', 'user-action'],
          counts: { views: 5, clicks: 2 }
        }
      };

      const event = EventFactory.create('complex.test', complexData, { source: 'test' });

      expect(event.data).toEqual(complexData);
      expect(event.data.user.profile.name).toBe('John Doe');
      expect(event.data.metadata.tags).toEqual(['important', 'user-action']);
    });
  });

  describe('User Events', () => {
    const mockUserData = {
      id: 123,
      email: '<EMAIL>',
      name: 'Test User',
      roles: ['user', 'admin']
    };

    it('should create userCreated event', () => {
      const metadata = { source: 'user-service', correlationId: 'test-123' };
      
      const event = EventFactory.userCreated('user-123', mockUserData, metadata?.correlationId);

      expect(event.type).toBe('user.created');
      expect(event.data).toEqual(expect.objectContaining(mockUserData));
      expect(event.metadata).toEqual(expect.objectContaining(metadata));
      expect(event.id).toBeTruthy();
      expect(event.timestamp).toBeInstanceOf(Date);
    });

    it('should create userUpdated event with changes', () => {
      const changes = { name: 'Updated Name', roles: ['user'] };
      const metadata = { source: 'user-service', userId: '123' };
      
      const event = EventFactory.userUpdated('user-123', changes, 'test-correlation-id');

      expect(event.type).toBe('user.updated');
      expect(event.data.userId).toBe(mockUserData.id);
      expect(event.data.changes).toEqual(changes);
      expect(event.metadata).toEqual(expect.objectContaining(metadata));
    });

    it('should create userDeleted event', () => {
      const reason = 'Account deactivation requested';
      const metadata = { source: 'user-service', adminId: 'admin-456' };
      
      const event = EventFactory.userDeleted('user-123', 'test-correlation-id');

      expect(event.type).toBe('user.deleted');
      expect(event.data.userId).toBe(mockUserData.id);
      expect(event.data.reason).toBe(reason);
      expect(event.metadata).toEqual(expect.objectContaining(metadata));
    });

    it('should create userRestored event', () => {
      const restoredData = { ...mockUserData, status: 'active' };
      const metadata = { source: 'user-service', adminId: 'admin-789' };
      
      const event = EventFactory.userRestored('user-123', 'test-correlation-id');

      expect(event.type).toBe('user.restored');
      expect(event.data.userId).toBe(mockUserData.id);
      expect(event.data.restoredData).toEqual(restoredData);
      expect(event.metadata).toEqual(expect.objectContaining(metadata));
    });

    it('should create generic user event with createUserEvent', () => {
      const customType = 'user.profile.updated';
      const customData = { profileField: 'updated' };
      const metadata = { source: 'profile-service' };
      
      const event = EventFactory.createUserEvent(customType, 'user-123', customData, { correlationId: 'test-correlation-id' });

      expect(event.type).toBe(customType);
      expect(event.data.userId).toBe(mockUserData.id);
      expect(event.data).toEqual(expect.objectContaining(customData));
      expect(event.metadata).toEqual(expect.objectContaining(metadata));
    });
  });

  describe('HTTP Lifecycle Events', () => {
    describe('HTTP Request Events', () => {
      it('should create httpRequest event with complete data', () => {
        const requestData = {
          method: 'POST',
          path: '/api/users',
          url: 'https://api.example.com/api/users',
          headers: {
            'content-type': 'application/json',
            'authorization': 'Bearer token123'
          },
          query: { limit: '10', offset: '0' },
          body: { name: 'New User', email: '<EMAIL>' },
          userAgent: 'Mozilla/5.0...',
          clientIp: '*************'
        };
        const metadata = { source: 'api-gateway', correlationId: 'req-123' };

        const event = EventFactory.httpRequest(requestData);

        expect(event.type).toBe('http.request.started');
        expect(event.data).toEqual(requestData);
        expect(event.metadata).toEqual(expect.objectContaining(metadata));
        expect(event.metadata.httpMethod).toBe('POST');
        expect(event.metadata.httpPath).toBe('/api/users');
      });

      it('should create httpRequest event with minimal data', () => {
        const requestData = {
          method: 'GET',
          path: '/health',
          url: 'https://api.example.com/health'
        };
        const event = EventFactory.httpRequest(requestData);

        expect(event.type).toBe('http.request.started');
        expect(event.data.method).toBe('GET');
        expect(event.data.path).toBe('/health');
        expect(event.data.headers).toBeUndefined();
        expect(event.metadata.httpMethod).toBe('GET');
        expect(event.metadata.httpPath).toBe('/health');
      });
    });

    describe('HTTP Response Events', () => {
      it('should create httpResponse event for successful response', () => {
        const responseData = {
          statusCode: 200,
          headers: {
            'content-type': 'application/json',
            'cache-control': 'no-cache'
          },
          body: { id: 123, name: 'Success' },
          size: 1024,
          duration: 150
        };
        const metadata = { source: 'api-gateway', correlationId: 'req-123' };

        const event = EventFactory.httpResponse(responseData);

        expect(event.type).toBe('http.request.completed');
        expect(event.data).toEqual(responseData);
        expect(event.metadata).toEqual(expect.objectContaining(metadata));
        expect(event.metadata.httpStatusCode).toBe(200);
        expect(event.metadata.success).toBe(true);
        expect(event.metadata.responseTime).toBe(150);
      });

      it('should create httpResponse event for client error', () => {
        const responseData = {
          statusCode: 400,
          headers: { 'content-type': 'application/json' },
          body: { error: 'Bad Request' },
          duration: 50
        };
        const event = EventFactory.httpResponse(responseData);

        expect(event.metadata.httpStatusCode).toBe(400);
        expect(event.metadata.success).toBe(false);
        expect(event.metadata.responseTime).toBe(50);
      });

      it('should create httpResponse event for server error', () => {
        const responseData = {
          statusCode: 500,
          duration: 5000
        };

        const event = EventFactory.httpResponse(responseData, { source: 'test' });

        expect(event.metadata.httpStatusCode).toBe(500);
        expect(event.metadata.success).toBe(false);
        expect(event.metadata.responseTime).toBe(5000);
      });
    });

    describe('HTTP Error Events', () => {
      it('should create httpError event with complete error data', () => {
        const errorData = {
          error: new Error('Connection timeout'),
          statusCode: 504,
          message: 'Gateway timeout occurred',
          stack: 'Error: Connection timeout\n    at ...',
          duration: 30000
        };
        const metadata = { source: 'api-gateway', correlationId: 'req-456' };

        const event = EventFactory.httpError(errorData);

        expect(event.type).toBe('http.request.failed');
        expect(event.data.error).toBeInstanceOf(Error);
        expect(event.data.statusCode).toBe(504);
        expect(event.data.message).toBe('Gateway timeout occurred');
        expect(event.metadata).toEqual(expect.objectContaining(metadata));
        expect(event.metadata.httpStatusCode).toBe(504);
        expect(event.metadata.severity).toBe('high');
        expect(event.metadata.responseTime).toBe(30000);
      });

      it('should determine error severity based on status code', () => {
        const clientError = EventFactory.httpError({ 
          error: new Error('Bad Request'), 
          statusCode: 400 
        }, { source: 'test' });

        const serverError = EventFactory.httpError({ 
          error: new Error('Internal Error'), 
          statusCode: 500 
        }, { source: 'test' });

        const timeoutError = EventFactory.httpError({ 
          error: new Error('Timeout'), 
          statusCode: 504 
        }, { source: 'test' });

        expect(clientError.metadata.severity).toBe('medium');
        expect(serverError.metadata.severity).toBe('high');
        expect(timeoutError.metadata.severity).toBe('high');
      });

      it('should handle error without status code', () => {
        const errorData = {
          error: new Error('Network error'),
          message: 'Failed to connect'
        };

        const event = EventFactory.httpError(errorData, { source: 'test' });

        expect(event.data.error).toBeInstanceOf(Error);
        expect(event.data.message).toBe('Failed to connect');
        expect(event.metadata.httpStatusCode).toBeUndefined();
        expect(event.metadata.severity).toBe('medium');
      });
    });
  });

  describe('Infrastructure Events', () => {
    describe('Circuit Breaker Events', () => {
      it('should create circuitBreakerStateChanged event', () => {
        const stateData = {
          circuitName: 'user-service-circuit',
          previousState: 'CLOSED' as const,
          newState: 'OPEN' as const,
          reason: 'Failure threshold exceeded',
          failureCount: 5,
          timestamp: new Date()
        };
        const metadata = { source: 'resilience-service', correlationId: 'circuit-123' };

        const event = EventFactory.circuitBreakerStateChanged(stateData, metadata);

        expect(event.type).toBe('circuit-breaker.state-changed');
        expect(event.data).toEqual(stateData);
        expect(event.metadata).toEqual(expect.objectContaining(metadata));
        expect(event.metadata.circuitName).toBe('user-service-circuit');
        expect(event.metadata.stateTransition).toBe('CLOSED -> OPEN');
      });

      it('should create state transition metadata correctly', () => {
        const stateData = {
          circuitName: 'test-circuit',
          previousState: 'OPEN' as const,
          newState: 'HALF_OPEN' as const,
          reason: 'Attempting recovery'
        };

        const event = EventFactory.circuitBreakerStateChanged(stateData, { source: 'test' });

        expect(event.metadata.stateTransition).toBe('OPEN -> HALF_OPEN');
      });
    });

    describe('Cache Operation Events', () => {
      it('should create cacheOperation event for cache hit', () => {
        const operationData = {
          operation: 'get' as const,
          key: 'user:123',
          hit: true,
          value: { id: 123, name: 'Cached User' },
          ttl: 300,
          responseTime: 5
        };
        const metadata = { source: 'cache-service', correlationId: 'cache-456' };

        const event = EventFactory.cacheOperation(operationData, metadata);

        expect(event.type).toBe('cache.operation');
        expect(event.data).toEqual(operationData);
        expect(event.metadata).toEqual(expect.objectContaining(metadata));
        expect(event.metadata.cacheKey).toBe('user:123');
        expect(event.metadata!.cacheEfficiency).toBe('hit');
        expect(event.data.operation).toBe('get');
      });

      it('should create cacheOperation event for cache miss', () => {
        const operationData = {
          operation: 'get' as const,
          key: 'user:999',
          hit: false,
          responseTime: 2
        };

        const event = EventFactory.cacheOperation(operationData);

        expect(event.metadata!.cacheEfficiency).toBe('miss');
        expect(event.metadata.cacheKey).toBe('user:999');
      });

      it('should create cacheOperation event for set operation', () => {
        const operationData = {
          operation: 'set' as const,
          key: 'product:456',
          value: { id: 456, name: 'New Product' },
          ttl: 600,
          responseTime: 8
        };

        const event = EventFactory.cacheOperation(operationData);

        expect(event.data.operation).toBe('set');
        expect(event.metadata!.cacheEfficiency).toBe('write');
      });

      it('should create cacheOperation event for delete operation', () => {
        const operationData = {
          operation: 'delete' as const,
          key: 'user:789',
          hit: true,
          responseTime: 3
        };

        const event = EventFactory.cacheOperation(operationData);

        expect(event.data.operation).toBe('delete');
        expect(event.metadata!.cacheEfficiency).toBe('delete');
      });
    });
  });

  describe('Event ID Generation', () => {
    it('should generate UUIDs for event IDs', () => {
      const event = EventFactory.create('test', {}, { source: 'test' });
      
      // UUID v4 format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      
      expect(event.id).toMatch(uuidRegex);
    });

    it('should generate different IDs for concurrent events', () => {
      const events = Array.from({ length: 100 }, () => 
        EventFactory.create('test', {}, { source: 'test' })
      );
      
      const ids = events.map(e => e.id);
      const uniqueIds = new Set(ids);
      
      expect(uniqueIds.size).toBe(100);
    });
  });

  describe('Timestamp Consistency', () => {
    it('should use consistent timestamp format', () => {
      const event1 = EventFactory.create('test1', {}, { source: 'test' });
      const event2 = EventFactory.create('test2', {}, { source: 'test' });
      
      expect(event1.timestamp).toBeInstanceOf(Date);
      expect(event2.timestamp).toBeInstanceOf(Date);
      expect(event2.timestamp.getTime()).toBeGreaterThanOrEqual(event1.timestamp.getTime());
    });
  });

  describe('Correlation ID Propagation', () => {
    it('should preserve correlation ID in all event types', () => {
      const correlationId = 'test-correlation-789';
      
      const genericEvent = EventFactory.create('test', {}, { 
        source: 'test', 
        correlationId 
      });
      
      const userEvent = EventFactory.userCreated({ id: 1, email: '<EMAIL>' }, { 
        source: 'test', 
        correlationId 
      });
      
      const httpEvent = EventFactory.httpRequest(
        { method: 'GET', path: '/', url: 'http://test' }, 
        { source: 'test', correlationId }
      );

      expect(genericEvent.correlationId).toBe(correlationId);
      expect(userEvent.correlationId).toBe(correlationId);
      expect(httpEvent.correlationId).toBe(correlationId);
    });
  });
});