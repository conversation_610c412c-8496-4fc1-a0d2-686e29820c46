import { Test, TestingModule } from '@nestjs/testing';
import Redis from 'ioredis';
import { MessagingModule } from '../../src/messaging.module';
import { RedisStreamsPublisher } from '../../src/redis/redis-streams-publisher';
import { EventFactory } from '../../src/events/event-factory';
import { MockFactory } from '@libs/testing-utils';

/**
 * Integration tests for messaging library with real Redis
 * Tests event flow, consumer groups, and cross-library integration
 */
describe('Messaging Integration Tests', () => {
  let module: TestingModule;
  let publisher: RedisStreamsPublisher;
  let redisClient: Redis;
  
  const STREAM_NAME = 'test-integration-events';
  const CONSUMER_GROUP = 'test-consumer-group';
  
  beforeAll(async () => {
    // Use real Redis for integration tests
    redisClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      db: 15 // Use separate test database
    });
    
    // Clean up any existing test data
    await redisClient.del(STREAM_NAME);
    await redisClient.flushdb();
  });
  
  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [MessagingModule.forRoot({
        useInMemory: false,
        redis: {
          redis: {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379'),
            db: 15
          },
          defaultStream: STREAM_NAME
        }
      })],
      providers: [
        { provide: 'LOGGER_FACTORY', useValue: MockFactory.createLoggerFactory() },
        { provide: 'METRICS_SERVICE', useValue: MockFactory.createMetricsService() }
      ]
    }).compile();
    
    publisher = module.get<RedisStreamsPublisher>('EventPublisher');
  });
  
  afterEach(async () => {
    await module.close();
    // Clean up test stream
    await redisClient.del(STREAM_NAME);
  });
  
  afterAll(async () => {
    await redisClient.quit();
  });

  describe('Redis Streams Event Publishing', () => {
    it('should publish events to Redis stream and persist them', async () => {
      const testEvent = EventFactory.create('integration.test', 
        { message: 'test data' },
        { source: 'integration-test', correlationId: 'test-123' }
      );
      
      await publisher.publish(testEvent);
      
      // Verify event was persisted in Redis stream
      const streamEntries = await redisClient.xrange(STREAM_NAME, '-', '+');
      expect(streamEntries).toHaveLength(1);
      
      const [entryId, fields] = streamEntries[0];
      expect(fields).toContain('eventType');
      expect(fields).toContain('test.integration');
    });
    
    it('should handle batch publishing correctly', async () => {
      const events = Array.from({ length: 5 }, (_, i) => 
        EventFactory.create('batch.test', 
          { index: i },
          { source: 'batch-test' }
        )
      );
      
      await publisher.publishBatch(events);
      
      const streamLength = await redisClient.xlen(STREAM_NAME);
      expect(streamLength).toBe(5);
    });
  });
  
  describe('Consumer Group Integration', () => {
    beforeEach(async () => {
      // Create consumer group for testing
      try {
        await redisClient.xgroup('CREATE', STREAM_NAME, CONSUMER_GROUP, '0', 'MKSTREAM');
      } catch (err) {
        // Group might already exist, ignore error
      }
    });
    
    it('should distribute messages across multiple consumers', async () => {
      // Publish test events
      const events = Array.from({ length: 10 }, (_, i) => 
        EventFactory.create('consumer.test', 
          { messageId: i },
          { source: 'consumer-test' }
        )
      );
      
      await publisher.publishBatch(events);
      
      // Create two consumers
      const consumer1Messages: any[] = [];
      const consumer2Messages: any[] = [];
      
      // Consumer 1 reads messages
      const c1Messages = await redisClient.xreadgroup(
        'GROUP', CONSUMER_GROUP, 'consumer-1',
        'COUNT', 5,
        'STREAMS', STREAM_NAME, '>'
      );
      
      if (c1Messages && c1Messages[0]) {
        consumer1Messages.push(...c1Messages[0][1]);
      }
      
      // Consumer 2 reads remaining messages
      const c2Messages = await redisClient.xreadgroup(
        'GROUP', CONSUMER_GROUP, 'consumer-2', 
        'COUNT', 5,
        'STREAMS', STREAM_NAME, '>'
      );
      
      if (c2Messages && c2Messages[0]) {
        consumer2Messages.push(...c2Messages[0][1]);
      }
      
      // Verify message distribution
      expect(consumer1Messages.length + consumer2Messages.length).toBe(10);
      expect(consumer1Messages.length).toBeGreaterThan(0);
      expect(consumer2Messages.length).toBeGreaterThan(0);
      
      // Verify no message overlap
      const c1Ids = consumer1Messages.map(m => m[0]);
      const c2Ids = consumer2Messages.map(m => m[0]);
      const overlap = c1Ids.filter(id => c2Ids.includes(id));
      expect(overlap).toHaveLength(0);
    });
    
    it('should handle message acknowledgment correctly', async () => {
      const testEvent = EventFactory.create('ack.test', 
        { data: 'ack test' },
        { source: 'ack-test' }
      );
      
      await publisher.publish(testEvent);
      
      // Read message as consumer
      const messages = await redisClient.xreadgroup(
        'GROUP', CONSUMER_GROUP, 'ack-consumer',
        'STREAMS', STREAM_NAME, '>'
      );
      
      expect(messages).toHaveLength(1);
      const messageId = messages[0][1][0][0];
      
      // Check pending messages before ack
      const pending = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
      expect(pending[0]).toBe(1); // 1 pending message
      
      // Acknowledge the message
      const ackResult = await redisClient.xack(STREAM_NAME, CONSUMER_GROUP, messageId);
      expect(ackResult).toBe(1);
      
      // Verify no pending messages after ack
      const pendingAfter = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
      expect(pendingAfter[0]).toBe(0); // 0 pending messages
    });
  });
  
  describe('Cross-Library Event Integration', () => {
    it('should publish HTTP lifecycle events correctly', async () => {
      const httpRequestEvent = EventFactory.httpRequest({
        method: 'POST',
        url: 'https://api.test.com/users',
        serviceName: 'user-service',
        correlationId: 'http-test-123',
        hasBody: true
      });
      
      const httpResponseEvent = EventFactory.httpResponse({
        method: 'POST',
        url: 'https://api.test.com/users',
        serviceName: 'user-service',
        correlationId: 'http-test-123',
        statusCode: 201,
        responseTime: 150
      });
      
      await publisher.publish(httpRequestEvent);
      await publisher.publish(httpResponseEvent);
      
      const streamEntries = await redisClient.xrange(STREAM_NAME, '-', '+');
      expect(streamEntries).toHaveLength(2);
      
      // Verify event types
      const eventTypes = streamEntries.map(entry => {
        const fields = entry[1];
        const typeIndex = fields.indexOf('eventType');
        return fields[typeIndex + 1];
      });
      
      expect(eventTypes).toContain('http.request');
      expect(eventTypes).toContain('http.response');
    });
    
    it('should publish cache operation events correctly', async () => {
      const cacheEvent = EventFactory.cacheOperation({
        operation: 'get',
        key: 'user:123',
        hit: true,
        responseTime: 5,
        correlationId: 'cache-test-456'
      });
      
      await publisher.publish(cacheEvent);
      
      const streamEntries = await redisClient.xrange(STREAM_NAME, '-', '+');
      expect(streamEntries).toHaveLength(1);
      
      const [entryId, fields] = streamEntries[0];
      const eventData = JSON.parse(fields[fields.indexOf('eventData') + 1]);
      
      expect(eventData.operation).toBe('get');
      expect(eventData.key).toBe('user:123');
      expect(eventData.hit).toBe(true);
    });
  });
  
  describe('Error Recovery and Resilience', () => {
    it('should handle Redis connection errors gracefully', async () => {
      // Simulate connection error by connecting to wrong port
      const faultyPublisher = new RedisStreamsPublisher(
        {
          redis: { host: 'localhost', port: 9999 },
          defaultStream: 'test-stream'
        },
        MockFactory.createLogger(),
        MockFactory.createMetricsService()
      );
      
      const testEvent = EventFactory.create('error.test', 
        { data: 'error test' },
        { source: 'error-test' }
      );
      
      // Should not throw but handle error gracefully
      await expect(faultyPublisher.publish(testEvent)).resolves.not.toThrow();
    });
    
    it('should support message redelivery for failed consumers', async () => {
      const testEvent = EventFactory.create('redelivery.test', 
        { data: 'redelivery test' },
        { source: 'redelivery-test' }
      );
      
      await publisher.publish(testEvent);
      
      // Consumer 1 reads but doesn't acknowledge (simulates crash)
      const messages = await redisClient.xreadgroup(
        'GROUP', CONSUMER_GROUP, 'failing-consumer',
        'STREAMS', STREAM_NAME, '>'
      );
      
      const messageId = messages[0][1][0][0];
      
      // Check pending messages
      const pending = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
      expect(pending[0]).toBe(1);
      
      // Auto-claim the message for another consumer (simulates redelivery)
      const claimedMessages = await redisClient.xautoclaim(
        STREAM_NAME, CONSUMER_GROUP, 'recovery-consumer',
        0, // min-idle-time: 0 for immediate claiming in test
        messageId
      );
      
      expect(claimedMessages[1]).toHaveLength(1);
      expect(claimedMessages[1][0][0]).toBe(messageId);
      
      // Acknowledge from recovery consumer
      await redisClient.xack(STREAM_NAME, CONSUMER_GROUP, messageId);
      
      const pendingAfter = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
      expect(pendingAfter[0]).toBe(0);
    });
  });
  
  describe('Performance and Monitoring', () => {
    it('should handle high-volume event publishing', async () => {
      const startTime = Date.now();
      const eventCount = 1000;
      
      const events = Array.from({ length: eventCount }, (_, i) => 
        EventFactory.create('performance.test', 
          { index: i, timestamp: Date.now() },
          { source: 'performance-test' }
        )
      );
      
      // Use batch publishing for better performance
      const batchSize = 100;
      const batches = [];
      for (let i = 0; i < events.length; i += batchSize) {
        batches.push(events.slice(i, i + batchSize));
      }
      
      for (const batch of batches) {
        await publisher.publishBatch(batch);
      }
      
      const duration = Date.now() - startTime;
      const streamLength = await redisClient.xlen(STREAM_NAME);
      
      expect(streamLength).toBe(eventCount);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      
      console.log(`Published ${eventCount} events in ${duration}ms (${(eventCount / duration * 1000).toFixed(0)} events/sec)`);
    });
    
    it('should provide stream information for monitoring', async () => {
      // Publish some events
      const events = Array.from({ length: 10 }, (_, i) => 
        EventFactory.create('monitoring.test', { index: i }, { source: 'monitoring-test' })
      );
      
      await publisher.publishBatch(events);
      
      // Get stream info
      const streamInfo = await redisClient.xinfoStream(STREAM_NAME);
      
      expect(streamInfo.length).toBe(10);
      expect(streamInfo['first-entry']).toBeDefined();
      expect(streamInfo['last-entry']).toBeDefined();
      
      // Get consumer group info
      const groupInfo = await redisClient.xinfoGroups(STREAM_NAME);
      expect(groupInfo).toHaveLength(1);
      expect(groupInfo[0].name).toBe(CONSUMER_GROUP);
    });
  });
});