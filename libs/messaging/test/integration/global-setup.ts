import Redis from 'ioredis';

/**
 * Global setup for integration tests
 * Prepares Redis test environment
 */
export default async function globalSetup() {
  console.log('🔧 Setting up messaging integration tests...');
  
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    db: 15 // Test database
  });

  try {
    // Clear test database
    await redis.flushdb();
    console.log('✅ Redis test database cleared');
    
    // Set up any global test data if needed
    // await redis.set('test:setup', 'complete');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error.message);
    throw error;
  } finally {
    await redis.quit();
  }
}