import Redis from 'ioredis';

/**
 * Global teardown for integration tests
 * Cleans up Redis test environment
 */
export default async function globalTeardown() {
  console.log('🧹 Cleaning up messaging integration tests...');
  
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    db: 15 // Test database
  });

  try {
    // Clean up test data
    await redis.flushdb();
    console.log('✅ Redis test database cleaned up');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error.message);
    // Don't throw - teardown failures shouldn't fail the test suite
  } finally {
    await redis.quit();
  }
}