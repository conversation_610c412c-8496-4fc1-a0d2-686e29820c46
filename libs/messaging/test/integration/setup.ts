import Redis from 'ioredis';

/**
 * Integration test setup
 * Ensures Redis is available and sets up test environment
 */

beforeAll(async () => {
  // Check if Redis is available
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    db: 15, // Use test database
    lazyConnect: true
  });

  try {
    await redis.connect();
    await redis.ping();
    console.log('✅ Redis connection successful for integration tests');
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    console.log('Please ensure <PERSON><PERSON> is running for integration tests');
    console.log('Docker: docker run -d -p 6379:6379 redis:7');
    process.exit(1);
  } finally {
    await redis.quit();
  }
});

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise in tests