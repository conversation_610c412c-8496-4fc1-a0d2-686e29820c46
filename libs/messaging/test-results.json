{"numFailedTestSuites": 4, "numFailedTests": 9, "numPassedTestSuites": 0, "numPassedTests": 16, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 3, "numTodoTests": 0, "numTotalTestSuites": 4, "numTotalTests": 25, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750353509479, "success": false, "testResults": [{"assertionResults": [], "coverage": {}, "endTime": 1750353518164, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m34\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2559: \u001b[0mType '{ host: string; port: number; db: number; streamName: string; maxLength: number; batchSize: number; }' has no properties in common with type 'RedisStreamsConfig'.\n\n    \u001b[7m34\u001b[0m       mockConfig,\n    \u001b[7m  \u001b[0m \u001b[91m      ~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m39\u001b[0m:\u001b[93m21\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'onModuleInit' does not exist on type 'RedisStreamsPublisher'.\n\n    \u001b[7m39\u001b[0m     await publisher.onModuleInit();\n    \u001b[7m  \u001b[0m \u001b[91m                    ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m54\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2559: \u001b[0mType '{ host: string; port: number; db: number; streamName: string; maxLength: number; batchSize: number; }' has no properties in common with type 'RedisStreamsConfig'.\n\n    \u001b[7m54\u001b[0m         mockConfig,\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m59\u001b[0m:\u001b[93m33\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'onModuleInit' does not exist on type 'RedisStreamsPublisher'.\n\n    \u001b[7m59\u001b[0m       await expect(newPublisher.onModuleInit()).resolves.not.toThrow();\n    \u001b[7m  \u001b[0m \u001b[91m                                ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m65\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2559: \u001b[0mType '{ host: string; port: number; db: number; streamName: string; maxLength: number; batchSize: number; }' has no properties in common with type 'RedisStreamsConfig'.\n\n    \u001b[7m65\u001b[0m         mockConfig,\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m70\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'onModuleInit' does not exist on type 'RedisStreamsPublisher'.\n\n    \u001b[7m70\u001b[0m       await testPublisher.onModuleInit();\n    \u001b[7m  \u001b[0m \u001b[91m                          ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m81\u001b[0m:\u001b[93m85\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType '{ id: string; type: string; timestamp: Date; version: string; source?: string | undefined; data: any; correlationId?: string | undefined; metadata: Record<string, any>; }' is not assignable to type 'DomainEvent'.\n      Types of property 'source' are incompatible.\n        Type 'string | undefined' is not assignable to type 'string'.\n          Type 'undefined' is not assignable to type 'string'.\n\n    \u001b[7m 81\u001b[0m     const createTestEvent = (overrides: Partial<DomainEvent> = {}): DomainEvent => ({\n    \u001b[7m   \u001b[0m \u001b[91m                                                                                    ~\u001b[0m\n    \u001b[7m 82\u001b[0m       id: 'test-event-1',\n    \u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[7m...\u001b[0m \n    \u001b[7m 88\u001b[0m       ...overrides\n    \u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[7m 89\u001b[0m     });\n    \u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m185\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType '{ id: string; type: string; data: { index: number; message: string; }; metadata: { source: string; correlationId: string; }; timestamp: Date; version: string; }[]' is not assignable to type 'DomainEvent[]'.\n      Property 'source' is missing in type '{ id: string; type: string; data: { index: number; message: string; }; metadata: { source: string; correlationId: string; }; timestamp: Date; version: string; }' but required in type 'DomainEvent'.\n\n    \u001b[7m185\u001b[0m       return Array.from({ length: count }, (_, i) => ({\n    \u001b[7m   \u001b[0m \u001b[91m      ~~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event.types.ts\u001b[0m:\u001b[93m28\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m28\u001b[0m   source: string;\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~\u001b[0m\n        'source' is declared here.\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m345\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'healthStatus.details' is possibly 'undefined'.\n\n    \u001b[7m345\u001b[0m       expect(healthStatus.details.streamName).toBe('events');\n    \u001b[7m   \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m346\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'healthStatus.details' is possibly 'undefined'.\n\n    \u001b[7m346\u001b[0m       expect(healthStatus.details.connected).toBeDefined();\n    \u001b[7m   \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m360\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2559: \u001b[0mType '{ streamName: string; host: string; port: number; db: number; maxLength: number; batchSize: number; }' has no properties in common with type 'RedisStreamsConfig'.\n\n    \u001b[7m360\u001b[0m         customConfig,\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m365\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'onModuleInit' does not exist on type 'RedisStreamsPublisher'.\n\n    \u001b[7m365\u001b[0m       await customPublisher.onModuleInit();\n    \u001b[7m   \u001b[0m \u001b[91m                            ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m376\u001b[0m:\u001b[93m37\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ id: string; type: string; data: {}; metadata: { source: string; }; timestamp: Date; version: string; }' is not assignable to parameter of type 'DomainEvent'.\n      Property 'source' is missing in type '{ id: string; type: string; data: {}; metadata: { source: string; }; timestamp: Date; version: string; }' but required in type 'DomainEvent'.\n\n    \u001b[7m376\u001b[0m       await customPublisher.publish(testEvent);\n    \u001b[7m   \u001b[0m \u001b[91m                                    ~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event.types.ts\u001b[0m:\u001b[93m28\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m28\u001b[0m   source: string;\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~\u001b[0m\n        'source' is declared here.\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m391\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2559: \u001b[0mType '{ maxLength: number; host: string; port: number; db: number; streamName: string; batchSize: number; }' has no properties in common with type 'RedisStreamsConfig'.\n\n    \u001b[7m391\u001b[0m         configWithMaxLength,\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m396\u001b[0m:\u001b[93m23\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'onModuleInit' does not exist on type 'RedisStreamsPublisher'.\n\n    \u001b[7m396\u001b[0m       await publisher.onModuleInit();\n    \u001b[7m   \u001b[0m \u001b[91m                      ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m412\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2559: \u001b[0mType '{ host: string; port: number; streamName: string; }' has no properties in common with type 'RedisStreamsConfig'.\n\n    \u001b[7m412\u001b[0m         minimalConfig,\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m417\u001b[0m:\u001b[93m37\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'onModuleInit' does not exist on type 'RedisStreamsPublisher'.\n\n    \u001b[7m417\u001b[0m       await expect(minimalPublisher.onModuleInit()).resolves.not.toThrow();\n    \u001b[7m   \u001b[0m \u001b[91m                                    ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m434\u001b[0m:\u001b[93m31\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ id: string; type: string; data: { success: boolean; }; metadata: { source: string; }; timestamp: Date; version: string; }' is not assignable to parameter of type 'DomainEvent'.\n      Property 'source' is missing in type '{ id: string; type: string; data: { success: boolean; }; metadata: { source: string; }; timestamp: Date; version: string; }' but required in type 'DomainEvent'.\n\n    \u001b[7m434\u001b[0m       await publisher.publish(event1);\n    \u001b[7m   \u001b[0m \u001b[91m                              ~~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event.types.ts\u001b[0m:\u001b[93m28\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m28\u001b[0m   source: string;\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~\u001b[0m\n        'source' is declared here.\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m450\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ id: string; type: string; data: { recovered: boolean; }; metadata: { source: string; }; timestamp: Date; version: string; }' is not assignable to parameter of type 'DomainEvent'.\n      Property 'source' is missing in type '{ id: string; type: string; data: { recovered: boolean; }; metadata: { source: string; }; timestamp: Date; version: string; }' but required in type 'DomainEvent'.\n\n    \u001b[7m450\u001b[0m       await expect(publisher.publish(event2)).resolves.not.toThrow();\n    \u001b[7m   \u001b[0m \u001b[91m                                     ~~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event.types.ts\u001b[0m:\u001b[93m28\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m28\u001b[0m   source: string;\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~\u001b[0m\n        'source' is declared here.\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m492\u001b[0m:\u001b[93m69\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ id: string; type: string; data: { index: number; }; metadata: { source: string; }; timestamp: Date; version: string; }' is not assignable to parameter of type 'DomainEvent'.\n      Property 'source' is missing in type '{ id: string; type: string; data: { index: number; }; metadata: { source: string; }; timestamp: Date; version: string; }' but required in type 'DomainEvent'.\n\n    \u001b[7m492\u001b[0m       const publishPromises = events.map(event => publisher.publish(event));\n    \u001b[7m   \u001b[0m \u001b[91m                                                                    ~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event.types.ts\u001b[0m:\u001b[93m28\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m28\u001b[0m   source: string;\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~\u001b[0m\n        'source' is declared here.\n    \u001b[96mtest/unit/redis-streams-publisher.unit.spec.ts\u001b[0m:\u001b[93m507\u001b[0m:\u001b[93m31\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ id: string; type: string; data: { size: string; }; metadata: { source: string; }; timestamp: Date; version: string; }' is not assignable to parameter of type 'DomainEvent'.\n      Property 'source' is missing in type '{ id: string; type: string; data: { size: string; }; metadata: { source: string; }; timestamp: Date; version: string; }' but required in type 'DomainEvent'.\n\n    \u001b[7m507\u001b[0m       await publisher.publish(testEvent);\n    \u001b[7m   \u001b[0m \u001b[91m                              ~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event.types.ts\u001b[0m:\u001b[93m28\u001b[0m:\u001b[93m3\u001b[0m\n        \u001b[7m28\u001b[0m   source: string;\n        \u001b[7m  \u001b[0m \u001b[96m  ~~~~~~\u001b[0m\n        'source' is declared here.\n", "name": "/root/code/polyrepo/libs/messaging/test/unit/redis-streams-publisher.unit.spec.ts", "startTime": 1750353518164, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750353518164, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m52\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m52\u001b[0m       expect(event.metadata.source).toBe('api-gateway');\n    \u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m54\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m54\u001b[0m       expect(event.metadata.userId).toBe('user-456');\n    \u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m55\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m55\u001b[0m       expect(event.metadata.sessionId).toBe('session-789');\n    \u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m56\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m56\u001b[0m       expect(event.metadata.customProperty).toBe('custom-value');\n    \u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m180\u001b[0m:\u001b[93m48\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ method: string; path: string; url: string; headers: { 'content-type': string; authorization: string; }; query: { limit: string; offset: string; }; body: { name: string; email: string; }; userAgent: string; clientIp: string; }' is not assignable to parameter of type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; userAgent?: string | undefined; ipAddress?: string | undefined; headers?: Record<...> | undefined; queryParams?: Record<...> | undefined; hasBody: boolean; contentType?: string | undefined; }'.\n      Type '{ method: string; path: string; url: string; headers: { 'content-type': string; authorization: string; }; query: { limit: string; offset: string; }; body: { name: string; email: string; }; userAgent: string; clientIp: string; }' is missing the following properties from type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; userAgent?: string | undefined; ipAddress?: string | undefined; headers?: Record<...> | undefined; queryParams?: Record<...> | undefined; hasBody: boolean; contentType?: string | undefined; }': serviceName, correlationId, hasBody\n\n    \u001b[7m180\u001b[0m         const event = EventFactory.httpRequest(requestData);\n    \u001b[7m   \u001b[0m \u001b[91m                                               ~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m185\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m185\u001b[0m         expect(event.metadata.httpMethod).toBe('POST');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m186\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m186\u001b[0m         expect(event.metadata.httpPath).toBe('/api/users');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m195\u001b[0m:\u001b[93m48\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ method: string; path: string; url: string; }' is not assignable to parameter of type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; userAgent?: string | undefined; ipAddress?: string | undefined; headers?: Record<...> | undefined; queryParams?: Record<...> | undefined; hasBody: boolean; contentType?: string | undefined; }'.\n      Type '{ method: string; path: string; url: string; }' is missing the following properties from type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; userAgent?: string | undefined; ipAddress?: string | undefined; headers?: Record<...> | undefined; queryParams?: Record<...> | undefined; hasBody: boolean; contentType?: string | undefined; }': serviceName, correlationId, hasBody\n\n    \u001b[7m195\u001b[0m         const event = EventFactory.httpRequest(requestData);\n    \u001b[7m   \u001b[0m \u001b[91m                                               ~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m199\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'path' does not exist on type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; userAgent?: string | undefined; ipAddress?: string | undefined; headers?: Record<...> | undefined; queryParams?: Record<...> | undefined; hasBody: boolean; contentType?: string | undefined; }'.\n\n    \u001b[7m199\u001b[0m         expect(event.data.path).toBe('/health');\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m201\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m201\u001b[0m         expect(event.metadata.httpMethod).toBe('GET');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m202\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m202\u001b[0m         expect(event.metadata.httpPath).toBe('/health');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m220\u001b[0m:\u001b[93m49\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ statusCode: number; headers: { 'content-type': string; 'cache-control': string; }; body: { id: number; name: string; }; size: number; duration: number; }' is not assignable to parameter of type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode: number; responseTime: number; responseSize?: number | undefined; cacheHit?: boolean | undefined; retryAttempt?: number | undefined; }'.\n      Type '{ statusCode: number; headers: { 'content-type': string; 'cache-control': string; }; body: { id: number; name: string; }; size: number; duration: number; }' is missing the following properties from type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode: number; responseTime: number; responseSize?: number | undefined; cacheHit?: boolean | undefined; retryAttempt?: number | undefined; }': method, url, serviceName, correlationId, responseTime\n\n    \u001b[7m220\u001b[0m         const event = EventFactory.httpResponse(responseData);\n    \u001b[7m   \u001b[0m \u001b[91m                                                ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m225\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m225\u001b[0m         expect(event.metadata.httpStatusCode).toBe(200);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m226\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m226\u001b[0m         expect(event.metadata.success).toBe(true);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m227\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m227\u001b[0m         expect(event.metadata.responseTime).toBe(150);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m237\u001b[0m:\u001b[93m49\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ statusCode: number; headers: { 'content-type': string; }; body: { error: string; }; duration: number; }' is not assignable to parameter of type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode: number; responseTime: number; responseSize?: number | undefined; cacheHit?: boolean | undefined; retryAttempt?: number | undefined; }'.\n      Type '{ statusCode: number; headers: { 'content-type': string; }; body: { error: string; }; duration: number; }' is missing the following properties from type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode: number; responseTime: number; responseSize?: number | undefined; cacheHit?: boolean | undefined; retryAttempt?: number | undefined; }': method, url, serviceName, correlationId, responseTime\n\n    \u001b[7m237\u001b[0m         const event = EventFactory.httpResponse(responseData);\n    \u001b[7m   \u001b[0m \u001b[91m                                                ~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m239\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m239\u001b[0m         expect(event.metadata.httpStatusCode).toBe(400);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m240\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m240\u001b[0m         expect(event.metadata.success).toBe(false);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m241\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m241\u001b[0m         expect(event.metadata.responseTime).toBe(50);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m250\u001b[0m:\u001b[93m63\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m250\u001b[0m         const event = EventFactory.httpResponse(responseData, { source: 'test' });\n    \u001b[7m   \u001b[0m \u001b[91m                                                              ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m252\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m252\u001b[0m         expect(event.metadata.httpStatusCode).toBe(500);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m253\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m253\u001b[0m         expect(event.metadata.success).toBe(false);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m254\u001b[0m         expect(event.metadata.responseTime).toBe(5000);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m269\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ error: Error; statusCode: number; message: string; stack: string; duration: number; }' is not assignable to parameter of type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode?: number | undefined; errorType: string; errorMessage: string; errorCode?: string | undefined; responseTime: number; retryAttempt?: number | undefined; circuitBreakerState?: string | undefined; }'.\n      Type '{ error: Error; statusCode: number; message: string; stack: string; duration: number; }' is missing the following properties from type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode?: number | undefined; errorType: string; errorMessage: string; errorCode?: string | undefined; responseTime: number; retryAttempt?: number | undefined; circuitBreakerState?: string | undefined; }': method, url, serviceName, correlationId, and 3 more.\n\n    \u001b[7m269\u001b[0m         const event = EventFactory.httpError(errorData);\n    \u001b[7m   \u001b[0m \u001b[91m                                             ~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m272\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'error' does not exist on type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode?: number | undefined; errorType: string; errorMessage: string; errorCode?: string | undefined; responseTime: number; retryAttempt?: number | undefined; circuitBreakerState?: string | undefined; }'.\n\n    \u001b[7m272\u001b[0m         expect(event.data.error).toBeInstanceOf(Error);\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m274\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode?: number | undefined; errorType: string; errorMessage: string; errorCode?: string | undefined; responseTime: number; retryAttempt?: number | undefined; circuitBreakerState?: string | undefined; }'.\n\n    \u001b[7m274\u001b[0m         expect(event.data.message).toBe('Gateway timeout occurred');\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m276\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m276\u001b[0m         expect(event.metadata.httpStatusCode).toBe(504);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m277\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m277\u001b[0m         expect(event.metadata.severity).toBe('high');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m278\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m278\u001b[0m         expect(event.metadata.responseTime).toBe(30000);\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m285\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m285\u001b[0m         }, { source: 'test' });\n    \u001b[7m   \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m290\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m290\u001b[0m         }, { source: 'test' });\n    \u001b[7m   \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m295\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m295\u001b[0m         }, { source: 'test' });\n    \u001b[7m   \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m297\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'clientError.metadata' is possibly 'undefined'.\n\n    \u001b[7m297\u001b[0m         expect(clientError.metadata.severity).toBe('medium');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m298\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'serverError.metadata' is possibly 'undefined'.\n\n    \u001b[7m298\u001b[0m         expect(serverError.metadata.severity).toBe('high');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m299\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'timeoutError.metadata' is possibly 'undefined'.\n\n    \u001b[7m299\u001b[0m         expect(timeoutError.metadata.severity).toBe('high');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m308\u001b[0m:\u001b[93m57\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m308\u001b[0m         const event = EventFactory.httpError(errorData, { source: 'test' });\n    \u001b[7m   \u001b[0m \u001b[91m                                                        ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m310\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'error' does not exist on type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode?: number | undefined; errorType: string; errorMessage: string; errorCode?: string | undefined; responseTime: number; retryAttempt?: number | undefined; circuitBreakerState?: string | undefined; }'.\n\n    \u001b[7m310\u001b[0m         expect(event.data.error).toBeInstanceOf(Error);\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m311\u001b[0m:\u001b[93m27\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'message' does not exist on type '{ method: string; url: string; serviceName: string; operationName?: string | undefined; correlationId: string; statusCode?: number | undefined; errorType: string; errorMessage: string; errorCode?: string | undefined; responseTime: number; retryAttempt?: number | undefined; circuitBreakerState?: string | undefined; }'.\n\n    \u001b[7m311\u001b[0m         expect(event.data.message).toBe('Failed to connect');\n    \u001b[7m   \u001b[0m \u001b[91m                          ~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m312\u001b[0m         expect(event.metadata.httpStatusCode).toBeUndefined();\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m313\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m313\u001b[0m         expect(event.metadata.severity).toBe('medium');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m331\u001b[0m:\u001b[93m74\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m331\u001b[0m         const event = EventFactory.circuitBreakerStateChanged(stateData, metadata);\n    \u001b[7m   \u001b[0m \u001b[91m                                                                         ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m336\u001b[0m         expect(event.metadata.circuitName).toBe('user-service-circuit');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m337\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m337\u001b[0m         expect(event.metadata.stateTransition).toBe('CLOSED -> OPEN');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m348\u001b[0m:\u001b[93m74\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m348\u001b[0m         const event = EventFactory.circuitBreakerStateChanged(stateData, { source: 'test' });\n    \u001b[7m   \u001b[0m \u001b[91m                                                                         ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m350\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m350\u001b[0m         expect(event.metadata.stateTransition).toBe('OPEN -> HALF_OPEN');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m366\u001b[0m:\u001b[93m66\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m366\u001b[0m         const event = EventFactory.cacheOperation(operationData, metadata);\n    \u001b[7m   \u001b[0m \u001b[91m                                                                 ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m371\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m371\u001b[0m         expect(event.metadata.cacheKey).toBe('user:123');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m387\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS18048: \u001b[0m'event.metadata' is possibly 'undefined'.\n\n    \u001b[7m387\u001b[0m         expect(event.metadata.cacheKey).toBe('user:999');\n    \u001b[7m   \u001b[0m \u001b[91m               ~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m399\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ operation: \"set\"; key: string; value: { id: number; name: string; }; ttl: number; responseTime: number; }' is not assignable to parameter of type '{ operation: \"get\" | \"set\" | \"delete\" | \"invalidate\"; key: string; hit: boolean; ttl?: number | undefined; size?: number | undefined; correlationId?: string | undefined; serviceName?: string | undefined; responseTime: number; }'.\n      Property 'hit' is missing in type '{ operation: \"set\"; key: string; value: { id: number; name: string; }; ttl: number; responseTime: number; }' but required in type '{ operation: \"get\" | \"set\" | \"delete\" | \"invalidate\"; key: string; hit: boolean; ttl?: number | undefined; size?: number | undefined; correlationId?: string | undefined; serviceName?: string | undefined; responseTime: number; }'.\n\n    \u001b[7m399\u001b[0m         const event = EventFactory.cacheOperation(operationData);\n    \u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~\u001b[0m\n\n      \u001b[96msrc/events/event-factory.ts\u001b[0m:\u001b[93m182\u001b[0m:\u001b[93m5\u001b[0m\n        \u001b[7m182\u001b[0m     hit: boolean;\n        \u001b[7m   \u001b[0m \u001b[96m    ~~~\u001b[0m\n        'hit' is declared here.\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m463\u001b[0m:\u001b[93m50\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ id: number; email: string; }' is not assignable to parameter of type 'string'.\n\n    \u001b[7m463\u001b[0m       const userEvent = EventFactory.userCreated({ id: 1, email: '<EMAIL>' }, {\n    \u001b[7m   \u001b[0m \u001b[91m                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/event-factory.unit.spec.ts\u001b[0m:\u001b[93m470\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 2.\n\n    \u001b[7m470\u001b[0m         { source: 'test', correlationId }\n    \u001b[7m   \u001b[0m \u001b[91m        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/messaging/test/unit/event-factory.unit.spec.ts", "startTime": 1750353518164, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750353518164, "message": "  ● Test suite failed to run\n\n    \u001b[96msrc/redis/redis-streams-publisher.ts\u001b[0m:\u001b[93m82\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType 'string | null' is not assignable to type 'string'.\n      Type 'null' is not assignable to type 'string'.\n\n    \u001b[7m82\u001b[0m           streamId = await this.redis.xadd(streamName, 'MAXLEN', '~', options.maxLength, '*', ...streamArgs);\n    \u001b[7m  \u001b[0m \u001b[91m          ~~~~~~~~\u001b[0m\n    \u001b[96msrc/redis/redis-streams-publisher.ts\u001b[0m:\u001b[93m84\u001b[0m:\u001b[93m11\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType 'string | null' is not assignable to type 'string'.\n      Type 'null' is not assignable to type 'string'.\n\n    \u001b[7m84\u001b[0m           streamId = await this.redis.xadd(streamName, 'MAXLEN', options.maxLength, '*', ...streamArgs);\n    \u001b[7m  \u001b[0m \u001b[91m          ~~~~~~~~\u001b[0m\n    \u001b[96msrc/redis/redis-streams-publisher.ts\u001b[0m:\u001b[93m87\u001b[0m:\u001b[93m9\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType 'string | null' is not assignable to type 'string'.\n      Type 'null' is not assignable to type 'string'.\n\n    \u001b[7m87\u001b[0m         streamId = await this.redis.xadd(streamName, '*', ...streamArgs);\n    \u001b[7m  \u001b[0m \u001b[91m        ~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/libs/messaging/test/unit/messaging.module.unit.spec.ts", "startTime": 1750353518164, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Publishing"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Publishing should publish single event successfully", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should publish single event successfully"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Publishing"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Publishing should emit wildcard events for all published events", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should emit wildcard events for all published events"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Publishing"], "duration": 3, "failureDetails": [{"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Publishing event\", ObjectContaining {\"correlationId\": \"req-123\", \"eventId\": \"test-3\", \"eventType\": \"cache.hit\"}\nReceived\n       1: \"Publishing event: cache.hit (test-3)\"\n       2: \"Successfully published event: cache.hit\"\n\nNumber of calls: 2", "pass": false}}], "failureMessages": ["Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Publishing event\", ObjectContaining {\"correlationId\": \"req-123\", \"eventId\": \"test-3\", \"eventType\": \"cache.hit\"}\nReceived\n       1: \"Publishing event: cache.hit (test-3)\"\n       2: \"Successfully published event: cache.hit\"\n\nNumber of calls: 2\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:88:32)"], "fullName": "InMemoryEventBus Unit Tests Event Publishing should log published events", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should log published events"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Publishing"], "duration": 6, "failureDetails": [{"message": "expect(received).resolves.not.toThrow()\n\nReceived promise rejected instead of resolved\nRejected to value: [TypeError: Cannot read properties of null (reading 'type')]"}], "failureMessages": ["Error: expect(received).resolves.not.toThrow()\n\nReceived promise rejected instead of resolved\nRejected to value: [TypeError: Cannot read properties of null (reading 'type')]\n    at expect (/root/code/polyrepo/node_modules/expect/build/index.js:113:15)\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:101:13)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "InMemoryEventBus Unit Tests Event Publishing should handle publishing errors gracefully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle publishing errors gracefully"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Publishing"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Publishing should publish events asynchronously", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should publish events asynchronously"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Subscription"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Subscription should register event handlers correctly", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should register event handlers correctly"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Subscription"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Subscription should support multiple handlers for same event type", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should support multiple handlers for same event type"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Subscription"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Subscription should track registered event types", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should track registered event types"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Subscription"], "duration": 3, "failureDetails": [{}, {"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Error in event handler\", ObjectContaining {\"error\": \"Handler error\", \"eventType\": \"error.test\"}\nReceived: {\"correlationId\": undefined, \"error\": \"Handler error\", \"eventId\": \"error-test\", \"eventType\": \"error.test\", \"handlerName\": \"Object\", \"message\": \"Event handler failed: error.test\", \"processingTime\": 1, \"source\": \"test\", \"stack\": \"Error: Handler error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:192:17)\n    at /root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:397:39\n    at Object.<anonymous> (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:404:13)\n    at Object.mockConstructor [as handle] (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:148:19)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:25)\n    at EventEmitter.emit (node:events:530:35)\n    at Immediate._onImmediate (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:43:27)\n    at processImmediate (node:internal/timers:485:21)\"}\n\nNumber of calls: 1", "pass": false}}], "failureMessages": ["Error: Handler error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:192:17)\n    at /root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:397:39\n    at Object.<anonymous> (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:404:13)\n    at Object.mockConstructor [as handle] (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:148:19)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:25)\n    at EventEmitter.emit (node:events:530:35)\n    at Immediate._onImmediate (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:43:27)\n    at processImmediate (node:internal/timers:485:21)", "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Error in event handler\", ObjectContaining {\"error\": \"Handler error\", \"eventType\": \"error.test\"}\nReceived: {\"correlationId\": undefined, \"error\": \"Handler error\", \"eventId\": \"error-test\", \"eventType\": \"error.test\", \"handlerName\": \"Object\", \"message\": \"Event handler failed: error.test\", \"processingTime\": 1, \"source\": \"test\", \"stack\": \"Error: Handler error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:192:17)\n    at /root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:397:39\n    at Object.<anonymous> (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:404:13)\n    at Object.mockConstructor [as handle] (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:148:19)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:25)\n    at EventEmitter.emit (node:events:530:35)\n    at Immediate._onImmediate (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:43:27)\n    at processImmediate (node:internal/timers:485:21)\"}\n\nNumber of calls: 1\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:221:32)"], "fullName": "InMemoryEventBus Unit Tests Event Subscription should wrap handlers with error isolation", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "failed", "title": "should wrap handlers with error isolation"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Subscription"], "duration": 11, "failureDetails": [{"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Event handler executed\", ObjectContaining {\"eventType\": \"performance.test\", \"handlerIndex\": 0, \"processingTime\": Any<Number>}\nReceived\n       1: \"Subscribing handler to event type: performance.test\"\n       2: \"Publishing event: performance.test (perf-test)\"\n       3: \"Successfully published event: performance.test\"\n\nNumber of calls: 6", "pass": false}}], "failureMessages": ["Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Event handler executed\", ObjectContaining {\"eventType\": \"performance.test\", \"handlerIndex\": 0, \"processingTime\": Any<Number>}\nReceived\n       1: \"Subscribing handler to event type: performance.test\"\n       2: \"Publishing event: performance.test (perf-test)\"\n       3: \"Successfully published event: performance.test\"\n\nNumber of calls: 6\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:256:32)"], "fullName": "InMemoryEventBus Unit Tests Event Subscription should track handler performance metrics", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should track handler performance metrics"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Unsubscription"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Unsubscription should remove specific handler from event type", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should remove specific handler from event type"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Unsubscription"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Unsubscription should remove handler from EventEmitter listeners", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should remove handler from EventEmitter listeners"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Unsubscription"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Unsubscription should handle unsubscribing non-existent handler gracefully", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should handle unsubscribing non-existent handler gracefully"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Event Unsubscription"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Event Unsubscription should clean up empty event type registrations", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should clean up empty event type registrations"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Bus Management"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Bus Management should clear all subscribers", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should clear all subscribers"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Bus Management"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Bus Management should report correct subscriber counts", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should report correct subscriber counts"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Bus Management"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Bus Management should report health status correctly", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should report health status correctly"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Bus Management"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Bus Management should enumerate registered event types correctly", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should enumerate registered event types correctly"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Correlation Context Tracking"], "duration": 1, "failureDetails": [{"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Event handler executed\", ObjectContaining {\"correlationId\": \"test-correlation-456\"}\nReceived\n       1: \"Subscribing handler to event type: correlation.test\"\n       2: \"Publishing event: correlation.test (corr-test)\"\n       3: \"Successfully published event: correlation.test\"\n\nNumber of calls: 6", "pass": false}}], "failureMessages": ["Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Event handler executed\", ObjectContaining {\"correlationId\": \"test-correlation-456\"}\nReceived\n       1: \"Subscribing handler to event type: correlation.test\"\n       2: \"Publishing event: correlation.test (corr-test)\"\n       3: \"Successfully published event: correlation.test\"\n\nNumber of calls: 6\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:427:32)"], "fullName": "InMemoryEventBus Unit Tests Correlation Context Tracking should track correlation IDs in event handling", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should track correlation IDs in event handling"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Correlation Context Tracking"], "duration": 1, "failureDetails": [{"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Event handler executed\", ObjectContaining {\"eventType\": \"no-correlation.test\"}\nReceived\n       1: \"Subscribing handler to event type: no-correlation.test\"\n       2: \"Publishing event: no-correlation.test (no-corr-test)\"\n       3: \"Successfully published event: no-correlation.test\"\n\nNumber of calls: 6", "pass": false}}], "failureMessages": ["Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Event handler executed\", ObjectContaining {\"eventType\": \"no-correlation.test\"}\nReceived\n       1: \"Subscribing handler to event type: no-correlation.test\"\n       2: \"Publishing event: no-correlation.test (no-corr-test)\"\n       3: \"Successfully published event: no-correlation.test\"\n\nNumber of calls: 6\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:455:32)"], "fullName": "InMemoryEventBus Unit Tests Correlation Context Tracking should handle events without correlation IDs", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should handle events without correlation IDs"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "<PERSON><PERSON><PERSON>"], "duration": 12, "failureDetails": [{"matcherResult": {"message": "expect(received).not.toThrow()\n\nError name:    \"TypeError\"\nError message: \"Cannot read properties of null (reading 'constructor')\"\n\n      144 |         message: `Failed to subscribe handler: ${eventType}`,\n      145 |         eventType,\n    > 146 |         handlerName: handler.constructor.name,\n          |                              ^\n      147 |         error: errorMessage,\n      148 |         stack: errorStack\n      149 |       });\n\n      at InMemoryEventBus.subscribe (src/events/in-memory-event-bus.ts:146:30)\n      at test/unit/in-memory-event-bus.unit.spec.ts:469:18\n      at Object.<anonymous> (../../node_modules/expect/build/toThrowMatchers.js:74:11)\n      at Object.throwingMatcher [as toThrow] (../../node_modules/expect/build/index.js:320:21)\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:470:14)", "pass": true}}], "failureMessages": ["Error: expect(received).not.toThrow()\n\nError name:    \"<PERSON>Error\"\nError message: \"Cannot read properties of null (reading 'constructor')\"\n\n      144 |         message: `Failed to subscribe handler: ${eventType}`,\n      145 |         eventType,\n    > 146 |         handlerName: handler.constructor.name,\n          |                              ^\n      147 |         error: errorMessage,\n      148 |         stack: errorStack\n      149 |       });\n\n      at InMemoryEventBus.subscribe (src/events/in-memory-event-bus.ts:146:30)\n      at test/unit/in-memory-event-bus.unit.spec.ts:469:18\n      at Object.<anonymous> (../../node_modules/expect/build/toThrowMatchers.js:74:11)\n      at Object.throwingMatcher [as toThrow] (../../node_modules/expect/build/index.js:320:21)\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:470:14)\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:470:14)\n    at Promise.then.completed (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/root/code/polyrepo/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:316:40)\n    at _runTest (/root/code/polyrepo/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at _runTestsForDescribeBlock (/root/code/polyrepo/node_modules/jest-circus/build/run.js:121:9)\n    at run (/root/code/polyrepo/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/root/code/polyrepo/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/root/code/polyrepo/node_modules/jest-runner/build/runTest.js:444:34)\n    at Object.worker (/root/code/polyrepo/node_modules/jest-runner/build/testWorker.js:106:12)"], "fullName": "InMemoryEventBus Unit Tests Error Scenarios should handle handler registration errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle handler registration errors"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "<PERSON><PERSON><PERSON>"], "duration": 1, "failureDetails": [{}, {}], "failureMessages": ["Error: <PERSON><PERSON> 1 error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:479:17)\n    at /root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:397:39\n    at Object.<anonymous> (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:404:13)\n    at Object.mockConstructor [as handle] (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:148:19)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:25)\n    at EventEmitter.emit (node:events:530:35)\n    at Immediate._onImmediate (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:43:27)\n    at processImmediate (node:internal/timers:485:21)", "Error: <PERSON><PERSON> 2 error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:485:17)\n    at /root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:397:39\n    at Object.<anonymous> (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:404:13)\n    at Object.mockConstructor [as handle] (/root/code/polyrepo/node_modules/jest-environment-node/node_modules/jest-mock/build/index.js:148:19)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:25)\n    at EventEmitter.emit (node:events:530:35)\n    at Immediate._onImmediate (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:43:27)\n    at processImmediate (node:internal/timers:485:21)"], "fullName": "InMemoryEventBus Unit Tests Error Scenarios should isolate errors between different handlers", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "failed", "title": "should isolate errors between different handlers"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "<PERSON><PERSON><PERSON>"], "duration": 12, "failureDetails": [{}, {"matcherResult": {"message": "expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Error in event handler\", ObjectContaining {\"error\": \"Async handler error\"}\nReceived: {\"correlationId\": undefined, \"error\": \"Async handler error\", \"eventId\": \"async-error-test\", \"eventType\": \"async.error\", \"handlerName\": \"Object\", \"message\": \"Event handler failed: async.error\", \"processingTime\": 1, \"source\": \"test\", \"stack\": \"Error: Async handler error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:523:17)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:11)\"}\n\nNumber of calls: 1", "pass": false}}], "failureMessages": ["Error: Async handler error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:523:17)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:11)", "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"Error in event handler\", ObjectContaining {\"error\": \"Async handler error\"}\nReceived: {\"correlationId\": undefined, \"error\": \"Async handler error\", \"eventId\": \"async-error-test\", \"eventType\": \"async.error\", \"handlerName\": \"Object\", \"message\": \"Event handler failed: async.error\", \"processingTime\": 1, \"source\": \"test\", \"stack\": \"Error: Async handler error\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:523:17)\n    at EventEmitter.eventListener (/root/code/polyrepo/libs/messaging/src/events/in-memory-event-bus.ts:94:11)\"}\n\nNumber of calls: 1\n    at Object.<anonymous> (/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts:546:32)"], "fullName": "InMemoryEventBus Unit Tests Error Scenarios should handle async handler errors", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should handle async handler errors"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Module Lifecycle Integration"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Module Lifecycle Integration should integrate with NestJS module lifecycle", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should integrate with NestJS module lifecycle"}, {"ancestorTitles": ["InMemoryEventBus Unit Tests", "Module Lifecycle Integration"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "InMemoryEventBus Unit Tests Module Lifecycle Integration should clean up resources on clear", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should clean up resources on clear"}], "endTime": 1750353518077, "message": "  ● InMemoryEventBus Unit Tests › Event Publishing › should log published events\n\n    expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n    Expected: \"Publishing event\", ObjectContaining {\"correlationId\": \"req-123\", \"eventId\": \"test-3\", \"eventType\": \"cache.hit\"}\n    Received\n           1: \"Publishing event: cache.hit (test-3)\"\n           2: \"Successfully published event: cache.hit\"\n\n    Number of calls: 2\n\n      86 |       await eventBus.publish(testEvent);\n      87 |\n    > 88 |       expect(mockLogger.debug).toHaveBeenCalledWith(\n         |                                ^\n      89 |         'Publishing event',\n      90 |         expect.objectContaining({\n      91 |           eventId: 'test-3',\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:88:32)\n\n  ● InMemoryEventBus Unit Tests › Event Publishing › should handle publishing errors gracefully\n\n    expect(received).resolves.not.toThrow()\n\n    Received promise rejected instead of resolved\n    Rejected to value: [TypeError: Cannot read properties of null (reading 'type')]\n\n       99 |       const invalidEvent = null as any;\n      100 |\n    > 101 |       await expect(eventBus.publish(invalidEvent)).resolves.not.toThrow();\n          |             ^\n      102 |       \n      103 |       expect(mockLogger.error).toHaveBeenCalledWith(\n      104 |         'Failed to publish event',\n\n      at expect (../../node_modules/expect/build/index.js:113:15)\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:101:13)\n\n  ● InMemoryEventBus Unit Tests › Event Subscription › should wrap handlers with error isolation\n\n    Handler error\n\n      190 |       const errorHandler = {\n      191 |         handle: jest.fn().mockImplementation(() => {\n    > 192 |           throw new Error('Handler error');\n          |                 ^\n      193 |         }),\n      194 |         supportedEvents: ['error.test']\n      195 |       };\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:192:17)\n      at EventEmitter.eventListener (src/events/in-memory-event-bus.ts:94:25)\n      at Immediate._onImmediate (src/events/in-memory-event-bus.ts:43:27)\n\n  ● InMemoryEventBus Unit Tests › Event Subscription › should wrap handlers with error isolation\n\n    expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n    Expected: \"Error in event handler\", ObjectContaining {\"error\": \"Handler error\", \"eventType\": \"error.test\"}\n    Received: {\"correlationId\": undefined, \"error\": \"Handler error\", \"eventId\": \"error-test\", \"eventType\": \"error.test\", \"handlerName\": \"Object\", \"message\": \"Event handler failed: error.test\", \"processingTime\": 1, \"source\": \"test\", \"stack\": \"Error: Handler error\n\n      190 |       const errorHandler = {\n      191 |         handle: jest.fn().mockImplementation(() => {\n    > 192 |           throw new Error('Handler error');\n          |                 ^\n      193 |         }),\n      194 |         supportedEvents: ['error.test']\n      195 |       };\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:192:17)\n      at EventEmitter.eventListener (src/events/in-memory-event-bus.ts:94:25)\n      at Immediate._onImmediate (src/events/in-memory-event-bus.ts:43:27)\n      at processImmediate (node:internal/timers:485:21)\"}\n      Number of calls: 1\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:221:32)\n\n  ● InMemoryEventBus Unit Tests › Event Subscription › should track handler performance metrics\n\n    expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n    Expected: \"Event handler executed\", ObjectContaining {\"eventType\": \"performance.test\", \"handlerIndex\": 0, \"processingTime\": Any<Number>}\n    Received\n           1: \"Subscribing handler to event type: performance.test\"\n           2: \"Publishing event: performance.test (perf-test)\"\n           3: \"Successfully published event: performance.test\"\n\n    Number of calls: 6\n\n      254 |       await new Promise(resolve => setImmediate(resolve));\n      255 |\n    > 256 |       expect(mockLogger.debug).toHaveBeenCalledWith(\n          |                                ^\n      257 |         'Event handler executed',\n      258 |         expect.objectContaining({\n      259 |           eventType: 'performance.test',\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:256:32)\n\n  ● InMemoryEventBus Unit Tests › Correlation Context Tracking › should track correlation IDs in event handling\n\n    expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n    Expected: \"Event handler executed\", ObjectContaining {\"correlationId\": \"test-correlation-456\"}\n    Received\n           1: \"Subscribing handler to event type: correlation.test\"\n           2: \"Publishing event: correlation.test (corr-test)\"\n           3: \"Successfully published event: correlation.test\"\n\n    Number of calls: 6\n\n      425 |       await new Promise(resolve => setImmediate(resolve));\n      426 |\n    > 427 |       expect(mockLogger.debug).toHaveBeenCalledWith(\n          |                                ^\n      428 |         'Event handler executed',\n      429 |         expect.objectContaining({\n      430 |           correlationId: 'test-correlation-456'\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:427:32)\n\n  ● InMemoryEventBus Unit Tests › Correlation Context Tracking › should handle events without correlation IDs\n\n    expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n    Expected: \"Event handler executed\", ObjectContaining {\"eventType\": \"no-correlation.test\"}\n    Received\n           1: \"Subscribing handler to event type: no-correlation.test\"\n           2: \"Publishing event: no-correlation.test (no-corr-test)\"\n           3: \"Successfully published event: no-correlation.test\"\n\n    Number of calls: 6\n\n      453 |\n      454 |       expect(handler.handle).toHaveBeenCalled();\n    > 455 |       expect(mockLogger.debug).toHaveBeenCalledWith(\n          |                                ^\n      456 |         'Event handler executed',\n      457 |         expect.objectContaining({\n      458 |           eventType: 'no-correlation.test'\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:455:32)\n\n  ● InMemoryEventBus Unit Tests › Error Scenarios › should handle handler registration errors\n\n    expect(received).not.toThrow()\n\n    Error name:    \"TypeError\"\n    Error message: \"Cannot read properties of null (reading 'constructor')\"\n\n          144 |         message: `Failed to subscribe handler: ${eventType}`,\n          145 |         eventType,\n        > 146 |         handlerName: handler.constructor.name,\n              |                              ^\n          147 |         error: errorMessage,\n          148 |         stack: errorStack\n          149 |       });\n\n      at InMemoryEventBus.subscribe (src/events/in-memory-event-bus.ts:146:30)\n      at test/unit/in-memory-event-bus.unit.spec.ts:469:18\n      at Object.<anonymous> (../../node_modules/expect/build/toThrowMatchers.js:74:11)\n      at Object.throwingMatcher [as toThrow] (../../node_modules/expect/build/index.js:320:21)\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:470:14)\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:470:14)\n\n  ● InMemoryEventBus Unit Tests › Error Scenarios › should isolate errors between different handlers\n\n    Handler 1 error\n\n      477 |       const errorHandler1 = {\n      478 |         handle: jest.fn().mockImplementation(() => {\n    > 479 |           throw new Error('Handler 1 error');\n          |                 ^\n      480 |         }),\n      481 |         supportedEvents: ['multi.error']\n      482 |       };\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:479:17)\n      at EventEmitter.eventListener (src/events/in-memory-event-bus.ts:94:25)\n      at Immediate._onImmediate (src/events/in-memory-event-bus.ts:43:27)\n\n  ● InMemoryEventBus Unit Tests › Error Scenarios › should isolate errors between different handlers\n\n    Handler 2 error\n\n      483 |       const errorHandler2 = {\n      484 |         handle: jest.fn().mockImplementation(() => {\n    > 485 |           throw new Error('Handler 2 error');\n          |                 ^\n      486 |         }),\n      487 |         supportedEvents: ['multi.error']\n      488 |       };\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:485:17)\n      at EventEmitter.eventListener (src/events/in-memory-event-bus.ts:94:25)\n      at Immediate._onImmediate (src/events/in-memory-event-bus.ts:43:27)\n\n  ● InMemoryEventBus Unit Tests › Error Scenarios › should handle async handler errors\n\n    Async handler error\n\n      521 |         handle: jest.fn().mockImplementation(async () => {\n      522 |           await new Promise(resolve => setTimeout(resolve, 1));\n    > 523 |           throw new Error('Async handler error');\n          |                 ^\n      524 |         }),\n      525 |         supportedEvents: ['async.error']\n      526 |       };\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:523:17)\n      at EventEmitter.eventListener (src/events/in-memory-event-bus.ts:94:11)\n\n  ● InMemoryEventBus Unit Tests › Error Scenarios › should handle async handler errors\n\n    expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n    Expected: \"Error in event handler\", ObjectContaining {\"error\": \"Async handler error\"}\n    Received: {\"correlationId\": undefined, \"error\": \"Async handler error\", \"eventId\": \"async-error-test\", \"eventType\": \"async.error\", \"handlerName\": \"Object\", \"message\": \"Event handler failed: async.error\", \"processingTime\": 1, \"source\": \"test\", \"stack\": \"Error: Async handler error\n\n      521 |         handle: jest.fn().mockImplementation(async () => {\n      522 |           await new Promise(resolve => setTimeout(resolve, 1));\n    > 523 |           throw new Error('Async handler error');\n          |                 ^\n      524 |         }),\n      525 |         supportedEvents: ['async.error']\n      526 |       };\n\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:523:17)\n      at EventEmitter.eventListener (src/events/in-memory-event-bus.ts:94:11)\"}\n      Number of calls: 1\n      at Object.<anonymous> (test/unit/in-memory-event-bus.unit.spec.ts:546:32)\n", "name": "/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts", "startTime": 1750353510280, "status": "failed", "summary": ""}], "wasInterrupted": false}