# Messaging Library Integration Testing

## Overview

The messaging library includes comprehensive integration tests that verify Redis Streams functionality, consumer group behavior, and cross-library event flow using real Redis instances.

## Test Categories

### 1. Redis Streams Event Publishing
- **Event Persistence**: Verifies events are correctly written to Redis streams
- **Batch Publishing**: Tests high-performance batch operations
- **Stream Integrity**: Ensures event data maintains structure and ordering

### 2. Consumer Group Integration
- **Message Distribution**: Tests parallel processing across multiple consumers
- **Acknowledgment Patterns**: Verifies XACK command functionality
- **Message Redelivery**: Tests unacknowledged message handling and auto-claiming

### 3. Cross-Library Event Integration
- **HTTP Lifecycle Events**: Tests HTTP request/response event publishing
- **Cache Operation Events**: Verifies cache operation event flow
- **Event Type Validation**: Ensures correct event structure across libraries

### 4. Error Recovery & Resilience
- **Connection Failure Handling**: Tests graceful degradation when Redis is unavailable
- **Consumer Failure Recovery**: Verifies message redelivery after consumer crashes
- **Auto-claiming Behavior**: Tests XAUTOCLAIM for unacknowledged messages

### 5. Performance & Monitoring
- **High-Volume Processing**: Tests 1000+ events/second throughput
- **Stream Information**: Verifies monitoring capabilities with XINFO commands
- **Consumer Group Metrics**: Tests consumer group status and pending message tracking

## Running Integration Tests

### Prerequisites

1. **Redis Server**: Integration tests require a running Redis instance
   ```bash
   # Using Docker
   docker run -d -p 6379:6379 redis:7
   
   # Using local Redis
   redis-server
   ```

2. **Environment Variables** (optional):
   ```bash
   export REDIS_HOST=localhost
   export REDIS_PORT=6379
   ```

### Test Commands

```bash
# Run only unit tests (use ioredis-mock)
yarn test:unit

# Run only integration tests (require real Redis)
yarn test:integration

# Run all tests
yarn test:all

# Watch integration tests during development
yarn test:integration:watch
```

### Test Configuration

- **Database Isolation**: Integration tests use Redis database `15` to avoid conflicts
- **Sequential Execution**: Tests run with `maxWorkers: 1` to prevent Redis race conditions
- **Timeout Settings**: 30-second timeout for Redis operations
- **Auto-cleanup**: Global setup/teardown ensures clean test environment

## Integration Test Patterns

### Consumer Group Testing Pattern

```typescript
it('should distribute messages across multiple consumers', async () => {
  // 1. Publish events to stream
  await publisher.publishBatch(events);
  
  // 2. Read with multiple consumers
  const c1Messages = await redisClient.xreadgroup(
    'GROUP', CONSUMER_GROUP, 'consumer-1', ...
  );
  const c2Messages = await redisClient.xreadgroup(
    'GROUP', CONSUMER_GROUP, 'consumer-2', ...
  );
  
  // 3. Verify distribution and no overlap
  expect(consumer1Messages.length + consumer2Messages.length).toBe(totalEvents);
  expect(hasNoOverlap(c1Messages, c2Messages)).toBe(true);
});
```

### Message Acknowledgment Pattern

```typescript
it('should handle message acknowledgment correctly', async () => {
  // 1. Publish and read message
  await publisher.publish(testEvent);
  const messages = await redisClient.xreadgroup(...);
  const messageId = messages[0][1][0][0];
  
  // 2. Verify pending before ack
  const pending = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
  expect(pending[0]).toBe(1);
  
  // 3. Acknowledge and verify cleanup
  await redisClient.xack(STREAM_NAME, CONSUMER_GROUP, messageId);
  const pendingAfter = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
  expect(pendingAfter[0]).toBe(0);
});
```

### Error Recovery Pattern

```typescript
it('should support message redelivery for failed consumers', async () => {
  // 1. Consumer reads but doesn't acknowledge (simulates crash)
  const messages = await redisClient.xreadgroup(...);
  // No XACK called - simulates consumer crash
  
  // 2. Verify message is pending
  const pending = await redisClient.xpending(...);
  expect(pending[0]).toBe(1);
  
  // 3. Auto-claim for recovery consumer
  const claimed = await redisClient.xautoclaim(
    STREAM_NAME, CONSUMER_GROUP, 'recovery-consumer', 0, messageId
  );
  
  // 4. Verify successful recovery
  expect(claimed[1]).toHaveLength(1);
});
```

## Performance Benchmarking

Integration tests include performance verification:

```typescript
it('should handle high-volume event publishing', async () => {
  const eventCount = 1000;
  const startTime = Date.now();
  
  // Batch publish for optimal performance
  await publishInBatches(events, batchSize = 100);
  
  const duration = Date.now() - startTime;
  const throughput = eventCount / duration * 1000; // events/sec
  
  expect(duration).toBeLessThan(5000); // < 5 seconds
  expect(throughput).toBeGreaterThan(200); // > 200 events/sec
});
```

## Monitoring Integration

Tests verify monitoring capabilities:

```typescript
// Stream information
const streamInfo = await redisClient.xinfoStream(STREAM_NAME);
expect(streamInfo.length).toBe(expectedCount);

// Consumer group status
const groupInfo = await redisClient.xinfoGroups(STREAM_NAME);
expect(groupInfo[0].name).toBe(CONSUMER_GROUP);

// Pending message tracking
const pending = await redisClient.xpending(STREAM_NAME, CONSUMER_GROUP);
// [count, start-id, end-id, consumers]
```

## Best Practices

### 1. Database Isolation
- Always use separate Redis database for tests (db: 15)
- Clean up test data in setup/teardown hooks

### 2. Consumer Group Management
- Create consumer groups in test setup
- Use unique consumer names to avoid conflicts
- Clean up consumer groups after tests

### 3. Message Acknowledgment
- Always acknowledge test messages to prevent buildup
- Test both successful and failed acknowledgment scenarios

### 4. Error Testing
- Test Redis connection failures
- Verify graceful degradation
- Test consumer failure and recovery scenarios

### 5. Performance Testing
- Use batch operations for high-volume tests
- Measure and verify throughput
- Test with realistic message sizes

## Troubleshooting

### Redis Connection Issues
```bash
# Check Redis status
redis-cli ping

# Check Redis logs
docker logs <redis-container-id>

# Verify Redis configuration
redis-cli config get "*"
```

### Test Database Issues
```bash
# Clear test database manually
redis-cli -n 15 flushdb

# Check test database content
redis-cli -n 15 keys "*"
```

### Consumer Group Issues
```bash
# List consumer groups
redis-cli xinfo groups <stream-name>

# Check pending messages
redis-cli xpending <stream-name> <group-name>

# Delete consumer group
redis-cli xgroup destroy <stream-name> <group-name>
```

## CI/CD Integration

For continuous integration, ensure Redis is available:

```yaml
# GitHub Actions example
services:
  redis:
    image: redis:7
    ports:
      - 6379:6379
    options: >-
      --health-cmd "redis-cli ping"
      --health-interval 10s
      --health-timeout 5s
      --health-retries 5

steps:
  - name: Run integration tests
    run: yarn test:integration
    env:
      REDIS_HOST: localhost
      REDIS_PORT: 6379
```

## Integration with Development Workflow

### Local Development
```bash
# Start Redis for development
docker-compose up redis

# Run tests during development
yarn test:integration:watch
```

### Testing New Features
1. Write unit tests first (fast feedback)
2. Add integration tests for Redis interactions
3. Test consumer group behavior if applicable
4. Verify cross-library event integration
5. Add performance benchmarks for high-volume features