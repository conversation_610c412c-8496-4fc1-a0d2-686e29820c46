#!/bin/bash

# Build all shared libraries script
# This script builds all shared libraries in the correct order

set -e

echo "Building shared libraries..."

LIBS_DIR="D:/Code/polyrepo/libs"
BUILT_COUNT=0
FAILED_COUNT=0

# List of libraries to build (can be in dependency order if needed)
LIBRARIES=(
    "shared-utils"
    "types" 
    "shared-types"
    "common"
    "observability"
    "resilience"
    "nestjs-common"
)

for lib in "${LIBRARIES[@]}"; do
    echo "Building library: $lib"
    if cd "$LIBS_DIR/$lib" && yarn build; then
        echo "✓ Successfully built $lib"
        ((BUILT_COUNT++))
    else
        echo "✗ Failed to build $lib"
        ((FAILED_COUNT++))
    fi
done

echo ""
echo "Build Summary:"
echo "Successfully built: $BUILT_COUNT libraries"
echo "Failed to build: $FAILED_COUNT libraries"

if [ $FAILED_COUNT -eq 0 ]; then
    echo "All shared libraries built successfully! 🎉"
    exit 0
else
    echo "Some libraries failed to build. Please check the errors above."
    exit 1
fi
