#!/bin/bash

echo "🔍 Validating New Observability Stack..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check service
check_service() {
    local service_name="$1"
    local url="$2"
    local expected_response="$3"
    
    echo -e "${BLUE}Testing ${service_name}...${NC}"
    
    if response=$(curl -s "$url" 2>/dev/null); then
        if [[ "$expected_response" == "" ]] || [[ "$response" == *"$expected_response"* ]]; then
            echo -e "${GREEN}✅ ${service_name} is healthy${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️  ${service_name} responded but with unexpected content${NC}"
            echo -e "   Response: ${response:0:100}..."
            return 1
        fi
    else
        echo -e "${RED}❌ ${service_name} is not responding${NC}"
        return 1
    fi
}

# Function to test trace ingestion
test_trace_ingestion() {
    echo -e "${BLUE}Testing trace ingestion...${NC}"
    
    local test_trace='{
        "resourceSpans": [{
            "resource": {
                "attributes": [{
                    "key": "service.name",
                    "value": {"stringValue": "test-service"}
                }]
            },
            "scopeSpans": [{
                "spans": [{
                    "traceId": "12345678901234567890123456789012",
                    "spanId": "1234567890123456",
                    "name": "test-span",
                    "startTimeUnixNano": "1640995200000000000",
                    "endTimeUnixNano": "1640995201000000000"
                }]
            }]
        }]
    }'
    
    if curl -s -X POST "http://localhost:4318/v1/traces" \
        -H "Content-Type: application/json" \
        -d "$test_trace" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Trace ingestion working${NC}"
        return 0
    else
        echo -e "${RED}❌ Trace ingestion failed${NC}"
        return 1
    fi
}

# Check if services are running
echo -e "${BLUE}Checking if containers are running...${NC}"
if ! docker ps --format "{{.Names}}" | grep -E "(tempo|pyroscope|otel-collector)" >/dev/null; then
    echo -e "${RED}❌ Required containers are not running${NC}"
    echo "Please run: cd infrastructure/monitoring && docker-compose up -d"
    exit 1
fi

echo -e "${GREEN}✅ All required containers are running${NC}"
echo ""

# Test services
failed_services=0

# Test Tempo
check_service "Tempo" "http://localhost:3201/ready" || ((failed_services++))

# Test Pyroscope (just check if it responds)
check_service "Pyroscope" "http://localhost:4040" || ((failed_services++))

# Test OpenTelemetry Collector health
check_service "OpenTelemetry Collector" "http://localhost:4318/v1/traces" || ((failed_services++))

# Test Grafana (should still be working)
check_service "Grafana" "http://localhost:3200/api/health" "ok" || ((failed_services++))

# Test Prometheus (should still be working)  
check_service "Prometheus" "http://localhost:9090/-/ready" "Prometheus" || ((failed_services++))

# Test Loki (should still be working)
check_service "Loki" "http://localhost:3100/ready" || ((failed_services++))

echo ""

# Test trace ingestion
test_trace_ingestion || ((failed_services++))

echo ""

# Final summary
if [ $failed_services -eq 0 ]; then
    echo -e "${GREEN}🎉 All observability services are healthy!${NC}"
    echo ""
    echo -e "${BLUE}📊 Access URLs:${NC}"
    echo "  • Grafana:      http://localhost:3200 (admin/admin)"
    echo "  • Tempo:        http://localhost:3201"
    echo "  • Pyroscope:    http://localhost:4040"
    echo "  • Prometheus:   http://localhost:9090"
    echo "  • Loki:         http://localhost:3100"
    echo ""
    echo -e "${BLUE}🔗 OTLP Endpoints:${NC}"
    echo "  • HTTP:         http://localhost:4318/v1/traces"
    echo "  • gRPC:         http://localhost:4317"
    echo ""
    echo -e "${GREEN}✅ Migration from Jaeger to Tempo completed successfully!${NC}"
    exit 0
else
    echo -e "${RED}⚠️  $failed_services service(s) failed health checks${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Troubleshooting:${NC}"
    echo "  • Check container logs: docker-compose logs <service-name>"
    echo "  • Restart services: docker-compose restart"
    echo "  • Check ports: netstat -tlnp | grep -E '(3201|4040|4317|4318)'"
    exit 1
fi