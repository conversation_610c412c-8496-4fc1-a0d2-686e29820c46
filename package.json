{"name": "polyrepo-root", "version": "1.0.0", "private": true, "description": "Root for the Manual Polyrepo setup", "workspaces": {"packages": ["services/*", "frontends/*", "libs/*"], "nohoist": ["@polyrepo/user-service/**", "libs/observability/**"]}, "scripts": {"dev": "echo \"Development Modes:\\n  - yarn dev:cli start               # RECOMMENDED: Enhanced bundled development with CLI\\n  - yarn start:dev:local             # Local services with Docker infrastructure\\n  - yarn start:prod:docker           # Production-like Docker build (needs restoration)\\n\\nFor help: yarn dev:cli --help\"", "prisma:generate": "cd services/user-service && npx prisma generate", "prisma:migrate": "cd services/user-service && npx prisma migrate deploy", "build:all": "npm run build --workspaces --if-present", "build:libs": "bash infrastructure/local-dev/build-libs-order.sh", "start:dev:local": "bash infrastructure/local-dev/start-dev-local.sh", "start:prod:docker": "bash infrastructure/local-dev/start-prod-like-docker.sh", "dev:cli": "node infrastructure/local-dev/watcher/cli/dev-cli.js", "lint:all": "npm run lint --workspaces --if-present", "test:all": "npm run test --workspaces --if-present", "install:all": "npm install --workspaces"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "babel-loader": "^10.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.3.3"}, "dependencies": {"@nestjs/terminus": "^10.2.0", "ioredis": "^5.4.1", "tslib": "^2.8.1"}, "resolutions": {"minipass": "^7.1.2", "tar": "^6.1.0", "node-pre-gyp": "^0.17.0"}}