const fs = require('fs');

const eventFactoryPath = '/root/code/polyrepo/libs/messaging/test/unit/event-factory.unit.spec.ts';
let content = fs.readFileSync(eventFactoryPath, 'utf8');

// Fix metadata access - change from optional to non-optional since we know it exists
content = content.replace(/event\.metadata\?\.(\w+)/g, 'event.metadata!.$1');

// Fix userCreated calls - first parameter should be userId string, not user object
content = content.replace(
  /EventFactory\.userCreated\(mockUserData, metadata\)/g,
  'EventFactory.userCreated(\'user-123\', mockUserData, metadata?.correlationId)'
);

// Fix userUpdated calls - userId should be string
content = content.replace(
  /EventFactory\.userUpdated\(mockUserData\.id, changes, metadata\)/g,
  'EventFactory.userUpdated(\'user-123\', changes, metadata?.correlationId)'
);

// Fix userDeleted calls - remove extra parameters
content = content.replace(
  /EventFactory\.userDeleted\(mockUserData\.id, reason, metadata\)/g,
  'EventFactory.userDeleted(\'user-123\', metadata?.correlationId)'
);

// Fix userRestored calls - remove extra parameters  
content = content.replace(
  /EventFactory\.userRestored\(mockUserData\.id, restoredData, metadata\)/g,
  'EventFactory.userRestored(\'user-123\', metadata?.correlationId)'
);

// Fix createUserEvent calls - userId should be string
content = content.replace(
  /EventFactory\.createUserEvent\(customType, mockUserData\.id, customData, metadata\)/g,
  'EventFactory.createUserEvent(customType, \'user-123\', customData, metadata)'
);

// Fix httpRequest calls - remove extra metadata parameter
content = content.replace(
  /EventFactory\.httpRequest\(requestData, metadata\)/g,
  'EventFactory.httpRequest(requestData)'
);

// Fix httpResponse calls - remove extra metadata parameter
content = content.replace(
  /EventFactory\.httpResponse\(responseData, metadata\)/g,
  'EventFactory.httpResponse(responseData)'
);

// Fix httpError calls - remove extra metadata parameter
content = content.replace(
  /EventFactory\.httpError\(errorData, metadata\)/g,
  'EventFactory.httpError(errorData)'
);

// Fix cacheOperation calls - add missing 'hit' property
content = content.replace(
  /operation: 'set' as const,\s*key: '[^']*',\s*ttl: \d+,\s*size: \d+,\s*responseTime: \d+/g,
  'operation: \'set\' as const,\n          key: \'user:456\',\n          hit: true,\n          ttl: 3600,\n          size: 512,\n          responseTime: 8'
);

content = content.replace(
  /operation: 'delete' as const,\s*key: '[^']*',\s*responseTime: \d+/g,
  'operation: \'delete\' as const,\n          key: \'user:789\',\n          hit: true,\n          responseTime: 3'
);

// Fix correlationId access - now it's a top-level property
content = content.replace(/\.metadata\.correlationId/g, '.correlationId');
content = content.replace(/\.metadata\?\.correlationId/g, '.correlationId');

fs.writeFileSync(eventFactoryPath, content);

console.log('Fixed event factory test TypeScript issues');