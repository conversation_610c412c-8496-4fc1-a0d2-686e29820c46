# Cross-Service Integration Tests

This directory contains integration tests that validate data consistency and communication patterns across service boundaries in the Polyrepo microservices architecture.

## Test Categories

### 1. Cross-Service Integration (`cross-service-integration.spec.ts`)
- **Purpose**: Conceptual framework and patterns for cross-service testing
- **Focus**: Data consistency patterns, error handling scenarios, business event correlation
- **Implementation**: Template/reference implementation with simulated responses

### 2. Auth-User Integration (`auth-user-integration.spec.ts`)
- **Purpose**: Practical integration tests between Auth and User services
- **Focus**: Real HTTP requests, actual data flow validation, performance testing
- **Implementation**: Executable tests against running services

## Running Cross-Service Tests

### Prerequisites

Ensure all required services are running:

```bash
# Start full development environment
cd /root/code/polyrepo
yarn start:dev:bundled:watch

# Verify services are healthy
curl http://localhost:3001/health  # Auth Service
curl http://localhost:3002/health  # User Service
curl http://localhost:8080/realms/polyrepo-test/.well-known/openid_configuration  # Keycloak
```

### Execute Tests

```bash
# Run from project root
cd /root/code/polyrepo

# Run cross-service integration tests
npx jest test/auth-user-integration.spec.ts --testTimeout=30000

# Run with detailed output
npx jest test/auth-user-integration.spec.ts --verbose --testTimeout=30000

# Run cross-service conceptual tests
npx jest test/cross-service-integration.spec.ts --testTimeout=30000
```

### Environment Configuration

Create test environment file if needed:

```bash
# Copy example environment
cp test/.env.test.integration.example test/.env.test.integration

# Configure service URLs (defaults to localhost)
echo "AUTH_SERVICE_URL=http://localhost:3001" >> test/.env.test.integration
echo "USER_SERVICE_URL=http://localhost:3002" >> test/.env.test.integration
```

## Test Scenarios Covered

### Data Consistency Validation
- ✅ User registration data consistency across Auth and User services
- ✅ Duplicate registration handling
- ✅ User profile data synchronization
- ✅ Authentication state consistency

### Authentication Flow Testing
- ✅ JWT token generation and validation
- ✅ Cross-service authentication verification
- ✅ Role-based access control consistency
- ✅ Token invalidation handling

### Error Handling and Resilience
- ✅ Service unavailability scenarios
- ✅ Partial failure rollback patterns
- ✅ Input validation consistency
- ✅ Authentication failure handling

### Business Event Validation
- ✅ Event correlation across services
- ✅ Event ordering consistency
- ✅ Business event generation during operations

### Performance and Scalability
- ✅ Concurrent operation handling
- ✅ Response time validation
- ✅ Load testing scenarios

## Test Architecture

### Service Communication Pattern

```
┌─────────────────┐    HTTP/JWT    ┌─────────────────┐
│   Auth Service  │◄──────────────►│  User Service   │
│   (Port 3001)   │                │   (Port 3002)   │
└─────────┬───────┘                └─────────┬───────┘
          │                                  │
          ▼                                  ▼
┌─────────────────┐                ┌─────────────────┐
│    Keycloak     │                │   PostgreSQL    │
│   (Port 8080)   │                │   (Port 5432)   │
└─────────────────┘                └─────────────────┘
```

### Data Flow Validation

1. **Registration Flow**:
   ```
   Client → Auth Service → Keycloak (user creation)
                      → User Service → PostgreSQL (profile storage)
   ```

2. **Authentication Flow**:
   ```
   Client → Auth Service → Keycloak (token generation)
                      → Return JWT token
   Client → User Service → JWT validation → Profile access
   ```

3. **Cross-Service Validation**:
   ```
   Test → Auth Service → Verify user created
       → User Service → Verify profile exists
       → Validate data consistency
   ```

## Key Testing Utilities

### From @libs/testing-utils

```typescript
import {
  TestEnvironment,
  TestDataGenerator,
  KeycloakTestUtils
} from '@libs/testing-utils';

// Generate unique test data
const testId = TestEnvironment.createTestId();
const userData = TestDataGenerator.createRegistrationData({
  email: `test-${testId}@example.com`
});

// Real Keycloak authentication
const token = await KeycloakTestUtils.authenticateTestUser(email, password);
```

### Test Data Management

- **Unique Identifiers**: All test data uses unique UUIDs to prevent conflicts
- **Cleanup**: Tests generate predictable data patterns for easy cleanup
- **Isolation**: Each test suite uses separate test identifiers

## Common Issues and Solutions

### 1. Service Unavailability

**Symptom**: Tests fail with connection errors

**Solution**:
```bash
# Check service health
curl http://localhost:3001/health
curl http://localhost:3002/health

# Restart services if needed
docker restart polyrepo_auth-service_volume polyrepo_user-service_volume
```

### 2. Authentication Failures

**Symptom**: JWT token validation fails

**Solution**:
```bash
# Verify Keycloak is running and configured
curl http://localhost:8080/realms/polyrepo-test/.well-known/openid_configuration

# Check test user exists
./services/auth-service/test/manual/test-user-management.sh
```

### 3. Data Consistency Issues

**Symptom**: User exists in one service but not the other

**Solution**:
- Check network connectivity between services
- Verify database connections
- Check service logs for errors

### 4. Test Timeouts

**Symptom**: Tests timeout during execution

**Solution**:
```bash
# Increase Jest timeout
npx jest test/auth-user-integration.spec.ts --testTimeout=60000

# Check service performance
docker stats
```

## Adding New Cross-Service Tests

### 1. Follow the Pattern

```typescript
describe('New Cross-Service Feature', () => {
  let testId: string;

  beforeAll(async () => {
    testId = TestEnvironment.createTestId();
    await validateServiceHealth();
  });

  it('should validate new feature across services', async () => {
    const testData = TestDataGenerator.createTestData({
      uniqueField: `test-${testId}`
    });

    // Test auth service
    const authResponse = await request(authServiceUrl)
      .post('/auth/new-endpoint')
      .send(testData)
      .expect(200);

    // Test user service
    const userResponse = await request(userServiceUrl)
      .get(`/users/by-field/${authResponse.body.fieldValue}`)
      .expect(200);

    // Validate consistency
    expect(userResponse.body.field).toBe(authResponse.body.fieldValue);
  });
});
```

### 2. Update Documentation

- Add new test scenarios to this README
- Update test coverage information
- Document any new patterns or utilities

### 3. Consider Performance

- Use appropriate timeouts for network operations
- Validate response times
- Test concurrent scenarios when applicable

## Integration with CI/CD

### GitHub Actions Configuration

```yaml
# .github/workflows/cross-service-tests.yml
name: Cross-Service Integration Tests

on: [push, pull_request]

jobs:
  cross-service-tests:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Start services
        run: |
          docker-compose up -d
          ./scripts/wait-for-services.sh
      
      - name: Run cross-service tests
        run: |
          yarn install
          yarn build:libs
          npx jest test/auth-user-integration.spec.ts --testTimeout=60000
      
      - name: Cleanup
        run: docker-compose down -v
```

## Best Practices

### 1. Test Isolation
- Use unique test identifiers for all data
- Clean up test resources after execution
- Avoid dependencies between test cases

### 2. Real Infrastructure
- Always test against actual running services
- Validate actual network communication
- Test real authentication flows

### 3. Error Scenarios
- Test service failures and recovery
- Validate error propagation
- Test timeout and retry scenarios

### 4. Performance Validation
- Test concurrent operations
- Validate response times
- Monitor resource usage

### 5. Documentation
- Document test scenarios and expectations
- Keep patterns consistent across tests
- Update documentation when adding new tests

This cross-service testing approach ensures robust validation of the microservices architecture while maintaining clear patterns and practices for future development.