# Polyrepo - Modern Microservices Architecture

A production-ready microservices application demonstrating enterprise-grade patterns with TypeScript, NestJS, and comprehensive observability.

## 🚀 **Key Features**

- **🔐 Enterprise Authentication** - Keycloak integration with JWT tokens and RBAC
- **📊 Full Observability Stack** - Prometheus metrics, Loki logging, Jaeger tracing, Grafana dashboards
- **🛡️ Resilience Patterns** - Circuit breakers, retries, timeouts, and bulkheads
- **🐳 Container-First Development** - Docker Compose for local development and deployment
- **🏗️ Microservices Architecture** - API Gateway, Auth Service, User Service with clear boundaries
- **📚 Shared Libraries** - Type-safe communication with reusable components
- **🧪 Comprehensive Testing** - 46/46 tests passing with real infrastructure validation
- **🔄 Multiple Development Modes** - Choose between local, containerized, or production-like environments

## 🏛️ **Architecture Overview**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │◄───┤   Auth Service  │◄───┤    Keycloak     │
│   (Port 3000)   │    │   (Port 3001)   │    │   (Port 8080)   │
└─────────┬───────┘    └─────────────────┘    └─────────────────┘
          │                                    
          ▼                                    
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  User Service   │◄───┤   PostgreSQL    │    │   Monitoring    │
│   (Port 3002)   │    │   (Port 5432)   │    │   (Port 3200)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ **Technology Stack**

- **Backend**: TypeScript, NestJS, Prisma ORM
- **Authentication**: Keycloak (OAuth2/OIDC)
- **Database**: PostgreSQL, Redis
- **Monitoring**: Grafana, Prometheus, Loki, Jaeger
- **Infrastructure**: Docker, Docker Compose
- **Testing**: Jest, Supertest

## 🚀 **Getting Started**

### Prerequisites

- Docker and Docker Compose
- Node.js 18+ with Yarn
- Git

### Quick Start

```bash
# 1. Clone and install dependencies
git clone <repository-url>
cd polyrepo
yarn install

# 2. Build shared libraries
yarn build:libs

# 3. Choose your development mode:
#    - Local services with Dockerized infrastructure
yarn start:dev:local

#    - All services in Docker with volume mounts (recommended for development)
yarn start:dev:docker:volumes

#    - Production-like Docker build (for final testing)
yarn start:prod:docker
```

📘 For detailed instructions, see our [Development Guide](infrastructure/local-dev/DEVELOPMENT.md).
./start-local-services.sh

# 4. Access services
# API Gateway: http://localhost:3000
# Keycloak Admin: http://localhost:8080 (admin/admin)
# Grafana Dashboard: http://localhost:3200 (admin/admin)
```

## 📖 **Documentation**

**For comprehensive documentation, setup guides, and development workflows:**

### 👉 **[Complete Documentation →](./docs/README.md)**

**Quick Links:**
- **[🏁 Getting Started](./INTRODUCTION.md)** - Comprehensive project introduction
- **[⚙️ Local Development Setup](./infrastructure/local-dev/LOCAL_DEVELOPMENT_SETUP.md)** - Step-by-step setup guide
- **[📊 Observability Integration](./docs/observability-integration.md)** - Monitoring stack guide
- **[🔧 Troubleshooting](./docs/troubleshooting/README.md)** - Common issues and solutions

## 🤝 **Contributing**

1. Follow the [Development Guidelines](./docs/development-roadmap.md)
2. Check [Testing Standards](./docs/testing-standards.md)
3. Review [Documentation Guidelines](./docs/documentation-guidelines.md)

## 📄 **License**

[Your License Here]

---

**Built with enterprise-grade patterns for scalable, maintainable microservices architecture.**
