NODE_ENV=development
PORT=3000 # Internal port for the container

# Database Connection (for Dockerized service connecting to Dockerized DB)
# Uses the service name 'postgres_user_service' and its internal port 5432
DATABASE_URL="*************************************************************************************/polyrepo_user_db?schema=public"

# Redis Configuration (for Docker environment)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://loki:3100
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://host.docker.internal:14268/api/traces
