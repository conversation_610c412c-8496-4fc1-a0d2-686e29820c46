{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 2, "numPassedTests": 2, "numPendingTestSuites": 1, "numPendingTests": 54, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 3, "numTotalTests": 56, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1749375155873, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["AppController", "root"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "AppController root should return service info", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return service info"}, {"ancestorTitles": ["AppController", "root"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "AppController root should call appService.getInfo", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should call appService.getInfo"}], "coverage": {}, "endTime": 1749375167958, "message": "", "name": "/root/code/polyrepo/services/user-service/src/app.controller.spec.ts", "startTime": 1749375167958, "status": "skipped", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["UsersService"], "duration": 33, "failureDetails": [], "failureMessages": [], "fullName": "UsersService should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["UsersService", "createUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService createUser should create a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create a user successfully"}, {"ancestorTitles": ["UsersService", "createUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService createUser should handle unique constraint violation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle unique constraint violation"}, {"ancestorTitles": ["UsersService", "createUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService createUser should handle unknown database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle unknown database errors"}, {"ancestorTitles": ["UsersService", "findAll"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findAll should return all active users by default", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return all active users by default"}, {"ancestorTitles": ["UsersService", "findAll"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findAll should include deleted users when requested", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should include deleted users when requested"}, {"ancestorTitles": ["UsersService", "findAll"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findAll should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findOne should return a user by id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return a user by id"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findOne should return null for non-existent user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return null for non-existent user"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findOne should include deleted users when requested", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should include deleted users when requested"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findOne should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "findByKeycloakId"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findByKeycloakId should return a user by keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return a user by keycloak id"}, {"ancestorTitles": ["UsersService", "findByKeycloakId"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findByKeycloakId should return null for non-existent keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return null for non-existent keycloak id"}, {"ancestorTitles": ["UsersService", "findByKeycloakId"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService findByKeycloakId should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService updateUser should update a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should update a user successfully"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService updateUser should update with optimistic locking", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should update with optimistic locking"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService updateUser should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not found"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService updateUser should handle version conflicts", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle version conflicts"}, {"ancestorTitles": ["UsersService", "remove (soft delete)"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService remove (soft delete) should soft delete a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should soft delete a user successfully"}, {"ancestorTitles": ["UsersService", "remove (soft delete)"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService remove (soft delete) should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not found"}, {"ancestorTitles": ["UsersService", "remove (soft delete)"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService remove (soft delete) should handle already deleted user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle already deleted user"}, {"ancestorTitles": ["UsersService", "restoreUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService restoreUser should restore a soft deleted user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should restore a soft deleted user"}, {"ancestorTitles": ["UsersService", "restoreUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService restoreUser should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not found"}, {"ancestorTitles": ["UsersService", "restoreUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService restoreUser should handle user not deleted", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not deleted"}, {"ancestorTitles": ["UsersService", "createInternalUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService createInternalUser should create an internal user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create an internal user"}, {"ancestorTitles": ["UsersService", "getUserStats"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService getUserStats should return user statistics", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return user statistics"}, {"ancestorTitles": ["UsersService", "getUserStats"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService getUserStats should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "bulkSoftDelete"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService bulkSoftDelete should bulk soft delete users", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should bulk soft delete users"}, {"ancestorTitles": ["UsersService", "bulkSoftDelete"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService bulkSoftDelete should handle empty user ids array", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty user ids array"}, {"ancestorTitles": ["UsersService", "bulkSoftDelete"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService bulkSoftDelete should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "hardDeleteUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService hardDeleteUser should permanently delete a user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should permanently delete a user"}, {"ancestorTitles": ["UsersService", "hardDeleteUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService hardDeleteUser should delete with version check", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should delete with version check"}, {"ancestorTitles": ["UsersService", "hardDeleteUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersService hardDeleteUser should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not found"}], "endTime": 1749375167370, "message": "", "name": "/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts", "startTime": 1749375156958, "status": "focused", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["UsersController"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "UsersController should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["UsersController", "createUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController createUser should create a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create a user successfully"}, {"ancestorTitles": ["UsersController", "createUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController createUser should handle service errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle service errors"}, {"ancestorTitles": ["UsersController", "findAll"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findAll should return all users", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return all users"}, {"ancestorTitles": ["UsersController", "findAll"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findAll should return users including deleted when requested", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return users including deleted when requested"}, {"ancestorTitles": ["UsersController", "findOne"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findOne should return a user by id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return a user by id"}, {"ancestorTitles": ["UsersController", "findOne"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findOne should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not found"}, {"ancestorTitles": ["UsersController", "findByKeycloakId"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findByKeycloakId should return a user by keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return a user by keycloak id"}, {"ancestorTitles": ["UsersController", "findByKeycloakId"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findByKeycloakId should handle user not found by keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle user not found by keycloak id"}, {"ancestorTitles": ["UsersController", "updateUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController updateUser should update a user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should update a user"}, {"ancestorTitles": ["UsersController", "updateUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController updateUser should handle update errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle update errors"}, {"ancestorTitles": ["UsersController", "softDeleteUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController softDeleteUser should soft delete a user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should soft delete a user"}, {"ancestorTitles": ["UsersController", "softDeleteUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController softDeleteUser should handle soft delete errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle soft delete errors"}, {"ancestorTitles": ["UsersController", "createInternalUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController createInternalUser should create an internal user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should create an internal user"}, {"ancestorTitles": ["UsersController", "getUserStats"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController getUserStats should return user statistics", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should return user statistics"}, {"ancestorTitles": ["UsersController", "restoreUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController restoreUser should restore a soft deleted user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should restore a soft deleted user"}, {"ancestorTitles": ["UsersController", "restoreUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController restoreUser should handle restore errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle restore errors"}, {"ancestorTitles": ["UsersController", "hardDeleteUser"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController hardDeleteUser should permanently delete a user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should permanently delete a user"}, {"ancestorTitles": ["UsersController", "updateUserOptimistic"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController updateUserOptimistic should update user with optimistic locking", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should update user with optimistic locking"}, {"ancestorTitles": ["UsersController", "bulkSoftDelete"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController bulkSoftDelete should bulk soft delete users", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should bulk soft delete users"}, {"ancestorTitles": ["UsersController", "bulkSoftDelete"], "duration": null, "failureDetails": [], "failureMessages": [], "fullName": "UsersController bulkSoftDelete should handle empty user ids", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "pending", "title": "should handle empty user ids"}], "endTime": 1749375167827, "message": "", "name": "/root/code/polyrepo/services/user-service/src/users/users.controller.spec.ts", "startTime": 1749375156928, "status": "focused", "summary": ""}], "wasInterrupted": false}