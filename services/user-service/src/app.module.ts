﻿import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { PrismaModule } from './prisma/prisma.module';
import { ObservabilityModule } from './observability/observability.module';
import { HttpModule } from '@libs/http';
import { MetricsModule } from './metrics/metrics.module';
import { HealthModule } from './health/health.module';
import { EventsModule } from './events/events.module';
import { CacheModule } from '@libs/caching';
import { MessagingModule } from '@libs/messaging';
import { AuthCommonModule } from '@libs/auth-common';
import { ErrorHandlingModule } from '@libs/error-handling';

@Module({
  imports: [
    // Load .env files and make environment variables available via ConfigService
    ConfigModule.forRoot({
      isGlobal: true, // Make ConfigModule available globally
      envFilePath: (() => {
        const env = process.env.NODE_ENV;
        if (env === 'test') {
          return 'test/.env.test';
        } else if (env === 'docker') {
          return '.env.docker';
        } else {
          // Default to .env.local for development or if NODE_ENV is not explicitly set to test/docker
          return '.env.local';
        }
      })(),
    }),
    ObservabilityModule, // Use our custom observability module
    CacheModule.registerAsync({
      useFactory: () => ({
        // Use process.env directly as ConfigService may not work properly with webpack bundled apps
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        db: parseInt(process.env.REDIS_DB || '0', 10),
        defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || '300', 10),
        keyPrefix: 'user-service:',
        connectTimeout: 5000,
        commandTimeout: 5000,
      }),
      inject: [], // No dependencies needed since we use process.env directly
    }),
    MessagingModule.forRoot({
      isGlobal: true,
      redis: {
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379'),
          db: parseInt(process.env.REDIS_DB || '0'),
          maxRetriesPerRequest: 3,
          retryDelayOnFailover: 100,
          lazyConnect: true,
        },
        defaultStream: 'events',
        retention: {
          maxLength: 10000,
          approximateMaxLength: true,
        },
      },
    }),
    AuthCommonModule.forServices(), // Only need guards, not JWT validation
    // Add comprehensive error handling for service
    ErrorHandlingModule.forRoot({
      config: {
        serviceName: 'user-service',
        environment: process.env.NODE_ENV as 'development' | 'production' || 'development',
        lokiBaseUrl: process.env.LOKI_BASE_URL || 'http://localhost:3100',
        includeLokiLinks: true,
        includeSensitiveDetails: process.env.NODE_ENV !== 'production',
      },
      useGlobalFilter: true,
      useHttpInterceptor: false, // Services don't need HTTP interceptor (API Gateway handles it)
    }),
    HttpModule.forRoot(), // Enterprise defaults: 3s timeout, 1 retry, HTTP/2
    MetricsModule, // Add metrics module
    HealthModule, // Add health module
    EventsModule, // Add events module for cross-service event handling
    PrismaModule,
    UsersModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

