import { Injectable } from '@nestjs/common';
import { BusinessLogger } from '@libs/observability';

/**
 * Service wrapper for business logging in the user service
 * Provides methods for logging user-specific business events
 *
 * @see docs/business-events.md for the complete business events standard
 */
@Injectable()
export class UserBusinessLogger {
  constructor(private readonly businessLogger: BusinessLogger) {}

  /**
   * Logs a user creation event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user being created
   * @param metadata Additional context for the event (email, keycloakId, etc.)
   */
  logUserCreationEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logUserActivity(userId, 'create', {
      status,
      ...metadata,
    });
  }

  /**
   * Logs a user update event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user being updated
   * @param metadata Additional context for the event (fields, etc.)
   */
  logUserUpdateEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logUserActivity(userId, 'update', {
      status,
      ...metadata,
    });
  }

  /**
   * Logs a user deletion event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user being deleted
   * @param metadata Additional context for the event
   */
  logUserDeletionEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logUserActivity(userId, 'delete', {
      status,
      ...metadata,
    });
  }

  /**
   * Logs a user status change event (e.g., account activation, deactivation).
   * @param status 'success' | 'failure' indicating the outcome of the status change
   * @param userId ID of the user whose status changed
   * @param metadata Additional context (e.g., oldStatus, newStatus, reason)
   */
  logUserStatusChangeEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logUserActivity(userId, 'status_change', {
      status,
      ...metadata,
    });
  }

  /**
   * Logs a user profile view event.
   * @param status 'success' | 'failure'
   * @param userId ID of the user whose profile is being viewed
   * @param metadata Additional context for the event (viewerId, etc.)
   */
  logUserProfileViewEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logUserActivity(userId, 'profile_view', {
      status,
      ...metadata,
    });
  }
}
