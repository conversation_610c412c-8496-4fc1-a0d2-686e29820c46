import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ObservabilityModule as BaseObservabilityModule } from '@libs/observability';
import { UserBusinessLogger } from './business-logger.service';
import { ObservabilityTestController } from './test.controller';

/**
 * This module integrates our user service with the observability infrastructure.
 * It configures logging, metrics, and tracing for the user service and provides
 * user-specific business logging capabilities.
 */
@Module({
  imports: [
    ConfigModule, // Ensure ConfigModule is imported
    BaseObservabilityModule.forRootAsync({
      useFactory: () => ({
        logging: {
          service: 'user-service',
          defaultContext: 'UserService',
          enableLoki: process.env.ENABLE_LOKI === 'true',
          lokiHost: process.env.LOKI_HOST || 'http://loki:3100',
          logLevel: process.env.LOG_LEVEL || 'info',
        },
        metrics: {
          prefix: 'user',
          defaultLabels: {
            service: 'user-service',
            environment: process.env.NODE_ENV || 'development',
          },
        },
        tracing: {
          serviceName: 'user-service',
          environment: process.env.NODE_ENV || 'development',
          tempoEndpoint: process.env.TEMPO_ENDPOINT || 'http://localhost:4318/v1/traces',
        },
      }),
      inject: [],
    }),
  ],
  controllers: process.env.NODE_ENV === 'production' ? [] : [ObservabilityTestController],
  providers: [UserBusinessLogger],
  exports: [UserBusinessLogger, BaseObservabilityModule],
})
export class ObservabilityModule {}
