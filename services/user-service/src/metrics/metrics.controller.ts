import { <PERSON>, Get, Header, Inject } from '@nestjs/common';
import { MetricsService, ObservabilityLogger } from '@libs/observability';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

/**
 * Controller for exposing Prometheus metrics
 */
@ApiTags('metrics')
@Controller('metrics')
export class MetricsController {
  constructor(
    private readonly metricsService: MetricsService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory.createLogger('MetricsController');
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Expose Prometheus metrics endpoint
   * @returns Prometheus metrics in text format
   */
  @Get()
  @Header('Content-Type', 'text/plain')
  @ApiOperation({ summary: 'Get metrics', description: 'Returns Prometheus metrics in text format' })
  @ApiResponse({ status: 200, description: 'Metrics returned successfully' })
  async getMetrics(): Promise<string> {
    this.logger.log('Metrics endpoint accessed');
    return this.metricsService.getMetrics();
  }
}
