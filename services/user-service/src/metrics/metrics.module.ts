import { Module } from '@nestjs/common';
import { MetricsController } from './metrics.controller';
import { MetricsModule as LibMetricsModule } from '@libs/observability';
import { ObservabilityModule } from '../observability/observability.module';

/**
 * Module for exposing Prometheus metrics
 */
@Module({
  imports: [
    LibMetricsModule.forRoot(),
    ObservabilityModule,
  ],
  controllers: [MetricsController],
})
export class MetricsModule {}
