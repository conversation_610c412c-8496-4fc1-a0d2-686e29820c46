
import { Injectable, OnModuleInit, Inject } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { softDeleteMiddleware } from '@libs/prisma-utils';
import { ObservabilityLogger } from '@libs/observability';
// import { CircuitBreakerService, Circuit } from '@libs/prisma-resilience';

/**
 * Service for interacting with the database using Prisma ORM
 */
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  // private dbConnectionCircuit: Circuit;

  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    // private readonly circuitBreakerService: CircuitBreakerService,
  ) {
    super();
    // Get a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger('PrismaService');

        // this.dbConnectionCircuit = this.circuitBreakerService.getCircuitBreaker('PrismaConnect', {
    //   timeout: 15000, // Timeout for connection attempt (ms)
    //   errorThresholdPercentage: 50,
    //   resetTimeout: 30000, // Time to wait before retrying in half-open state (ms)
    // });

    // Register Prisma middleware
    this.$use(softDeleteMiddleware());
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Connect to the database when the module initializes
   */
  async onModuleInit() {
    this.logger.log('Attempting to connect to database...');
    try {
      // await this.dbConnectionCircuit.execute(async () => {
      //   this.logger.log('Circuit closed or half-open: attempting $connect().');
      await this.$connect();
      this.logger.log('Database connection established successfully.');
      // });
    } catch (error: any) {
      this.logger.error(
        `Failed to connect to database: ${error.message}`,
        error.stack,
        'PrismaServiceInitError',
      );
      // Depending on the desired behavior, you might rethrow or handle this to prevent app startup
      // For now, logging the error. The app might start but DB operations will fail until connection is up.
      throw error;
    }
  }

  /**
   * Check database health by executing a simple query
   * @returns Object with database status and response time
   */
  async checkHealth(): Promise<{ status: string; responseTime: number; details?: any }> {
    try {
      const start = Date.now();

      // Execute a simple query to check database connectivity
      await this.$queryRaw`SELECT 1`;

      const end = Date.now();
      const responseTime = end - start;

      this.logger.log(`Database health check successful (${responseTime}ms)`);

      return {
        status: 'ok',
        responseTime,
      };
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      const errorStack = error?.stack || '';
      this.logger.error(`Database health check failed: ${errorMessage}`, errorStack);

      return {
        status: 'error',
        responseTime: 0,
        details: {
          message: errorMessage,
          code: error?.code || 'UNKNOWN',
        },
      };
    }
  }
}
