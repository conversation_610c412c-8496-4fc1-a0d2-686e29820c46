import 'reflect-metadata'; // Ensure metadata reflection is enabled
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { LoggerService, ValidationPipe } from '@nestjs/common';
// HttpExceptionFilter is now handled by ErrorHandlingModule
import { ConfigService } from '@nestjs/config';
import { LOGGER_SERVICE } from '@libs/observability';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get the logger service from the DI container
  const appLogger = app.get<LoggerService>(LOGGER_SERVICE);
  app.useLogger(appLogger);

  // Apply global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Global exception filter is now handled by ErrorHandlingModule

  // Set up Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('User Service API')
    .setDescription('User Management API for the Polyrepo Application')
    .setVersion('1.0')
    .addTag('users')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // Expose JSON endpoint for API Gateway aggregation
  app.use('/api/docs-json', (req: any, res: any) => {
    res.json(document);
  });

  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT') || 3002;
  const host = '0.0.0.0';
  await app.listen(port, host);

  appLogger.log(`UserService is running on port ${port}, connect at http://${host}:${port} or http://localhost:${port}`, 'Bootstrap');
}
bootstrap();
