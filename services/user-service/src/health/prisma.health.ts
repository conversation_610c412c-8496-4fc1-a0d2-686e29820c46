import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class PrismaHealthIndicator extends HealthIndicator {
  constructor(private readonly prismaService: PrismaService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const dbHealth = await this.prismaService.checkHealth();
      if (dbHealth.status === 'ok') {
        return this.getStatus(key, true, { responseTime: dbHealth.responseTime });
      }
      // Use the details from prismaService.checkHealth if available
      throw new HealthCheckError(
        'Prisma health check failed',
        this.getStatus(key, false, dbHealth.details || { message: 'Prisma checkHealth returned error' })
      );
    } catch (error) {
      // If error is already a HealthCheckError, rethrow it, otherwise wrap it
      if (error && error.constructor?.name === 'HealthCheckError') {
        throw error;
      }
      const errorMessage = error && error.message ? error.message : 'Unknown error during Prisma health check';
      throw new HealthCheckError(
        errorMessage,
        this.getStatus(key, false, { message: errorMessage })
      );
    }
  }
}
