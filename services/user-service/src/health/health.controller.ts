import { Controller, Get, Inject } from '@nestjs/common';
import { HealthCheck, HealthCheckService, HealthIndicatorResult, HealthCheckResult, HealthCheckError } from '@nestjs/terminus';
import { PrismaHealthIndicator } from './prisma.health';
import { ObservabilityLogger, MetricsService } from '@libs/observability';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CacheService } from '@libs/caching';
import { RedisStreamsPublisher } from '@libs/messaging';
import { HealthCheckResponse, DatabaseHealth } from '@libs/shared-types';


/**
 * Controller for health checks and service status
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly metricsService: MetricsService,
    private readonly healthCheckService: HealthCheckService,
    private readonly prismaHealth: PrismaHealthIndicator,
    private readonly cacheService: CacheService,
    private readonly messagingPublisher: RedisStreamsPublisher,
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory.createLogger('HealthController');
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Basic health check endpoint
   * @returns Health status
   */
  @Get()
  @ApiOperation({ summary: 'Health check', description: 'Basic health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  healthCheck() {
    this.logger.log('Health check endpoint accessed');
    return {
      status: 'ok',
      service: 'user-service',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Detailed health check including observability status
   * @returns Detailed health status
   */
  @Get('detailed')
  @ApiOperation({ summary: 'Detailed health check', description: 'Detailed health check including observability status' })
  @ApiResponse({ status: 200, description: 'Detailed health status' })
  async detailedHealthCheck(): Promise<HealthCheckResponse> {
    this.logger.log('Detailed health check endpoint accessed');

    // Get metrics to check if metrics service is working
    const metrics = await this.metricsService.getMetrics();
    const metricsStatus = metrics ? 'ok' : 'error';

    // Check if logger is working
    const loggerStatus = this.logger ? 'ok' : 'error';

    // Check database health using Terminus
    let dbHealth: DatabaseHealth;
    try {
      const dbHealthResult = await this.healthCheckService.check([
        () => this.prismaHealth.isHealthy('prisma'),
      ]);
      const prismaDetails = dbHealthResult.details?.prisma;
      dbHealth = {
        status: dbHealthResult.status === 'ok' ? 'ok' : 'error',
        responseTime: prismaDetails?.responseTime || 0,
        details: prismaDetails,
      };
    } catch (e) {
      const errorMessage = e && e.message ? e.message : 'Unknown database error';
      dbHealth = {
        status: 'error',
        responseTime: 0,
        error: errorMessage,
      };
    }

    // Check Redis infrastructure health
    const cacheHealth = await this.cacheService.getHealthStatus();
    const messagingHealth = await this.messagingPublisher.getHealthStatus();

    // Determine overall status
    const overallStatus =
      loggerStatus === 'ok' &&
      metricsStatus === 'ok' &&
      dbHealth.status === 'ok' &&
      cacheHealth.status === 'ok' &&
      messagingHealth.status === 'ok' ? 'ok' : 'degraded';

    return {
      status: overallStatus,
      service: 'user-service',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      observability: {
        logging: loggerStatus,
        metrics: metricsStatus,
      },
      infrastructure: {
        database: dbHealth,
        cache: cacheHealth,
        messaging: messagingHealth,
      },
    };
  }

  /**
   * Database health check endpoint
   * @returns Database health status
   */
  @Get('db')
  @HealthCheck()
  @ApiOperation({ summary: 'Database health check', description: 'Check database connectivity using Terminus' })
  @ApiResponse({ status: 200, description: 'Database health status' })
  @ApiResponse({ status: 503, description: 'Database is unhealthy' })
  async dbHealthCheck() {
    this.logger.log('Database health check endpoint accessed via Terminus');
    return this.healthCheckService.check([
      () => this.prismaHealth.isHealthy('prisma'),
    ]);
  }

  /**
   * Cache health check endpoint
   * @returns Redis cache health status
   */
  @Get('cache')
  @ApiOperation({ summary: 'Cache health check', description: 'Check Redis cache connectivity and performance' })
  @ApiResponse({ status: 200, description: 'Cache health status' })
  async cacheHealthCheck() {
    this.logger.log('Cache health check endpoint accessed');

    const cacheHealth = await this.cacheService.getHealthStatus();

    return {
      status: cacheHealth.status,
      service: 'cache',
      timestamp: new Date().toISOString(),
      responseTime: cacheHealth.responseTime,
      infrastructure: {
        cache: cacheHealth,
      },
    };
  }

  /**
   * Messaging health check endpoint
   * @returns Redis messaging health status
   */
  @Get('messaging')
  @ApiOperation({ summary: 'Messaging health check', description: 'Check Redis messaging connectivity and performance' })
  @ApiResponse({ status: 200, description: 'Messaging health status' })
  async messagingHealthCheck() {
    this.logger.log('Messaging health check endpoint accessed');

    const messagingHealth = await this.messagingPublisher.getHealthStatus();

    return {
      status: messagingHealth.status,
      service: 'messaging',
      timestamp: new Date().toISOString(),
      responseTime: messagingHealth.responseTime,
      infrastructure: {
        messaging: messagingHealth,
      },
    };
  }

  /**
   * Infrastructure health check endpoint
   * @returns Complete infrastructure status
   */
  @Get('infrastructure')
  @ApiOperation({ summary: 'Infrastructure health check', description: 'Check all infrastructure components' })
  @ApiResponse({ status: 200, description: 'Infrastructure health status' })
  async infrastructureHealthCheck() {
    this.logger.log('Infrastructure health check endpoint accessed');

    // Check database health
    let dbHealth: DatabaseHealth;
    try {
      const dbHealthResult = await this.healthCheckService.check([
        () => this.prismaHealth.isHealthy('prisma'),
      ]);
      const prismaDetails = dbHealthResult.details?.prisma;
      dbHealth = {
        status: dbHealthResult.status === 'ok' ? 'ok' : 'error',
        responseTime: prismaDetails?.responseTime || 0,
        details: prismaDetails,
      };
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'Unknown database error';
      dbHealth = {
        status: 'error',
        responseTime: 0,
        error: errorMessage,
      };
    }

    const cacheHealth = await this.cacheService.getHealthStatus();
    const messagingHealth = await this.messagingPublisher.getHealthStatus();

    const overallStatus = 
      dbHealth.status === 'ok' &&
      cacheHealth.status === 'ok' && 
      messagingHealth.status === 'ok' ? 'ok' : 'degraded';

    return {
      status: overallStatus,
      service: 'infrastructure',
      timestamp: new Date().toISOString(),
      infrastructure: {
        database: dbHealth,
        cache: cacheHealth,
        messaging: messagingHealth,
      },
    };
  }
}
