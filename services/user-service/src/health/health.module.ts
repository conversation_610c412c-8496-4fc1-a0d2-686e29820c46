import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HealthController } from './health.controller';
import { ObservabilityModule } from '../observability/observability.module';
import { MetricsModule as LibMetricsModule } from '@libs/observability';
import { PrismaModule } from '../prisma/prisma.module';
import { PrismaHealthIndicator } from './prisma.health';

/**
 * Module for health checks and service status
 */
@Module({
  imports: [
    ObservabilityModule,
    LibMetricsModule.forRoot(),
    PrismaModule,
    TerminusModule,
  ],
  controllers: [HealthController],
  providers: [PrismaHealthIndicator],
})
export class HealthModule {}
