import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { PrismaService } from '../prisma/prisma.service';
import { ConflictException, InternalServerErrorException, NotFoundException, BadRequestException } from '@nestjs/common';
import { User } from '@prisma/client';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserInternalDto } from '@libs/shared-types';
import { CacheService } from '@libs/caching';
import { MockFactory, TestDataGenerator } from '@libs/testing-utils';

// Mock Prisma.PrismaClientKnownRequestError for instanceof checks
jest.mock('../prisma', () => {
  const actualPrisma = jest.requireActual('../prisma');
  return {
    ...actualPrisma,
    Prisma: {
      ...actualPrisma.Prisma,
      PrismaClientKnownRequestError: class extends Error {
        public code: string;
        public meta?: any;
        public clientVersion?: string;
        
        constructor(message: string, options: { code: string; meta?: any; clientVersion?: string }) {
          super(message);
          this.name = 'PrismaClientKnownRequestError';
          this.code = options.code;
          this.meta = options.meta;
          this.clientVersion = options.clientVersion;
        }
      }
    }
  };
});

import { Prisma } from '../prisma';

describe('UsersService', () => {
  let service: UsersService;
  let prismaService: PrismaService;

  // Mock user data with all required fields
  const mockUser: User = {
    id: '1',
    keycloakId: 'test-keycloak-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    deletedAt: null,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockPrismaService = {
    user: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: 'LOGGER_FACTORY',
          useValue: MockFactory.createLoggerFactory(),
        },
        {
          provide: CacheService,
          useValue: MockFactory.createCacheService(),
        },
        {
          provide: 'EVENT_PUBLISHER',
          useValue: MockFactory.createEventPublisher(),
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    prismaService = module.get<PrismaService>(PrismaService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a user successfully', async () => {
      const createUserDto: CreateUserDto = TestDataGenerator.createTestUser();
      
      mockPrismaService.user.create.mockResolvedValue(mockUser);

      const result = await service.createUser(createUserDto);

      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: createUserDto.email,
          firstName: createUserDto.firstName,
          lastName: createUserDto.lastName,
          keycloakId: createUserDto.keycloakId,
        }),
      });
      expect(result).toEqual(mockUser);
    });

    it('should handle unique constraint violation', async () => {
      const createUserDto: CreateUserDto = TestDataGenerator.createTestUser();
      
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Unique constraint failed',
        { code: 'P2002', meta: { target: ['email'] }, clientVersion: '4.0.0' }
      );
      
      mockPrismaService.user.create.mockRejectedValue(prismaError);

      await expect(service.createUser(createUserDto)).rejects.toThrow(ConflictException);
    });

    it('should handle unknown database errors', async () => {
      const createUserDto: CreateUserDto = TestDataGenerator.createTestUser();
      
      mockPrismaService.user.create.mockRejectedValue(new Error('Database connection failed'));

      await expect(service.createUser(createUserDto)).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('findAll', () => {
    it('should return all active users by default', async () => {
      const users = [mockUser];
      mockPrismaService.user.findMany.mockResolvedValue(users);

      const result = await service.findAll();

      expect(mockPrismaService.user.findMany).toHaveBeenCalledWith({
        includeDeleted: false,
        orderBy: { createdAt: 'desc' },
      });
      expect(result).toEqual(users);
    });

    it('should include deleted users when requested', async () => {
      const users = [mockUser, { ...mockUser, id: 2, deletedAt: new Date() }];
      mockPrismaService.user.findMany.mockResolvedValue(users);

      const result = await service.findAll(true);

      expect(mockPrismaService.user.findMany).toHaveBeenCalledWith({
        includeDeleted: true,
        orderBy: { createdAt: 'desc' },
      });
      expect(result).toEqual(users);
    });

    it('should handle database errors', async () => {
      mockPrismaService.user.findMany.mockRejectedValue(new Error('Database error'));

      await expect(service.findAll()).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('findOne', () => {
    it('should return a user by id', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);

      const result = await service.findOne('1');

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        includeDeleted: false,
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null for non-existent user', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      const result = await service.findOne('999');

      expect(result).toBeNull();
    });

    it('should include deleted users when requested', async () => {
      const deletedUser = { ...mockUser, deletedAt: new Date() };
      mockPrismaService.user.findUnique.mockResolvedValue(deletedUser);

      const result = await service.findOne('1', true);

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        includeDeleted: true,
      });
      expect(result).toEqual(deletedUser);
    });

    it('should handle database errors', async () => {
      mockPrismaService.user.findUnique.mockRejectedValue(new Error('Database error'));

      await expect(service.findOne('1')).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('findByKeycloakId', () => {
    it('should return a user by keycloak id', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);

      const result = await service.findByKeycloakId('test-keycloak-id');

      expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
        where: { keycloakId: 'test-keycloak-id' },
        includeDeleted: false,
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null for non-existent keycloak id', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      const result = await service.findByKeycloakId('invalid-id');

      expect(result).toBeNull();
    });

    it('should handle database errors', async () => {
      mockPrismaService.user.findUnique.mockRejectedValue(new Error('Database error'));

      await expect(service.findByKeycloakId('test-id')).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('updateUser', () => {
    it('should update a user successfully', async () => {
      const updateUserDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };
      
      const updatedUser = { ...mockUser, ...updateUserDto, version: 2 };
      
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUser('1', updateUserDto);

      expect(mockPrismaService.user.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: expect.objectContaining({
          ...updateUserDto,
          version: { increment: 1 },
        }),
      });
      expect(result).toEqual(updatedUser);
    });

    it('should update with optimistic locking', async () => {
      const updateUserDto: UpdateUserDto = { firstName: 'Updated' };
      const expectedVersion = 1;
      
      const updatedUser = { ...mockUser, ...updateUserDto, version: 2 };
      
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUser('1', updateUserDto, expectedVersion);

      expect(mockPrismaService.user.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: expect.objectContaining({
          ...updateUserDto,
          version: { increment: 1 },
        }),
      });
      expect(result).toEqual(updatedUser);
    });

    it('should handle user not found', async () => {
      const updateUserDto: UpdateUserDto = { firstName: 'Updated' };
      
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(service.updateUser('999', updateUserDto)).rejects.toThrow(NotFoundException);
    });

    it('should handle version conflicts', async () => {
      const updateUserDto: UpdateUserDto = { firstName: 'Updated' };
      
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      
      const prismaError = new Prisma.PrismaClientKnownRequestError(
        'Record not found',
        { code: 'P2025', meta: {}, clientVersion: '4.0.0' }
      );
      
      mockPrismaService.user.update.mockRejectedValue(prismaError);

      await expect(service.updateUser('1', updateUserDto, 999)).rejects.toThrow(ConflictException);
    });
  });

  describe('remove (soft delete)', () => {
    it('should soft delete a user successfully', async () => {
      const deletedUser = { ...mockUser, deletedAt: new Date(), version: 2 };
      
      mockPrismaService.user.delete.mockResolvedValue(deletedUser);

      const result = await service.remove('1');

      expect(mockPrismaService.user.delete).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(result).toEqual(deletedUser);
    });

    it('should handle user not found', async () => {
      const notFoundError = new Prisma.PrismaClientKnownRequestError(
        'Record not found',
        { code: 'P2025', meta: {}, clientVersion: '4.0.0' }
      );
      mockPrismaService.user.delete.mockRejectedValue(notFoundError);

      await expect(service.remove('999')).rejects.toThrow(InternalServerErrorException);
    });

    it('should handle already deleted user', async () => {
      const alreadyDeletedError = new Error('User already deleted');
      mockPrismaService.user.delete.mockRejectedValue(alreadyDeletedError);

      await expect(service.remove('1')).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('restoreUser', () => {
    it('should restore a soft deleted user', async () => {
      const deletedUser = { ...mockUser, deletedAt: new Date() };
      const restoredUser = { ...mockUser, deletedAt: null, version: 2 };
      
      mockPrismaService.user.findUnique.mockResolvedValue(deletedUser);
      mockPrismaService.user.update.mockResolvedValue(restoredUser);

      const result = await service.restoreUser('1');

      expect(mockPrismaService.user.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: {
          deletedAt: null,
          version: { increment: 1 },
        },
        includeDeleted: true,
      });
      expect(result).toEqual(restoredUser);
    });

    it('should handle user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(service.restoreUser('999')).rejects.toThrow(NotFoundException);
    });

    it('should handle user not deleted', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);

      await expect(service.restoreUser('1')).rejects.toThrow(BadRequestException);
    });
  });

  describe('createInternalUser', () => {
    it('should create an internal user', async () => {
      const internalUserDto: CreateUserInternalDto = {
        email: '<EMAIL>',
        firstName: 'Internal',
        lastName: 'User',
        keycloakId: 'internal-keycloak-id',
      };
      
      mockPrismaService.user.create.mockResolvedValue(mockUser);

      const result = await service.createInternalUser(internalUserDto);

      expect(mockPrismaService.user.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          email: internalUserDto.email,
          firstName: internalUserDto.firstName,
          lastName: internalUserDto.lastName,
          keycloakId: internalUserDto.keycloakId,
        }),
      });
      expect(result).toEqual(mockUser);
    });
  });

  describe('getUserStats', () => {
    it('should return user statistics', async () => {
      mockPrismaService.user.count
        .mockResolvedValueOnce(10) // total
        .mockResolvedValueOnce(8)  // active
        .mockResolvedValueOnce(2); // deleted

      const result = await service.getUserStats();

      expect(result).toEqual({
        total: 10,
        active: 8,
        deleted: 2,
      });

      expect(mockPrismaService.user.count).toHaveBeenCalledTimes(3);
      expect(mockPrismaService.user.count).toHaveBeenNthCalledWith(1);
      expect(mockPrismaService.user.count).toHaveBeenNthCalledWith(2, {
        where: { deletedAt: null },
      });
      expect(mockPrismaService.user.count).toHaveBeenNthCalledWith(3, {
        where: { deletedAt: { not: null } },
      });
    });

    it('should handle database errors', async () => {
      mockPrismaService.user.count.mockRejectedValue(new Error('Database error'));

      await expect(service.getUserStats()).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('bulkSoftDelete', () => {
    it('should bulk soft delete users', async () => {
      const userIds = ['1', '2', '3'];
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return await callback({
          user: {
            updateMany: jest.fn().mockResolvedValue({ count: 3 })
          }
        });
      });

      const result = await service.bulkSoftDelete(userIds);

      expect(mockPrismaService.$transaction).toHaveBeenCalled();
      expect(result).toBe(3);
    });

    it('should handle empty user ids array', async () => {
      const userIds: string[] = [];
      
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return await callback({
          user: {
            updateMany: jest.fn().mockResolvedValue({ count: 0 })
          }
        });
      });

      const result = await service.bulkSoftDelete(userIds);

      expect(result).toBe(0);
      expect(mockPrismaService.$transaction).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      const userIds = ['1', '2', '3'];
      
      mockPrismaService.$transaction.mockRejectedValue(new Error('Transaction failed'));

      await expect(service.bulkSoftDelete(userIds)).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('hardDeleteUser', () => {
    it('should permanently delete a user', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.user.delete.mockResolvedValue(mockUser);

      await service.hardDeleteUser('1');

      expect(mockPrismaService.user.delete).toHaveBeenCalledWith({
        where: { id: '1' },
        _unsafeBypassSoftDelete: true,
      });
    });

    it('should delete with version check', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.user.delete.mockResolvedValue(mockUser);

      await service.hardDeleteUser('1', 1);

      expect(mockPrismaService.user.delete).toHaveBeenCalledWith({
        where: { id: '1' },
        _unsafeBypassSoftDelete: true,
      });
    });

    it('should handle user not found', async () => {
      mockPrismaService.user.findUnique.mockResolvedValue(null);

      await expect(service.hardDeleteUser('999')).rejects.toThrow(NotFoundException);
    });
  });
});