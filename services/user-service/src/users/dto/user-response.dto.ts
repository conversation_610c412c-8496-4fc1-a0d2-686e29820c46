import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for user response
 * Used to standardize the response format and for Swagger documentation
 */
export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'Keycloak user ID',
    example: '456e7890-e12d-34a5-b678-426614174000',
  })
  keycloakId!: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  email!: string;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  firstName!: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  lastName!: string;

  @ApiProperty({
    description: 'User creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'User last update date',
    example: '2023-01-02T00:00:00.000Z',
  })
  updatedAt!: Date;
}
