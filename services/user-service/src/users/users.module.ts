import { Module } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { PrismaService } from '../prisma/prisma.service';
import { ObservabilityModule } from '../observability/observability.module';
import { PrismaRetryInterceptor } from '@libs/prisma-resilience';
import { CircuitBreakerModule } from '@libs/resilience';

@Module({
  imports: [
    ObservabilityModule, // Import ObservabilityModule for logging, metrics, and tracing
    CircuitBreakerModule.register(), // Import CircuitBreakerModule for resilience patterns
  ],
  controllers: [UsersController],
  providers: [UsersService, PrismaService],
  exports: [UsersService], // Export UsersService for use in other modules
})
export class UsersModule {}
