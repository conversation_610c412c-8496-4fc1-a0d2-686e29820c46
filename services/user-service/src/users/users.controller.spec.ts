import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { NotFoundException } from '@nestjs/common';
import { User } from '@prisma/client';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserInternalDto } from '@libs/shared-types';
import { UserBusinessLogger } from '../observability/business-logger.service';
import { MockFactory, TestDataGenerator } from '@libs/testing-utils';

describe('UsersController', () => {
  let controller: UsersController;
  let service: UsersService;

  // Generate test data using our utilities
  const mockUser: User = {
    id: '1',
    keycloakId: 'test-keycloak-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    deletedAt: null,
    version: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            createUser: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            findByKeycloakId: jest.fn(),
            updateUser: jest.fn(),
            remove: jest.fn(),
            hardDeleteUser: jest.fn(),
            restoreUser: jest.fn(),
            createInternalUser: jest.fn(),
            getUserStats: jest.fn(),
            bulkSoftDelete: jest.fn(),
          },
        },
        {
          provide: 'LOGGER_FACTORY',
          useValue: MockFactory.createLoggerFactory(),
        },
        {
          provide: UserBusinessLogger,
          useValue: MockFactory.createBusinessLogger(),
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createUser', () => {
    it('should create a user successfully', async () => {
      const createUserDto: CreateUserDto = TestDataGenerator.createTestUser();
      
      jest.spyOn(service, 'createUser').mockResolvedValue(mockUser);

      const result = await controller.createUser(createUserDto);

      expect(service.createUser).toHaveBeenCalledWith(createUserDto);
      expect(result).toEqual({
        id: mockUser.id,
        keycloakId: mockUser.keycloakId,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
      });
    });

    it('should handle service errors', async () => {
      const createUserDto: CreateUserDto = TestDataGenerator.createTestUser();
      
      jest.spyOn(service, 'createUser').mockRejectedValue(new Error('Database error'));

      await expect(controller.createUser(createUserDto)).rejects.toThrow('Database error');
    });
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      const users = [mockUser];
      jest.spyOn(service, 'findAll').mockResolvedValue(users);

      const result = await controller.findAll();

      expect(service.findAll).toHaveBeenCalledWith(false);
      expect(result).toEqual(users.map(user => ({
        id: user.id,
        keycloakId: user.keycloakId,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })));
    });

    it('should return users including deleted when requested', async () => {
      const users = [mockUser];
      jest.spyOn(service, 'findAll').mockResolvedValue(users);

      const result = await controller.findAll('true');

      expect(service.findAll).toHaveBeenCalledWith(true);
      expect(result).toBeDefined();
    });
  });

  describe('findOne', () => {
    it('should return a user by id', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(mockUser);

      const result = await controller.findOne('1');

      expect(service.findOne).toHaveBeenCalledWith('1', false);
      expect(result).toEqual({
        id: mockUser.id,
        keycloakId: mockUser.keycloakId,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
      });
    });

    it('should handle user not found', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(null);

      await expect(controller.findOne('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('findByKeycloakId', () => {
    it('should return a user by keycloak id', async () => {
      jest.spyOn(service, 'findByKeycloakId').mockResolvedValue(mockUser);

      const result = await controller.findByKeycloakId('test-keycloak-id');

      expect(service.findByKeycloakId).toHaveBeenCalledWith('test-keycloak-id', false);
      expect(result).toEqual({
        id: mockUser.id,
        keycloakId: mockUser.keycloakId,
        email: mockUser.email,
        firstName: mockUser.firstName,
        lastName: mockUser.lastName,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
      });
    });

    it('should handle user not found by keycloak id', async () => {
      jest.spyOn(service, 'findByKeycloakId').mockResolvedValue(null);

      await expect(controller.findByKeycloakId('invalid-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateUser', () => {
    it('should update a user', async () => {
      const updateUserDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };
      
      const updatedUser = { ...mockUser, ...updateUserDto };
      jest.spyOn(service, 'updateUser').mockResolvedValue(updatedUser);

      const result = await controller.updateUser('1', updateUserDto);

      expect(service.updateUser).toHaveBeenCalledWith('1', updateUserDto);
      expect(result.firstName).toBe('Updated');
      expect(result.lastName).toBe('Name');
    });

    it('should handle update errors', async () => {
      const updateUserDto: UpdateUserDto = { firstName: 'Updated' };
      
      jest.spyOn(service, 'updateUser').mockRejectedValue(new NotFoundException());

      await expect(controller.updateUser('999', updateUserDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('softDeleteUser', () => {
    it('should soft delete a user', async () => {
      const deletedUser = { ...mockUser, deletedAt: new Date() };
      jest.spyOn(service, 'remove').mockResolvedValue(deletedUser);

      const result = await controller.softDeleteUser('1');

      expect(service.remove).toHaveBeenCalledWith('1');
      expect(result).toBeDefined();
    });

    it('should handle soft delete errors', async () => {
      jest.spyOn(service, 'remove').mockRejectedValue(new NotFoundException());

      await expect(controller.softDeleteUser('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('createInternalUser', () => {
    it('should create an internal user', async () => {
      const internalUserDto: CreateUserInternalDto = {
        email: '<EMAIL>',
        firstName: 'Internal',
        lastName: 'User',
        keycloakId: 'internal-keycloak-id',
      };
      
      jest.spyOn(service, 'createInternalUser').mockResolvedValue(mockUser);

      const result = await controller.createInternalUser(internalUserDto);

      expect(service.createInternalUser).toHaveBeenCalledWith(internalUserDto);
      expect(result).toBeDefined();
    });
  });

  describe('getUserStats', () => {
    it('should return user statistics', async () => {
      const stats = { total: 10, active: 8, deleted: 2 };
      jest.spyOn(service, 'getUserStats').mockResolvedValue(stats);

      const result = await controller.getUserStats();

      expect(service.getUserStats).toHaveBeenCalled();
      expect(result).toEqual(stats);
    });
  });

  describe('restoreUser', () => {
    it('should restore a soft deleted user', async () => {
      const restoredUser = { ...mockUser, deletedAt: null };
      jest.spyOn(service, 'restoreUser').mockResolvedValue(restoredUser);

      const result = await controller.restoreUser('1');

      expect(service.restoreUser).toHaveBeenCalledWith('1');
      expect(result).toBeDefined();
    });

    it('should handle restore errors', async () => {
      jest.spyOn(service, 'restoreUser').mockRejectedValue(new NotFoundException());

      await expect(controller.restoreUser('999')).rejects.toThrow(NotFoundException);
    });
  });

  describe('hardDeleteUser', () => {
    it('should permanently delete a user', async () => {
      jest.spyOn(service, 'hardDeleteUser').mockResolvedValue(undefined);

      const result = await controller.hardDeleteUser('1', '1');

      expect(service.hardDeleteUser).toHaveBeenCalledWith('1', 1);
      expect(result).toEqual({ message: 'User with ID 1 permanently deleted' });
    });
  });

  describe('updateUserOptimistic', () => {
    it('should update user with optimistic locking', async () => {
      const updateDto: UpdateUserDto = { firstName: 'Optimistic' };
      const expectedVersion = 2;
      
      const updatedUser = { ...mockUser, ...updateDto, version: expectedVersion };
      jest.spyOn(service, 'updateUser').mockResolvedValue(updatedUser);

      const result = await controller.updateUserOptimistic('1', '1', updateDto);

      expect(service.updateUser).toHaveBeenCalledWith('1', updateDto, 1);
      expect(result).toBeDefined();
    });
  });

  describe('bulkSoftDelete', () => {
    it('should bulk soft delete users', async () => {
      const userIds = ['1', '2', '3'];
      const deletedCount = 3;
      
      jest.spyOn(service, 'bulkSoftDelete').mockResolvedValue(deletedCount);

      const result = await controller.bulkSoftDelete({ userIds });

      expect(service.bulkSoftDelete).toHaveBeenCalledWith(['1', '2', '3']);
      expect(result).toEqual({ deletedCount, message: 'Successfully soft deleted 3 users' });
    });

    it('should handle empty user ids', async () => {
      const userIds: string[] = [];
      const deletedCount = 0;
      
      jest.spyOn(service, 'bulkSoftDelete').mockResolvedValue(deletedCount);

      const result = await controller.bulkSoftDelete({ userIds });

      expect(service.bulkSoftDelete).toHaveBeenCalledWith([]);
      expect(result).toEqual({ deletedCount, message: 'Successfully soft deleted 0 users' });
    });
  });
});