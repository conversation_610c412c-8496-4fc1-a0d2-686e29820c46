import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  NotFoundException,
  UsePipes,
  ValidationPipe,
  Inject,
  HttpCode,
  HttpStatus,
  Delete,
  Put,
  Query,
  Patch,
  UseInterceptors,
} from '@nestjs/common';
// Error handling library with correlation support
import { 
  ERROR_CODES, 
  CorrelationId, 
  ErrorResponseBuilderService,
  ApiErrorResponse
} from '@libs/error-handling';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { User, Prisma } from '../prisma';
import { CreateUserInternalDto } from '@libs/shared-types';
import { ObservabilityLogger, LOGGER_SERVICE } from '@libs/observability';
import { UserBusinessLogger } from '../observability/business-logger.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { PrismaRetryInterceptor } from '@libs/prisma-resilience';
import { GatewayUser, UserContext } from '@libs/auth-common';

@ApiTags('users')
@Controller('users')
@UseInterceptors(new PrismaRetryInterceptor()) // Base path for this controller
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly businessLogger: UserBusinessLogger,
    private readonly errorBuilder: ErrorResponseBuilderService,
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory.createLogger(UsersController.name);
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  @ApiOperation({
    summary: 'Create a new user',
    description: 'Creates a new user in the database',
  })
  @ApiBody({ type: CreateUserDto })
  @ApiResponse({
    status: 201,
    description: 'User successfully created',
    type: UserResponseDto,
  })
  @ApiErrorResponse('VALIDATION_FAILED', 'Request validation failed')
  @ApiErrorResponse('EMAIL_ALREADY_EXISTS', 'User with this email already exists')
  @ApiErrorResponse('INTERNAL_SERVER_ERROR', 'Internal server error')
  async createUser(@Body() userData: CreateUserDto): Promise<UserResponseDto> {
    this.logger.log(`Creating new user with email: ${userData.email}`);

    const user = await this.usersService.createUser(userData);

    // Log business event
    this.businessLogger.logUserCreationEvent('success', user.id, {
      email: user.email,
      keycloakId: user.keycloakId,
    });

    return this.mapToResponseDto(user);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all users',
    description: 'Returns a list of all users (excludes soft-deleted by default)',
  })
  @ApiQuery({ name: 'includeDeleted', required: false, description: 'Include soft-deleted users' })
  @ApiResponse({
    status: 200,
    description: 'List of users',
    type: [UserResponseDto],
  })
  @ApiErrorResponse('INTERNAL_SERVER_ERROR', 'Internal server error')
  async findAll(@Query('includeDeleted') includeDeleted?: string): Promise<UserResponseDto[]> {
    const includeDeletedFlag = includeDeleted === 'true';
    this.logger.log(`Getting all users (includeDeleted: ${includeDeletedFlag})`);
    const users = await this.usersService.findAll(includeDeletedFlag);
    return users.map((user) => this.mapToResponseDto(user));
  }

  @Get('me')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Returns the profile of the currently authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Current user profile',
    type: UserResponseDto,
  })
  @ApiErrorResponse('UNAUTHORIZED', 'Invalid or missing authentication token')
  @ApiErrorResponse('USER_NOT_FOUND', 'User profile not found')
  @ApiErrorResponse('INTERNAL_SERVER_ERROR', 'Internal server error')
  async getCurrentUser(
    @GatewayUser() userContext: UserContext,
    @CorrelationId() correlationId?: string
  ): Promise<UserResponseDto> {
    this.logger.log(`Getting current user profile for user ID: ${userContext.userId} [${correlationId}]`);

    // Find user by Keycloak ID (which is the userId in the context)
    const user = await this.usersService.findByKeycloakId(userContext.userId, false);
    if (!user) {
      this.logger.warn(`User profile not found for Keycloak ID: ${userContext.userId} [${correlationId}]`);
      
      // Use the type-safe error-handling library to create a standardized error
      throw this.errorBuilder.createHttpException(
        'USER_NOT_FOUND', // TypeScript will validate this is a valid ERROR_CODES key
        `User profile not found for ID: ${userContext.userId}`
      );
    }

    // Log business event
    this.businessLogger.logUserUpdateEvent('success', user.id, {
      operationType: 'profile_access',
      email: user.email,
      keycloakId: user.keycloakId,
      correlationId,
    });

    return this.mapToResponseDto(user);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Returns a user by their ID',
  })
  @ApiQuery({ name: 'includeDeleted', required: false, description: 'Include soft-deleted users' })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserResponseDto,
  })
  @ApiErrorResponse('USER_NOT_FOUND', 'User with specified ID not found')
  @ApiErrorResponse('INTERNAL_SERVER_ERROR', 'Internal server error')
  async findOne(
    @Param('id') id: string, 
    @Query('includeDeleted') includeDeleted?: string,
    @CorrelationId() correlationId?: string
  ): Promise<UserResponseDto> {
    const includeDeletedFlag = includeDeleted === 'true';
    this.logger.log(`Getting user with ID: ${id} (includeDeleted: ${includeDeletedFlag})`);

    const user = await this.usersService.findOne(id, includeDeletedFlag);
    if (!user) {
      this.logger.warn(`User with ID ${id} not found [${correlationId}]`);
      throw this.errorBuilder.createHttpException(
        'USER_NOT_FOUND',
        `User with ID ${id} not found`
      );
    }

    return this.mapToResponseDto(user);
  }

  @Get('keycloak/:keycloakId')
  @ApiOperation({
    summary: 'Get user by Keycloak ID',
    description: 'Returns a user by their Keycloak ID',
  })
  @ApiParam({ name: 'keycloakId', description: 'Keycloak User ID' })
  @ApiQuery({ name: 'includeDeleted', required: false, description: 'Include soft-deleted users' })
  @ApiResponse({
    status: 200,
    description: 'User found',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async findByKeycloakId(
    @Param('keycloakId') keycloakId: string,
    @Query('includeDeleted') includeDeleted?: string,
  ): Promise<UserResponseDto> {
    const includeDeletedFlag = includeDeleted === 'true';
    this.logger.log(`Getting user with Keycloak ID: ${keycloakId} (includeDeleted: ${includeDeletedFlag})`);

    const user = await this.usersService.findByKeycloakId(keycloakId, includeDeletedFlag);
    if (!user) {
      this.logger.warn(`User with Keycloak ID ${keycloakId} not found`);
      throw new NotFoundException(
        `User with Keycloak ID ${keycloakId} not found`,
      );
    }

    return this.mapToResponseDto(user);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update user',
    description: 'Updates a user by their ID',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateUser(
    @Param('id') id: string,
    @Body() updateData: UpdateUserDto,
  ): Promise<UserResponseDto> {
    this.logger.log(`Updating user with ID: ${id}`);

    const user = await this.usersService.updateUser(id, updateData);

    // Log business event
    this.businessLogger.logUserUpdateEvent('success', user.id, {
      email: user.email,
      keycloakId: user.keycloakId,
    });

    return this.mapToResponseDto(user);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Soft delete user',
    description: 'Soft deletes a user by their ID (sets deletedAt timestamp).',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User soft deleted successfully', type: UserResponseDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async softDeleteUser(@Param('id') id: string): Promise<UserResponseDto> {
    this.logger.log(`Soft deleting user with ID: ${id}`);

    const user = await this.usersService.remove(id);

    // Log business event
    this.businessLogger.logUserDeletionEvent('success', user.id, { deletionType: 'soft', email: user.email, keycloakId: user.keycloakId });

    return this.mapToResponseDto(user);
  }

  // Internal endpoint for AuthService to create a user record after Keycloak registration
  @Post('internal')
  @UsePipes(new ValidationPipe({ whitelist: true, transform: true }))
  @ApiOperation({
    summary: 'Create internal user',
    description: 'Creates a user from Keycloak registration (internal use)',
  })
  @ApiBody({ type: CreateUserInternalDto })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 409, description: 'Conflict - user already exists' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async createInternalUser(
    @Body() data: CreateUserInternalDto,
  ): Promise<UserResponseDto> {
    this.logger.log(
      `Creating internal user with email: ${data.email} and Keycloak ID: ${data.keycloakId}`,
    );

    const user = await this.usersService.createInternalUser(data);

    // Log business event
    this.businessLogger.logUserCreationEvent('success', user.id, {
      email: user.email,
      keycloakId: user.keycloakId,
      source: 'internal',
    });

    return this.mapToResponseDto(user);
  }

  // Enhanced endpoints for soft delete, optimistic locking, and statistics

  @Get('stats/overview')
  @ApiOperation({
    summary: 'Get user statistics',
    description: 'Returns user count statistics including active and deleted users',
  })
  @ApiResponse({
    status: 200,
    description: 'User statistics',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
        deleted: { type: 'number' },
      },
    },
  })
  async getUserStats(): Promise<{ total: number; active: number; deleted: number }> {
    this.logger.log('Getting user statistics');
    return await this.usersService.getUserStats();
  }

  @Patch(':id/restore')
  @ApiOperation({
    summary: 'Restore soft-deleted user',
    description: 'Restores a user that was previously soft-deleted',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'User restored successfully',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'User is not deleted' })
  async restoreUser(@Param('id') id: string): Promise<UserResponseDto> {
    this.logger.log(`Restoring user with ID: ${id}`);
    const user = await this.usersService.restoreUser(id);
    
    // Log business event
    this.businessLogger.logUserUpdateEvent('success', user.id, {
      operationType: 'restore',
      email: user.email,
      keycloakId: user.keycloakId,
    });

    return this.mapToResponseDto(user);
  }

  @Delete(':id/hard')
  @ApiOperation({
    summary: 'Permanently delete user',
    description: 'Permanently deletes a user (cannot be restored)',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiQuery({ name: 'version', required: false, description: 'Expected version for optimistic locking' })
  @ApiResponse({ status: 200, description: 'User permanently deleted' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 409, description: 'Version conflict (optimistic locking)' })
  async hardDeleteUser(
    @Param('id') id: string,
    @Query('version') version?: string,
  ): Promise<{ message: string }> {
    this.logger.log(`Hard deleting user with ID: ${id}`);
    
    const expectedVersion = version ? parseInt(version, 10) : undefined;
    await this.usersService.hardDeleteUser(id, expectedVersion);

    // Log business event
    this.businessLogger.logUserDeletionEvent('success', id, { deletionType: 'hard' });

    return { message: `User with ID ${id} permanently deleted` };
  }

  @Put(':id/optimistic')
  @ApiOperation({
    summary: 'Update user with optimistic locking',
    description: 'Updates a user with version checking for concurrent update protection',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiQuery({ name: 'version', required: true, description: 'Current version of the user' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserResponseDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 409, description: 'Version conflict (user modified by another process)' })
  async updateUserOptimistic(
    @Param('id') id: string,
    @Query('version') version: string,
    @Body() updateData: UpdateUserDto,
  ): Promise<UserResponseDto> {
    this.logger.log(`Updating user with ID: ${id} and version: ${version}`);

    const expectedVersion = parseInt(version, 10);
    const user = await this.usersService.updateUser(id, updateData, expectedVersion);

    // Log business event
    this.businessLogger.logUserUpdateEvent('success', user.id, {
      operationType: 'optimistic_update',
      email: user.email,
      keycloakId: user.keycloakId,
      previousVersion: expectedVersion,
      newVersion: user.version,
    });

    return this.mapToResponseDto(user);
  }

  @Delete('bulk')
  @ApiOperation({
    summary: 'Bulk soft delete users',
    description: 'Soft deletes multiple users in a single transaction',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        userIds: {
          type: 'array',
          items: { type: 'string' },
          description: 'Array of user IDs to delete',
        },
      },
      required: ['userIds'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Users deleted successfully',
    schema: {
      type: 'object',
      properties: {
        deletedCount: { type: 'number' },
        message: { type: 'string' },
      },
    },
  })
  async bulkSoftDelete(@Body() body: { userIds: string[] }): Promise<{ deletedCount: number; message: string }> {
    this.logger.log(`Bulk deleting ${body.userIds.length} users`);
    
    const deletedCount = await this.usersService.bulkSoftDelete(body.userIds);

    // Log business event
    this.businessLogger.logUserUpdateEvent('success', 'multiple', {
      operationType: 'bulk_delete',
      userIds: body.userIds,
      deletedCount,
    });

    return { 
      deletedCount, 
      message: `Successfully soft deleted ${deletedCount} users` 
    };
  }

  /**
   * Maps a User entity to a UserResponseDto
   * This prevents exposing sensitive data and ensures consistent response format
   */
  private mapToResponseDto(user: User): UserResponseDto {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      keycloakId: user.keycloakId,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
