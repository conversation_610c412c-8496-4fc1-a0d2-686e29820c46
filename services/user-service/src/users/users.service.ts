﻿import { Injectable, ConflictException, InternalServerErrorException, NotFoundException, Inject, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma';
import { User, Prisma, PrismaClientKnownRequestError } from '../prisma';
import { CreateUserInternalDto } from '@libs/shared-types';
import { ObservabilityLogger } from '@libs/observability';
import { CacheService, UseCache, InvalidateCache } from '@libs/caching';
import { EventPublisher, EventFactory } from '@libs/messaging';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(
    private prisma: PrismaService,
    private readonly cacheService: CacheService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    @Inject('EVENT_PUBLISHER') private readonly eventPublisher: EventPublisher,
  ) {
    // Get a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger(UsersService.name);
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Creates a new user with cache invalidation
   * @param data User data
   * @returns Created user
   */
  async createUser(data: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with email: ${data.email}`);

    try {
      // Map DTO to Prisma input type
      const createInput: Prisma.UserCreateInput = {
        keycloakId: data.keycloakId,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
      };

      const newUser = await this.prisma.user.create({ data: createInput });
      
      // Invalidate findAll cache patterns as user count has changed
      await this.cacheService.invalidate({
        pattern: 'users:findAll:*'
      });
      
      // Publish user created event (non-blocking)
      this.publishEventSafely(
        EventFactory.userCreated(newUser.id, newUser),
        'user.created'
      );
      
      this.logger.log(`Successfully created user with ID: ${newUser.id}`);
      return newUser;
    } catch (error) {
      this.handlePrismaError(error, 'create user');
    }
  }

  /**
   * Creates a new user and optionally their profile in a transaction.
   * If a bio is provided in the DTO, a profile will be created.
   * @param data User data including optional bio for profile
   * @returns Created user
   */
  async createUserWithProfile(data: CreateUserDto): Promise<User> {
    this.logger.log(`Attempting to create user with profile for email: ${data.email}`);

    try {
      const userWithProfile = await this.prisma.$transaction(async (tx) => {
        // 1. Create the user
        const newUser = await tx.user.create({
          data: {
            keycloakId: data.keycloakId,
            email: data.email,
            firstName: data.firstName,
            lastName: data.lastName,
          },
        });
        this.logger.log(`User created with ID: ${newUser.id} within transaction`);

        // 2. If bio is provided, create the profile
        if (data.bio) {
          this.logger.log(`Bio provided, creating profile for user ID: ${newUser.id}`);
          await tx.profile.create({
            data: {
              userId: newUser.id,
              bio: data.bio,
            },
          });
          this.logger.log(`Profile created for user ID: ${newUser.id} within transaction`);
        } else {
          this.logger.log(`No bio provided, skipping profile creation for user ID: ${newUser.id}`);
        }
        return newUser;
      });

      // Publish user created event (non-blocking)
      this.publishEventSafely(
        EventFactory.userCreated(userWithProfile.id, userWithProfile),
        'user.created'
      );

      this.logger.log(`Successfully created user (and profile if bio was provided) with ID: ${userWithProfile.id}`);
      return userWithProfile;
    } catch (error) {
      this.logger.error(`Failed to create user with profile for email ${data.email}: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      this.handlePrismaError(error, 'create user with profile');
    }
  }

  /**
   * Finds all users (excluding soft-deleted ones)
   * @param includeDeleted Optional flag to include soft-deleted users
   * @returns Array of users
   */
  async findAll(includeDeletedFlag: boolean = false): Promise<User[]> {
    this.logger.log(`Finding all users (includeDeleted: ${includeDeletedFlag})`);

    try {
      // The middleware will handle filtering based on 'includeDeletedFlag'
      return await this.prisma.user.findMany({
        includeDeleted: includeDeletedFlag,
        orderBy: { createdAt: 'desc' },
      } as any);
    } catch (error) {
      this.logger.error(`Failed to find all users: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException('Failed to retrieve users');
    }
  }

  /**
   * Finds a user by ID (excluding soft-deleted by default)
   * Uses @UseCache decorator for automatic caching
   * @param id User ID
   * @param includeDeleted Optional flag to include soft-deleted users
   * @returns User or null if not found
   */
  @UseCache({
    key: (id: string, includeDeletedFlag: boolean = false) => `user:${id}:includeDeleted:${includeDeletedFlag}`,
    ttl: 300, // 5 minutes
    namespace: 'users',
    condition: (user) => user !== null
  })
  async findOne(id: string, includeDeletedFlag: boolean = false): Promise<User | null> {
    this.logger.log(`Finding user with ID: ${id} (includeDeleted: ${includeDeletedFlag})`);

    try {
      // Direct database fetch - caching handled by decorator
      const user = await this.prisma.user.findUnique({
        where: { id },
        includeDeleted: includeDeletedFlag,
      } as any);

      return user;
    } catch (error) {
      this.logger.error(`Failed to find user with ID ${id}: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException(`Failed to retrieve user with ID ${id}`);
    }
  }

  /**
   * Finds a user by Keycloak ID (excluding soft-deleted by default)
   * Uses caching for improved performance
   * @param keycloakId Keycloak user ID
   * @param includeDeleted Optional flag to include soft-deleted users
   * @returns User or null if not found
   */
  async findByKeycloakId(keycloakId: string, includeDeletedFlag: boolean = false): Promise<User | null> {
    this.logger.log(`Finding user with Keycloak ID: ${keycloakId} (includeDeleted: ${includeDeletedFlag})`);

    try {
      // Generate cache key based on parameters
      const cacheKey = `user:keycloak:${keycloakId}:includeDeleted:${includeDeletedFlag}`;
      
      // Try to get from cache first
      const cached = await this.cacheService.get<User>(cacheKey);
      if (cached.hit && cached.value) {
        this.logger.debug(`Cache hit for Keycloak ID: ${keycloakId}`);
        return cached.value;
      }

      // Cache miss - fetch from database
      const user = await this.prisma.user.findUnique({
        where: { keycloakId },
        includeDeleted: includeDeletedFlag,
      } as any);

      // Cache the result for 5 minutes (300 seconds)
      if (user) {
        await this.cacheService.set(cacheKey, user, 300);
        this.logger.debug(`Cached user with Keycloak ID: ${keycloakId}`);
      }

      return user;
    } catch (error) {
      this.logger.error(`Failed to find user with Keycloak ID ${keycloakId}: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException(`Failed to retrieve user with Keycloak ID ${keycloakId}`);
    }
  }

  /**
   * Updates a user with optimistic locking and cache invalidation
   * @param id User ID
   * @param data Update data
   * @param expectedVersion Expected version for optimistic locking (optional)
   * @returns Updated user
   */
  @InvalidateCache({
    keys: (id: string) => [`user:${id}:includeDeleted:false`, `user:${id}:includeDeleted:true`],
    pattern: 'users:*',
    namespace: 'users'
  })
  async updateUser(id: string, data: UpdateUserDto, expectedVersion?: number): Promise<User> {
    this.logger.log(`Updating user with ID: ${id}${expectedVersion ? ` (expected version: ${expectedVersion})` : ''}`);

    try {
      // Check if user exists (middleware handles soft-delete check by default)
      const existingUser = await this.prisma.user.findUnique({ where: { id } } as any);
      
      if (!existingUser) {
        this.logger.warn(`User with ID ${id} not found or is deleted`);
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Optimistic locking check if version is provided
      if (expectedVersion !== undefined && existingUser.version !== expectedVersion) {
        this.logger.warn(`Version mismatch for user ${id}: expected ${expectedVersion}, got ${existingUser.version}`);
        throw new ConflictException(`User has been modified by another process. Expected version ${expectedVersion}, but current version is ${existingUser.version}`);
      }

      // Map DTO to Prisma input type
      const updateInput: Prisma.UserUpdateInput = {};
      if (data.firstName !== undefined) updateInput.firstName = data.firstName;
      if (data.lastName !== undefined) updateInput.lastName = data.lastName;
      if (data.email !== undefined) updateInput.email = data.email;

      // Increment version for optimistic locking
      updateInput.version = { increment: 1 };

      const updatedUser = await this.prisma.user.update({
        where: { id }, // Middleware handles deletedAt: null check by default
        data: updateInput,
      } as any);

      // Invalidate cache for this user
      await this.invalidateUserCache(updatedUser);
      
      // Publish user updated event (non-blocking)
      this.publishEventSafely(
        EventFactory.userUpdated(updatedUser.id, updatedUser),
        'user.updated'
      );
      
      this.logger.log(`Successfully updated user with ID: ${id}, new version: ${updatedUser.version}`);
      return updatedUser;
    } catch (error) {
      if (error && (error.constructor?.name === 'NotFoundException' || error.constructor?.name === 'ConflictException')) {
        throw error;
      }
      this.handlePrismaError(error, 'update user');
    }
  }

  /**
   * Soft deletes a user (sets deletedAt timestamp)
   * @param id User ID
   * @param expectedVersion Expected version for optimistic locking (optional)
   */
  async deleteUser(id: string, expectedVersion?: number): Promise<void> {
    this.logger.log(`Soft deleting user with ID: ${id}${expectedVersion ? ` (expected version: ${expectedVersion})` : ''}`);

    try {
      // Check if user exists and is not already soft-deleted
      const existingUser = await this.prisma.user.findUnique({ 
        where: { id, deletedAt: null } 
      });
      
      if (!existingUser) {
        this.logger.warn(`User with ID ${id} not found or already deleted`);
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Optimistic locking check if version is provided
      if (expectedVersion !== undefined && existingUser.version !== expectedVersion) {
        this.logger.warn(`Version mismatch for user ${id}: expected ${expectedVersion}, got ${existingUser.version}`);
        throw new ConflictException(`User has been modified by another process. Expected version ${expectedVersion}, but current version is ${existingUser.version}`);
      }

      await this.prisma.user.update({
        where: { id, deletedAt: null },
        data: { 
          deletedAt: new Date(),
          version: { increment: 1 }
        },
      });

      // Publish user deleted event (non-blocking)
      this.publishEventSafely(
        EventFactory.userDeleted(id),
        'user.deleted'
      );

      this.logger.log(`Successfully soft deleted user with ID: ${id}`);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to delete user with ID ${id}: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException(`Failed to delete user with ID ${id}`);
    }
  }

  /**
   * Permanently deletes a user (hard delete)
   * @param id User ID
   * @param expectedVersion Expected version for optimistic locking (optional)
   */
  async hardDeleteUser(id: string, expectedVersion?: number): Promise<void> {
    this.logger.log(`Hard deleting user with ID: ${id}${expectedVersion ? ` (expected version: ${expectedVersion})` : ''}`);

    try {
      // Check if user exists
      const existingUser = await this.prisma.user.findUnique({ 
        where: { id },
        includeDeleted: true, // Check even if soft-deleted, as hard delete is final
      } as any);
      
      if (!existingUser) {
        this.logger.warn(`User with ID ${id} not found for hard deletion`);
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Optimistic locking check if version is provided
      if (expectedVersion !== undefined && existingUser.version !== expectedVersion) {
        this.logger.warn(`Version mismatch for user ${id}: expected ${expectedVersion}, got ${existingUser.version}`);
        throw new ConflictException(`User has been modified by another process. Expected version ${expectedVersion}, but current version is ${existingUser.version}`);
      }

      await this.prisma.user.delete({ 
        where: { id }, 
        _unsafeBypassSoftDelete: true 
      } as any);
      
      // Publish user permanently deleted event (non-blocking)
      this.publishEventSafely(
        EventFactory.create("user.permanently_deleted", { userId: id }, { source: "user-service" }),
        'user.permanently_deleted'
      );
      
      this.logger.log(`Successfully hard deleted user with ID: ${id}`);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to hard delete user with ID ${id}: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException(`Failed to hard delete user with ID ${id}`);
    }
  }

  /**
   * Restores a soft-deleted user
   * @param id User ID
   * @returns Restored user
   */
  async restoreUser(id: string): Promise<User> {
    this.logger.log(`Restoring user with ID: ${id}`);

    try {
      // Check if user exists and is soft-deleted
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
        includeDeleted: true, // Ensure we can find it even if soft-deleted
      } as any);
      
      if (!existingUser) {
        this.logger.warn(`User with ID ${id} not found`);
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      if (!existingUser.deletedAt) {
        this.logger.warn(`User with ID ${id} is not deleted`);
        throw new BadRequestException(`User with ID ${id} is not deleted`);
      }

      // Perform the update to clear deletedAt and increment version
      // Pass includeDeleted: true to ensure middleware allows updating a soft-deleted record
      const restoredUser = await this.prisma.user.update({
        where: { id },
        data: {
          deletedAt: null,
          version: { increment: 1 },
        },
        includeDeleted: true, 
      } as any);

      // Publish user restored event (non-blocking)
      this.publishEventSafely(
        EventFactory.userRestored(restoredUser.id),
        'user.restored'
      );

      this.logger.log(`Successfully restored user with ID: ${id}`);
      return restoredUser;
    } catch (error) {
      if (error && (error.constructor?.name === 'NotFoundException' || error.constructor?.name === 'BadRequestException')) {
        throw error;
      }
      this.logger.error(`Failed to restore user with ID ${id}: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException(`Failed to restore user with ID ${id}`);
    }
  }

  /**
   * Creates a user with profile in a transaction (example of transaction pattern)
   * @param data User data
   * @returns Created user with transaction
   */
  async createUserWithTransaction(data: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user with transaction: ${data.email}`);

    try {
      return await this.prisma.$transaction(async (tx) => {
        // Create the user
        const createInput: Prisma.UserCreateInput = {
          keycloakId: data.keycloakId,
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
        };

        const newUser = await tx.user.create({ data: createInput });

        // Here you could add related record creation
        // For example: user profile, settings, etc.
        // await tx.userProfile.create({ data: { userId: newUser.id, ... } });

        this.logger.log(`Successfully created user with transaction: ${newUser.id}`);
        return newUser;
      });
    } catch (error) {
      this.logger.error(`Transaction failed for creating user: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      this.handlePrismaError(error, 'create user with transaction');
    }
  }

  /**
   * Bulk operations in transaction
   * @param userIds Array of user IDs to soft delete
   * @returns Number of affected users
   */
  async bulkSoftDelete(userIds: string[]): Promise<number> {
    this.logger.log(`Bulk soft deleting ${userIds.length} users`);

    try {
      const result = await this.prisma.$transaction(async (tx) => {
        const updateResult = await tx.user.updateMany({
          where: { 
            id: { in: userIds },
            deletedAt: null // Only delete non-deleted users
          },
          data: { 
            deletedAt: new Date(),
            version: { increment: 1 }
          },
        });

        this.logger.log(`Bulk soft deleted ${updateResult.count} users`);
        return updateResult.count;
      });

      return result;
    } catch (error) {
      this.logger.error(`Failed to bulk soft delete users: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException('Failed to bulk delete users');
    }
  }

  /**
   * Get user statistics
   * @returns User count statistics
   */
  async getUserStats(): Promise<{
    total: number;
    active: number;
    deleted: number;
  }> {
    this.logger.log('Getting user statistics');

    try {
      const [total, active, deleted] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.user.count({ where: { deletedAt: null } }),
        this.prisma.user.count({ where: { deletedAt: { not: null } } }),
      ]);

      return { total, active, deleted };
    } catch (error) {
      this.logger.error(`Failed to get user statistics: ${this.getErrorMessage(error)}`, this.getErrorStack(error));
      throw new InternalServerErrorException('Failed to get user statistics');
    }
  }

  /**
   * Publishes an event in a non-blocking way with error handling
   * Failed event publishing doesn't break the user operation
   * @param event Event to publish
   * @param eventDescription Description for logging
   */
  private publishEventSafely(event: any, eventDescription: string): void {
    // Publish event asynchronously without blocking the main operation
    setImmediate(async () => {
      try {
        await this.eventPublisher.publish(event);
        this.logger.debug({
          message: `Successfully published ${eventDescription} event`,
          eventType: eventDescription,
          eventId: event.id,
          correlationId: event.correlationId
        });
      } catch (error) {
        this.logger.error({
          message: `Failed to publish ${eventDescription} event`,
          eventType: eventDescription,
          eventId: event.id,
          correlationId: event.correlationId,
          error: error && error.message ? error.message : String(error)
        });
        // Don't throw - events are non-critical for user operations
      }
    });
  }

  /**
   * Invalidates cache entries for a specific user
   * @param user User object to invalidate cache for
   */
  private async invalidateUserCache(user: User): Promise<void> {
    try {
      // Invalidate all cache entries for this user
      await this.cacheService.invalidate({
        pattern: `user:${user.id}:*`
      });
      
      // Also invalidate by Keycloak ID if available
      if (user.keycloakId) {
        await this.cacheService.invalidate({
          pattern: `user:keycloak:${user.keycloakId}:*`
        });
      }
      
      this.logger.debug(`Invalidated cache for user ID: ${user.id}`);
    } catch (error) {
      this.logger.warn(`Failed to invalidate cache for user ${user.id}: ${this.getErrorMessage(error)}`);
      // Don't throw error here, cache invalidation failure shouldn't break the operation
    }
  }
  async createInternalUser(data: CreateUserInternalDto): Promise<User> {
    this.logger.log(`Creating internal user with email: ${data.email} and Keycloak ID: ${data.keycloakId}`);

    // Map DTO to Prisma input type
    const createInput: Prisma.UserCreateInput = {
      keycloakId: data.keycloakId,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
    };

    try {
      const newUser = await this.prisma.user.create({ data: createInput });
      
      // Publish user created event (non-blocking)
      this.publishEventSafely(
        EventFactory.userCreated(newUser.id, newUser),
        'user.created'
      );
      
      this.logger.log(`Successfully created internal user with ID: ${newUser.id} and Keycloak ID: ${newUser.keycloakId}`);
      return newUser;
    } catch (error) {
      this.handlePrismaError(error, 'create internal user');
    }
  }

  /**
   * Removes a user (soft delete).
   * @param id User ID
   * @returns The user with deletedAt set.
   */
  async remove(id: string): Promise<User> {
    this.logger.log(`Soft deleting user with ID: ${id}`);
    try {
      // The middleware will change this to an update operation
      // and set deletedAt.
      const deletedUser = await this.prisma.user.delete({ where: { id } } as any);
      if (!deletedUser) { // Should not happen if middleware works, but good for robustness
        throw new NotFoundException(`User with ID ${id} not found for soft delete.`);
      }
      this.logger.log(`Successfully soft-deleted user with ID: ${id}`);
      return deletedUser;
    } catch (error) {
      this.handlePrismaError(error, `soft delete user with ID ${id}`);
    }
  }

  /**
   * Permanently deletes a user from the database.
   * @param id User ID
   * @returns The result of the delete operation.
   */
  async hardDelete(id: string): Promise<User> {
    this.logger.log(`Hard deleting user with ID: ${id}`);
    try {
      // Use _unsafeBypassSoftDelete to skip the soft delete middleware
      const deletedUser = await this.prisma.user.delete({
        where: { id },
        _unsafeBypassSoftDelete: true,
      } as any);
      if (!deletedUser) {
        throw new NotFoundException(`User with ID ${id} not found for hard delete.`);
      }
      this.logger.log(`Successfully hard-deleted user with ID: ${id}`);
      return deletedUser;
    } catch (error) {
      this.handlePrismaError(error, `hard delete user with ID ${id}`);
    }
  }

  /**
   * Restores a soft-deleted user.
   * @param id User ID
   * @returns The restored user.
   */
  async restore(id: string): Promise<User> {
    this.logger.log(`Restoring user with ID: ${id}`);
    try {
      // First, find the user, including soft-deleted ones
      const userToRestore = await this.prisma.user.findUnique({
        where: { id },
        includeDeleted: true,
      } as any);

      if (!userToRestore) {
        throw new NotFoundException(`User with ID ${id} not found.`);
      }

      if (!userToRestore.deletedAt) {
        throw new BadRequestException(`User with ID ${id} is not soft-deleted.`);
      }

      // Restore the user by setting deletedAt to null and incrementing version
      // We use _unsafeBypassSoftDelete to ensure the update can target a soft-deleted record
      const restoredUser = await this.prisma.user.update({
        where: { id },
        data: { deletedAt: null, version: { increment: 1 } },
        _unsafeBypassSoftDelete: true, // Allows updating a record that would normally be filtered by deletedAt: null
      } as any);
      this.logger.log(`Successfully restored user with ID: ${id}`);
      return restoredUser;
    } catch (error) {
      this.handlePrismaError(error, `restore user with ID ${id}`);
    }
  }

  private handlePrismaError(error: any, operation: string): never {
    const errorMessage = this.getErrorMessage(error);
    const errorStack = this.getErrorStack(error);

    this.logger.error(`Failed to ${operation}: ${errorMessage}`, errorStack);

    // Webpack-safe error checking: check error properties instead of instanceof
    // After bundling, instanceof PrismaClientKnownRequestError doesn't work properly
    if (error && error.code && typeof error.code === 'string') {
      // P2002 is the Prisma code for unique constraint violation
      if (error.code === 'P2002') {
        const target = error.meta?.target as string[];
        this.logger.warn(`Unique constraint violation for target: ${target?.join(', ')}`);
        throw new ConflictException(`User with this ${target?.join(' or ')} already exists.`);
      }
      
      // P2025 - Record not found
      if (error.code === 'P2025') {
        throw new NotFoundException(`Record not found for ${operation}.`);
      }
      
      // P2003 - Foreign key constraint failed
      if (error.code === 'P2003') {
        throw new BadRequestException(`Foreign key constraint failed for ${operation}.`);
      }
      
      // P2016 - Query interpretation error
      if (error.code === 'P2016') {
        throw new BadRequestException(`Query interpretation error for ${operation}.`);
      }
    }

    throw new InternalServerErrorException(`Could not ${operation}.`);
  }

  /**
   * Gets a string error message from any error type
   * @param error Error object
   * @returns Error message as string
   */
  private getErrorMessage(error: any): string {
    return error && error.message ? error.message : String(error);
  }

  /**
   * Gets the error stack if available
   * @param error Error object
   * @returns Error stack or undefined
   */
  private getErrorStack(error: any): string | undefined {
    return error && error.stack ? error.stack : undefined;
  }
}







