import { Injectable, Inject } from '@nestjs/common';
import { EventHandler, DomainEvent } from '@libs/messaging';
import { CacheService } from '@libs/caching';
import { ObservabilityLogger } from '@libs/observability';

/**
 * Event handler for cross-service cache invalidation
 * Listens to events from other services and invalidates related cache entries
 */
@Injectable()
export class CacheInvalidationHandler implements EventHandler {
  supportedEvents = [
    'auth.logout',
    'auth.registration.success', 
    'user.updated',
    'user.deleted',
    'permissions.changed'
  ];

  constructor(
    private readonly cacheService: CacheService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    this.logger = this.loggerFactory.createLogger(CacheInvalidationHandler.name);
  }

  private readonly logger: ObservabilityLogger;

  /**
   * Handle cache invalidation events from other services
   * @param event Domain event
   */
  async handle(event: DomainEvent): Promise<void> {
    const startTime = Date.now();
    
    this.logger.debug({
      message: `Handling cache invalidation event: ${event.type}`,
      eventId: event.id,
      correlationId: event.correlationId,
      source: event.source
    });

    try {
      switch (event.type) {
        case 'auth.logout':
          await this.handleAuthLogout(event);
          break;
        case 'auth.registration.success':
          await this.handleUserRegistration(event);
          break;
        case 'user.updated':
          await this.handleUserUpdate(event);
          break;
        case 'user.deleted':
          await this.handleUserDeletion(event);
          break;
        case 'permissions.changed':
          await this.handlePermissionsChange(event);
          break;
        default:
          this.logger.warn({
            message: `Unsupported event type for cache invalidation: ${event.type}`,
            eventId: event.id
          });
      }

      const processingTime = Date.now() - startTime;
      this.logger.log({
        message: `Cache invalidation event processed successfully: ${event.type}`,
        eventId: event.id,
        processingTime,
        correlationId: event.correlationId
      });
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      this.logger.error({
        message: `Cache invalidation event processing failed: ${event.type}`,
        eventId: event.id,
        error: error.message,
        processingTime,
        correlationId: event.correlationId
      });
      
      // Don't throw - cache invalidation failures shouldn't break event processing
      // The system can still function with stale cache data
    }
  }

  /**
   * Handle auth logout events - invalidate user session caches
   * @param event Logout event
   */
  private async handleAuthLogout(event: DomainEvent): Promise<void> {
    const { userId } = event.data;
    
    if (!userId) {
      this.logger.warn('Auth logout event missing userId', { eventId: event.id });
      return;
    }

    // Invalidate all user-related cache entries
    await this.cacheService.invalidate({
      pattern: `user:${userId}:*`,
      namespace: 'users'
    });

    // Invalidate user list caches that might include this user
    await this.cacheService.invalidate({
      pattern: 'users:findAll:*',
      namespace: 'users'
    });

    this.logger.debug({
      message: `Invalidated user caches for logout`,
      userId,
      eventId: event.id
    });
  }

  /**
   * Handle user registration events - invalidate list caches
   * @param event Registration event
   */
  private async handleUserRegistration(event: DomainEvent): Promise<void> {
    // New user registered, invalidate list caches
    await this.cacheService.invalidate({
      pattern: 'users:findAll:*',
      namespace: 'users'
    });

    this.logger.debug({
      message: `Invalidated user list caches for new registration`,
      email: event.data.email,
      eventId: event.id
    });
  }

  /**
   * Handle user update events - invalidate specific user caches
   * @param event User update event
   */
  private async handleUserUpdate(event: DomainEvent): Promise<void> {
    const { userId } = event.data;
    
    if (!userId) {
      this.logger.warn('User update event missing userId', { eventId: event.id });
      return;
    }

    // Invalidate specific user cache entries
    await this.cacheService.invalidate({
      keys: [`user:${userId}:includeDeleted:false`, `user:${userId}:includeDeleted:true`],
      namespace: 'users'
    });

    // Invalidate user list caches
    await this.cacheService.invalidate({
      pattern: 'users:findAll:*',
      namespace: 'users'
    });

    this.logger.debug({
      message: `Invalidated user caches for update`,
      userId,
      eventId: event.id
    });
  }

  /**
   * Handle user deletion events - invalidate all related caches
   * @param event User deletion event
   */
  private async handleUserDeletion(event: DomainEvent): Promise<void> {
    const { userId } = event.data;
    
    if (!userId) {
      this.logger.warn('User deletion event missing userId', { eventId: event.id });
      return;
    }

    // Invalidate all user-related cache entries
    await this.cacheService.invalidate({
      pattern: `user:${userId}:*`,
      namespace: 'users'
    });

    // Invalidate user list caches
    await this.cacheService.invalidate({
      pattern: 'users:findAll:*',
      namespace: 'users'
    });

    this.logger.debug({
      message: `Invalidated user caches for deletion`,
      userId,
      eventId: event.id
    });
  }

  /**
   * Handle permission change events - invalidate auth-related caches
   * @param event Permission change event
   */
  private async handlePermissionsChange(event: DomainEvent): Promise<void> {
    const { userId } = event.data;
    
    if (!userId) {
      this.logger.warn('Permission change event missing userId', { eventId: event.id });
      return;
    }

    // Invalidate user data that might include permissions
    await this.cacheService.invalidate({
      pattern: `user:${userId}:*`,
      namespace: 'users'
    });

    // Also invalidate auth caches in case they're cross-cached
    await this.cacheService.invalidate({
      pattern: `auth:user:${userId}:*`,
      namespace: 'auth'
    });

    this.logger.debug({
      message: `Invalidated user and auth caches for permission change`,
      userId,
      eventId: event.id
    });
  }
}