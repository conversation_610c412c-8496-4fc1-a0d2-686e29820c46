import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MockFactory } from '@libs/testing-utils';

describe('AppController', () => {
  let appController: AppController;
  let appService: AppService;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        AppService,
        {
          provide: 'LOGGER_FACTORY',
          useValue: MockFactory.createLoggerFactory(),
        },
      ],
    }).compile();

    appController = app.get<AppController>(AppController);
    appService = app.get<AppService>(AppService);
  });

  describe('root', () => {
    it('should return service info', () => {
      const result = appController.getInfo();
      expect(typeof result).toBe('string');
      expect(result).toContain('User Service');
    });

    it('should call appService.getInfo', () => {
      const spy = jest.spyOn(appService, 'getInfo');
      appController.getInfo();
      expect(spy).toHaveBeenCalled();
    });
  });
});
