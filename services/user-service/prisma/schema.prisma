// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  output        = "../../node_modules/.prisma/client"  // Generate directly in workspace root
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"] // Ensures compatibility across environments
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Base model with common fields for future entities
model BaseEntity {
  id        String    @id @default(uuid()) // Using uuid as per workplan example
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime? // Soft delete support
  version   Int       @default(1) // Optimistic locking
}

model User {
  id         String    @id @default(cuid())
  keycloakId String    @unique // Link to Keycloak user
  email      String    @unique
  firstName  String
  lastName   String
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime? // Soft delete support
  version    Int       @default(1) // Optimistic locking
  
  // Indexes for performance
  @@index([email])
  @@index([keycloakId])
  @@index([deletedAt]) // For efficient soft delete queries

  profile Profile? // Relation to Profile model (one-to-one)
}

model Profile {
  id     String @id @default(uuid())
  bio    String?
  userId String @unique // Foreign key to User
  user   User   @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
