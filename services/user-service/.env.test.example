# User Service Test Environment Variables

# Node Environment
NODE_ENV=test

# Server Configuration
PORT=3002

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/polyrepo_test

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json

# Observability Configuration
METRICS_ENABLED=true
TRACING_ENABLED=true
LOKI_HOST=http://localhost:3100
PROMETHEUS_HOST=http://localhost:9090
JAEGER_HOST=http://localhost:16686
JAEGER_AGENT_HOST=localhost
JAEGER_AGENT_PORT=6832
JAEGER_SAMPLER_TYPE=const
JAEGER_SAMPLER_PARAM=1
