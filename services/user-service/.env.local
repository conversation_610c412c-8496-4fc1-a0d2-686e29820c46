NODE_ENV=development
PORT=3002

# Database Connection (for local service connecting to Dockerized DB)
# Uses localhost and the externally mapped port for postgres_user_service (5433)
DB_HOST=localhost
DB_PORT=5433
DB_USERNAME=user
DB_PASSWORD=password
DB_DATABASE=user_service
DATABASE_URL="postgresql://user:password@localhost:5433/user_service?schema=public"

# Redis Configuration for Caching
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
CACHE_DEFAULT_TTL=300

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=false
LOKI_HOST=http://localhost:3100
JAEGER_ENDPOINT=http://localhost:14268/api/traces
