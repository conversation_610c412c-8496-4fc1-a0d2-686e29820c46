{"numFailedTestSuites": 2, "numFailedTests": 36, "numPassedTestSuites": 1, "numPassedTests": 20, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 3, "numTotalTests": 56, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1749336557350, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["AppController", "root"], "duration": 44, "failureDetails": [], "failureMessages": [], "fullName": "AppController root should return service info", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return service info"}, {"ancestorTitles": ["AppController", "root"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "AppController root should call appService.getInfo", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should call appService.getInfo"}], "endTime": 1749336567087, "message": "", "name": "/root/code/polyrepo/services/user-service/src/app.controller.spec.ts", "startTime": 1749336558488, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["UsersService"], "duration": 27, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "de4e9c50120e13d378666"}, "moduleRef": {"id": "8f2d9df158de4e9c50120"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService should be defined", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should be defined"}, {"ancestorTitles": ["UsersService", "createUser"], "duration": 4, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "a133abedb947990eca9e1"}, "moduleRef": {"id": "378666ee1da133abedb94"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService createUser should create a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should create a user successfully"}, {"ancestorTitles": ["UsersService", "createUser"], "duration": 4, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "7aa0a1853b9627d09f8a4"}, "moduleRef": {"id": "eca9e157577aa0a1853b9"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService createUser should handle unique constraint violation", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle unique constraint violation"}, {"ancestorTitles": ["UsersService", "createUser"], "duration": 4, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "0848180abd094227be1d9"}, "moduleRef": {"id": "09f8a4a5210848180abd0"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService createUser should handle unknown database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle unknown database errors"}, {"ancestorTitles": ["UsersService", "findAll"], "duration": 4, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "a96e51a5e8677284b7e7f"}, "moduleRef": {"id": "7be1d97889a96e51a5e86"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findAll should return all active users by default", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return all active users by default"}, {"ancestorTitles": ["UsersService", "findAll"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "09cb205708357e6b564d3"}, "moduleRef": {"id": "4b7e7fca7809cb2057083"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findAll should include deleted users when requested", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should include deleted users when requested"}, {"ancestorTitles": ["UsersService", "findAll"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "fb0e16ae5f5ed3f753ebc"}, "moduleRef": {"id": "b564d34586fb0e16ae5f5"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findAll should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "6cbcb2405f4416446f8f6"}, "moduleRef": {"id": "753ebc45d26cbcb2405f4"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findOne should return a user by id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return a user by id"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": 4, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "90153a04e8349896cbcac"}, "moduleRef": {"id": "46f8f6f86a90153a04e83"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findOne should return null for non-existent user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return null for non-existent user"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "1b8facb1a1474f35f98f6"}, "moduleRef": {"id": "6cbcac10451b8facb1a14"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findOne should include deleted users when requested", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should include deleted users when requested"}, {"ancestorTitles": ["UsersService", "findOne"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "0631b36a05fec8cea1d52"}, "moduleRef": {"id": "5f98f6d2250631b36a05f"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findOne should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "findByKeycloakId"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "f8895a26d3171bc714122"}, "moduleRef": {"id": "ea1d52f5f5f8895a26d31"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findByKeycloakId should return a user by keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return a user by keycloak id"}, {"ancestorTitles": ["UsersService", "findByKeycloakId"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "2a44c6e579aba0ce90b98"}, "moduleRef": {"id": "714122ca362a44c6e579a"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findByKeycloakId should return null for non-existent keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return null for non-existent keycloak id"}, {"ancestorTitles": ["UsersService", "findByKeycloakId"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "f03afbf02d5913a3d61da"}, "moduleRef": {"id": "e90b983da3f03afbf02d5"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService findByKeycloakId should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "b411d6b5b5b9eb6612aa9"}, "moduleRef": {"id": "3d61da04c3b411d6b5b5b"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService updateUser should update a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should update a user successfully"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": 10, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "5927e1eda0f2a11961814"}, "moduleRef": {"id": "612aa9a8de5927e1eda0f"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService updateUser should update with optimistic locking", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should update with optimistic locking"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "7eca7ab0cf15523251769"}, "moduleRef": {"id": "9618144b067eca7ab0cf1"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService updateUser should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user not found"}, {"ancestorTitles": ["UsersService", "updateUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "564fab98a97a9af5c3b32"}, "moduleRef": {"id": "2517698243564fab98a97"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService updateUser should handle version conflicts", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle version conflicts"}, {"ancestorTitles": ["UsersService", "remove (soft delete)"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "4525eaa23f05c8022912b"}, "moduleRef": {"id": "5c3b327fda4525eaa23f0"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService remove (soft delete) should soft delete a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should soft delete a user successfully"}, {"ancestorTitles": ["UsersService", "remove (soft delete)"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "36a228af475b4225da50f"}, "moduleRef": {"id": "22912b4db1474b8ec3ef3"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService remove (soft delete) should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user not found"}, {"ancestorTitles": ["UsersService", "remove (soft delete)"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "2325efafb4f63e734b557"}, "moduleRef": {"id": "5da50f18a72325efafb4f"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService remove (soft delete) should handle already deleted user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle already deleted user"}, {"ancestorTitles": ["UsersService", "restoreUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "6f62329bb697277ec20e3"}, "moduleRef": {"id": "34b557bb4a6f62329bb69"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService restoreUser should restore a soft deleted user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should restore a soft deleted user"}, {"ancestorTitles": ["UsersService", "restoreUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "f4d8d871f48d6338258dd"}, "moduleRef": {"id": "ec20e35d80f4d8d871f48"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService restoreUser should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user not found"}, {"ancestorTitles": ["UsersService", "restoreUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "ff259ae9054896e350ff9"}, "moduleRef": {"id": "8258dd0579ff259ae9054"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService restoreUser should handle user not deleted", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user not deleted"}, {"ancestorTitles": ["UsersService", "createInternalUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "873c3225f61421d19829a"}, "moduleRef": {"id": "350ff98996873c3225f61"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService createInternalUser should create an internal user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should create an internal user"}, {"ancestorTitles": ["UsersService", "getUserStats"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "7079c08dbc019ade6048a"}, "moduleRef": {"id": "19829aad747079c08dbc0"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService getUserStats should return user statistics", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should return user statistics"}, {"ancestorTitles": ["UsersService", "getUserStats"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "ce4461ee89ef60b2c2e75"}, "moduleRef": {"id": "e6048a18bcce4461ee89e"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService getUserStats should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "bulkSoftDelete"], "duration": 3, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "009ef8d43f6f05424527a"}, "moduleRef": {"id": "2c2e75c015009ef8d43f6"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService bulkSoftDelete should bulk soft delete users", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should bulk soft delete users"}, {"ancestorTitles": ["UsersService", "bulkSoftDelete"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "f597d7bfca5cd286a3e8f"}, "moduleRef": {"id": "24527af89cf597d7bfca5"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService bulkSoftDelete should handle empty user ids array", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle empty user ids array"}, {"ancestorTitles": ["UsersService", "bulkSoftDelete"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "4ca5995d90225b9467708"}, "moduleRef": {"id": "6a3e8fe36e4ca5995d902"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService bulkSoftDelete should handle database errors", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle database errors"}, {"ancestorTitles": ["UsersService", "hardDeleteUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "b4e7a0d4b13211efcf12a"}, "moduleRef": {"id": "467708ad5bb4e7a0d4b13"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService hardDeleteUser should permanently delete a user", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should permanently delete a user"}, {"ancestorTitles": ["UsersService", "hardDeleteUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "26889f169d6fb54495319"}, "moduleRef": {"id": "fcf12a88de26889f169d6"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService hardDeleteUser should delete with version check", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should delete with version check"}, {"ancestorTitles": ["UsersService", "hardDeleteUser"], "duration": 2, "failureDetails": [{"type": "UsersService", "context": {"index": 1, "dependencies": [null, null, "LOGGER_FACTORY", "EVENT_PUBLISHER"]}, "metadata": {"id": "02eb6938bb6999d8b5c0d"}, "moduleRef": {"id": "495319d46502eb6938bb6"}}], "failureMessages": ["Error: Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\nPotential solutions:\n- Is RootTestModule a valid NestJS module?\n- If CacheService is a provider, is it part of the current RootTestModule?\n- If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n  @Module({\n    imports: [ /* the Module containing CacheService */ ]\n  })\n\n    at TestingInjector.lookupComponentInParentModules (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:262:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:215:33)\n    at TestingInjector.resolveComponentInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-injector.js:19:45)\n    at resolveParam (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:129:38)\n    at async Promise.all (index 1)\n    at TestingInjector.resolveConstructorParams (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:144:27)\n    at TestingInjector.loadInstance (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:70:13)\n    at TestingInjector.loadProvider (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/injector.js:98:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:56:13\n    at async Promise.all (index 3)\n    at TestingInstanceLoader.createInstancesOfProviders (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n    at /root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:40:13\n    at async Promise.all (index 1)\n    at TestingInstanceLoader.createInstances (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n    at TestingInstanceLoader.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n    at TestingModuleBuilder.createInstancesOfDependencies (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n    at TestingModuleBuilder.compile (/root/code/polyrepo/services/user-service/node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts:41:35)"], "fullName": "UsersService hardDeleteUser should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle user not found"}], "endTime": 1749336570856, "message": "  ● UsersService › should be defined\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › createUser › should create a user successfully\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › createUser › should handle unique constraint violation\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › createUser › should handle unknown database errors\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findAll › should return all active users by default\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findAll › should include deleted users when requested\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findAll › should handle database errors\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findOne › should return a user by id\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findOne › should return null for non-existent user\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findOne › should include deleted users when requested\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findOne › should handle database errors\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findByKeycloakId › should return a user by keycloak id\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findByKeycloakId › should return null for non-existent keycloak id\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › findByKeycloakId › should handle database errors\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › updateUser › should update a user successfully\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › updateUser › should update with optimistic locking\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › updateUser › should handle user not found\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › updateUser › should handle version conflicts\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › remove (soft delete) › should soft delete a user successfully\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › remove (soft delete) › should handle user not found\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › remove (soft delete) › should handle already deleted user\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › restoreUser › should restore a soft deleted user\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › restoreUser › should handle user not found\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › restoreUser › should handle user not deleted\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › createInternalUser › should create an internal user\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › getUserStats › should return user statistics\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › getUserStats › should handle database errors\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › bulkSoftDelete › should bulk soft delete users\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › bulkSoftDelete › should handle empty user ids array\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › bulkSoftDelete › should handle database errors\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › hardDeleteUser › should permanently delete a user\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › hardDeleteUser › should delete with version check\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n\n  ● UsersService › hardDeleteUser › should handle user not found\n\n    Nest can't resolve dependencies of the UsersService (PrismaService, ?, LOGGER_FACTORY, EVENT_PUBLISHER). Please make sure that the argument CacheService at index [1] is available in the RootTestModule context.\n\n    Potential solutions:\n    - Is RootTestModule a valid NestJS module?\n    - If CacheService is a provider, is it part of the current RootTestModule?\n    - If CacheService is exported from a separate @Module, is that module imported within RootTestModule?\n      @Module({\n        imports: [ /* the Module containing CacheService */ ]\n      })\n\n      39 |\n      40 |   beforeEach(async () => {\n    > 41 |     const module: TestingModule = await Test.createTestingModule({\n         |                                   ^\n      42 |       providers: [\n      43 |         UsersService,\n      44 |         {\n\n      at TestingInjector.lookupComponentInParentModules (../node_modules/@nestjs/core/injector/injector.js:262:19)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/core/injector/injector.js:215:33)\n      at TestingInjector.resolveComponentInstance (../node_modules/@nestjs/testing/testing-injector.js:19:45)\n      at resolveParam (../node_modules/@nestjs/core/injector/injector.js:129:38)\n          at async Promise.all (index 1)\n      at TestingInjector.resolveConstructorParams (../node_modules/@nestjs/core/injector/injector.js:144:27)\n      at TestingInjector.loadInstance (../node_modules/@nestjs/core/injector/injector.js:70:13)\n      at TestingInjector.loadProvider (../node_modules/@nestjs/core/injector/injector.js:98:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:56:13\n          at async Promise.all (index 3)\n      at TestingInstanceLoader.createInstancesOfProviders (../node_modules/@nestjs/core/injector/instance-loader.js:55:9)\n      at ../node_modules/@nestjs/core/injector/instance-loader.js:40:13\n          at async Promise.all (index 1)\n      at TestingInstanceLoader.createInstances (../node_modules/@nestjs/core/injector/instance-loader.js:39:9)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/core/injector/instance-loader.js:22:13)\n      at TestingInstanceLoader.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-instance-loader.js:9:9)\n      at TestingModuleBuilder.createInstancesOfDependencies (../node_modules/@nestjs/testing/testing-module.builder.js:118:9)\n      at TestingModuleBuilder.compile (../node_modules/@nestjs/testing/testing-module.builder.js:74:9)\n      at Object.<anonymous> (users/users.service.spec.ts:41:35)\n", "name": "/root/code/polyrepo/services/user-service/src/users/users.service.spec.ts", "startTime": 1749336558464, "status": "failed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["UsersController"], "duration": 31, "failureDetails": [], "failureMessages": [], "fullName": "UsersController should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["UsersController", "createUser"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "UsersController createUser should create a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should create a user successfully"}, {"ancestorTitles": ["UsersController", "createUser"], "duration": 23, "failureDetails": [], "failureMessages": [], "fullName": "UsersController createUser should handle service errors", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle service errors"}, {"ancestorTitles": ["UsersController", "findAll"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findAll should return all users", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return all users"}, {"ancestorTitles": ["UsersController", "findAll"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findAll should return users including deleted when requested", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return users including deleted when requested"}, {"ancestorTitles": ["UsersController", "findOne"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findOne should return a user by id", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return a user by id"}, {"ancestorTitles": ["UsersController", "findOne"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findOne should handle user not found", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle user not found"}, {"ancestorTitles": ["UsersController", "findByKeycloakId"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findByKeycloakId should return a user by keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return a user by keycloak id"}, {"ancestorTitles": ["UsersController", "findByKeycloakId"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "UsersController findByKeycloakId should handle user not found by keycloak id", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle user not found by keycloak id"}, {"ancestorTitles": ["UsersController", "updateUser"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "UsersController updateUser should update a user", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should update a user"}, {"ancestorTitles": ["UsersController", "updateUser"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "UsersController updateUser should handle update errors", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle update errors"}, {"ancestorTitles": ["UsersController", "softDeleteUser"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "UsersController softDeleteUser should soft delete a user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should soft delete a user"}, {"ancestorTitles": ["UsersController", "softDeleteUser"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "UsersController softDeleteUser should handle soft delete errors", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle soft delete errors"}, {"ancestorTitles": ["UsersController", "createInternalUser"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "UsersController createInternalUser should create an internal user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should create an internal user"}, {"ancestorTitles": ["UsersController", "getUserStats"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "UsersController getUserStats should return user statistics", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return user statistics"}, {"ancestorTitles": ["UsersController", "restoreUser"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "UsersController restoreUser should restore a soft deleted user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should restore a soft deleted user"}, {"ancestorTitles": ["UsersController", "restoreUser"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "UsersController restoreUser should handle restore errors", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle restore errors"}, {"ancestorTitles": ["UsersController", "hardDeleteUser"], "duration": 10, "failureDetails": [{"matcherResult": {"actual": {"message": "User with ID 1 permanently deleted"}, "expected": {"message": "User permanently deleted successfully"}, "message": "expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n-   \"message\": \"User permanently deleted successfully\",\n+   \"message\": \"User with ID 1 permanently deleted\",\n  }", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n-   \"message\": \"User permanently deleted successfully\",\n+   \"message\": \"User with ID 1 permanently deleted\",\n  }\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.controller.spec.ts:276:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "UsersController hardDeleteUser should permanently delete a user", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should permanently delete a user"}, {"ancestorTitles": ["UsersController", "updateUserOptimistic"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "UsersController updateUserOptimistic should update user with optimistic locking", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should update user with optimistic locking"}, {"ancestorTitles": ["UsersController", "bulkSoftDelete"], "duration": 4, "failureDetails": [{"matcherResult": {"actual": {"deletedCount": 3, "message": "Successfully soft deleted 3 users"}, "expected": {"deletedCount": 3, "message": "Successfully deleted 3 users"}, "message": "expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"deletedCount\": 3,\n-   \"message\": \"Successfully deleted 3 users\",\n+   \"message\": \"Successfully soft deleted 3 users\",\n  }", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"deletedCount\": 3,\n-   \"message\": \"Successfully deleted 3 users\",\n+   \"message\": \"Successfully soft deleted 3 users\",\n  }\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.controller.spec.ts:305:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "UsersController bulkSoftDelete should bulk soft delete users", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should bulk soft delete users"}, {"ancestorTitles": ["UsersController", "bulkSoftDelete"], "duration": 3, "failureDetails": [{"matcherResult": {"actual": {"deletedCount": 0, "message": "Successfully soft deleted 0 users"}, "expected": {"deletedCount": 0, "message": "No users to delete"}, "message": "expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"deletedCount\": 0,\n-   \"message\": \"No users to delete\",\n+   \"message\": \"Successfully soft deleted 0 users\",\n  }", "name": "toEqual", "pass": false}}], "failureMessages": ["Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"deletedCount\": 0,\n-   \"message\": \"No users to delete\",\n+   \"message\": \"Successfully soft deleted 0 users\",\n  }\n    at Object.<anonymous> (/root/code/polyrepo/services/user-service/src/users/users.controller.spec.ts:317:22)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "UsersController bulkSoftDelete should handle empty user ids", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "failed", "title": "should handle empty user ids"}], "endTime": 1749336571469, "message": "  ● UsersController › hardDeleteUser › should permanently delete a user\n\n    expect(received).toEqual(expected) // deep equality\n\n    - Expected  - 1\n    + Received  + 1\n\n      Object {\n    -   \"message\": \"User permanently deleted successfully\",\n    +   \"message\": \"User with ID 1 permanently deleted\",\n      }\n\n      274 |\n      275 |       expect(service.hardDeleteUser).toHaveBeenCalledWith('1', 1);\n    > 276 |       expect(result).toEqual({ message: 'User permanently deleted successfully' });\n          |                      ^\n      277 |     });\n      278 |   });\n      279 |\n\n      at Object.<anonymous> (users/users.controller.spec.ts:276:22)\n\n  ● UsersController › bulkSoftDelete › should bulk soft delete users\n\n    expect(received).toEqual(expected) // deep equality\n\n    - Expected  - 1\n    + Received  + 1\n\n      Object {\n        \"deletedCount\": 3,\n    -   \"message\": \"Successfully deleted 3 users\",\n    +   \"message\": \"Successfully soft deleted 3 users\",\n      }\n\n      303 |\n      304 |       expect(service.bulkSoftDelete).toHaveBeenCalledWith(['1', '2', '3']);\n    > 305 |       expect(result).toEqual({ deletedCount, message: 'Successfully deleted 3 users' });\n          |                      ^\n      306 |     });\n      307 |\n      308 |     it('should handle empty user ids', async () => {\n\n      at Object.<anonymous> (users/users.controller.spec.ts:305:22)\n\n  ● UsersController › bulkSoftDelete › should handle empty user ids\n\n    expect(received).toEqual(expected) // deep equality\n\n    - Expected  - 1\n    + Received  + 1\n\n      Object {\n        \"deletedCount\": 0,\n    -   \"message\": \"No users to delete\",\n    +   \"message\": \"Successfully soft deleted 0 users\",\n      }\n\n      315 |\n      316 |       expect(service.bulkSoftDelete).toHaveBeenCalledWith([]);\n    > 317 |       expect(result).toEqual({ deletedCount, message: 'No users to delete' });\n          |                      ^\n      318 |     });\n      319 |   });\n      320 | });\n\n      at Object.<anonymous> (users/users.controller.spec.ts:317:22)\n", "name": "/root/code/polyrepo/services/user-service/src/users/users.controller.spec.ts", "startTime": 1749336558452, "status": "failed", "summary": ""}], "wasInterrupted": false, "coverageMap": {"/root/code/polyrepo/services/user-service/src/app.controller.ts": {"path": "/root/code/polyrepo/services/user-service/src/app.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 57}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 43}}, "2": {"start": {"line": 6, "column": 26}, "end": {"line": 22, "column": null}}, "3": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 33}}, "4": {"start": {"line": 11, "column": 47}, "end": {"line": 11, "column": 65}}, "5": {"start": {"line": 14, "column": 5}, "end": {"line": 14, "column": 71}}, "6": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 44}}, "7": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 37}}, "8": {"start": {"line": 6, "column": 13}, "end": {"line": 6, "column": 26}}, "9": {"start": {"line": 18, "column": 2}, "end": {"line": 21, "column": null}}, "10": {"start": {"line": 6, "column": 13}, "end": {"line": 22, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 11, "column": 65}, "end": {"line": 15, "column": 3}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 9}}, "loc": {"start": {"line": 18, "column": 9}, "end": {"line": 21, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 1, "9": 1, "10": 1}, "f": {"0": 2, "1": 2}, "b": {}}, "/root/code/polyrepo/services/user-service/src/app.service.ts": {"path": "/root/code/polyrepo/services/user-service/src/app.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 4, "column": 7}, "end": {"line": 8, "column": null}}, "2": {"start": {"line": 6, "column": 4}, "end": {"line": 6, "column": 71}}, "3": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 23}}, "4": {"start": {"line": 4, "column": 13}, "end": {"line": 8, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 9}}, "loc": {"start": {"line": 5, "column": 9}, "end": {"line": 7, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 2, "3": 1, "4": 1}, "f": {"0": 2}, "b": {}}, "/root/code/polyrepo/services/user-service/src/users/users.service.ts": {"path": "/root/code/polyrepo/services/user-service/src/users/users.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 141}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 72}}, "3": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 45}}, "4": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 63}}, "5": {"start": {"line": 12, "column": 25}, "end": {"line": 708, "column": null}}, "6": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 20}}, "7": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 35}}, "8": {"start": {"line": 16, "column": 47}, "end": {"line": 16, "column": 65}}, "9": {"start": {"line": 17, "column": 48}, "end": {"line": 17, "column": 78}}, "10": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 69}}, "11": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 63}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 60, "column": 5}}, "13": {"start": {"line": 36, "column": 50}, "end": {"line": 41, "column": 8}}, "14": {"start": {"line": 43, "column": 22}, "end": {"line": 43, "column": 74}}, "15": {"start": {"line": 46, "column": 6}, "end": {"line": 48, "column": 9}}, "16": {"start": {"line": 51, "column": 6}, "end": {"line": 54, "column": 8}}, "17": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 74}}, "18": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 21}}, "19": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 51}}, "20": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 87}}, "21": {"start": {"line": 72, "column": 4}, "end": {"line": 112, "column": 5}}, "22": {"start": {"line": 73, "column": 30}, "end": {"line": 99, "column": 8}}, "23": {"start": {"line": 75, "column": 24}, "end": {"line": 82, "column": 10}}, "24": {"start": {"line": 83, "column": 8}, "end": {"line": 83, "column": 82}}, "25": {"start": {"line": 86, "column": 8}, "end": {"line": 97, "column": 9}}, "26": {"start": {"line": 87, "column": 10}, "end": {"line": 87, "column": 87}}, "27": {"start": {"line": 88, "column": 10}, "end": {"line": 93, "column": 13}}, "28": {"start": {"line": 94, "column": 10}, "end": {"line": 94, "column": 91}}, "29": {"start": {"line": 96, "column": 10}, "end": {"line": 96, "column": 99}}, "30": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": 23}}, "31": {"start": {"line": 102, "column": 6}, "end": {"line": 105, "column": 8}}, "32": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 116}}, "33": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": 29}}, "34": {"start": {"line": 110, "column": 6}, "end": {"line": 110, "column": 145}}, "35": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": 64}}, "36": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 81}}, "37": {"start": {"line": 123, "column": 4}, "end": {"line": 132, "column": 5}}, "38": {"start": {"line": 125, "column": 6}, "end": {"line": 128, "column": 16}}, "39": {"start": {"line": 130, "column": 6}, "end": {"line": 130, "column": 111}}, "40": {"start": {"line": 131, "column": 6}, "end": {"line": 131, "column": 73}}, "41": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 91}}, "42": {"start": {"line": 145, "column": 4}, "end": {"line": 172, "column": 5}}, "43": {"start": {"line": 147, "column": 23}, "end": {"line": 147, "column": 72}}, "44": {"start": {"line": 150, "column": 21}, "end": {"line": 150, "column": 64}}, "45": {"start": {"line": 151, "column": 6}, "end": {"line": 154, "column": 7}}, "46": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 58}}, "47": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": 28}}, "48": {"start": {"line": 157, "column": 19}, "end": {"line": 160, "column": 15}}, "49": {"start": {"line": 163, "column": 6}, "end": {"line": 166, "column": 7}}, "50": {"start": {"line": 164, "column": 8}, "end": {"line": 164, "column": 57}}, "51": {"start": {"line": 165, "column": 8}, "end": {"line": 165, "column": 51}}, "52": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 18}}, "53": {"start": {"line": 170, "column": 6}, "end": {"line": 170, "column": 120}}, "54": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 86}}, "55": {"start": {"line": 183, "column": 4}, "end": {"line": 183, "column": 108}}, "56": {"start": {"line": 185, "column": 4}, "end": {"line": 212, "column": 5}}, "57": {"start": {"line": 187, "column": 23}, "end": {"line": 187, "column": 89}}, "58": {"start": {"line": 190, "column": 21}, "end": {"line": 190, "column": 64}}, "59": {"start": {"line": 191, "column": 6}, "end": {"line": 194, "column": 7}}, "60": {"start": {"line": 192, "column": 8}, "end": {"line": 192, "column": 70}}, "61": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": 28}}, "62": {"start": {"line": 197, "column": 19}, "end": {"line": 200, "column": 15}}, "63": {"start": {"line": 203, "column": 6}, "end": {"line": 206, "column": 7}}, "64": {"start": {"line": 204, "column": 8}, "end": {"line": 204, "column": 57}}, "65": {"start": {"line": 205, "column": 8}, "end": {"line": 205, "column": 73}}, "66": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 18}}, "67": {"start": {"line": 210, "column": 6}, "end": {"line": 210, "column": 137}}, "68": {"start": {"line": 211, "column": 6}, "end": {"line": 211, "column": 103}}, "69": {"start": {"line": 223, "column": 4}, "end": {"line": 223, "column": 119}}, "70": {"start": {"line": 225, "column": 4}, "end": {"line": 270, "column": 5}}, "71": {"start": {"line": 227, "column": 27}, "end": {"line": 227, "column": 86}}, "72": {"start": {"line": 229, "column": 6}, "end": {"line": 232, "column": 7}}, "73": {"start": {"line": 230, "column": 8}, "end": {"line": 230, "column": 71}}, "74": {"start": {"line": 231, "column": 8}, "end": {"line": 231, "column": 68}}, "75": {"start": {"line": 235, "column": 6}, "end": {"line": 238, "column": 7}}, "76": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": 118}}, "77": {"start": {"line": 237, "column": 8}, "end": {"line": 237, "column": 166}}, "78": {"start": {"line": 241, "column": 50}, "end": {"line": 241, "column": 52}}, "79": {"start": {"line": 242, "column": 6}, "end": {"line": 242, "column": 79}}, "80": {"start": {"line": 242, "column": 40}, "end": {"line": 242, "column": 79}}, "81": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 76}}, "82": {"start": {"line": 243, "column": 39}, "end": {"line": 243, "column": 76}}, "83": {"start": {"line": 244, "column": 6}, "end": {"line": 244, "column": 67}}, "84": {"start": {"line": 244, "column": 36}, "end": {"line": 244, "column": 67}}, "85": {"start": {"line": 247, "column": 6}, "end": {"line": 247, "column": 45}}, "86": {"start": {"line": 249, "column": 26}, "end": {"line": 252, "column": 15}}, "87": {"start": {"line": 255, "column": 6}, "end": {"line": 255, "column": 50}}, "88": {"start": {"line": 258, "column": 6}, "end": {"line": 261, "column": 8}}, "89": {"start": {"line": 263, "column": 6}, "end": {"line": 263, "column": 103}}, "90": {"start": {"line": 264, "column": 6}, "end": {"line": 264, "column": 25}}, "91": {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, "92": {"start": {"line": 267, "column": 8}, "end": {"line": 267, "column": 20}}, "93": {"start": {"line": 269, "column": 6}, "end": {"line": 269, "column": 51}}, "94": {"start": {"line": 279, "column": 4}, "end": {"line": 279, "column": 124}}, "95": {"start": {"line": 281, "column": 4}, "end": {"line": 319, "column": 5}}, "96": {"start": {"line": 283, "column": 27}, "end": {"line": 285, "column": 8}}, "97": {"start": {"line": 287, "column": 6}, "end": {"line": 290, "column": 7}}, "98": {"start": {"line": 288, "column": 8}, "end": {"line": 288, "column": 76}}, "99": {"start": {"line": 289, "column": 8}, "end": {"line": 289, "column": 68}}, "100": {"start": {"line": 293, "column": 6}, "end": {"line": 296, "column": 7}}, "101": {"start": {"line": 294, "column": 8}, "end": {"line": 294, "column": 118}}, "102": {"start": {"line": 295, "column": 8}, "end": {"line": 295, "column": 166}}, "103": {"start": {"line": 298, "column": 6}, "end": {"line": 304, "column": 9}}, "104": {"start": {"line": 307, "column": 6}, "end": {"line": 310, "column": 8}}, "105": {"start": {"line": 312, "column": 6}, "end": {"line": 312, "column": 71}}, "106": {"start": {"line": 314, "column": 6}, "end": {"line": 316, "column": 7}}, "107": {"start": {"line": 315, "column": 8}, "end": {"line": 315, "column": 20}}, "108": {"start": {"line": 317, "column": 6}, "end": {"line": 317, "column": 122}}, "109": {"start": {"line": 318, "column": 6}, "end": {"line": 318, "column": 84}}, "110": {"start": {"line": 328, "column": 4}, "end": {"line": 328, "column": 124}}, "111": {"start": {"line": 330, "column": 4}, "end": {"line": 366, "column": 5}}, "112": {"start": {"line": 332, "column": 27}, "end": {"line": 335, "column": 15}}, "113": {"start": {"line": 337, "column": 6}, "end": {"line": 340, "column": 7}}, "114": {"start": {"line": 338, "column": 8}, "end": {"line": 338, "column": 75}}, "115": {"start": {"line": 339, "column": 8}, "end": {"line": 339, "column": 68}}, "116": {"start": {"line": 343, "column": 6}, "end": {"line": 346, "column": 7}}, "117": {"start": {"line": 344, "column": 8}, "end": {"line": 344, "column": 118}}, "118": {"start": {"line": 345, "column": 8}, "end": {"line": 345, "column": 166}}, "119": {"start": {"line": 348, "column": 6}, "end": {"line": 351, "column": 16}}, "120": {"start": {"line": 354, "column": 6}, "end": {"line": 357, "column": 8}}, "121": {"start": {"line": 359, "column": 6}, "end": {"line": 359, "column": 71}}, "122": {"start": {"line": 361, "column": 6}, "end": {"line": 363, "column": 7}}, "123": {"start": {"line": 362, "column": 8}, "end": {"line": 362, "column": 20}}, "124": {"start": {"line": 364, "column": 6}, "end": {"line": 364, "column": 127}}, "125": {"start": {"line": 365, "column": 6}, "end": {"line": 365, "column": 89}}, "126": {"start": {"line": 375, "column": 4}, "end": {"line": 375, "column": 53}}, "127": {"start": {"line": 377, "column": 4}, "end": {"line": 419, "column": 5}}, "128": {"start": {"line": 379, "column": 27}, "end": {"line": 382, "column": 15}}, "129": {"start": {"line": 384, "column": 6}, "end": {"line": 387, "column": 7}}, "130": {"start": {"line": 385, "column": 8}, "end": {"line": 385, "column": 57}}, "131": {"start": {"line": 386, "column": 8}, "end": {"line": 386, "column": 68}}, "132": {"start": {"line": 389, "column": 6}, "end": {"line": 392, "column": 7}}, "133": {"start": {"line": 390, "column": 8}, "end": {"line": 390, "column": 62}}, "134": {"start": {"line": 391, "column": 8}, "end": {"line": 391, "column": 75}}, "135": {"start": {"line": 396, "column": 27}, "end": {"line": 403, "column": 15}}, "136": {"start": {"line": 406, "column": 6}, "end": {"line": 409, "column": 8}}, "137": {"start": {"line": 411, "column": 6}, "end": {"line": 411, "column": 67}}, "138": {"start": {"line": 412, "column": 6}, "end": {"line": 412, "column": 26}}, "139": {"start": {"line": 414, "column": 6}, "end": {"line": 416, "column": 7}}, "140": {"start": {"line": 415, "column": 8}, "end": {"line": 415, "column": 20}}, "141": {"start": {"line": 417, "column": 6}, "end": {"line": 417, "column": 123}}, "142": {"start": {"line": 418, "column": 6}, "end": {"line": 418, "column": 85}}, "143": {"start": {"line": 428, "column": 4}, "end": {"line": 428, "column": 69}}, "144": {"start": {"line": 430, "column": 4}, "end": {"line": 452, "column": 5}}, "145": {"start": {"line": 431, "column": 6}, "end": {"line": 448, "column": 9}}, "146": {"start": {"line": 433, "column": 52}, "end": {"line": 438, "column": 10}}, "147": {"start": {"line": 440, "column": 24}, "end": {"line": 440, "column": 67}}, "148": {"start": {"line": 446, "column": 8}, "end": {"line": 446, "column": 85}}, "149": {"start": {"line": 447, "column": 8}, "end": {"line": 447, "column": 23}}, "150": {"start": {"line": 450, "column": 6}, "end": {"line": 450, "column": 123}}, "151": {"start": {"line": 451, "column": 6}, "end": {"line": 451, "column": 68}}, "152": {"start": {"line": 461, "column": 4}, "end": {"line": 461, "column": 66}}, "153": {"start": {"line": 463, "column": 4}, "end": {"line": 484, "column": 5}}, "154": {"start": {"line": 464, "column": 21}, "end": {"line": 478, "column": 8}}, "155": {"start": {"line": 465, "column": 29}, "end": {"line": 474, "column": 10}}, "156": {"start": {"line": 476, "column": 8}, "end": {"line": 476, "column": 73}}, "157": {"start": {"line": 477, "column": 8}, "end": {"line": 477, "column": 34}}, "158": {"start": {"line": 480, "column": 6}, "end": {"line": 480, "column": 20}}, "159": {"start": {"line": 482, "column": 6}, "end": {"line": 482, "column": 119}}, "160": {"start": {"line": 483, "column": 6}, "end": {"line": 483, "column": 76}}, "161": {"start": {"line": 496, "column": 4}, "end": {"line": 496, "column": 47}}, "162": {"start": {"line": 498, "column": 4}, "end": {"line": 509, "column": 5}}, "163": {"start": {"line": 499, "column": 39}, "end": {"line": 503, "column": 8}}, "164": {"start": {"line": 505, "column": 6}, "end": {"line": 505, "column": 40}}, "165": {"start": {"line": 507, "column": 6}, "end": {"line": 507, "column": 116}}, "166": {"start": {"line": 508, "column": 6}, "end": {"line": 508, "column": 78}}, "167": {"start": {"line": 520, "column": 4}, "end": {"line": 539, "column": 7}}, "168": {"start": {"line": 521, "column": 6}, "end": {"line": 538, "column": 7}}, "169": {"start": {"line": 522, "column": 8}, "end": {"line": 522, "column": 49}}, "170": {"start": {"line": 523, "column": 8}, "end": {"line": 528, "column": 11}}, "171": {"start": {"line": 530, "column": 8}, "end": {"line": 536, "column": 11}}, "172": {"start": {"line": 547, "column": 4}, "end": {"line": 564, "column": 5}}, "173": {"start": {"line": 549, "column": 6}, "end": {"line": 551, "column": 9}}, "174": {"start": {"line": 554, "column": 6}, "end": {"line": 558, "column": 7}}, "175": {"start": {"line": 555, "column": 8}, "end": {"line": 557, "column": 11}}, "176": {"start": {"line": 560, "column": 6}, "end": {"line": 560, "column": 69}}, "177": {"start": {"line": 562, "column": 6}, "end": {"line": 562, "column": 105}}, "178": {"start": {"line": 567, "column": 4}, "end": {"line": 567, "column": 108}}, "179": {"start": {"line": 570, "column": 48}, "end": {"line": 575, "column": 6}}, "180": {"start": {"line": 577, "column": 4}, "end": {"line": 590, "column": 5}}, "181": {"start": {"line": 578, "column": 22}, "end": {"line": 578, "column": 74}}, "182": {"start": {"line": 581, "column": 6}, "end": {"line": 584, "column": 8}}, "183": {"start": {"line": 586, "column": 6}, "end": {"line": 586, "column": 122}}, "184": {"start": {"line": 587, "column": 6}, "end": {"line": 587, "column": 21}}, "185": {"start": {"line": 589, "column": 6}, "end": {"line": 589, "column": 60}}, "186": {"start": {"line": 599, "column": 4}, "end": {"line": 599, "column": 57}}, "187": {"start": {"line": 600, "column": 4}, "end": {"line": 611, "column": 5}}, "188": {"start": {"line": 603, "column": 26}, "end": {"line": 603, "column": 81}}, "189": {"start": {"line": 604, "column": 6}, "end": {"line": 606, "column": 7}}, "190": {"start": {"line": 605, "column": 8}, "end": {"line": 605, "column": 85}}, "191": {"start": {"line": 607, "column": 6}, "end": {"line": 607, "column": 71}}, "192": {"start": {"line": 608, "column": 6}, "end": {"line": 608, "column": 25}}, "193": {"start": {"line": 610, "column": 6}, "end": {"line": 610, "column": 70}}, "194": {"start": {"line": 620, "column": 4}, "end": {"line": 620, "column": 57}}, "195": {"start": {"line": 621, "column": 4}, "end": {"line": 634, "column": 5}}, "196": {"start": {"line": 623, "column": 26}, "end": {"line": 626, "column": 15}}, "197": {"start": {"line": 627, "column": 6}, "end": {"line": 629, "column": 7}}, "198": {"start": {"line": 628, "column": 8}, "end": {"line": 628, "column": 85}}, "199": {"start": {"line": 630, "column": 6}, "end": {"line": 630, "column": 71}}, "200": {"start": {"line": 631, "column": 6}, "end": {"line": 631, "column": 25}}, "201": {"start": {"line": 633, "column": 6}, "end": {"line": 633, "column": 70}}, "202": {"start": {"line": 643, "column": 4}, "end": {"line": 643, "column": 53}}, "203": {"start": {"line": 644, "column": 4}, "end": {"line": 670, "column": 5}}, "204": {"start": {"line": 646, "column": 28}, "end": {"line": 649, "column": 15}}, "205": {"start": {"line": 651, "column": 6}, "end": {"line": 653, "column": 7}}, "206": {"start": {"line": 652, "column": 8}, "end": {"line": 652, "column": 69}}, "207": {"start": {"line": 655, "column": 6}, "end": {"line": 657, "column": 7}}, "208": {"start": {"line": 656, "column": 8}, "end": {"line": 656, "column": 81}}, "209": {"start": {"line": 661, "column": 27}, "end": {"line": 665, "column": 15}}, "210": {"start": {"line": 666, "column": 6}, "end": {"line": 666, "column": 67}}, "211": {"start": {"line": 667, "column": 6}, "end": {"line": 667, "column": 26}}, "212": {"start": {"line": 669, "column": 6}, "end": {"line": 669, "column": 66}}, "213": {"start": {"line": 674, "column": 25}, "end": {"line": 674, "column": 52}}, "214": {"start": {"line": 675, "column": 23}, "end": {"line": 675, "column": 48}}, "215": {"start": {"line": 677, "column": 4}, "end": {"line": 677, "column": 77}}, "216": {"start": {"line": 679, "column": 4}, "end": {"line": 686, "column": 5}}, "217": {"start": {"line": 681, "column": 6}, "end": {"line": 685, "column": 7}}, "218": {"start": {"line": 682, "column": 23}, "end": {"line": 682, "column": 53}}, "219": {"start": {"line": 683, "column": 8}, "end": {"line": 683, "column": 90}}, "220": {"start": {"line": 684, "column": 8}, "end": {"line": 684, "column": 94}}, "221": {"start": {"line": 688, "column": 4}, "end": {"line": 688, "column": 70}}, "222": {"start": {"line": 697, "column": 4}, "end": {"line": 697, "column": 66}}, "223": {"start": {"line": 706, "column": 4}, "end": {"line": 706, "column": 60}}, "224": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 25}}, "225": {"start": {"line": 12, "column": 13}, "end": {"line": 708, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}, "loc": {"start": {"line": 17, "column": 78}, "end": {"line": 21, "column": 3}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 7}}, "loc": {"start": {"line": 31, "column": 38}, "end": {"line": 61, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 7}}, "loc": {"start": {"line": 69, "column": 49}, "end": {"line": 113, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 73, "column": 61}, "end": {"line": 73, "column": 66}}, "loc": {"start": {"line": 73, "column": 74}, "end": {"line": 99, "column": 7}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 7}}, "loc": {"start": {"line": 120, "column": 51}, "end": {"line": 133, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 142, "column": 2}, "end": {"line": 142, "column": 7}}, "loc": {"start": {"line": 142, "column": 63}, "end": {"line": 173, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 182, "column": 2}, "end": {"line": 182, "column": 7}}, "loc": {"start": {"line": 182, "column": 80}, "end": {"line": 213, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 222, "column": 2}, "end": {"line": 222, "column": 7}}, "loc": {"start": {"line": 222, "column": 76}, "end": {"line": 271, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 278, "column": 2}, "end": {"line": 278, "column": 7}}, "loc": {"start": {"line": 278, "column": 55}, "end": {"line": 320, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 327, "column": 2}, "end": {"line": 327, "column": 7}}, "loc": {"start": {"line": 327, "column": 59}, "end": {"line": 367, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 374, "column": 2}, "end": {"line": 374, "column": 7}}, "loc": {"start": {"line": 374, "column": 30}, "end": {"line": 420, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 427, "column": 2}, "end": {"line": 427, "column": 7}}, "loc": {"start": {"line": 427, "column": 53}, "end": {"line": 453, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 431, "column": 44}, "end": {"line": 431, "column": 49}}, "loc": {"start": {"line": 431, "column": 57}, "end": {"line": 448, "column": 7}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 460, "column": 2}, "end": {"line": 460, "column": 7}}, "loc": {"start": {"line": 460, "column": 40}, "end": {"line": 485, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 464, "column": 52}, "end": {"line": 464, "column": 57}}, "loc": {"start": {"line": 464, "column": 65}, "end": {"line": 478, "column": 7}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 491, "column": 2}, "end": {"line": 491, "column": 7}}, "loc": {"start": {"line": 491, "column": 20}, "end": {"line": 510, "column": 3}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 518, "column": 10}, "end": {"line": 518, "column": 28}}, "loc": {"start": {"line": 518, "column": 65}, "end": {"line": 540, "column": 3}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 520, "column": 17}, "end": {"line": 520, "column": 22}}, "loc": {"start": {"line": 520, "column": 28}, "end": {"line": 539, "column": 5}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 546, "column": 10}, "end": {"line": 546, "column": 15}}, "loc": {"start": {"line": 546, "column": 46}, "end": {"line": 565, "column": 3}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 566, "column": 2}, "end": {"line": 566, "column": 7}}, "loc": {"start": {"line": 566, "column": 54}, "end": {"line": 591, "column": 3}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 598, "column": 2}, "end": {"line": 598, "column": 7}}, "loc": {"start": {"line": 598, "column": 25}, "end": {"line": 612, "column": 3}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 619, "column": 2}, "end": {"line": 619, "column": 7}}, "loc": {"start": {"line": 619, "column": 29}, "end": {"line": 635, "column": 3}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 642, "column": 2}, "end": {"line": 642, "column": 7}}, "loc": {"start": {"line": 642, "column": 26}, "end": {"line": 671, "column": 3}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 673, "column": 10}, "end": {"line": 673, "column": 27}}, "loc": {"start": {"line": 673, "column": 57}, "end": {"line": 689, "column": 3}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 696, "column": 10}, "end": {"line": 696, "column": 25}}, "loc": {"start": {"line": 696, "column": 36}, "end": {"line": 698, "column": 3}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 705, "column": 10}, "end": {"line": 705, "column": 23}}, "loc": {"start": {"line": 705, "column": 34}, "end": {"line": 707, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 86, "column": 8}, "end": {"line": 97, "column": 9}}, "type": "if", "locations": [{"start": {"line": 86, "column": 8}, "end": {"line": 97, "column": 9}}, {"start": {"line": 95, "column": 15}, "end": {"line": 97, "column": 9}}]}, "1": {"loc": {"start": {"line": 120, "column": 16}, "end": {"line": 120, "column": 51}}, "type": "default-arg", "locations": [{"start": {"line": 120, "column": 46}, "end": {"line": 120, "column": 51}}]}, "2": {"loc": {"start": {"line": 142, "column": 28}, "end": {"line": 142, "column": 63}}, "type": "default-arg", "locations": [{"start": {"line": 142, "column": 58}, "end": {"line": 142, "column": 63}}]}, "3": {"loc": {"start": {"line": 151, "column": 6}, "end": {"line": 154, "column": 7}}, "type": "if", "locations": [{"start": {"line": 151, "column": 6}, "end": {"line": 154, "column": 7}}]}, "4": {"loc": {"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 20}}, {"start": {"line": 151, "column": 24}, "end": {"line": 151, "column": 36}}]}, "5": {"loc": {"start": {"line": 163, "column": 6}, "end": {"line": 166, "column": 7}}, "type": "if", "locations": [{"start": {"line": 163, "column": 6}, "end": {"line": 166, "column": 7}}]}, "6": {"loc": {"start": {"line": 182, "column": 45}, "end": {"line": 182, "column": 80}}, "type": "default-arg", "locations": [{"start": {"line": 182, "column": 75}, "end": {"line": 182, "column": 80}}]}, "7": {"loc": {"start": {"line": 191, "column": 6}, "end": {"line": 194, "column": 7}}, "type": "if", "locations": [{"start": {"line": 191, "column": 6}, "end": {"line": 194, "column": 7}}]}, "8": {"loc": {"start": {"line": 191, "column": 10}, "end": {"line": 191, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 191, "column": 10}, "end": {"line": 191, "column": 20}}, {"start": {"line": 191, "column": 24}, "end": {"line": 191, "column": 36}}]}, "9": {"loc": {"start": {"line": 203, "column": 6}, "end": {"line": 206, "column": 7}}, "type": "if", "locations": [{"start": {"line": 203, "column": 6}, "end": {"line": 206, "column": 7}}]}, "10": {"loc": {"start": {"line": 223, "column": 51}, "end": {"line": 223, "column": 115}}, "type": "cond-expr", "locations": [{"start": {"line": 223, "column": 69}, "end": {"line": 223, "column": 110}}, {"start": {"line": 223, "column": 113}, "end": {"line": 223, "column": 115}}]}, "11": {"loc": {"start": {"line": 229, "column": 6}, "end": {"line": 232, "column": 7}}, "type": "if", "locations": [{"start": {"line": 229, "column": 6}, "end": {"line": 232, "column": 7}}]}, "12": {"loc": {"start": {"line": 235, "column": 6}, "end": {"line": 238, "column": 7}}, "type": "if", "locations": [{"start": {"line": 235, "column": 6}, "end": {"line": 238, "column": 7}}]}, "13": {"loc": {"start": {"line": 235, "column": 10}, "end": {"line": 235, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 235, "column": 10}, "end": {"line": 235, "column": 39}}, {"start": {"line": 235, "column": 43}, "end": {"line": 235, "column": 83}}]}, "14": {"loc": {"start": {"line": 242, "column": 6}, "end": {"line": 242, "column": 79}}, "type": "if", "locations": [{"start": {"line": 242, "column": 6}, "end": {"line": 242, "column": 79}}]}, "15": {"loc": {"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 76}}, "type": "if", "locations": [{"start": {"line": 243, "column": 6}, "end": {"line": 243, "column": 76}}]}, "16": {"loc": {"start": {"line": 244, "column": 6}, "end": {"line": 244, "column": 67}}, "type": "if", "locations": [{"start": {"line": 244, "column": 6}, "end": {"line": 244, "column": 67}}]}, "17": {"loc": {"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}, "type": "if", "locations": [{"start": {"line": 266, "column": 6}, "end": {"line": 268, "column": 7}}]}, "18": {"loc": {"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 82}}, "type": "binary-expr", "locations": [{"start": {"line": 266, "column": 10}, "end": {"line": 266, "column": 44}}, {"start": {"line": 266, "column": 48}, "end": {"line": 266, "column": 82}}]}, "19": {"loc": {"start": {"line": 279, "column": 56}, "end": {"line": 279, "column": 120}}, "type": "cond-expr", "locations": [{"start": {"line": 279, "column": 74}, "end": {"line": 279, "column": 115}}, {"start": {"line": 279, "column": 118}, "end": {"line": 279, "column": 120}}]}, "20": {"loc": {"start": {"line": 287, "column": 6}, "end": {"line": 290, "column": 7}}, "type": "if", "locations": [{"start": {"line": 287, "column": 6}, "end": {"line": 290, "column": 7}}]}, "21": {"loc": {"start": {"line": 293, "column": 6}, "end": {"line": 296, "column": 7}}, "type": "if", "locations": [{"start": {"line": 293, "column": 6}, "end": {"line": 296, "column": 7}}]}, "22": {"loc": {"start": {"line": 293, "column": 10}, "end": {"line": 293, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 293, "column": 10}, "end": {"line": 293, "column": 39}}, {"start": {"line": 293, "column": 43}, "end": {"line": 293, "column": 83}}]}, "23": {"loc": {"start": {"line": 314, "column": 6}, "end": {"line": 316, "column": 7}}, "type": "if", "locations": [{"start": {"line": 314, "column": 6}, "end": {"line": 316, "column": 7}}]}, "24": {"loc": {"start": {"line": 314, "column": 10}, "end": {"line": 314, "column": 82}}, "type": "binary-expr", "locations": [{"start": {"line": 314, "column": 10}, "end": {"line": 314, "column": 44}}, {"start": {"line": 314, "column": 48}, "end": {"line": 314, "column": 82}}]}, "25": {"loc": {"start": {"line": 328, "column": 56}, "end": {"line": 328, "column": 120}}, "type": "cond-expr", "locations": [{"start": {"line": 328, "column": 74}, "end": {"line": 328, "column": 115}}, {"start": {"line": 328, "column": 118}, "end": {"line": 328, "column": 120}}]}, "26": {"loc": {"start": {"line": 337, "column": 6}, "end": {"line": 340, "column": 7}}, "type": "if", "locations": [{"start": {"line": 337, "column": 6}, "end": {"line": 340, "column": 7}}]}, "27": {"loc": {"start": {"line": 343, "column": 6}, "end": {"line": 346, "column": 7}}, "type": "if", "locations": [{"start": {"line": 343, "column": 6}, "end": {"line": 346, "column": 7}}]}, "28": {"loc": {"start": {"line": 343, "column": 10}, "end": {"line": 343, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 343, "column": 10}, "end": {"line": 343, "column": 39}}, {"start": {"line": 343, "column": 43}, "end": {"line": 343, "column": 83}}]}, "29": {"loc": {"start": {"line": 361, "column": 6}, "end": {"line": 363, "column": 7}}, "type": "if", "locations": [{"start": {"line": 361, "column": 6}, "end": {"line": 363, "column": 7}}]}, "30": {"loc": {"start": {"line": 361, "column": 10}, "end": {"line": 361, "column": 82}}, "type": "binary-expr", "locations": [{"start": {"line": 361, "column": 10}, "end": {"line": 361, "column": 44}}, {"start": {"line": 361, "column": 48}, "end": {"line": 361, "column": 82}}]}, "31": {"loc": {"start": {"line": 384, "column": 6}, "end": {"line": 387, "column": 7}}, "type": "if", "locations": [{"start": {"line": 384, "column": 6}, "end": {"line": 387, "column": 7}}]}, "32": {"loc": {"start": {"line": 389, "column": 6}, "end": {"line": 392, "column": 7}}, "type": "if", "locations": [{"start": {"line": 389, "column": 6}, "end": {"line": 392, "column": 7}}]}, "33": {"loc": {"start": {"line": 414, "column": 6}, "end": {"line": 416, "column": 7}}, "type": "if", "locations": [{"start": {"line": 414, "column": 6}, "end": {"line": 416, "column": 7}}]}, "34": {"loc": {"start": {"line": 414, "column": 10}, "end": {"line": 414, "column": 84}}, "type": "binary-expr", "locations": [{"start": {"line": 414, "column": 10}, "end": {"line": 414, "column": 44}}, {"start": {"line": 414, "column": 48}, "end": {"line": 414, "column": 84}}]}, "35": {"loc": {"start": {"line": 535, "column": 17}, "end": {"line": 535, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 535, "column": 42}, "end": {"line": 535, "column": 55}}, {"start": {"line": 535, "column": 58}, "end": {"line": 535, "column": 71}}]}, "36": {"loc": {"start": {"line": 554, "column": 6}, "end": {"line": 558, "column": 7}}, "type": "if", "locations": [{"start": {"line": 554, "column": 6}, "end": {"line": 558, "column": 7}}]}, "37": {"loc": {"start": {"line": 604, "column": 6}, "end": {"line": 606, "column": 7}}, "type": "if", "locations": [{"start": {"line": 604, "column": 6}, "end": {"line": 606, "column": 7}}]}, "38": {"loc": {"start": {"line": 627, "column": 6}, "end": {"line": 629, "column": 7}}, "type": "if", "locations": [{"start": {"line": 627, "column": 6}, "end": {"line": 629, "column": 7}}]}, "39": {"loc": {"start": {"line": 651, "column": 6}, "end": {"line": 653, "column": 7}}, "type": "if", "locations": [{"start": {"line": 651, "column": 6}, "end": {"line": 653, "column": 7}}]}, "40": {"loc": {"start": {"line": 655, "column": 6}, "end": {"line": 657, "column": 7}}, "type": "if", "locations": [{"start": {"line": 655, "column": 6}, "end": {"line": 657, "column": 7}}]}, "41": {"loc": {"start": {"line": 679, "column": 4}, "end": {"line": 686, "column": 5}}, "type": "if", "locations": [{"start": {"line": 679, "column": 4}, "end": {"line": 686, "column": 5}}]}, "42": {"loc": {"start": {"line": 681, "column": 6}, "end": {"line": 685, "column": 7}}, "type": "if", "locations": [{"start": {"line": 681, "column": 6}, "end": {"line": 685, "column": 7}}]}, "43": {"loc": {"start": {"line": 697, "column": 11}, "end": {"line": 697, "column": 65}}, "type": "cond-expr", "locations": [{"start": {"line": 697, "column": 36}, "end": {"line": 697, "column": 49}}, {"start": {"line": 697, "column": 52}, "end": {"line": 697, "column": 65}}]}, "44": {"loc": {"start": {"line": 706, "column": 11}, "end": {"line": 706, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 706, "column": 36}, "end": {"line": 706, "column": 47}}, {"start": {"line": 706, "column": 50}, "end": {"line": 706, "column": 59}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "204": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "213": 0, "214": 0, "215": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 2, "225": 2}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "b": {"0": [0, 0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0], "6": [0], "7": [0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0], "12": [0], "13": [0, 0], "14": [0], "15": [0], "16": [0], "17": [0], "18": [0, 0], "19": [0, 0], "20": [0], "21": [0], "22": [0, 0], "23": [0], "24": [0, 0], "25": [0, 0], "26": [0], "27": [0], "28": [0, 0], "29": [0], "30": [0, 0], "31": [0], "32": [0], "33": [0], "34": [0, 0], "35": [0, 0], "36": [0], "37": [0], "38": [0], "39": [0], "40": [0], "41": [0], "42": [0], "43": [0, 0], "44": [0, 0]}}, "/root/code/polyrepo/services/user-service/src/prisma/index.ts": {"path": "/root/code/polyrepo/services/user-service/src/prisma/index.ts", "statementMap": {"0": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 33}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 46}}, "2": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 9}}, "3": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 94}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 38}}, "loc": {"start": {"line": 11, "column": 9}, "end": {"line": 11, "column": 94}}}}, "branchMap": {}, "s": {"0": 2, "1": 2, "2": 2, "3": 2}, "f": {"0": 0}, "b": {}}, "/root/code/polyrepo/services/user-service/src/prisma/prisma.service.ts": {"path": "/root/code/polyrepo/services/user-service/src/prisma/prisma.service.ts", "statementMap": {"0": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 66}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 61}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 58}}, "3": {"start": {"line": 12, "column": 7}, "end": {"line": 94, "column": null}}, "4": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 12}}, "5": {"start": {"line": 16, "column": 47}, "end": {"line": 16, "column": 65}}, "6": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 67}}, "7": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": 38}}, "8": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 60}}, "9": {"start": {"line": 41, "column": 4}, "end": {"line": 56, "column": 5}}, "10": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 28}}, "11": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 71}}, "12": {"start": {"line": 48, "column": 6}, "end": {"line": 52, "column": 8}}, "13": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 18}}, "14": {"start": {"line": 64, "column": 4}, "end": {"line": 92, "column": 5}}, "15": {"start": {"line": 65, "column": 20}, "end": {"line": 65, "column": 30}}, "16": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 37}}, "17": {"start": {"line": 70, "column": 18}, "end": {"line": 70, "column": 28}}, "18": {"start": {"line": 71, "column": 27}, "end": {"line": 71, "column": 38}}, "19": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 78}}, "20": {"start": {"line": 75, "column": 6}, "end": {"line": 78, "column": 8}}, "21": {"start": {"line": 80, "column": 27}, "end": {"line": 80, "column": 60}}, "22": {"start": {"line": 81, "column": 25}, "end": {"line": 81, "column": 43}}, "23": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 85}}, "24": {"start": {"line": 84, "column": 6}, "end": {"line": 91, "column": 8}}, "25": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 26}}, "26": {"start": {"line": 12, "column": 13}, "end": {"line": 94, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "loc": {"start": {"line": 16, "column": 65}, "end": {"line": 31, "column": 3}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 7}}, "loc": {"start": {"line": 39, "column": 20}, "end": {"line": 57, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 7}}, "loc": {"start": {"line": 63, "column": 19}, "end": {"line": 93, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 80, "column": 27}, "end": {"line": 80, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 27}, "end": {"line": 80, "column": 41}}, {"start": {"line": 80, "column": 45}, "end": {"line": 80, "column": 60}}]}, "1": {"loc": {"start": {"line": 81, "column": 25}, "end": {"line": 81, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 25}, "end": {"line": 81, "column": 37}}, {"start": {"line": 81, "column": 41}, "end": {"line": 81, "column": 43}}]}, "2": {"loc": {"start": {"line": 89, "column": 16}, "end": {"line": 89, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 89, "column": 16}, "end": {"line": 89, "column": 27}}, {"start": {"line": 89, "column": 31}, "end": {"line": 89, "column": 40}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 2, "26": 2}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/root/code/polyrepo/services/user-service/src/users/users.controller.ts": {"path": "/root/code/polyrepo/services/user-service/src/users/users.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": null}}, "2": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 47}}, "3": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 59}}, "4": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 78}}, "5": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 54}}, "6": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 54}}, "7": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 58}}, "8": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 65}}, "9": {"start": {"line": 40, "column": 28}, "end": {"line": 426, "column": null}}, "10": {"start": {"line": 42, "column": 21}, "end": {"line": 42, "column": 35}}, "11": {"start": {"line": 43, "column": 47}, "end": {"line": 43, "column": 65}}, "12": {"start": {"line": 44, "column": 21}, "end": {"line": 44, "column": 37}}, "13": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 72}}, "14": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 71}}, "15": {"start": {"line": 75, "column": 17}, "end": {"line": 75, "column": 61}}, "16": {"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": 7}}, "17": {"start": {"line": 83, "column": 4}, "end": {"line": 83, "column": 39}}, "18": {"start": {"line": 99, "column": 31}, "end": {"line": 99, "column": 56}}, "19": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 81}}, "20": {"start": {"line": 101, "column": 18}, "end": {"line": 101, "column": 69}}, "21": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 60}}, "22": {"start": {"line": 102, "column": 31}, "end": {"line": 102, "column": 58}}, "23": {"start": {"line": 120, "column": 31}, "end": {"line": 120, "column": 56}}, "24": {"start": {"line": 121, "column": 4}, "end": {"line": 121, "column": 91}}, "25": {"start": {"line": 123, "column": 17}, "end": {"line": 123, "column": 72}}, "26": {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, "27": {"start": {"line": 125, "column": 6}, "end": {"line": 125, "column": 55}}, "28": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 66}}, "29": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": 39}}, "30": {"start": {"line": 150, "column": 31}, "end": {"line": 150, "column": 56}}, "31": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 108}}, "32": {"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": 89}}, "33": {"start": {"line": 154, "column": 4}, "end": {"line": 159, "column": 5}}, "34": {"start": {"line": 155, "column": 6}, "end": {"line": 155, "column": 72}}, "35": {"start": {"line": 156, "column": 6}, "end": {"line": 158, "column": 8}}, "36": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": 39}}, "37": {"start": {"line": 183, "column": 4}, "end": {"line": 183, "column": 52}}, "38": {"start": {"line": 185, "column": 17}, "end": {"line": 185, "column": 67}}, "39": {"start": {"line": 188, "column": 4}, "end": {"line": 191, "column": 7}}, "40": {"start": {"line": 193, "column": 4}, "end": {"line": 193, "column": 39}}, "41": {"start": {"line": 206, "column": 4}, "end": {"line": 206, "column": 57}}, "42": {"start": {"line": 208, "column": 17}, "end": {"line": 208, "column": 51}}, "43": {"start": {"line": 211, "column": 4}, "end": {"line": 211, "column": 139}}, "44": {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 39}}, "45": {"start": {"line": 235, "column": 4}, "end": {"line": 237, "column": 6}}, "46": {"start": {"line": 239, "column": 17}, "end": {"line": 239, "column": 65}}, "47": {"start": {"line": 242, "column": 4}, "end": {"line": 246, "column": 7}}, "48": {"start": {"line": 248, "column": 4}, "end": {"line": 248, "column": 39}}, "49": {"start": {"line": 271, "column": 4}, "end": {"line": 271, "column": 47}}, "50": {"start": {"line": 272, "column": 4}, "end": {"line": 272, "column": 50}}, "51": {"start": {"line": 289, "column": 4}, "end": {"line": 289, "column": 53}}, "52": {"start": {"line": 290, "column": 17}, "end": {"line": 290, "column": 56}}, "53": {"start": {"line": 293, "column": 4}, "end": {"line": 297, "column": 7}}, "54": {"start": {"line": 299, "column": 4}, "end": {"line": 299, "column": 39}}, "55": {"start": {"line": 316, "column": 4}, "end": {"line": 316, "column": 57}}, "56": {"start": {"line": 318, "column": 28}, "end": {"line": 318, "column": 71}}, "57": {"start": {"line": 319, "column": 4}, "end": {"line": 319, "column": 64}}, "58": {"start": {"line": 322, "column": 4}, "end": {"line": 322, "column": 86}}, "59": {"start": {"line": 324, "column": 4}, "end": {"line": 324, "column": 65}}, "60": {"start": {"line": 347, "column": 4}, "end": {"line": 347, "column": 76}}, "61": {"start": {"line": 349, "column": 28}, "end": {"line": 349, "column": 49}}, "62": {"start": {"line": 350, "column": 17}, "end": {"line": 350, "column": 84}}, "63": {"start": {"line": 353, "column": 4}, "end": {"line": 359, "column": 7}}, "64": {"start": {"line": 361, "column": 4}, "end": {"line": 361, "column": 39}}, "65": {"start": {"line": 394, "column": 4}, "end": {"line": 394, "column": 66}}, "66": {"start": {"line": 396, "column": 25}, "end": {"line": 396, "column": 77}}, "67": {"start": {"line": 399, "column": 4}, "end": {"line": 403, "column": 7}}, "68": {"start": {"line": 405, "column": 4}, "end": {"line": 408, "column": 6}}, "69": {"start": {"line": 416, "column": 4}, "end": {"line": 424, "column": 6}}, "70": {"start": {"line": 40, "column": 13}, "end": {"line": 40, "column": 28}}, "71": {"start": {"line": 72, "column": 8}, "end": {"line": 84, "column": null}}, "72": {"start": {"line": 98, "column": 8}, "end": {"line": 103, "column": null}}, "73": {"start": {"line": 119, "column": 8}, "end": {"line": 130, "column": null}}, "74": {"start": {"line": 146, "column": 8}, "end": {"line": 162, "column": null}}, "75": {"start": {"line": 179, "column": 8}, "end": {"line": 194, "column": null}}, "76": {"start": {"line": 205, "column": 8}, "end": {"line": 214, "column": null}}, "77": {"start": {"line": 232, "column": 8}, "end": {"line": 249, "column": null}}, "78": {"start": {"line": 270, "column": 8}, "end": {"line": 273, "column": null}}, "79": {"start": {"line": 288, "column": 8}, "end": {"line": 300, "column": null}}, "80": {"start": {"line": 312, "column": 8}, "end": {"line": 325, "column": null}}, "81": {"start": {"line": 342, "column": 8}, "end": {"line": 362, "column": null}}, "82": {"start": {"line": 393, "column": 8}, "end": {"line": 409, "column": null}}, "83": {"start": {"line": 40, "column": 13}, "end": {"line": 426, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": null}}, "loc": {"start": {"line": 44, "column": 55}, "end": {"line": 48, "column": 3}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 7}}, "loc": {"start": {"line": 72, "column": 50}, "end": {"line": 84, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 7}}, "loc": {"start": {"line": 98, "column": 64}, "end": {"line": 103, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 102, "column": 21}, "end": {"line": 102, "column": 22}}, "loc": {"start": {"line": 102, "column": 31}, "end": {"line": 102, "column": 58}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 119, "column": 2}, "end": {"line": 119, "column": 7}}, "loc": {"start": {"line": 119, "column": 89}, "end": {"line": 130, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 146, "column": 2}, "end": {"line": 146, "column": 7}}, "loc": {"start": {"line": 148, "column": 52}, "end": {"line": 162, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 179, "column": 2}, "end": {"line": 179, "column": 7}}, "loc": {"start": {"line": 181, "column": 37}, "end": {"line": 194, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 205, "column": 2}, "end": {"line": 205, "column": 7}}, "loc": {"start": {"line": 205, "column": 46}, "end": {"line": 214, "column": 3}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 232, "column": 2}, "end": {"line": 232, "column": 7}}, "loc": {"start": {"line": 233, "column": 39}, "end": {"line": 249, "column": 3}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 270, "column": 2}, "end": {"line": 270, "column": 7}}, "loc": {"start": {"line": 270, "column": 20}, "end": {"line": 273, "column": 3}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 288, "column": 2}, "end": {"line": 288, "column": 7}}, "loc": {"start": {"line": 288, "column": 43}, "end": {"line": 300, "column": 3}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 312, "column": 2}, "end": {"line": 312, "column": 7}}, "loc": {"start": {"line": 314, "column": 38}, "end": {"line": 325, "column": 3}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 342, "column": 2}, "end": {"line": 342, "column": 7}}, "loc": {"start": {"line": 345, "column": 37}, "end": {"line": 362, "column": 3}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 393, "column": 2}, "end": {"line": 393, "column": 7}}, "loc": {"start": {"line": 393, "column": 58}, "end": {"line": 409, "column": 3}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 415, "column": 10}, "end": {"line": 415, "column": 26}}, "loc": {"start": {"line": 415, "column": 37}, "end": {"line": 425, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}, "type": "if", "locations": [{"start": {"line": 124, "column": 4}, "end": {"line": 127, "column": 5}}]}, "1": {"loc": {"start": {"line": 154, "column": 4}, "end": {"line": 159, "column": 5}}, "type": "if", "locations": [{"start": {"line": 154, "column": 4}, "end": {"line": 159, "column": 5}}]}, "2": {"loc": {"start": {"line": 318, "column": 28}, "end": {"line": 318, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 318, "column": 38}, "end": {"line": 318, "column": 59}}, {"start": {"line": 318, "column": 62}, "end": {"line": 318, "column": 71}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 21, "11": 21, "12": 21, "13": 21, "14": 2, "15": 2, "16": 1, "17": 1, "18": 2, "19": 2, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 2, "26": 2, "27": 1, "28": 1, "29": 1, "30": 2, "31": 2, "32": 2, "33": 2, "34": 1, "35": 1, "36": 1, "37": 2, "38": 2, "39": 1, "40": 1, "41": 2, "42": 2, "43": 1, "44": 1, "45": 1, "46": 1, "47": 1, "48": 1, "49": 1, "50": 1, "51": 2, "52": 2, "53": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 1, "62": 1, "63": 1, "64": 1, "65": 2, "66": 2, "67": 2, "68": 2, "69": 10, "70": 1, "71": 1, "72": 1, "73": 1, "74": 1, "75": 1, "76": 1, "77": 1, "78": 1, "79": 1, "80": 1, "81": 1, "82": 1, "83": 1}, "f": {"0": 21, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 1, "9": 1, "10": 2, "11": 1, "12": 1, "13": 2, "14": 10}, "b": {"0": [1], "1": [1], "2": [1, 0]}}, "/root/code/polyrepo/services/user-service/src/observability/business-logger.service.ts": {"path": "/root/code/polyrepo/services/user-service/src/observability/business-logger.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 53}}, "2": {"start": {"line": 11, "column": 7}, "end": {"line": 98, "column": null}}, "3": {"start": {"line": 12, "column": 31}, "end": {"line": 12, "column": 47}}, "4": {"start": {"line": 25, "column": 4}, "end": {"line": 28, "column": 7}}, "5": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 7}}, "6": {"start": {"line": 59, "column": 4}, "end": {"line": 62, "column": 7}}, "7": {"start": {"line": 76, "column": 4}, "end": {"line": 79, "column": 7}}, "8": {"start": {"line": 93, "column": 4}, "end": {"line": 96, "column": 7}}, "9": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 31}}, "10": {"start": {"line": 11, "column": 13}, "end": {"line": 98, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 31}}, "loc": {"start": {"line": 12, "column": 61}, "end": {"line": 12, "column": 65}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 22}}, "loc": {"start": {"line": 23, "column": 34}, "end": {"line": 29, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 20}}, "loc": {"start": {"line": 40, "column": 34}, "end": {"line": 46, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 22}}, "loc": {"start": {"line": 57, "column": 34}, "end": {"line": 63, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 26}}, "loc": {"start": {"line": 74, "column": 34}, "end": {"line": 80, "column": 3}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 88, "column": 2}, "end": {"line": 88, "column": 25}}, "loc": {"start": {"line": 91, "column": 34}, "end": {"line": 97, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 1, "10": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {}}, "/root/code/polyrepo/services/user-service/src/users/dto/create-user.dto.ts": {"path": "/root/code/polyrepo/services/user-service/src/users/dto/create-user.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 98}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 13}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "4": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "5": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": null}}, "6": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "7": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/users/dto/update-user.dto.ts": {"path": "/root/code/polyrepo/services/user-service/src/users/dto/update-user.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 86}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 13}}, "3": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "4": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": null}}, "5": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/users/dto/user-response.dto.ts": {"path": "/root/code/polyrepo/services/user-service/src/users/dto/user-response.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 13}}, "2": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "3": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "4": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "5": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "6": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "7": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "8": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/health/health.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/health/health.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 50}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 55}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 76}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 72}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 55}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 56}}, "7": {"start": {"line": 22, "column": 7}, "end": {"line": 22, "column": null}}, "8": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": 25}}, "9": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/http/http.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/http/http.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 75}}, "2": {"start": {"line": 18, "column": 7}, "end": {"line": 18, "column": null}}, "3": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": 23}}, "4": {"start": {"line": 18, "column": 13}, "end": {"line": 18, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/metrics/metrics.controller.ts": {"path": "/root/code/polyrepo/services/user-service/src/metrics/metrics.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 65}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 74}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 69}}, "3": {"start": {"line": 10, "column": 7}, "end": {"line": 34, "column": null}}, "4": {"start": {"line": 12, "column": 21}, "end": {"line": 12, "column": 37}}, "5": {"start": {"line": 13, "column": 47}, "end": {"line": 13, "column": 65}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 71}}, "7": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 49}}, "8": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 44}}, "9": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": 30}}, "10": {"start": {"line": 30, "column": 8}, "end": {"line": 33, "column": null}}, "11": {"start": {"line": 10, "column": 13}, "end": {"line": 34, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}, "loc": {"start": {"line": 13, "column": 65}, "end": {"line": 17, "column": 3}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 7}}, "loc": {"start": {"line": 30, "column": 18}, "end": {"line": 33, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/root/code/polyrepo/services/user-service/src/metrics/metrics.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/metrics/metrics.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 57}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 72}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 76}}, "4": {"start": {"line": 16, "column": 7}, "end": {"line": 16, "column": null}}, "5": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 26}}, "6": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/observability/observability.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/observability/observability.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 85}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 63}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 64}}, "5": {"start": {"line": 44, "column": 7}, "end": {"line": 44, "column": null}}, "6": {"start": {"line": 44, "column": 13}, "end": {"line": 44, "column": 32}}, "7": {"start": {"line": 44, "column": 13}, "end": {"line": 44, "column": null}}, "8": {"start": {"line": 16, "column": 25}, "end": {"line": 36, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 18}, "end": {"line": 16, "column": 21}}, "loc": {"start": {"line": 16, "column": 25}, "end": {"line": 36, "column": 8}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 20}, "end": {"line": 21, "column": 41}}, {"start": {"line": 21, "column": 45}, "end": {"line": 21, "column": 63}}]}, "1": {"loc": {"start": {"line": 22, "column": 20}, "end": {"line": 22, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 20}, "end": {"line": 22, "column": 41}}, {"start": {"line": 22, "column": 45}, "end": {"line": 22, "column": 51}}]}, "2": {"loc": {"start": {"line": 28, "column": 25}, "end": {"line": 28, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 25}, "end": {"line": 28, "column": 45}}, {"start": {"line": 28, "column": 49}, "end": {"line": 28, "column": 62}}]}, "3": {"loc": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 43}}, {"start": {"line": 33, "column": 47}, "end": {"line": 33, "column": 60}}]}, "4": {"loc": {"start": {"line": 34, "column": 26}, "end": {"line": 34, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 34, "column": 26}, "end": {"line": 34, "column": 53}}, {"start": {"line": 34, "column": 57}, "end": {"line": 34, "column": 89}}]}, "5": {"loc": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 89}}, "type": "cond-expr", "locations": [{"start": {"line": 40, "column": 55}, "end": {"line": 40, "column": 57}}, {"start": {"line": 40, "column": 60}, "end": {"line": 40, "column": 89}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/root/code/polyrepo/services/user-service/src/observability/test.controller.ts": {"path": "/root/code/polyrepo/services/user-service/src/observability/test.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 76}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 90}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 63}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 69}}, "4": {"start": {"line": 11, "column": 7}, "end": {"line": 92, "column": null}}, "5": {"start": {"line": 13, "column": 47}, "end": {"line": 13, "column": 65}}, "6": {"start": {"line": 14, "column": 21}, "end": {"line": 14, "column": 37}}, "7": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 37}}, "8": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 81}}, "9": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 73}}, "10": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 136}}, "11": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 78}}, "12": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 141}}, "13": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 86}}, "14": {"start": {"line": 46, "column": 4}, "end": {"line": 64, "column": 7}}, "15": {"start": {"line": 47, "column": 6}, "end": {"line": 49, "column": 7}}, "16": {"start": {"line": 48, "column": 8}, "end": {"line": 48, "column": 86}}, "17": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 79}}, "18": {"start": {"line": 53, "column": 6}, "end": {"line": 61, "column": 9}}, "19": {"start": {"line": 54, "column": 8}, "end": {"line": 56, "column": 9}}, "20": {"start": {"line": 55, "column": 10}, "end": {"line": 55, "column": 89}}, "21": {"start": {"line": 57, "column": 8}, "end": {"line": 57, "column": 75}}, "22": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 63}}, "23": {"start": {"line": 60, "column": 37}, "end": {"line": 60, "column": 61}}, "24": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 95}}, "25": {"start": {"line": 74, "column": 19}, "end": {"line": 74, "column": 50}}, "26": {"start": {"line": 76, "column": 4}, "end": {"line": 87, "column": 5}}, "27": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 135}}, "28": {"start": {"line": 78, "column": 11}, "end": {"line": 87, "column": 5}}, "29": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 133}}, "30": {"start": {"line": 80, "column": 11}, "end": {"line": 87, "column": 5}}, "31": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 135}}, "32": {"start": {"line": 82, "column": 11}, "end": {"line": 87, "column": 5}}, "33": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 139}}, "34": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 111}}, "35": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 112}}, "36": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 78}}, "37": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 73}}, "38": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 40}}, "39": {"start": {"line": 30, "column": 2}, "end": {"line": 37, "column": null}}, "40": {"start": {"line": 45, "column": 8}, "end": {"line": 65, "column": null}}, "41": {"start": {"line": 73, "column": 2}, "end": {"line": 91, "column": null}}, "42": {"start": {"line": 11, "column": 13}, "end": {"line": 92, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "loc": {"start": {"line": 15, "column": 55}, "end": {"line": 19, "column": 3}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 13}}, "loc": {"start": {"line": 30, "column": 13}, "end": {"line": 37, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 7}}, "loc": {"start": {"line": 45, "column": 19}, "end": {"line": 65, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 46, "column": 98}, "end": {"line": 46, "column": 103}}, "loc": {"start": {"line": 46, "column": 113}, "end": {"line": 64, "column": 5}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 53, "column": 103}, "end": {"line": 53, "column": 108}}, "loc": {"start": {"line": 53, "column": 121}, "end": {"line": 61, "column": 7}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 60, "column": 26}, "end": {"line": 60, "column": 33}}, "loc": {"start": {"line": 60, "column": 37}, "end": {"line": 60, "column": 61}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 26}}, "loc": {"start": {"line": 73, "column": 131}, "end": {"line": 91, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 47, "column": 6}, "end": {"line": 49, "column": 7}}, "type": "if", "locations": [{"start": {"line": 47, "column": 6}, "end": {"line": 49, "column": 7}}]}, "1": {"loc": {"start": {"line": 47, "column": 10}, "end": {"line": 47, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 10}, "end": {"line": 47, "column": 14}}, {"start": {"line": 47, "column": 18}, "end": {"line": 47, "column": 35}}]}, "2": {"loc": {"start": {"line": 54, "column": 8}, "end": {"line": 56, "column": 9}}, "type": "if", "locations": [{"start": {"line": 54, "column": 8}, "end": {"line": 56, "column": 9}}]}, "3": {"loc": {"start": {"line": 54, "column": 12}, "end": {"line": 54, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 54, "column": 12}, "end": {"line": 54, "column": 19}}, {"start": {"line": 54, "column": 23}, "end": {"line": 54, "column": 43}}]}, "4": {"loc": {"start": {"line": 73, "column": 44}, "end": {"line": 73, "column": 85}}, "type": "default-arg", "locations": [{"start": {"line": 73, "column": 76}, "end": {"line": 73, "column": 85}}]}, "5": {"loc": {"start": {"line": 73, "column": 103}, "end": {"line": 73, "column": 131}}, "type": "default-arg", "locations": [{"start": {"line": 73, "column": 123}, "end": {"line": 73, "column": 131}}]}, "6": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 87, "column": 5}}, {"start": {"line": 78, "column": 11}, "end": {"line": 87, "column": 5}}]}, "7": {"loc": {"start": {"line": 78, "column": 11}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 11}, "end": {"line": 87, "column": 5}}, {"start": {"line": 80, "column": 11}, "end": {"line": 87, "column": 5}}]}, "8": {"loc": {"start": {"line": 80, "column": 11}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 80, "column": 11}, "end": {"line": 87, "column": 5}}, {"start": {"line": 82, "column": 11}, "end": {"line": 87, "column": 5}}]}, "9": {"loc": {"start": {"line": 82, "column": 11}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 82, "column": 11}, "end": {"line": 87, "column": 5}}, {"start": {"line": 84, "column": 11}, "end": {"line": 87, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0], "4": [0], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}, "/root/code/polyrepo/services/user-service/src/health/prisma.health.ts": {"path": "/root/code/polyrepo/services/user-service/src/health/prisma.health.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 92}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 57}}, "3": {"start": {"line": 6, "column": 7}, "end": {"line": 34, "column": null}}, "4": {"start": {"line": 8, "column": 4}, "end": {"line": 8, "column": 12}}, "5": {"start": {"line": 7, "column": 31}, "end": {"line": 7, "column": 46}}, "6": {"start": {"line": 12, "column": 4}, "end": {"line": 32, "column": 5}}, "7": {"start": {"line": 13, "column": 23}, "end": {"line": 13, "column": 61}}, "8": {"start": {"line": 14, "column": 6}, "end": {"line": 16, "column": 7}}, "9": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 82}}, "10": {"start": {"line": 18, "column": 6}, "end": {"line": 21, "column": 8}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 26, "column": 7}}, "12": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 20}}, "13": {"start": {"line": 27, "column": 27}, "end": {"line": 27, "column": 110}}, "14": {"start": {"line": 28, "column": 6}, "end": {"line": 31, "column": 8}}, "15": {"start": {"line": 6, "column": 13}, "end": {"line": 6, "column": 34}}, "16": {"start": {"line": 6, "column": 13}, "end": {"line": 34, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 31}}, "loc": {"start": {"line": 7, "column": 59}, "end": {"line": 9, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 7}}, "loc": {"start": {"line": 11, "column": 29}, "end": {"line": 33, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 6}, "end": {"line": 16, "column": 7}}, "type": "if", "locations": [{"start": {"line": 14, "column": 6}, "end": {"line": 16, "column": 7}}]}, "1": {"loc": {"start": {"line": 20, "column": 35}, "end": {"line": 20, "column": 103}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 35}, "end": {"line": 20, "column": 51}}, {"start": {"line": 20, "column": 55}, "end": {"line": 20, "column": 103}}]}, "2": {"loc": {"start": {"line": 24, "column": 6}, "end": {"line": 26, "column": 7}}, "type": "if", "locations": [{"start": {"line": 24, "column": 6}, "end": {"line": 26, "column": 7}}]}, "3": {"loc": {"start": {"line": 27, "column": 27}, "end": {"line": 27, "column": 110}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 52}, "end": {"line": 27, "column": 65}}, {"start": {"line": 27, "column": 68}, "end": {"line": 27, "column": 110}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0, 0], "2": [0], "3": [0, 0]}}, "/root/code/polyrepo/services/user-service/src/users/users.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/users/users.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 53}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 47}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 57}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 76}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "6": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 56}}, "7": {"start": {"line": 20, "column": 7}, "end": {"line": 20, "column": null}}, "8": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": 24}}, "9": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/prisma/prisma.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/prisma/prisma.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 9, "column": 7}, "end": {"line": 9, "column": null}}, "3": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": 25}}, "4": {"start": {"line": 9, "column": 13}, "end": {"line": 9, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {}}, "/root/code/polyrepo/services/user-service/src/app.module.ts": {"path": "/root/code/polyrepo/services/user-service/src/app.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 1}, "end": {"line": 1, "column": 41}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 43}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 51}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 54}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 75}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 48}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 57}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 54}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 44}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 50}}, "12": {"start": {"line": 72, "column": 7}, "end": {"line": 72, "column": null}}, "13": {"start": {"line": 72, "column": 13}, "end": {"line": 72, "column": 22}}, "14": {"start": {"line": 72, "column": 13}, "end": {"line": 72, "column": null}}, "15": {"start": {"line": 20, "column": 20}, "end": {"line": 20, "column": 40}}, "16": {"start": {"line": 21, "column": 8}, "end": {"line": 28, "column": 9}}, "17": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 34}}, "18": {"start": {"line": 23, "column": 15}, "end": {"line": 28, "column": 9}}, "19": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 31}}, "20": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 30}}, "21": {"start": {"line": 33, "column": 25}, "end": {"line": 42, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 20}, "end": {"line": 19, "column": 23}}, "loc": {"start": {"line": 19, "column": 25}, "end": {"line": 29, "column": 7}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 33, "column": 18}, "end": {"line": 33, "column": 21}}, "loc": {"start": {"line": 33, "column": 25}, "end": {"line": 42, "column": 8}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 8}, "end": {"line": 28, "column": 9}}, "type": "if", "locations": [{"start": {"line": 21, "column": 8}, "end": {"line": 28, "column": 9}}, {"start": {"line": 23, "column": 15}, "end": {"line": 28, "column": 9}}]}, "1": {"loc": {"start": {"line": 23, "column": 15}, "end": {"line": 28, "column": 9}}, "type": "if", "locations": [{"start": {"line": 23, "column": 15}, "end": {"line": 28, "column": 9}}, {"start": {"line": 25, "column": 15}, "end": {"line": 28, "column": 9}}]}, "2": {"loc": {"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 14}, "end": {"line": 35, "column": 36}}, {"start": {"line": 35, "column": 40}, "end": {"line": 35, "column": 51}}]}, "3": {"loc": {"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 36, "column": 23}, "end": {"line": 36, "column": 45}}, {"start": {"line": 36, "column": 49}, "end": {"line": 36, "column": 55}}]}, "4": {"loc": {"start": {"line": 37, "column": 21}, "end": {"line": 37, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 21}, "end": {"line": 37, "column": 41}}, {"start": {"line": 37, "column": 45}, "end": {"line": 37, "column": 48}}]}, "5": {"loc": {"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 29}, "end": {"line": 38, "column": 58}}, {"start": {"line": 38, "column": 62}, "end": {"line": 38, "column": 67}}]}, "6": {"loc": {"start": {"line": 49, "column": 16}, "end": {"line": 49, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 49, "column": 16}, "end": {"line": 49, "column": 38}}, {"start": {"line": 49, "column": 42}, "end": {"line": 49, "column": 53}}]}, "7": {"loc": {"start": {"line": 50, "column": 25}, "end": {"line": 50, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 25}, "end": {"line": 50, "column": 47}}, {"start": {"line": 50, "column": 51}, "end": {"line": 50, "column": 57}}]}, "8": {"loc": {"start": {"line": 51, "column": 23}, "end": {"line": 51, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 51, "column": 23}, "end": {"line": 51, "column": 43}}, {"start": {"line": 51, "column": 47}, "end": {"line": 51, "column": 50}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}}}