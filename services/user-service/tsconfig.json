{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "paths": {"@libs/*": ["../../../libs/*"], "@prisma/client": ["../generated/prisma-client"], "@prisma/client/*": ["../../../node_modules/.prisma/client/default/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}