# --- Base Stage ---
# Uses the common base image where yarn install and shared lib builds are done.
# The polyrepo-node-base already contains:
# - Root package.json, yarn.lock, tsconfig.base.json
# - All source code from ./libs and ./services
# - All node_modules installed via yarn install
# - Built shared libraries (@libs/*)
FROM polyrepo-node-base:latest AS base

WORKDIR /app/services/user-service

RUN echo "DEBUG: Listing /app/services/user-service/src directory contents BEFORE prisma generate and build:"
RUN ls -R /app/services/user-service/src

# Extended Debugging:
RUN echo "==== USER-SERVICE DEBUG START ===="
RUN echo "Current working directory: $(pwd)"

RUN echo "--- Listing /app/libs/shared-types/dist ---"
RUN ls -R /app/libs/shared-types/dist || echo "WARN: /app/libs/shared-types/dist not found or empty"

RUN echo "--- Listing /app/libs/nestjs-common/dist ---"
RUN ls -R /app/libs/nestjs-common/dist || echo "WARN: /app/libs/nestjs-common/dist not found or empty"

RUN echo "--- Listing symlinks in /app/node_modules/@libs ---"
RUN ls -la /app/node_modules/@libs || echo "WARN: /app/node_modules/@libs not found or empty"

RUN echo "--- Contents of /app/services/user-service/tsconfig.json ---"
RUN cat /app/services/user-service/tsconfig.json || echo "WARN: /app/services/user-service/tsconfig.json not found"

RUN echo "--- Contents of /app/services/user-service/tsconfig.build.json ---"
RUN cat /app/services/user-service/tsconfig.build.json || echo "WARN: /app/services/user-service/tsconfig.build.json not found"

RUN echo "==== USER-SERVICE DEBUG END ===="

# Prisma generate needs access to the schema and @prisma/client (already in node_modules from 'base')
# Ensure Prisma schema is present (it should be from 'base' stage's COPY services ./services)
RUN echo ">>> user-service: Running Prisma Generate..."
RUN yarn prisma generate

# Build the user-service application using its own tsconfig.build.json
RUN echo ">>> user-service: Cleaning potential stale build artifacts..."
RUN rm -rf dist && rm -f tsconfig.build.tsbuildinfo tsconfig.tsbuildinfo

RUN echo ">>> user-service: Running Build (tsc -p tsconfig.build.json --listEmittedFiles direct call)..."
RUN (yarn tsc -p tsconfig.build.json --listEmittedFiles > /tmp/user_service_tsc_build.log 2>&1) || \
    (echo "TSC COMPILE FAILED! Displaying log:" && cat /tmp/user_service_tsc_build.log && exit 1)
RUN echo "TSC build log for user-service (if successful):" && cat /tmp/user_service_tsc_build.log

RUN echo "DEBUG: Listing /app/services/user-service/dist RECURSIVELY in base stage after yarn build:"
RUN ls -R /app/services/user-service/dist
RUN echo "DEBUG: Checking for /app/services/user-service/dist/main.js in base stage (will fail build if not found):"
RUN test -f /app/services/user-service/dist/main.js || (echo "ERROR: /app/services/user-service/dist/main.js not found!" && exit 1)
RUN echo "DEBUG: /app/services/user-service/dist/main.js FOUND. Verifying it's NOT EMPTY (will fail build if empty):"
RUN test -s /app/services/user-service/dist/main.js || (echo "ERROR: /app/services/user-service/dist/main.js is empty!" && exit 1)
RUN echo "DEBUG: /app/services/user-service/dist/main.js is present and NOT EMPTY. Printing first 5 lines:"
RUN head -n 5 /app/services/user-service/dist/main.js

# --- Final Stage ---
# Creates the lean production image.
FROM node:22.1.0-alpine AS final

# Set up user and group
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

WORKDIR /app

# Copy essential files from the 'base' stage
# Root package.json and yarn.lock might be needed for some runtime tooling or context.
COPY --from=base --chown=appuser:appgroup /app/package.json ./package.json
COPY --from=base --chown=appuser:appgroup /app/yarn.lock ./yarn.lock

# Copy all installed node_modules from the 'base' stage.
# This includes hoisted dependencies and specific versions for services.
COPY --from=base --chown=appuser:appgroup /app/node_modules ./node_modules

# Copy compiled shared libraries from the 'base' stage.
COPY --from=base --chown=appuser:appgroup /app/libs ./libs

# --- User Service Specifics ---
WORKDIR /app/services/user-service

# Copy service-specific package.json (needed for 'yarn prisma migrate deploy')
COPY --from=base --chown=appuser:appgroup /app/services/user-service/package.json ./package.json

# Copy service-specific node_modules (for nohoisted dependencies including Prisma client)
COPY --from=base --chown=appuser:appgroup /app/services/user-service/node_modules ./node_modules

# Copy built user-service application code from the 'Service Build Stage' (which was based on 'base')
# Note: Since 'Service Build Stage' is just an alias for 'base' after build commands,
# we are copying from 'base' which now contains the built 'dist' for user-service.
COPY --from=base --chown=appuser:appgroup /app/services/user-service/dist ./dist

# Copy Prisma schema for runtime migrations
COPY --from=base --chown=appuser:appgroup /app/services/user-service/prisma ./prisma

# Optional: Copy .env file if it's specific to this service and located within its directory
# COPY --chown=appuser:appgroup ./services/user-service/.env ./.env
# Note: .env files are typically managed via docker-compose for local dev.

# Ensure all files in /app (especially service code) are owned by appuser
USER root
RUN chown -R appuser:appgroup /app
USER appuser

# Set NODE_ENV to production
ENV NODE_ENV=production

# Expose port
EXPOSE 3000

# Define the command to run migrations and then the service
CMD ["sh", "-c", "yarn prisma migrate deploy && node dist/main.js"]
