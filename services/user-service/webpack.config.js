const path = require('path');
const webpack = require('webpack');
const DotenvWebpack = require('dotenv-webpack');
const nodeExternals = require('webpack-node-externals');
const { execSync } = require('child_process');

// Simple Prisma client generation plugin
class PrismaGenerationPlugin {
  apply(compiler) {
    compiler.hooks.beforeCompile.tapAsync('PrismaGenerationPlugin', (compilation, callback) => {
      try {
        console.log('🔧 Generating Prisma client...');
        execSync('npx prisma generate', { stdio: 'inherit', cwd: process.cwd() });
        console.log('✅ Prisma client generated successfully');
        callback();
      } catch (error) {
        console.error('❌ Prisma client generation failed:', error.message);
        console.warn('⚠️  Continuing with webpack build...');
        callback();
      }
    });
  }
}

module.exports = {
  // Target Node.js environment
  target: 'node',
  mode: 'development',
  
  // ===================================================================
  // ESM Support Configuration (Node.js 22)
  // ===================================================================
  // Enable ESM imports for pure ESM modules (like got v14)
  // Node.js 22 handles ESM natively, so we can import ESM modules directly
  externalsType: 'import',
  
  // Entry point
  entry: './src/main.ts',
  
  // Output configuration
  output: {
    path: path.resolve(__dirname, 'dist-webpack'),
    filename: 'main.bundle.js',
    libraryTarget: 'commonjs2',
  },
  
  // Module resolution
  resolve: {
    extensions: ['.ts', '.js', '.mjs', '.json'],
    alias: {
      // BUNDLE OPTIMIZATION: Use src paths for 6x faster library change feedback
      '@libs/auth-common': path.resolve(__dirname, '../../libs/auth-common/src'),
      '@libs/http': path.resolve(__dirname, '../../libs/http/src'),
      '@libs/observability': path.resolve(__dirname, '../../libs/observability/src'),
      '@libs/shared-types': path.resolve(__dirname, '../../libs/shared-types/src'),
      '@libs/caching': path.resolve(__dirname, '../../libs/caching/src'),
      '@libs/messaging': path.resolve(__dirname, '../../libs/messaging/src'),
      '@libs/prisma-utils': path.resolve(__dirname, '../../libs/prisma-utils/src'),
      '@libs/prisma-resilience': path.resolve(__dirname, '../../libs/prisma-resilience/src'),
      '@libs/resilience': path.resolve(__dirname, '../../libs/resilience/src'),
      '@libs/keycloak-client': path.resolve(__dirname, '../../libs/keycloak-client/src'),
      '@libs/error-handling': path.resolve(__dirname, '../../libs/error-handling/src'),
    },
    // Fallback for node modules
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },
  
  // Loader configuration with explicit resolution
  resolveLoader: {
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },

  // Module rules
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        include: [
          path.resolve(__dirname, 'src'),
          path.resolve(__dirname, '../../libs') // Include all library source
        ],
        use: [
          {
            // Use require.resolve for explicit loader path
            loader: require.resolve('ts-loader'),
            options: {
              configFile: path.resolve(__dirname, 'tsconfig.webpack.json'),
              // Skip type checking for faster builds - focus on bundling
              transpileOnly: true,
              // Important for NestJS decorators and metadata
              experimentalWatchApi: true,
              compilerOptions: {
                // Preserve decorator metadata for NestJS DI
                experimentalDecorators: true,
                emitDecoratorMetadata: true,
                // Enable source maps for debugging
                sourceMap: true,
              }
            }
          }
        ]
      },
      // Handle binary files by returning empty module
      {
        test: /\.(node|so|dylib)$/,
        use: 'null-loader'
      },
      // Handle other assets
      {
        test: /\.(txt|md)$/,
        type: 'asset/source',
      },
      // =================================================================
      // ESM Module Handling (Node.js 22 Compatible)
      // =================================================================
      // With Node.js 22, we can handle ESM modules natively.
      // This simplified rule handles .mjs files and modern JS modules
      // without complex transpilation since Node.js 22 supports ESM properly.
      {
        test: /\.mjs$/,
        type: 'javascript/esm',
      }
    ]
  },
  
  // =================================================================
  // External Dependencies Configuration
  // =================================================================
  // This section determines which modules are bundled vs externalized.
  // 
  // Strategy:
  // - Bundle: @libs/*, critical runtime deps, and ESM modules that need transpilation
  // - Externalize: Large deps that work fine as CommonJS externals (ioredis, prisma, etc.)
  //
  // Why this matters: ESM modules must be bundled to be transpiled by babel-loader.
  // If they're externalized, they're loaded directly and cause ERR_REQUIRE_ESM errors.
  externals: [
    nodeExternals({
      // ===============================================================
      // ALLOWLIST: Modules that WILL BE BUNDLED (not externalized)
      // ===============================================================
      allowlist: [
        // Local shared libraries (always bundle these)
        /^@libs\//,                   // All our internal @libs/* packages
        
        // Critical runtime dependencies for TypeScript/NestJS
        'tslib',                      // TypeScript runtime helpers
        'reflect-metadata',           // Required for NestJS decorators and DI
        
        // Babel runtime helpers (prevent ESM helper issues)
        /^@babel\/runtime/,          // Include all babel runtime modules
        
        // ============================================================
        // ESM LIBRARIES: Must be bundled to enable transpilation
        // ============================================================
        // These correspond to the libraries listed in the babel-loader rule above.
        // If we externalize these, they can't be transpiled and will cause ERR_REQUIRE_ESM.
        
        // Core HTTP client (got ecosystem)
        'got',                        // Main got library
        /got/,                        // Any got sub-modules
        /cacheable-request/,          // HTTP caching layer
        /keyv/,                       // Key-value storage
        /form-data-encoder/,          // Form data handling
        /decompress-response/,        // Response decompression
        /mimic-response/,             // Response mocking utilities
        /normalize-url/,              // URL normalization
        /responselike/,               // Response-like objects
        
        // Utility libraries (sindresorhus ecosystem)
        /@sindresorhus/,             // All @sindresorhus/* packages
        /type-fest/,                 // TypeScript type utilities
        /p-cancelable/,              // Cancelable promises
        /lowercase-keys/,            // Object key transformation
        /defer-to-connect/,          // Connection deferring
        /quick-lru/,                 // LRU cache implementation
        /escape-string-regexp/,      // RegExp escaping
        /is-plain-obj/,              // Object type checking
        
        // HTTP/2 and networking
        /http2-wrapper/,             // HTTP/2 client wrapper
        /@szmarczak/,                // HTTP utilities
        
        // Additional dependencies that may need transpilation
        /wrappy/,                    // Function wrapping utilities
        /json-buffer/,               // JSON buffer handling
        /clone-response/,            // Response cloning
        /get-stream/,                // Stream utilities
        /is-retry-allowed/,          // Retry logic utilities
      ],
      
      // Look for modules in parent node_modules too
      additionalModuleDirs: ['../../node_modules']
    }),
    
    // ===============================================================
    // EXPLICIT EXTERNALS: Large deps that work fine as externals
    // ===============================================================
    // These are explicitly externalized because:
    // 1. They're CommonJS compatible 
    // 2. They're large and don't benefit from bundling
    // 3. They don't have ESM issues
    'ioredis',                      // Redis client (large, CommonJS compatible)
    'redis',                        // Alternative Redis client  
    '@prisma/client',               // Prisma ORM client (large, has native binaries)
    'prisma',                       // Prisma CLI tools
    '@prisma/engines',              // Prisma engines (binaries)
    '@prisma/engines-version',      // Prisma engines version
    // External reference to generated Prisma client to avoid bundling conflicts
    ({request}, callback) => {
      if (request.includes('generated/prisma-client')) {
        return callback(null, 'commonjs ' + request);
      }
      // Externalize any Prisma engine requests
      if (request.includes('@prisma/engines') || request.includes('schema-engine') || request.includes('query-engine')) {
        return callback(null, 'commonjs ' + request);
      }
      callback();
    },
  ],

  // Plugins
  plugins: [
    // Ignore optional NestJS modules that might not be installed
    new webpack.IgnorePlugin({
      resourceRegExp: /^@nestjs\/(microservices|websockets)/,
    }),
    
    // Ignore optional dependencies
    new webpack.IgnorePlugin({
      resourceRegExp: /^class-transformer\/storage$/,
    }),
    
    // Ignore native modules and binary files
    new webpack.IgnorePlugin({
      resourceRegExp: /\.node$/,
    }),
    
    // Ignore problematic Prisma runtime paths that cause bundling conflicts
    new webpack.IgnorePlugin({
      resourceRegExp: /runtime\/wasm/,
      contextRegExp: /prisma-client/,
    }),
    
    // Ignore dynamic requires that NestJS might use
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
    
    // Generate Prisma client during webpack build
    new PrismaGenerationPlugin(),

    // Use dotenv-webpack to inject environment variables correctly
    new DotenvWebpack({
      path: './.env.bundling',  // Use bundling-specific environment
      systemvars: true,         // Load all system environment variables as well
      safe: false,         
      defaults: false,
      allowEmptyValues: true,
      // This ensures process.env remains intact during runtime to work with ConfigService
      ignoreStub: true
    }),
  ],
  
  // Development settings
  devtool: 'source-map',
  
  // Stats configuration for better output
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    chunkModules: false,
    reasons: false,
    hash: false,
    version: false,
    timings: true,
    assets: true,
    warnings: true,
    errors: true,
    errorDetails: true
  },
  
  // Performance hints
  performance: {
    hints: false
  },
  
  // Node.js polyfills/mocks
  node: {
    // Don't mock these Node.js globals, use the real ones
    __dirname: false,
    __filename: false,
  }
};
