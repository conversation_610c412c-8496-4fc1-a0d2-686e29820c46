{"name": "@polyrepo/user-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "rimraf dist tsconfig.build.tsbuildinfo", "build": "rimraf dist tsconfig.build.tsbuildinfo && tsc -p tsconfig.build.json", "build:webpack": "webpack --config webpack.config.js", "build:webpack:watch": "webpack --config webpack.config.js --watch", "start:bundle": "node dist-webpack/main.bundle.js", "dev:bundle": "yarn build:webpack && yarn start:bundle", "bundle:rebuild:restart": "yarn build:webpack && cd ../../infrastructure/local-dev && docker restart polyrepo_user_service_volume", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node -r tsconfig-paths/register dist/main.js", "start:dev": "rimraf dist tsconfig.build.tsbuildinfo && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node -r tsconfig-paths/register dist/main.js", "postinstall": "prisma generate", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset --force", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --json --outputFile=e2e-test-results.json", "test:integration": "jest --config ./test/jest-integration.json --json --outputFile=integration-test-results.json"}, "dependencies": {"@libs/auth-common": "file:../../libs/auth-common", "@libs/caching": "file:../../libs/caching", "@libs/error-handling": "file:../../libs/error-handling", "@libs/http": "file:../../libs/http", "@libs/observability": "file:../../libs/observability", "@libs/prisma-resilience": "file:../../libs/prisma-resilience", "@libs/prisma-utils": "file:../../libs/prisma-utils", "@libs/resilience": "file:../../libs/resilience", "@libs/shared-types": "file:../../libs/shared-types", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^10.2.0", "@prisma/client": "^6.8.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^20.17.41", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "axios": "^1.9.0", "dotenv": "^16.5.0", "dotenv-webpack": "^8.1.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.8.0", "rimraf": "^4.4.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "typescript": "*", "uuid": "^11.1.0", "webpack": "^5.92.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}