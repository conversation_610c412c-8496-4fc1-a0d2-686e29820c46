NODE_ENV=development
PORT=3000

# Database Connection (for Docker bundling environment)
# Uses the internal Docker service name and port since the service runs in Docker
DATABASE_URL="*************************************************************************************/polyrepo_user_db?schema=public"

# Redis Configuration (for Docker environment)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
ENABLE_TRACING=true
TEMPO_ENDPOINT=http://localhost:4318/v1/traces