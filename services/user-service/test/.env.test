NODE_ENV=test
PORT=3002

# --- Database Connection ---
# For testing, use an in-memory database or a test database
DB_HOST=localhost
DB_PORT=5433
DB_USERNAME=test_user
DB_PASSWORD=test_password
DB_DATABASE=test_db
DATABASE_URL="postgresql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}?schema=public"

# HTTP Client Configuration
HTTP_TIMEOUT=5000
HTTP_MAX_REDIRECTS=5

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=false
LOKI_HOST=http://localhost:3100
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Test Configuration
# Set to true to use real services instead of mocks
