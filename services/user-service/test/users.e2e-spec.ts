import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from './../src/app.module';
import { PrismaService } from '../src/prisma/prisma.service';
import { v4 as uuidv4 } from 'uuid';
import { CreateUserInternalDto } from '@libs/shared-types';

describe('UsersController (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    
    // Enable validation pipes for e2e tests
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }));
    
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    
    await app.init();
    
    // Clean up database before each test
    await prisma.user.deleteMany({});
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /users/internal', () => {
    it('should create a new user', async () => {
      const userData = {
        keycloakId: uuidv4(),
        email: `test.${Date.now()}@example.com`,
        firstName: 'Test',
        lastName: 'User',
      };

      const response = await request(app.getHttpServer())
        .post('/users/internal')
        .send(userData)
        .expect(201);

      expect(response.body).toMatchObject({
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        keycloakId: userData.keycloakId,
      });
      expect(response.body.id).toBeDefined();
      expect(response.body.createdAt).toBeDefined();
      expect(response.body.updatedAt).toBeDefined();
    });

    it('should return 400 for invalid email', async () => {
      const userData = {
        keycloakId: uuidv4(),
        email: 'invalid-email',
        firstName: 'Test',
        lastName: 'User',
      };

      const response = await request(app.getHttpServer())
        .post('/users/internal')
        .send(userData)
        .expect(400);

      expect(response.body.message).toContain('email must be an email');
    });

    it('should return 400 for missing required fields', async () => {
      const userData = {
        // Missing keycloakId, email, firstName, lastName
      };

      const response = await request(app.getHttpServer())
        .post('/users/internal')
        .send(userData)
        .expect(400);

      expect(response.body.message).toContain('keycloakId should not be empty');
      expect(response.body.message).toContain('email must be an email');
      expect(response.body.message).toContain('firstName should not be empty');
      expect(response.body.message).toContain('lastName should not be empty');
    });

    it('should return 409 for duplicate keycloakId', async () => {
      const keycloakId = uuidv4();
      const userData = {
        keycloakId,
        email: `test1.${Date.now()}@example.com`,
        firstName: 'Test',
        lastName: 'User',
      };

      // First request should succeed
      await request(app.getHttpServer())
        .post('/users/internal')
        .send(userData)
        .expect(201);

      // Second request with same keycloakId should fail
      const response = await request(app.getHttpServer())
        .post('/users/internal')
        .send({
          ...userData,
          email: `test2.${Date.now()}@example.com`,
        })
        .expect(409);

      expect(response.body.message).toContain('already exists');
    });
  });

  describe('GET /users', () => {
    it('should return an empty array when no users exist', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .expect(200);

      expect(response.body).toEqual([]);
    });

    it('should return all users', async () => {
      // Create test users
      const user1 = {
        keycloakId: uuidv4(),
        email: `user1.${Date.now()}@example.com`,
        firstName: 'User',
        lastName: 'One',
      };

      const user2 = {
        keycloakId: uuidv4(),
        email: `user2.${Date.now()}@example.com`,
        firstName: 'User',
        lastName: 'Two',
      };

      await request(app.getHttpServer())
        .post('/users/internal')
        .send(user1);

      await request(app.getHttpServer())
        .post('/users/internal')
        .send(user2);


      const response = await request(app.getHttpServer())
        .get('/users')
        .expect(200);

      expect(response.body.length).toBe(2);
      expect(response.body[0]).toMatchObject({
        email: expect.any(String),
        firstName: expect.any(String),
        lastName: expect.any(String),
        keycloakId: expect.any(String),
      });
    });
  });

  describe('GET /users/:id', () => {
    it('should return a user by id', async () => {
      // Create a test user
      const userData = {
        keycloakId: uuidv4(),
        email: `test.${Date.now()}@example.com`,
        firstName: 'Test',
        lastName: 'User',
      };

      const createResponse = await request(app.getHttpServer())
        .post('/users/internal')
        .send(userData);

      const userId = createResponse.body.id;

      // Get the user by id
      const response = await request(app.getHttpServer())
        .get(`/users/${userId}`)
        .expect(200);

      expect(response.body).toMatchObject({
        id: userId,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        keycloakId: userData.keycloakId,
      });
    });

    it('should return 404 for non-existent user', async () => {
      const nonExistentId = '00000000-0000-0000-0000-000000000000';
      
      const response = await request(app.getHttpServer())
        .get(`/users/${nonExistentId}`)
        .expect(404);

      expect(response.body.message).toContain(`User with ID ${nonExistentId} not found`);
    });
  });
});
