/**
 * Test setup file for User Service
 *
 * This file is executed before running tests to set up the test environment.
 */

import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { config } from 'dotenv';
import { join } from 'path';
import * as fs from 'fs';

// Load environment variables from .env.test file
const envFile = join(process.cwd(), '.env.test');
if (fs.existsSync(envFile)) {
  config({ path: envFile });
} else {
  console.warn(`Warning: ${envFile} not found. Using default environment variables.`);
  // Load from example file if .env.test doesn't exist
  const exampleFile = join(process.cwd(), '.env.test.example');
  if (fs.existsSync(exampleFile)) {
    config({ path: exampleFile });
  }
}

// Set default environment variables for tests
process.env.NODE_ENV = 'test';

declare global {
  // eslint-disable-next-line no-var
  var app: INestApplication;
}

// Configure Jest global setup
beforeAll(async () => {
  // This runs before all tests
  jest.resetAllMocks();
});

// Configure Jest global teardown
afterAll(async () => {
  // Clean up any resources created during tests
  if (global.app) {
    await global.app.close();
  }
});

// Configure Jest timeout
jest.setTimeout(30000); // 30 seconds

// Configure console output during tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

if (process.env.JEST_HIDE_CONSOLE_OUTPUT === 'true') {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
}

// Restore console output after tests
afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
