NODE_ENV=development
PORT=3002

# --- Database Connection ---
# For local development, service runs on host, <PERSON> in Docker exposed on DB_PORT
DB_HOST=localhost
DB_PORT=5433
DB_USERNAME=YOUR_DB_USERNAME
DB_PASSWORD=YOUR_DB_PASSWORD
DB_DATABASE=YOUR_DB_NAME
DATABASE_URL="postgresql://${DB_USERNAME}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_DATABASE}?schema=public"

# HTTP Client Configuration
HTTP_TIMEOUT=5000
HTTP_MAX_REDIRECTS=5

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# --- Service Specific Variables ---
# EXAMPLE_VARIABLE=example_value

# --- Dependent Service URLs (if applicable) ---
# OTHER_SERVICE_URL=http://other-service:3000
