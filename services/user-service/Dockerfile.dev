# Development Dockerfile for user-service optimized for host bind mounts
FROM node:18-alpine

# Install curl for healthchecks
RUN apk add --no-cache curl

# Set environment variables
ENV NODE_ENV=development
ENV DEBUG=nest:*,polyrepo:*
ENV PORT=3000

# Create app directory structure
WORKDIR /app

# Set up directories
# Note: We expect these to be mounted from host, this is just a fallback
RUN mkdir -p /app/node_modules \
    /app/services/user-service/dist \
    /app/services/user-service/node_modules \
    /app/services/user-service/node_modules/.prisma

# For Prisma - ensures binaries are executable if mounted from host
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo 'if [ -d "/app/services/user-service/node_modules/.prisma" ]; then' >> /docker-entrypoint.sh && \
    echo '  chmod +x /app/services/user-service/node_modules/.prisma/client/libquery_engine-*' >> /docker-entrypoint.sh && \
    echo 'fi' >> /docker-entrypoint.sh && \
    echo 'exec "$@"' >> /docker-entrypoint.sh && \
    chmod +x /docker-entrypoint.sh

# Set working directory to user-service
WORKDIR /app/services/user-service

# Expose main port and debug port
EXPOSE 3000 9229

# Ensure Prisma binaries are executable, then start the application
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["yarn", "start:dev"]
