# User Service

## Purpose
User management and profile service providing comprehensive user operations with database persistence, caching, and event-driven messaging. This service handles user profiles, preferences, and user-related business logic within the Polyrepo microservices architecture.

## Features

### Core Functionality
- **User Profile Management**: Complete CRUD operations for user profiles
- **Database Persistence**: PostgreSQL with Prisma ORM for type-safe database operations
- **Soft Delete Support**: Soft delete patterns with recovery capabilities
- **Optimistic Locking**: Version-based concurrency control for data integrity
- **Profile Management**: Extended user profiles with custom fields

### Advanced Features
- **Redis Caching**: Cache-aside patterns with automatic invalidation (~95% performance improvement)
- **Event-Driven Messaging**: Redis Streams for user lifecycle events
- **Health Monitoring**: Comprehensive health checks including database and cache status
- **Observability Integration**: Structured logging, metrics, and distributed tracing
- **API Documentation**: Complete OpenAPI/Swagger documentation

## API Endpoints

### Core User Operations
- `GET /users` - List users with filtering and pagination
- `GET /users/:id` - Get user by ID
- `POST /users` - Create new user
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Soft delete user
- `POST /users/:id/restore` - Restore soft-deleted user

### Health and Monitoring
- `GET /health` - Service health check with database and cache status
- `GET /metrics` - Prometheus metrics exposure
- `GET /api/docs` - Swagger API documentation
- `GET /api/docs-json` - OpenAPI specification (JSON)

## Data Model

### User Entity
```typescript
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  firstName String?
  lastName  String?
  keycloakId String? @unique
  
  // Soft delete and optimistic locking
  deletedAt DateTime?
  version   Int      @default(1)
  
  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  profile   Profile?
}

model Profile {
  id       Int    @id @default(autoincrement())
  userId   Int    @unique
  bio      String?
  avatar   String?
  
  // Soft delete and optimistic locking
  deletedAt DateTime?
  version   Int      @default(1)
  
  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  user User @relation(fields: [userId], references: [id])
}
```

### Event Model
User lifecycle events published to Redis Streams:
```typescript
interface UserEvent {
  eventType: 'user.created' | 'user.updated' | 'user.deleted' | 'user.restored';
  userId: number;
  timestamp: Date;
  correlationId: string;
  metadata: {
    email?: string;
    changes?: Record<string, any>;
    actor?: string;
  };
}
```

## Architecture

### Service Layer Structure
```
src/
├── users/                 # User domain module
│   ├── dto/              # Data transfer objects
│   ├── users.controller.ts
│   ├── users.service.ts
│   └── users.module.ts
├── prisma/               # Database layer
│   ├── prisma.service.ts
│   └── prisma.module.ts
├── health/               # Health checks
├── metrics/              # Metrics exposure
└── observability/        # Logging and tracing
```

### Caching Strategy
- **Cache-aside pattern** with Redis
- **Automatic invalidation** on data changes
- **Performance optimization**: ~95% response time improvement
- **Graceful degradation** when cache is unavailable

### Event Publishing
- **User lifecycle events** published to Redis Streams
- **Correlation ID tracking** for distributed tracing
- **Retry mechanisms** for failed event publishing
- **Batch publishing** for performance optimization

## Configuration

### Environment Variables
```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/userdb
TEST_DATABASE_URL=postgresql://username:password@localhost:5433/userdb_test

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Server Configuration
PORT=3002
NODE_ENV=production

# Observability
LOG_LEVEL=info
METRICS_ENABLED=true
TRACING_ENABLED=true

# Caching
CACHE_TTL=3600
CACHE_ENABLED=true

# Messaging
MESSAGING_ENABLED=true
EVENT_STREAM_NAME=user-events
```

### Database Configuration
```bash
# Prisma configuration
DATABASE_URL="postgresql://username:password@localhost:5432/userdb?schema=public"

# For development with Docker
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/userdb?schema=public"
```

## Development

### Prerequisites
- Node.js 20+
- PostgreSQL 14+
- Redis 7+
- Yarn package manager

### Local Development Setup
```bash
# Install dependencies
cd services/user-service
yarn install

# Build shared libraries (required)
cd ../..
yarn build:libs

# Database setup
cd services/user-service
yarn prisma:migrate:dev
yarn prisma:generate

# Start development server
yarn start:dev
```

### Database Operations
```bash
# Generate Prisma client
yarn prisma:generate

# Run migrations
yarn prisma:migrate:dev

# Deploy migrations (production)
yarn prisma:migrate:deploy

# Reset database
yarn prisma:migrate:reset

# Database GUI
yarn prisma:studio
```

### Testing
```bash
# Unit tests
yarn test

# Integration tests
yarn test:integration

# E2E tests
yarn test:e2e

# Coverage report
yarn test:cov
```

## Template Customization

### Adding Custom User Fields
1. **Update Prisma schema**:
```prisma
model User {
  // ... existing fields
  customField String?
  preferences Json?
}
```

2. **Generate migration**:
```bash
yarn prisma:migrate:dev --name add_custom_fields
```

3. **Update DTOs**:
```typescript
// dto/create-user.dto.ts
export class CreateUserDto {
  @IsEmail()
  email: string;
  
  @IsOptional()
  @IsString()
  customField?: string;
  
  @IsOptional()
  preferences?: Record<string, any>;
}
```

### Custom Business Logic
Add domain-specific user operations:
```typescript
// users.service.ts
@Injectable()
export class UsersService {
  // ... existing methods
  
  async updateUserPreferences(
    id: number, 
    preferences: Record<string, any>
  ): Promise<User> {
    const user = await this.prisma.user.update({
      where: { id },
      data: { preferences, version: { increment: 1 } },
    });
    
    // Invalidate cache
    await this.cacheService.invalidate(`user:${id}`);
    
    // Publish event
    await this.publishUserEvent('user.preferences.updated', user);
    
    return user;
  }
}
```

### Custom Events
Define domain-specific events:
```typescript
// events/custom-events.ts
export interface UserPreferencesUpdatedEvent extends BaseUserEvent {
  eventType: 'user.preferences.updated';
  metadata: {
    userId: number;
    preferences: Record<string, any>;
    previousPreferences?: Record<string, any>;
  };
}
```

### Custom Caching Strategies
Implement specialized caching patterns:
```typescript
// decorators/cache-user-profile.decorator.ts
export function CacheUserProfile(ttl = 3600) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const userId = args[0];
      const cacheKey = `user:profile:${userId}`;
      
      const cached = await this.cacheService.get(cacheKey);
      if (cached.success) {
        return cached.data;
      }
      
      const result = await originalMethod.apply(this, args);
      await this.cacheService.set(cacheKey, result, ttl);
      
      return result;
    };
  };
}
```

## Data Access Patterns

### Repository Pattern Implementation
```typescript
@Injectable()
export class UserRepository {
  constructor(private prisma: PrismaService) {}
  
  async findById(id: number): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id, deletedAt: null },
      include: { profile: true },
    });
  }
  
  async findWithCache(id: number): Promise<User | null> {
    const cacheKey = `user:${id}`;
    
    const cached = await this.cacheService.get(cacheKey);
    if (cached.success) {
      return cached.data;
    }
    
    const user = await this.findById(id);
    if (user) {
      await this.cacheService.set(cacheKey, user, 3600);
    }
    
    return user;
  }
}
```

### Soft Delete Middleware
```typescript
// prisma/soft-delete.middleware.ts
export function softDeleteMiddleware(): Prisma.Middleware {
  return async (params, next) => {
    if (params.action === 'delete') {
      params.action = 'update';
      params.args['data'] = {
        deletedAt: new Date(),
        version: { increment: 1 },
      };
    }
    
    if (params.action === 'findMany' || params.action === 'findFirst') {
      if (!params.args) params.args = {};
      if (!params.args.where) params.args.where = {};
      if (!params.args.where.deletedAt) {
        params.args.where.deletedAt = null;
      }
    }
    
    return next(params);
  };
}
```

## Monitoring and Observability

### Health Checks
Comprehensive health monitoring:
```json
{
  "status": "ok",
  "service": "user-service",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "checks": {
    "database": {
      "status": "ok",
      "latency": "15ms"
    },
    "cache": {
      "status": "ok",
      "latency": "2ms"
    },
    "messaging": {
      "status": "ok",
      "queueDepth": 0
    }
  }
}
```

### Metrics
Prometheus metrics at `/metrics`:
- Database query duration and count
- Cache hit/miss ratios
- Event publishing metrics
- HTTP request metrics
- User operation counts

### Business Events
Structured business event logging:
```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "eventType": "user.created",
  "userId": 123,
  "correlationId": "123e4567-e89b-12d3-a456-426614174000",
  "metadata": {
    "email": "<EMAIL>",
    "source": "api",
    "actor": "system"
  }
}
```

## Performance

### Caching Performance
- **Read operations**: ~95% performance improvement with cache hits
- **Cache hit ratio**: Target >90% for frequently accessed users
- **Cache invalidation**: <10ms for pattern-based invalidation

### Database Performance
- **Query optimization**: Indexed fields for common queries
- **Connection pooling**: Configured for optimal throughput
- **Migration performance**: Zero-downtime migrations

### Event Publishing
- **Batch publishing**: Up to 100 events per batch
- **Async processing**: Non-blocking event publishing
- **Retry logic**: Exponential backoff for failed events

## Security

### Data Protection
- **Field validation**: Strong input validation with class-validator
- **SQL injection protection**: Prisma ORM provides built-in protection
- **Soft delete**: Prevents accidental data loss
- **Optimistic locking**: Prevents concurrent update conflicts

### Authentication Integration
```typescript
// guards/user-owner.guard.ts
@Injectable()
export class UserOwnerGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const userId = parseInt(request.params.id);
    
    // Allow if user is accessing their own data or is admin
    return user.id === userId || user.roles.includes('admin');
  }
}
```

### Data Validation
```typescript
// dto/create-user.dto.ts
export class CreateUserDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;
  
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName: string;
  
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName: string;
  
  @IsOptional()
  @IsUUID()
  keycloakId?: string;
}
```

## Error Handling

### Custom Exceptions
```typescript
// exceptions/user-not-found.exception.ts
export class UserNotFoundException extends NotFoundException {
  constructor(userId: number) {
    super(`User with ID ${userId} not found`);
  }
}

// exceptions/user-already-exists.exception.ts
export class UserAlreadyExistsException extends ConflictException {
  constructor(email: string) {
    super(`User with email ${email} already exists`);
  }
}
```

### Database Error Handling
```typescript
// utils/prisma-error-handler.ts
export function handlePrismaError(error: any) {
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P2002':
        throw new ConflictException('Unique constraint violation');
      case 'P2025':
        throw new NotFoundException('Record not found');
      default:
        throw new InternalServerErrorException('Database error');
    }
  }
  throw error;
}
```

## Testing

### Unit Testing
```typescript
// users.service.spec.ts
describe('UsersService', () => {
  let service: UsersService;
  let prisma: PrismaService;
  let cache: CacheService;
  
  beforeEach(async () => {
    const { service: usersService } = await TestModuleBuilder
      .createServiceTestModule(UsersService, [
        { provide: PrismaService, useValue: mockPrismaService },
      ]);
    
    service = usersService;
    prisma = service['prisma'];
    cache = service['cacheService'];
  });
  
  it('should create user with cache invalidation', async () => {
    const userData = TestDataGenerator.createTestUser();
    const result = await service.create(userData);
    
    expect(result).toBeDefined();
    expect(cache.invalidate).toHaveBeenCalledWith('users:*');
  });
});
```

### Integration Testing
```typescript
// users.integration.spec.ts
describe('Users Integration', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  
  beforeAll(async () => {
    TestEnvironment.setupEnvironment('integration', 'user-service');
    
    const module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    
    app = module.createNestApplication();
    await app.init();
    
    prisma = app.get(PrismaService);
  });
  
  it('should create and retrieve user', async () => {
    const userData = TestDataGenerator.createTestUser();
    
    const createResponse = await request(app.getHttpServer())
      .post('/users')
      .send(userData)
      .expect(201);
    
    const userId = createResponse.body.id;
    
    await request(app.getHttpServer())
      .get(`/users/${userId}`)
      .expect(200)
      .expect(res => {
        expect(res.body.email).toBe(userData.email);
      });
  });
});
```

## Troubleshooting

### Common Issues

**Database Connection Problems**
```bash
# Check database connectivity
yarn prisma:studio

# Verify connection string
echo $DATABASE_URL

# Test database connection
yarn prisma db pull
```

**Cache Connection Issues**
```bash
# Test Redis connectivity
redis-cli ping

# Check Redis configuration
redis-cli info

# Monitor cache operations
redis-cli monitor
```

**Migration Problems**
```bash
# Check migration status
yarn prisma:migrate:status

# Reset migrations (development only)
yarn prisma:migrate:reset

# Deploy migrations
yarn prisma:migrate:deploy
```

**Performance Issues**
```bash
# Check slow queries
yarn prisma:studio

# Monitor cache hit ratio
curl http://localhost:3002/metrics | grep cache

# Check event queue depth
curl http://localhost:3002/health
```

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=debug
export PRISMA_DEBUG=true

# Database query logging
export DATABASE_LOGGING=true

# Cache operation logging
export CACHE_DEBUG=true
```

## Dependencies

### Core Dependencies
- `@nestjs/common` - NestJS framework core
- `@prisma/client` - Database ORM client
- `prisma` - Database toolkit
- `@nestjs/swagger` - API documentation

### Template Dependencies
- `@libs/observability` - Logging, metrics, tracing
- `@libs/caching` - Redis caching layer
- `@libs/messaging` - Event-driven messaging
- `@libs/prisma-utils` - Prisma utilities and middleware
- `@libs/testing-utils` - Shared testing utilities

### Development Dependencies
- `@nestjs/testing` - Testing utilities
- `jest` - Testing framework
- `supertest` - HTTP testing
- `@types/*` - TypeScript type definitions

## Contributing

### Code Standards
- Follow NestJS and Prisma conventions
- Use TypeScript strict mode
- Implement comprehensive error handling
- Add unit and integration tests
- Update API documentation
- Include database migrations

### Database Guidelines
- Use descriptive migration names
- Include rollback instructions
- Test migrations thoroughly
- Document schema changes
- Follow naming conventions

### Testing Requirements
- Unit test coverage >90%
- Integration tests for all endpoints
- Database integration tests
- Cache integration tests
- Event publishing tests

## License
Private - Part of Polyrepo template project