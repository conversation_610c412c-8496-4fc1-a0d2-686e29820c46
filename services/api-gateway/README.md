# API Gateway Service

## Purpose
Central entry point and routing service for the Polyrepo microservices architecture. The API Gateway provides unified access to all backend services with cross-cutting concerns like authentication, rate limiting, documentation aggregation, and request tracing.

## Features

### Core Functionality
- **Request Routing & Proxying**: Intelligent routing to backend services based on URL patterns
- **Documentation Aggregation**: Combines OpenAPI specifications from all services into unified documentation
- **Request Correlation**: Automatic correlation ID generation and propagation for distributed tracing
- **Rate Limiting**: Configurable rate limiting to protect backend services
- **Health Monitoring**: Comprehensive health checks and metrics exposure

### Cross-Cutting Concerns
- **Authentication**: JWT token validation and forwarding to backend services
- **CORS Handling**: Cross-origin request support for web applications
- **Error Handling**: Standardized error responses and graceful failure handling
- **Observability**: Structured logging, metrics, and tracing integration
- **Security Headers**: Automatic security header injection

## API Endpoints

### Core Endpoints
- `GET /` - Welcome message and service status
- `GET /health` - Health check endpoint
- `GET /metrics` - Prometheus metrics exposure

### Documentation Endpoints
- `GET /docs` - Aggregated Swagger UI for all services
- `GET /docs/swagger` - Aggregated OpenAPI specification (JSON)
- `GET /api/docs` - Gateway-specific Swagger UI
- `GET /api/docs-json` - Gateway-specific OpenAPI specification

### Proxied Service Endpoints
- `/auth/**` - Routes to Auth Service (authentication, user management)
- `/users/**` - Routes to User Service (user profiles, operations)

## Architecture

### Service Discovery
The gateway automatically discovers and aggregates documentation from:
```typescript
const services = [
  {
    name: 'Auth Service',
    url: process.env.AUTH_SERVICE_URL || 'http://auth-service:3001',
    pathPrefix: '/auth',
    docsEndpoint: '/api/docs-json'
  },
  {
    name: 'User Service', 
    url: process.env.USER_SERVICE_URL || 'http://user-service:3002',
    pathPrefix: '/users',
    docsEndpoint: '/api/docs-json'
  }
];
```

### Middleware Chain
1. **Correlation Middleware** - Generates/propagates correlation IDs
2. **Throttle Middleware** - Rate limiting protection
3. **Proxy Middleware** - Service routing and forwarding

### Request Flow
```
Client Request → API Gateway → Correlation ID → Rate Limiting → Service Proxy → Backend Service
                     ↓
                Response ← Correlation Headers ← Service Response ← Backend Service
```

## Configuration

### Environment Variables
```bash
# Service URLs
AUTH_SERVICE_URL=http://auth-service:3001
USER_SERVICE_URL=http://user-service:3002

# Server Configuration
PORT=3000
NODE_ENV=production

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Observability
LOG_LEVEL=info
METRICS_ENABLED=true
```

### Docker Configuration
```dockerfile
# Production deployment
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/main.js"]
```

## Development

### Prerequisites
- Node.js 20+
- Yarn package manager
- Docker & Docker Compose (for full stack development)

### Local Development Setup
```bash
# Install dependencies
cd services/api-gateway
yarn install

# Build shared libraries (required)
cd ../..
yarn build:libs

# Development mode
cd services/api-gateway
yarn start:dev

# Watch mode with webpack
yarn build:webpack:watch
```

### Testing
```bash
# Unit tests
yarn test

# Integration tests
yarn test:integration

# E2E tests
yarn test:e2e

# Coverage report
yarn test:cov
```

### Build and Deployment
```bash
# Standard build
yarn build

# Webpack build (production)
yarn build:webpack

# Docker build and restart
yarn bundle:rebuild:restart
```

## Template Customization

### Adding New Services
1. **Update service configuration** in `src/proxy/proxy.service.ts`:
```typescript
private readonly serviceConfigs = [
  // Existing services...
  {
    name: 'New Service',
    url: this.configService.get<string>('NEW_SERVICE_URL') || 'http://new-service:3003',
    pathPrefix: '/new',
    tag: 'new-service',
  }
];
```

2. **Add proxy route** in `src/app.module.ts`:
```typescript
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CorrelationMiddleware, ThrottleMiddleware)
      .forRoutes('*')
      .apply(this.proxyService.getProxyMiddleware('/new'))
      .forRoutes('/new/*');
  }
}
```

3. **Update environment variables**:
```bash
NEW_SERVICE_URL=http://new-service:3003
```

### Custom Middleware
Add custom middleware to the chain:
```typescript
// src/middleware/custom.middleware.ts
@Injectable()
export class CustomMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Custom logic here
    next();
  }
}

// Add to app.module.ts
consumer
  .apply(CorrelationMiddleware, CustomMiddleware, ThrottleMiddleware)
  .forRoutes('*');
```

### Authentication Integration
Configure JWT authentication:
```typescript
// src/auth/jwt.strategy.ts
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(payload: any) {
    return { userId: payload.sub, username: payload.username };
  }
}
```

### Rate Limiting Configuration
Customize rate limiting rules:
```typescript
// src/throttler/custom-throttler.guard.ts
@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  protected async getTracker(req: Record<string, any>): Promise<string> {
    // Custom tracking logic (e.g., by user ID, API key)
    return req.user?.id || req.ip;
  }
}
```

## Monitoring and Observability

### Health Checks
The gateway provides detailed health information:
```json
{
  "status": "ok",
  "service": "api-gateway",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "environment": "production"
}
```

### Metrics
Prometheus metrics available at `/metrics`:
- HTTP request duration and count
- Rate limiting metrics
- Proxy response times
- Error rates by service

### Logging
Structured logging with correlation IDs:
```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "level": "info",
  "message": "Request processed",
  "correlationId": "123e4567-e89b-12d3-a456-426614174000",
  "method": "GET",
  "path": "/auth/login",
  "duration": 150,
  "statusCode": 200
}
```

### Tracing
Distributed tracing with OpenTelemetry:
- Automatic span creation for incoming requests
- Correlation ID propagation to backend services
- Jaeger integration for trace visualization

## Security

### CORS Configuration
```typescript
app.enableCors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Correlation-ID'],
  credentials: true,
});
```

### Security Headers
Automatic security headers:
- `X-Correlation-ID` - Request tracing
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`

### Input Validation
```typescript
// Global validation pipe
app.useGlobalPipes(new ValidationPipe({
  whitelist: true,
  forbidNonWhitelisted: true,
  transform: true,
}));
```

## Error Handling

### Global Exception Filter
```typescript
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = exception instanceof HttpException
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    response.status(status).json({
      statusCode: status,
      timestamp: new Date().toISOString(),
      path: request.url,
      correlationId: request.headers['x-correlation-id'],
    });
  }
}
```

### Proxy Error Handling
Graceful handling of backend service failures:
- 502 Bad Gateway - Service unavailable
- 503 Service Unavailable - Service overloaded
- 504 Gateway Timeout - Service timeout
- Automatic retry logic for transient failures

## Performance

### Optimization Features
- HTTP keep-alive connections to backend services
- Response compression
- Static asset caching
- Connection pooling

### Load Testing
```bash
# Artillery load testing
artillery run test/load/gateway-load-test.yml

# K6 performance testing
k6 run test/performance/gateway-perf-test.js
```

### Performance Metrics
- Average response time: <100ms
- 95th percentile: <500ms
- Throughput: 1000+ RPS
- Error rate: <0.1%

## Troubleshooting

### Common Issues

**Service Discovery Problems**
- Check service URLs in environment variables
- Verify backend services are running and healthy
- Test direct service connectivity

**Rate Limiting Issues**
- Adjust `THROTTLE_TTL` and `THROTTLE_LIMIT`
- Check client rate limiting headers
- Monitor rate limiting metrics

**Documentation Aggregation Failures**
- Verify backend services expose `/api/docs-json`
- Check service connectivity
- Review aggregation logs for errors

**CORS Errors**
- Update `ALLOWED_ORIGINS` environment variable
- Check request headers and methods
- Verify credentials configuration

### Debugging
```bash
# Enable debug logging
export LOG_LEVEL=debug

# Check service health
curl http://localhost:3000/health

# Test specific service proxy
curl http://localhost:3000/auth/health

# Check metrics
curl http://localhost:3000/metrics
```

## Dependencies

### Core Dependencies
- `@nestjs/common` - NestJS framework core
- `@nestjs/swagger` - OpenAPI documentation
- `http-proxy-middleware` - Service proxying
- `@nestjs/throttler` - Rate limiting

### Template Dependencies
- `@libs/observability` - Logging, metrics, tracing
- `@libs/testing-utils` - Shared testing utilities

## Contributing

### Code Standards
- Follow NestJS conventions and patterns
- Use TypeScript strict mode
- Implement comprehensive error handling
- Add unit tests for all new features
- Update documentation for API changes

### Testing Requirements
- Unit test coverage >90%
- Integration tests for all endpoints
- E2E tests for complete workflows
- Performance tests for critical paths

### Documentation Updates
- Update this README for new features
- Add OpenAPI annotations for new endpoints
- Document configuration changes
- Include troubleshooting information

## License
Private - Part of Polyrepo template project