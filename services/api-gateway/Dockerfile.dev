# Development Dockerfile for API Gateway optimized for host bind mounts
FROM node:18-alpine

# Install curl for healthchecks
RUN apk add --no-cache curl

# Set environment variables
ENV NODE_ENV=development
ENV DEBUG=nest:*,polyrepo:*
ENV PORT=3000

# Create app directory structure
WORKDIR /app

# Set up directories
# Note: We expect these to be mounted from host, this is just a fallback
RUN mkdir -p /app/node_modules \
    /app/services/api-gateway/dist \
    /app/services/api-gateway/node_modules

# Set working directory to api-gateway
WORKDIR /app/services/api-gateway

# Expose main port and debug port
EXPOSE 3000 9229

# Start the application with hot reloading
# All node_modules are mounted from host so no install needed
CMD ["yarn", "start:dev"]
