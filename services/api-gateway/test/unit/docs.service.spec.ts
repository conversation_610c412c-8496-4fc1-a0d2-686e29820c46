import { Test, TestingModule } from '@nestjs/testing';
import { DocsService } from '../../src/docs/docs.service';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { MockFactory, TestDataGenerator } from '@libs/testing-utils';
import { of, throwError } from 'rxjs';

describe('DocsService', () => {
  let service: DocsService;
  let httpService: jest.Mocked<HttpService>;
  let configService: jest.Mocked<ConfigService>;

  const mockConfigService = MockFactory.createConfigService({
    AUTH_SERVICE_URL: 'http://auth-service:3001',
    USER_SERVICE_URL: 'http://user-service:3002',
  });

  const mockHttpService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocsService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<DocsService>(DocsService);
    httpService = module.get(HttpService) as jest.Mocked<HttpService>;
    configService = module.get(ConfigService) as jest.Mocked<ConfigService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAggregatedDocs', () => {
    it('should aggregate documentation from all services', async () => {
      const authDocs = {
        openapi: '3.0.0',
        info: { title: 'Auth Service', version: '1.0.0' },
        paths: {
          '/auth/login': {
            post: { summary: 'Login user' }
          }
        }
      };

      const userDocs = {
        openapi: '3.0.0',
        info: { title: 'User Service', version: '1.0.0' },
        paths: {
          '/users': {
            get: { summary: 'Get users' }
          }
        }
      };

      // Mock HTTP responses
      httpService.get
        .mockReturnValueOnce(of({ data: authDocs } as any))
        .mockReturnValueOnce(of({ data: userDocs } as any));

      const result = await service.getAggregatedDocs();

      expect(result).toBeDefined();
      expect(result.info.title).toBe('Polyrepo API Gateway');
      expect(result.paths).toHaveProperty('/auth/auth/login');
      expect(result.paths).toHaveProperty('/users/users');
      
      expect(httpService.get).toHaveBeenCalledTimes(2);
      expect(httpService.get).toHaveBeenCalledWith('http://auth-service:3001/api/docs-json');
      expect(httpService.get).toHaveBeenCalledWith('http://user-service:3002/api/docs-json');
    });

    it('should handle service documentation fetch errors', async () => {
      // Mock auth service success, user service failure
      const authDocs = {
        openapi: '3.0.0',
        info: { title: 'Auth Service', version: '1.0.0' },
        paths: {
          '/auth/login': {
            post: { summary: 'Login user' }
          }
        }
      };

      httpService.get
        .mockReturnValueOnce(of({ data: authDocs } as any))
        .mockReturnValueOnce(throwError(() => new Error('Service unavailable')));

      const result = await service.getAggregatedDocs();

      expect(result).toBeDefined();
      expect(result.info.title).toBe('Polyrepo API Gateway');
      expect(result.paths).toHaveProperty('/auth/auth/login');
      // Should not have user service paths due to error
      expect(Object.keys(result.paths).some(path => path.includes('/users'))).toBe(false);
    });

    it('should create proper path prefixes for services', async () => {
      const serviceDocs = {
        openapi: '3.0.0',
        info: { title: 'Service', version: '1.0.0' },
        paths: {
          '/test': {
            get: { summary: 'Test endpoint' }
          },
          '/nested/path': {
            post: { summary: 'Nested endpoint' }
          }
        }
      };

      httpService.get
        .mockReturnValueOnce(of({ data: serviceDocs } as any))
        .mockReturnValueOnce(of({ data: serviceDocs } as any));

      const result = await service.getAggregatedDocs();

      expect(result.paths).toHaveProperty('/auth/test');
      expect(result.paths).toHaveProperty('/auth/nested/path');
      expect(result.paths).toHaveProperty('/users/test');
      expect(result.paths).toHaveProperty('/users/nested/path');
    });

    it('should set proper tags for service endpoints', async () => {
      const serviceDocs = {
        openapi: '3.0.0',
        info: { title: 'Service', version: '1.0.0' },
        paths: {
          '/test': {
            get: { 
              summary: 'Test endpoint',
              tags: ['original-tag']
            }
          }
        }
      };

      httpService.get
        .mockReturnValueOnce(of({ data: serviceDocs } as any))
        .mockReturnValueOnce(of({ data: serviceDocs } as any));

      const result = await service.getAggregatedDocs();

      expect(result.paths['/auth/test'].get.tags).toContain('auth');
      expect(result.paths['/users/test'].get.tags).toContain('users');
    });
  });

  describe('error handling', () => {
    it('should return base documentation when all services fail', async () => {
      httpService.get
        .mockReturnValueOnce(throwError(() => new Error('Auth service down')))
        .mockReturnValueOnce(throwError(() => new Error('User service down')));

      const result = await service.getAggregatedDocs();

      expect(result).toBeDefined();
      expect(result.info.title).toBe('Polyrepo API Gateway');
      expect(result.paths).toEqual({});
    });

    it('should handle malformed documentation responses', async () => {
      httpService.get
        .mockReturnValueOnce(of({ data: 'invalid json' } as any))
        .mockReturnValueOnce(of({ data: null } as any));

      const result = await service.getAggregatedDocs();

      expect(result).toBeDefined();
      expect(result.info.title).toBe('Polyrepo API Gateway');
    });
  });
});