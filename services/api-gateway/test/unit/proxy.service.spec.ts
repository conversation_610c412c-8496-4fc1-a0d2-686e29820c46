import { Test, TestingModule } from '@nestjs/testing';
import { ProxyService } from '../../src/proxy/proxy.service';
import { ConfigService } from '@nestjs/config';
import { MockFactory, TestDataGenerator } from '@libs/testing-utils';

describe('ProxyService', () => {
  let service: ProxyService;
  let configService: jest.Mocked<ConfigService>;

  const mockConfigService = MockFactory.createConfigService({
    AUTH_SERVICE_URL: 'http://auth-service:3001',
    USER_SERVICE_URL: 'http://user-service:3002',
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProxyService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<ProxyService>(ProxyService);
    configService = module.get(ConfigService) as jest.Mocked<ConfigService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getProxyMiddleware', () => {
    it('should create auth service proxy middleware', () => {
      const middleware = service.getProxyMiddleware('/auth');
      
      expect(middleware).toBeDefined();
      expect(typeof middleware).toBe('function');
    });

    it('should create user service proxy middleware', () => {
      const middleware = service.getProxyMiddleware('/users');
      
      expect(middleware).toBeDefined();
      expect(typeof middleware).toBe('function');
    });

    it('should throw error for unknown service path', () => {
      expect(() => {
        service.getProxyMiddleware('/unknown');
      }).toThrow();
    });
  });

  describe('service configuration', () => {
    it('should use correct auth service URL from config', () => {
      expect(configService.get).toHaveBeenCalledWith('AUTH_SERVICE_URL');
    });

    it('should use correct user service URL from config', () => {
      expect(configService.get).toHaveBeenCalledWith('USER_SERVICE_URL');
    });

    it('should have fallback URLs when config is not provided', () => {
      // Test fallback behavior
      const fallbackConfigService = MockFactory.createConfigService({});
      const module = Test.createTestingModule({
        providers: [
          ProxyService,
          {
            provide: ConfigService,
            useValue: fallbackConfigService,
          },
        ],
      });

      expect(fallbackConfigService).toBeDefined();
    });
  });

  describe('proxy error handling', () => {
    it('should handle proxy errors gracefully', () => {
      const middleware = service.getProxyMiddleware('/auth');
      
      // Test that middleware function is properly created
      expect(middleware).toBeDefined();
      expect(typeof middleware).toBe('function');
      
      // Note: Full error handling testing would require integration tests
      // with actual HTTP requests and responses
    });
  });
});