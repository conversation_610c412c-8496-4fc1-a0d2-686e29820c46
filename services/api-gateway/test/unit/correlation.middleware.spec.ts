import { Test, TestingModule } from '@nestjs/testing';
import { CorrelationMiddleware } from '../../src/correlation/correlation.middleware';
import { MockFactory, TestDataGenerator } from '@libs/testing-utils';
import { Request, Response, NextFunction } from 'express';

interface RequestWithCorrelation extends Request {
  correlationId?: string;
}

describe('CorrelationMiddleware', () => {
  let middleware: CorrelationMiddleware;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CorrelationMiddleware],
    }).compile();

    middleware = module.get<CorrelationMiddleware>(CorrelationMiddleware);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(middleware).toBeDefined();
  });

  describe('use', () => {
    let mockRequest: Partial<RequestWithCorrelation>;
    let mockResponse: Partial<Response>;
    let mockNext: NextFunction;

    beforeEach(() => {
      mockRequest = {
        headers: {},
      };
      mockResponse = {
        setHeader: jest.fn(),
      };
      mockNext = jest.fn();
    });

    it('should generate new correlation ID when none exists', () => {
      middleware.use(
        mockRequest as RequestWithCorrelation,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.correlationId).toBeDefined();
      expect(typeof mockRequest.correlationId).toBe('string');
      expect(mockRequest.correlationId).toMatch(/^[0-9a-f-]{36}$/); // UUID pattern
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Correlation-ID',
        mockRequest.correlationId
      );
      expect(mockRequest.headers!['x-correlation-id']).toBe(mockRequest.correlationId);
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should use existing correlation ID from header', () => {
      const existingId = TestDataGenerator.generateCorrelationId();
      mockRequest.headers = {
        'x-correlation-id': existingId,
      };

      middleware.use(
        mockRequest as RequestWithCorrelation,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.correlationId).toBe(existingId);
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Correlation-ID',
        existingId
      );
      expect(mockRequest.headers!['x-correlation-id']).toBe(existingId);
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle correlation ID as array (take first)', () => {
      const firstId = TestDataGenerator.generateCorrelationId();
      const secondId = TestDataGenerator.generateCorrelationId();
      mockRequest.headers = {
        'x-correlation-id': [firstId, secondId],
      };

      middleware.use(
        mockRequest as RequestWithCorrelation,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.correlationId).toBe(firstId);
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'X-Correlation-ID',
        firstId
      );
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle empty correlation ID header', () => {
      mockRequest.headers = {
        'x-correlation-id': '',
      };

      middleware.use(
        mockRequest as RequestWithCorrelation,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.correlationId).toBeDefined();
      expect(mockRequest.correlationId).not.toBe('');
      expect(typeof mockRequest.correlationId).toBe('string');
      expect(mockRequest.correlationId).toMatch(/^[0-9a-f-]{36}$/);
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should handle missing headers object', () => {
      mockRequest = {}; // No headers property

      middleware.use(
        mockRequest as RequestWithCorrelation,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.correlationId).toBeDefined();
      expect(typeof mockRequest.correlationId).toBe('string');
      expect(mockRequest.correlationId).toMatch(/^[0-9a-f-]{36}$/);
      expect(mockNext).toHaveBeenCalledTimes(1);
    });

    it('should generate unique correlation IDs for different requests', () => {
      const request1: Partial<RequestWithCorrelation> = { headers: {} };
      const request2: Partial<RequestWithCorrelation> = { headers: {} };
      const response1: Partial<Response> = { setHeader: jest.fn() };
      const response2: Partial<Response> = { setHeader: jest.fn() };

      middleware.use(
        request1 as RequestWithCorrelation,
        response1 as Response,
        mockNext
      );

      middleware.use(
        request2 as RequestWithCorrelation,
        response2 as Response,
        mockNext
      );

      expect(request1.correlationId).toBeDefined();
      expect(request2.correlationId).toBeDefined();
      expect(request1.correlationId).not.toBe(request2.correlationId);
    });

    it('should properly set headers in request for downstream services', () => {
      const correlationId = TestDataGenerator.generateCorrelationId();
      mockRequest.headers = {
        'x-correlation-id': correlationId,
        'other-header': 'value',
      };

      middleware.use(
        mockRequest as RequestWithCorrelation,
        mockResponse as Response,
        mockNext
      );

      expect(mockRequest.headers!['x-correlation-id']).toBe(correlationId);
      expect(mockRequest.headers!['other-header']).toBe('value');
      expect(mockRequest.correlationId).toBe(correlationId);
    });
  });
});