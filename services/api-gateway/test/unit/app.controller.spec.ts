import { Test, TestingModule } from '@nestjs/testing';
import { AppController } from '../../src/app.controller';
import { AppService } from '../../src/app.service';
import { MockFactory, TestDataGenerator } from '@libs/testing-utils';

describe('AppController', () => {
  let controller: AppController;
  let appService: jest.Mocked<AppService>;

  const mockAppService = {
    getHello: jest.fn(),
    getHealthStatus: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        {
          provide: AppService,
          useValue: mockAppService,
        },
      ],
    }).compile();

    controller = module.get<AppController>(AppController);
    appService = module.get(AppService) as jest.Mocked<AppService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getHello', () => {
    it('should return "Hello World!"', () => {
      const expectedMessage = 'Hello API Gateway!';
      mockAppService.getHello.mockReturnValue(expectedMessage);

      const result = controller.getHello();

      expect(result).toBe(expectedMessage);
      expect(appService.getHello).toHaveBeenCalledTimes(1);
    });
  });

  describe('getHealthStatus', () => {
    it('should return health status', () => {
      const healthStatus = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'api-gateway',
      };
      
      mockAppService.getHealthStatus.mockReturnValue(healthStatus);

      const result = controller.getHealthStatus();

      expect(result).toEqual(healthStatus);
      expect(appService.getHealthStatus).toHaveBeenCalledTimes(1);
    });

    it('should return health status with proper format', () => {
      const mockTimestamp = '2024-01-01T00:00:00.000Z';
      const healthStatus = {
        status: 'ok',
        timestamp: mockTimestamp,
        service: 'api-gateway',
      };
      
      mockAppService.getHealthStatus.mockReturnValue(healthStatus);

      const result = controller.getHealthStatus();

      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('service');
      expect(result.service).toBe('api-gateway');
    });
  });
});