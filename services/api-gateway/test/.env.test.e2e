# E2E test environment variables  
NODE_ENV=test
LOG_LEVEL=info

# Service URLs for E2E tests
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002

# Test configuration
RUN_INTEGRATION_TESTS=true
RUN_E2E_TESTS=true

# E2E test specific
E2E_TIMEOUT=60000
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!

# Retry configuration for flaky external dependencies
TEST_RETRY_COUNT=3
TEST_RETRY_DELAY=1000