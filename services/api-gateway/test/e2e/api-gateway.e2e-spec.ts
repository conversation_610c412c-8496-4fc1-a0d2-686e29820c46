import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { TestEnvironment, TestDataGenerator } from '@libs/testing-utils';

describe('API Gateway E2E', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // Setup test environment
    TestEnvironment.setupEnvironment('e2e', 'api-gateway');

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Wait for application to be fully ready
    await TestEnvironment.waitForProcessing(1000);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Complete workflow tests', () => {
    it('should handle full API Gateway workflow', async () => {
      const correlationId = TestDataGenerator.generateCorrelationId();

      // 1. Check health
      const healthResponse = await request(app.getHttpServer())
        .get('/health')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(healthResponse.headers['x-correlation-id']).toBe(correlationId);
      expect(healthResponse.body.status).toBe('ok');

      // 2. Get aggregated docs
      const docsResponse = await request(app.getHttpServer())
        .get('/docs/swagger')
        .set('X-Correlation-ID', correlationId)
        .expect(200);

      expect(docsResponse.headers['x-correlation-id']).toBe(correlationId);
      expect(docsResponse.body.info.title).toBe('Polyrepo API Gateway');

      // 3. Check metrics
      await request(app.getHttpServer())
        .get('/metrics')
        .set('X-Correlation-ID', correlationId)
        .expect(200);
    });

    it('should maintain correlation ID across multiple requests', async () => {
      const correlationId = TestDataGenerator.generateCorrelationId();
      const endpoints = ['/', '/health', '/metrics'];

      for (const endpoint of endpoints) {
        const response = await request(app.getHttpServer())
          .get(endpoint)
          .set('X-Correlation-ID', correlationId);

        expect(response.headers['x-correlation-id']).toBe(correlationId);
      }
    });
  });

  describe('Service proxy end-to-end', () => {
    // Note: These tests require actual backend services to be running
    // E2E tests automatically use real services

    it('should proxy requests to auth service', async () => {

      const correlationId = TestDataGenerator.generateCorrelationId();

      const response = await request(app.getHttpServer())
        .get('/auth/health')
        .set('X-Correlation-ID', correlationId);

      // Should either succeed (200) or fail gracefully (404/502/503)
      expect([200, 404, 502, 503]).toContain(response.status);
      
      if (response.status === 200) {
        expect(response.body).toHaveProperty('status');
        expect(response.body.service).toBe('auth-service');
      }
    });

    it('should proxy requests to user service', async () => {

      const correlationId = TestDataGenerator.generateCorrelationId();

      const response = await request(app.getHttpServer())
        .get('/users/health')
        .set('X-Correlation-ID', correlationId);

      // Should either succeed (200) or fail gracefully (404/502/503)
      expect([200, 404, 502, 503]).toContain(response.status);
      
      if (response.status === 200) {
        expect(response.body).toHaveProperty('status');
        expect(response.body.service).toBe('user-service');
      }
    });
  });

  describe('Authentication flow simulation', () => {
    it('should handle authentication workflow through proxy', async () => {

      const correlationId = TestDataGenerator.generateCorrelationId();
      const testUser = TestDataGenerator.createRegistrationData();

      // 1. Try to register user through auth service
      const registerResponse = await request(app.getHttpServer())
        .post('/auth/auth/register')
        .set('X-Correlation-ID', correlationId)
        .send(testUser);

      // Should either succeed or fail gracefully
      expect([200, 201, 400, 404, 409, 502, 503]).toContain(registerResponse.status);

      // 2. Try to login through auth service
      const loginData = TestDataGenerator.createLoginData({
        email: testUser.email,
        password: testUser.password,
      });

      const loginResponse = await request(app.getHttpServer())
        .post('/auth/auth/login')
        .set('X-Correlation-ID', correlationId)
        .send(loginData);

      // Should either succeed or fail gracefully
      expect([200, 400, 401, 404, 502, 503]).toContain(loginResponse.status);

      // If successful, should have tokens
      if (loginResponse.status === 200) {
        expect(loginResponse.body).toHaveProperty('access_token');
        expect(loginResponse.body).toHaveProperty('refresh_token');
      }
    });
  });

  describe('Performance and reliability', () => {
    it('should handle multiple concurrent requests', async () => {
      const concurrentRequests = 10;
      const promises = [];

      for (let i = 0; i < concurrentRequests; i++) {
        const correlationId = TestDataGenerator.generateCorrelationId();
        promises.push(
          request(app.getHttpServer())
            .get('/health')
            .set('X-Correlation-ID', correlationId)
        );
      }

      const responses = await Promise.all(promises);

      // All requests should complete
      expect(responses).toHaveLength(concurrentRequests);
      
      // All should have status 200
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.headers['x-correlation-id']).toBeDefined();
      });

      // All correlation IDs should be unique
      const correlationIds = responses.map(r => r.headers['x-correlation-id']);
      const uniqueIds = new Set(correlationIds);
      expect(uniqueIds.size).toBe(concurrentRequests);
    });

    it('should handle requests with various payload sizes', async () => {
      const smallPayload = { test: 'small' };
      const largePayload = {
        test: 'large',
        data: 'x'.repeat(1000), // 1KB of data
      };

      // Small payload
      const smallResponse = await request(app.getHttpServer())
        .post('/unknown-endpoint')
        .send(smallPayload);

      // Large payload
      const largeResponse = await request(app.getHttpServer())
        .post('/unknown-endpoint')
        .send(largePayload);

      // Both should be handled (even if they return 404)
      expect([404, 500, 502, 503]).toContain(smallResponse.status);
      expect([404, 500, 502, 503]).toContain(largeResponse.status);
    });
  });

  describe('Error scenarios', () => {
    it('should handle malformed requests gracefully', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/auth/register')
        .send('invalid json string')
        .set('Content-Type', 'application/json');

      // Should handle malformed JSON gracefully
      expect([400, 404, 502, 503]).toContain(response.status);
    });

    it('should handle missing content-type headers', async () => {
      const response = await request(app.getHttpServer())
        .post('/auth/auth/register')
        .send({ test: 'data' });

      // Should handle missing content-type gracefully
      expect([400, 404, 502, 503]).toContain(response.status);
    });

    it('should handle requests with invalid correlation IDs', async () => {
      const response = await request(app.getHttpServer())
        .get('/health')
        .set('X-Correlation-ID', 'invalid-uuid-format');

      // Should handle invalid correlation ID by generating new one
      expect(response.status).toBe(200);
      expect(response.headers['x-correlation-id']).toBeDefined();
      expect(response.headers['x-correlation-id']).not.toBe('invalid-uuid-format');
    });
  });

  describe('Documentation aggregation', () => {
    it('should successfully aggregate service documentation', async () => {
      const response = await request(app.getHttpServer())
        .get('/docs/swagger')
        .expect(200);

      expect(response.body).toHaveProperty('openapi');
      expect(response.body).toHaveProperty('info');
      expect(response.body).toHaveProperty('paths');
      
      // Should have aggregated paths from services (if available)
      const paths = Object.keys(response.body.paths);
      
      // Even if services are down, should return valid OpenAPI spec
      expect(response.body.info.title).toBe('Polyrepo API Gateway');
      expect(response.body.openapi).toBeDefined();
    });

    it('should handle service documentation unavailability', async () => {
      // This test verifies that the gateway handles cases where
      // individual services are down or don't respond
      const response = await request(app.getHttpServer())
        .get('/docs/swagger')
        .expect(200);

      // Should still return valid documentation even if services are down
      expect(response.body.info.title).toBe('Polyrepo API Gateway');
      expect(response.body).toHaveProperty('paths');
    });
  });
});