import { TestEnvironment } from '@libs/testing-utils';

// Setup test environment based on test type
const testType = process.env.NODE_ENV === 'test' ? 'unit' : 'integration';

// Load appropriate environment configuration
TestEnvironment.setupEnvironment(testType as any, 'api-gateway');

// Global test configuration
jest.setTimeout(TestEnvironment.getTimeout(testType as any));

// Global test setup
beforeAll(async () => {
  // Any global setup can go here
});

afterAll(async () => {
  // Any global cleanup can go here
});

// Handle unhandled promises in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Export for use in tests
export { TestEnvironment };