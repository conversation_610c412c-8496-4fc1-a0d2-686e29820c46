import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { TestEnvironment, TestDataGenerator } from '@libs/testing-utils';

describe('API Gateway Integration', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // Setup test environment
    TestEnvironment.setupEnvironment('integration', 'api-gateway');

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Health endpoints', () => {
    it('/health (GET) should return health status', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body).toHaveProperty('service', 'api-gateway');
          expect(res.body).toHaveProperty('timestamp');
        });
    });

    it('/metrics (GET) should return metrics', () => {
      return request(app.getHttpServer())
        .get('/metrics')
        .expect(200);
    });
  });

  describe('Correlation ID middleware', () => {
    it('should add correlation ID to responses', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect((res) => {
          expect(res.headers).toHaveProperty('x-correlation-id');
          expect(res.headers['x-correlation-id']).toMatch(/^[0-9a-f-]{36}$/);
        });
    });

    it('should preserve provided correlation ID', () => {
      const correlationId = TestDataGenerator.generateCorrelationId();
      
      return request(app.getHttpServer())
        .get('/')
        .set('X-Correlation-ID', correlationId)
        .expect((res) => {
          expect(res.headers['x-correlation-id']).toBe(correlationId);
        });
    });

    it('should generate new correlation ID for each request', async () => {
      const response1 = await request(app.getHttpServer()).get('/');
      const response2 = await request(app.getHttpServer()).get('/');

      expect(response1.headers['x-correlation-id']).toBeDefined();
      expect(response2.headers['x-correlation-id']).toBeDefined();
      expect(response1.headers['x-correlation-id']).not.toBe(
        response2.headers['x-correlation-id']
      );
    });
  });

  describe('Documentation endpoints', () => {
    it('/docs (GET) should serve aggregated documentation', () => {
      return request(app.getHttpServer())
        .get('/docs')
        .expect(200)
        .expect('Content-Type', /html/);
    });

    it('/docs/swagger (GET) should return aggregated OpenAPI spec', () => {
      return request(app.getHttpServer())
        .get('/docs/swagger')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('openapi');
          expect(res.body).toHaveProperty('info');
          expect(res.body.info.title).toBe('Polyrepo API Gateway');
          expect(res.body).toHaveProperty('paths');
        });
    });

    it('/api/docs (GET) should serve gateway-specific documentation', () => {
      return request(app.getHttpServer())
        .get('/api/docs')
        .expect(200)
        .expect('Content-Type', /html/);
    });

    it('/api/docs-json (GET) should return gateway OpenAPI spec', () => {
      return request(app.getHttpServer())
        .get('/api/docs-json')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('openapi');
          expect(res.body).toHaveProperty('info');
          expect(res.body.info.title).toBe('API Gateway');
        });
    });
  });

  describe('Rate limiting', () => {
    it('should allow normal request rate', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect((res) => {
          expect(res.status).not.toBe(429);
        });
    });

    // Note: Rate limiting tests require multiple rapid requests
    // This is a basic test - more comprehensive testing would require
    // configuring lower limits for testing or using a dedicated test environment
  });

  describe('Error handling', () => {
    it('should handle 404 for unknown routes', () => {
      return request(app.getHttpServer())
        .get('/unknown-route')
        .expect(404);
    });

    it('should return proper error format', () => {
      return request(app.getHttpServer())
        .get('/unknown-route')
        .expect(404)
        .expect((res) => {
          expect(res.body).toHaveProperty('statusCode', 404);
          expect(res.body).toHaveProperty('message');
        });
    });
  });

  describe('Proxy functionality', () => {
    // Note: These tests assume the backend services are running
    // In a real test environment, you might want to mock these services
    // or use the TestEnvironment.shouldUseRealServices() check

    it('should handle requests to unknown service paths', () => {
      return request(app.getHttpServer())
        .get('/unknown-service/test')
        .expect((res) => {
          // Should either proxy (if service exists) or return 404/500
          expect([404, 500, 502, 503]).toContain(res.status);
        });
    });

    it('should preserve headers in proxied requests', () => {
      const customHeader = 'test-value';
      
      return request(app.getHttpServer())
        .get('/auth/health')
        .set('X-Custom-Header', customHeader)
        .expect((res) => {
          // Test should verify that custom headers are passed through
          // The actual response depends on whether auth service is running
          expect([200, 404, 500, 502, 503]).toContain(res.status);
        });
    });
  });

  describe('CORS handling', () => {
    it('should handle OPTIONS requests', () => {
      return request(app.getHttpServer())
        .options('/')
        .expect((res) => {
          // Should not fail with CORS error
          expect([200, 204, 404]).toContain(res.status);
        });
    });
  });

  describe('Security headers', () => {
    it('should include security headers in responses', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect((res) => {
          // Check for correlation ID (our security/tracing header)
          expect(res.headers).toHaveProperty('x-correlation-id');
        });
    });
  });
});