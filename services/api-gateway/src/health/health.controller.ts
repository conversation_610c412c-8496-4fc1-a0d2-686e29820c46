import { <PERSON>, Get, Post, Param, Inject } from '@nestjs/common';
import { ObservabilityLogger, MetricsService } from '@libs/observability';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HttpClientService } from '@libs/http';
import { CircuitBreakerService } from '@libs/resilience';
import { HealthCheckResponse, DependencyHealth } from '@libs/shared-types';

/**
 * Controller for health checks and service status
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  private readonly logger: ObservabilityLogger;

  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly metricsService: MetricsService,
    private readonly httpClientService: HttpClientService,
    private readonly circuitBreakerService: CircuitBreakerService,
  ) {
    this.logger = this.loggerFactory.createLogger(HealthController.name);
  }

  /**
   * Basic health check endpoint
   * @returns Health status
   */
  @Get()
  @ApiOperation({ summary: 'Health check', description: 'Basic health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  healthCheck() {
    this.logger.log('Health check endpoint accessed');
    return {
      status: 'ok',
      service: 'api-gateway',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
    };
  }

  /**
   * Detailed health check including downstream services
   * @returns Detailed health status
   */
  @Get('detailed')
  @ApiOperation({ summary: 'Detailed health check', description: 'Detailed health check including downstream services' })
  @ApiResponse({ status: 200, description: 'Detailed health status' })
  async detailedHealthCheck(): Promise<HealthCheckResponse> {
    this.logger.log('Detailed health check endpoint accessed');

    // Get metrics to check if metrics service is working
    const metrics = await this.metricsService.getMetrics();
    const metricsStatus = metrics ? 'ok' : 'error';

    // Check if logger is working
    const loggerStatus = this.logger ? 'ok' : 'error';

    // Check circuit breaker health via HttpClientService (single source of truth)
    const circuitBreakerInfo = this.httpClientService.getCircuitBreakerStatus();
    const circuitBreakerStatus = circuitBreakerInfo.hasUnhealthyCircuits ? 'degraded' : 'ok';

    // Check downstream services
    const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
    const userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3002';
    
    const authServiceHealth = await this.checkDownstreamService('auth-service', `${authServiceUrl}/health`);
    const userServiceHealth = await this.checkDownstreamService('user-service', `${userServiceUrl}/health`);

    // Determine overall status
    const overallStatus =
      loggerStatus === 'ok' &&
      metricsStatus === 'ok' &&
      circuitBreakerStatus === 'ok' &&
      authServiceHealth.status === 'ok' &&
      userServiceHealth.status === 'ok' ? 'ok' : 'degraded';

    return {
      status: overallStatus,
      service: 'api-gateway',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      observability: {
        logging: loggerStatus,
        metrics: metricsStatus,
      },
      resilience: {
        circuitBreakers: {
          status: circuitBreakerStatus,
          summary: circuitBreakerInfo.summary,
          openServices: circuitBreakerInfo.summary.openServices,
          warnings: circuitBreakerInfo.summary.open > 0 ? [`${circuitBreakerInfo.summary.open} circuit breaker(s) are OPEN. Services may be experiencing issues.`] : [],
        },
      },
      dependencies: {
        'auth-service': authServiceHealth,
        'user-service': userServiceHealth,
      },
    };
  }

  /**
   * Downstream services health check endpoint
   * @returns Downstream services health status
   */
  @Get('downstream')
  @ApiOperation({ summary: 'Downstream services health', description: 'Check health of all downstream services' })
  @ApiResponse({ status: 200, description: 'Downstream services health status' })
  async downstreamHealthCheck() {
    this.logger.log('Downstream services health check endpoint accessed');

    const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3001';
    const userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:3002';
    
    const authServiceHealth = await this.checkDownstreamService('auth-service', `${authServiceUrl}/health`);
    const userServiceHealth = await this.checkDownstreamService('user-service', `${userServiceUrl}/health`);

    const overallStatus = 
      authServiceHealth.status === 'ok' && 
      userServiceHealth.status === 'ok' ? 'ok' : 'degraded';

    return {
      status: overallStatus,
      service: 'downstream-services',
      timestamp: new Date().toISOString(),
      dependencies: {
        'auth-service': authServiceHealth,
        'user-service': userServiceHealth,
      },
    };
  }

  /**
   * Check health of a downstream service
   */
  private async checkDownstreamService(serviceName: string, healthUrl: string): Promise<DependencyHealth> {
    const startTime = Date.now();
    
    try {
      const response = await this.httpClientService.get(
        healthUrl,
        {
          serviceName,
          operationName: 'health-check',
          headers: {
            'Accept': 'application/json',
          }
        }
      );
      
      const responseTime = Date.now() - startTime;
      
      // Check if response indicates the service is healthy
      const isHealthy = response.status === 200 && (
        response.data?.status === 'ok' || 
        response.data?.status === 'up'
      );
      
      return {
        status: isHealthy ? 'ok' : 'degraded',
        responseTime,
        details: {
          httpStatus: response.status,
          serviceResponse: response.data,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.warn(`Health check failed for ${serviceName}: ${errorMessage}`);
      
      return {
        status: 'error',
        responseTime,
        error: errorMessage,
        details: {
          serviceName,
          healthUrl,
        },
      };
    }
  }

  /**
   * Circuit breaker status endpoint
   * @returns Circuit breaker states for all services
   */
  @Get('circuit-breakers')
  @ApiOperation({ summary: 'Circuit breaker status', description: 'Get status of all circuit breakers' })
  @ApiResponse({ status: 200, description: 'Circuit breaker status for all services' })
  getCircuitBreakerStatus() {
    this.logger.log('Circuit breaker status endpoint accessed');
    
    try {
      // Use HttpClientService as single source of truth for circuit breaker state
      const circuitBreakerInfo = this.httpClientService.getCircuitBreakerStatus();
      const servicesNeedingAttention = this.httpClientService.getServicesNeedingAttention();
      
      return {
        status: circuitBreakerInfo.summary.open > 0 ? 'degraded' : 'ok',
        service: 'circuit-breakers',
        timestamp: new Date().toISOString(),
        circuitBreakers: circuitBreakerInfo.circuitBreakers,
        summary: circuitBreakerInfo.summary,
        alerts: {
          openCircuits: circuitBreakerInfo.summary.open,
          servicesAffected: servicesNeedingAttention,
          warnings: circuitBreakerInfo.summary.open > 0 ? [
            `${circuitBreakerInfo.summary.open} circuit breaker(s) are OPEN, blocking requests to protect against failures`,
            ...servicesNeedingAttention.map((service: string) => `Service '${service}' is unavailable due to circuit breaker`)
          ] : [],
          actions: circuitBreakerInfo.summary.open > 0 ? [
            'Check individual service health endpoints',
            `Reset specific circuits: curl -X POST http://localhost:3000/health/circuit-breakers/reset/{serviceName}`,
            'Reset all circuits: curl -X POST http://localhost:3000/health/circuit-breakers/reset',
            'Monitor service logs for underlying issues'
          ] : []
        },
        troubleshooting: circuitBreakerInfo.summary.open > 0 ? {
          possibleCauses: [
            'Service experiencing high error rates',
            'Network connectivity issues',
            'Service overload or resource constraints',
            'Database or dependency failures'
          ],
          diagnosticSteps: [
            'Check service logs: docker logs <service-name>',
            'Test service directly: curl http://localhost:<port>/health',
            'Check resource usage: docker stats',
            'Verify dependencies are running'
          ]
        } : undefined
      };
    } catch (error) {
      this.logger.error(`Failed to get circuit breaker status: ${error}`);
      return {
        status: 'error',
        service: 'circuit-breakers',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Reset all circuit breakers
   * @returns Reset confirmation
   */
  @Post('circuit-breakers/reset')
  @ApiOperation({ summary: 'Reset circuit breakers', description: 'Reset all circuit breakers to CLOSED state' })
  @ApiResponse({ status: 200, description: 'Circuit breakers reset successfully' })
  resetCircuitBreakers() {
    this.logger.warn('Circuit breaker reset endpoint accessed - resetting all circuit breakers');
    
    try {
      // Use the same CircuitBreakerService instance that HttpClientService uses
      this.circuitBreakerService.resetAllCircuitBreakers();
      this.logger.log('All circuit breakers reset via HealthController (same instance as HttpClientService)');
      
      return {
        status: 'ok',
        service: 'circuit-breakers',
        timestamp: new Date().toISOString(),
        action: 'reset',
        message: 'All circuit breakers have been reset to CLOSED state',
      };
    } catch (error) {
      this.logger.error(`Failed to reset circuit breakers: ${error}`);
      return {
        status: 'error',
        service: 'circuit-breakers',
        timestamp: new Date().toISOString(),
        action: 'reset',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Reset specific circuit breaker
   * @param serviceName Name of the service circuit breaker to reset
   * @returns Reset confirmation
   */
  @Post('circuit-breakers/reset/:serviceName')
  @ApiOperation({ summary: 'Reset specific circuit breaker', description: 'Reset circuit breaker for a specific service' })
  @ApiResponse({ status: 200, description: 'Circuit breaker reset successfully' })
  resetSpecificCircuitBreaker(@Param('serviceName') serviceName: string) {
    this.logger.warn(`Circuit breaker reset endpoint accessed for service: ${serviceName}`);
    
    try {
      // Use the same CircuitBreakerService instance that HttpClientService uses
      this.circuitBreakerService.resetCircuitBreaker(serviceName);
      this.logger.log(`Circuit breaker reset for ${serviceName} via HealthController (same instance as HttpClientService)`);
      
      return {
        status: 'ok',
        service: 'circuit-breakers',
        timestamp: new Date().toISOString(),
        action: 'reset',
        serviceName,
        message: `Circuit breaker for ${serviceName} has been reset to CLOSED state`,
      };
    } catch (error) {
      this.logger.error(`Failed to reset circuit breaker for ${serviceName}: ${error}`);
      return {
        status: 'error',
        service: 'circuit-breakers',
        timestamp: new Date().toISOString(),
        action: 'reset',
        serviceName,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
