import { Injectable, Inject } from '@nestjs/common';
import { Request, Response } from 'express';
import { HttpClientService } from '@libs/http';
import { ObservabilityLogger } from '@libs/observability';
import { ApiGatewayBusinessLogger } from '../observability/business-logger.service';
import { ErrorResponseBuilderService } from '@libs/error-handling';
import { RouteConfigMatcher } from '../routing/route.config';

/**
 * Simplified proxy service using HttpClientService with pre-configured service clients
 * Leverages @libs/http with HTTP/2, circuit breakers, and comprehensive observability
 */
@Injectable()
export class ProxyService {
  private readonly logger: ObservabilityLogger;

  constructor(
    private readonly httpClient: HttpClientService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly businessLogger: ApiGatewayBusinessLogger,
    private readonly errorResponseBuilder: ErrorResponseBuilderService,
  ) {
    this.logger = this.loggerFactory.createLogger(ProxyService.name);
    
    // URGENT DEBUG: Check what type of object httpClient is
    console.log(`[ProxyService] constructor - httpClient type:`, {
      type: typeof this.httpClient,
      constructor: this.httpClient?.constructor?.name,
      hasRequestMethod: typeof this.httpClient?.request,
      methods: Object.getOwnPropertyNames(Object.getPrototypeOf(this.httpClient)),
    });
  }

  /**
   * Handle proxy request using pre-configured service clients
   * Much simpler approach leveraging HttpClientService configuration
   */
  async handleRequest(req: Request, res: Response, serviceName: string): Promise<void> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    const originalPath = req.originalUrl || req.url;
    
    try {
      // Look up route configuration for timeout/retry overrides
      const routeConfig = RouteConfigMatcher.findRouteConfig(originalPath);
      
      this.logger.debug({
        message: 'PROXY_HANDLE: Route configuration lookup',
        requestId,
        originalPath,
        foundRouteConfig: !!routeConfig,
        routePattern: routeConfig?.pattern,
        routeTimeout: routeConfig?.timeout,
        routeRetryLimit: routeConfig?.retryLimit,
        serviceName,
      });

      // Transform the path (e.g., /api/auth/login -> /auth/login)
      const transformedPath = this.transformPath(originalPath, serviceName);
      
      // Extract request data
      this.logger.debug({
        message: 'PROXY_HANDLE: About to extract request data',
        requestId,
        method: req.method,
        originalUrl: req.originalUrl,
        url: req.url,
        hasBody: !!req.body,
        contentType: req.headers['content-type'],
        bodyType: typeof req.body,
      });

      const requestData = this.extractRequestData(req);
      
      this.logger.debug({
        message: 'PROXY_HANDLE: Request data extracted successfully',
        requestId,
        hasExtractedBody: !!requestData.body,
        extractedBodyType: typeof requestData.body,
        extractedBodySize: requestData.body ? JSON.stringify(requestData.body).length : 0,
        extractedHeaderCount: Object.keys(requestData.headers).length,
        extractedQueryCount: Object.keys(requestData.query).length,
      });
      
      // Log request initiation
      this.logProxyRequest(req, serviceName, requestId, transformedPath, startTime);

      this.logger.debug({
        message: 'PROXY_HANDLE: About to make HTTP request via HttpClient',
        requestId,
        serviceName,
        method: req.method.toUpperCase(),
        transformedPath,
        hasBody: !!requestData.body,
        headerCount: Object.keys(requestData.headers).length,
        queryCount: Object.keys(requestData.query).length,
        requestDataKeys: Object.keys(requestData),
        httpClientOptionsWillBe: {
          serviceName,
          operationName: 'proxy-request',
          dataType: typeof requestData.body,
          hasData: !!requestData.body,
          headersCount: Object.keys(requestData.headers).length,
          paramsCount: Object.keys(requestData.query).length,
        }
      });

      // Debug: Check if HttpClientService has service clients
      const serviceClientsMap = (this.httpClient as any).serviceClients;
      this.logger.debug({
        message: 'Debug HttpClient service configuration',
        requestId,
        serviceName,
        httpClientType: typeof this.httpClient,
        httpClientMethods: Object.getOwnPropertyNames(Object.getPrototypeOf(this.httpClient)),
        hasServiceClientsProperty: 'serviceClients' in this.httpClient,
        serviceClientsType: typeof serviceClientsMap,
        serviceClientsSize: serviceClientsMap ? serviceClientsMap.size : 0,
        hasAuthService: serviceClientsMap ? serviceClientsMap.has('auth-service') : false,
        serviceClientKeys: serviceClientsMap ? Array.from(serviceClientsMap.keys()) : [],
      });

      // Use the pre-configured service client from HttpModule
      console.log(`[ProxyService] About to call httpClient.request with method=${req.method.toUpperCase()}, path=${transformedPath}, serviceName=${serviceName}`);
      console.log(`[ProxyService] httpClient.request type:`, typeof this.httpClient.request);
      console.log(`[ProxyService] httpClient.request toString:`, this.httpClient.request.toString().substring(0, 200));
      
      this.logger.debug({
        message: 'PROXY_HANDLE: Calling HttpClient.request with options',
        requestId,
        httpClientMethod: req.method.toUpperCase(),
        httpClientUrl: transformedPath,
        httpClientOptions: {
          serviceName,
          operationName: 'proxy-request',
          hasData: !!requestData.body,
          dataType: typeof requestData.body,
          dataSize: requestData.body ? JSON.stringify(requestData.body).length : 0,
          dataPreview: requestData.body ? JSON.stringify(requestData.body).substring(0, 100) : 'N/A',
          headersCount: Object.keys(requestData.headers).length,
          headerContentType: requestData.headers['content-type'],
          paramsCount: Object.keys(requestData.query).length,
        }
      });

      // Build HTTP client options with route-specific overrides
      const httpOptions: any = {
        serviceName,
        operationName: 'proxy-request',
        data: requestData.body,
        headers: requestData.headers,
        params: requestData.query,
      };

      // Apply route-specific timeout and retry overrides if configured
      if (routeConfig?.timeout) {
        httpOptions.timeout = routeConfig.timeout;
      }
      if (routeConfig?.retryLimit !== undefined) {
        httpOptions.retries = routeConfig.retryLimit;
      }

      this.logger.debug({
        message: 'PROXY_HANDLE: HTTP options with route overrides',
        requestId,
        httpOptions: {
          ...httpOptions,
          data: httpOptions.data ? '[REDACTED]' : undefined,
        },
        appliedTimeout: httpOptions.timeout,
        appliedRetries: httpOptions.retries,
        routeConfigSource: routeConfig?.pattern || 'library-defaults',
      });

      const response = await this.httpClient.request(
        req.method.toUpperCase() as any,
        transformedPath,
        httpOptions
      );

      console.log(`[ProxyService] httpClient.request returned:`, { responseType: typeof response, responseStatus: response?.status, hasResponse: !!response });

      this.logger.debug({
        message: 'PROXY_HANDLE: HttpClient.request completed successfully',
        requestId,
        responseType: typeof response,
        responseStatus: response?.status,
        responseStatusCode: response?.statusCode,
        hasResponse: !!response,
        responseKeys: response ? Object.keys(response) : [],
        responseDataType: typeof response?.data,
        responseDataSize: response?.data ? JSON.stringify(response.data).length : 0,
        duration: Date.now() - startTime,
      });

      this.logger.debug({
        message: 'HTTP request completed successfully',
        requestId,
        serviceName,
        responseStatus: response?.status,
        responseType: typeof response,
        responseKeys: response ? Object.keys(response) : [],
      });

      // Send successful response
      await this.sendSuccessResponse(res, response, req, serviceName, requestId, startTime);

    } catch (error: any) {
      this.logger.error({
        message: 'Exception in handleRequest',
        requestId,
        serviceName,
        error: error.message,
        errorType: typeof error,
        errorKeys: Object.keys(error),
        stack: error.stack
      });
      await this.handleProxyError(error, res, req, serviceName, requestId, startTime);
    }
  }

  /**
   * Transform incoming path to backend service path
   * IMPORTANT: When using Got with prefixUrl (baseURL), paths must NOT start with '/'
   * /api/auth/login -> auth/login (relative path for Got)
   * /api/users/123 -> users/123 (relative path for Got)
   */
  private transformPath(originalPath: string, serviceName: string): string {
    // Map service names to path prefixes
    const servicePathMap: Record<string, string> = {
      'auth-service': 'auth',
      'user-service': 'users',
    };
    
    const pathPrefix = servicePathMap[serviceName];
    if (!pathPrefix) {
      throw new Error(`Unknown service: ${serviceName}`);
    }
    
    // Special handling for health endpoints - route to root health (relative to baseURL)
    if (originalPath === `/api/${pathPrefix}/health`) {
      return 'health'; // No leading slash for Got with prefixUrl
    }
    
    // Standard rewriting: /api/auth/login -> auth/login (relative path)
    // Remove the /api/{prefix} part and return relative path without leading slash
    const rewritten = originalPath.replace(new RegExp(`^/api/${pathPrefix}`), `${pathPrefix}`);
    
    // Ensure no leading slash for Got with prefixUrl
    return rewritten.startsWith('/') ? rewritten.substring(1) : rewritten;
  }

  /**
   * Extract request data (body, headers, query) for proxying
   */
  private extractRequestData(req: Request): {
    body?: any;
    headers: Record<string, string>;
    query: Record<string, string>;
  } {
    // ========================================
    // COMPREHENSIVE DEBUGGING: Request Data Extraction
    // ========================================
    
    this.logger.debug({
      message: 'EXTRACT_REQUEST_DATA: Starting request data extraction',
      method: req.method,
      url: req.url,
      originalUrl: req.originalUrl,
      hasBody: !!req.body,
      bodyType: typeof req.body,
      bodyIsObject: typeof req.body === 'object',
      bodyIsString: typeof req.body === 'string',
      bodyKeys: req.body && typeof req.body === 'object' ? Object.keys(req.body) : [],
      bodySize: req.body ? JSON.stringify(req.body).length : 0,
      contentType: req.headers['content-type'],
      contentLength: req.headers['content-length'],
      headerCount: Object.keys(req.headers).length,
      queryCount: Object.keys(req.query).length,
    });

    // Filter and transform headers
    const headers: Record<string, string> = {};
    const droppedHeaders: string[] = [];
    const transformedHeaders: string[] = [];
    
    Object.entries(req.headers).forEach(([key, value]) => {
      // Skip hop-by-hop headers and host
      if (!this.isHopByHopHeader(key) && key !== 'host') {
        const transformedValue = Array.isArray(value) ? value.join(', ') : (value as string);
        headers[key] = transformedValue;
        if (Array.isArray(value)) {
          transformedHeaders.push(key);
        }
      } else {
        droppedHeaders.push(key);
      }
    });

    // Add gateway-specific headers
    headers['x-forwarded-by'] = 'api-gateway';
    headers['x-forwarded-for'] = req.ip || req.socket.remoteAddress || 'unknown';
    headers['x-forwarded-proto'] = req.protocol;
    
    this.logger.debug({
      message: 'EXTRACT_REQUEST_DATA: Headers processed',
      originalHeaderCount: Object.keys(req.headers).length,
      finalHeaderCount: Object.keys(headers).length,
      droppedHeaders,
      transformedHeaders,
      addedGatewayHeaders: ['x-forwarded-by', 'x-forwarded-for', 'x-forwarded-proto'],
      finalContentType: headers['content-type'],
      finalContentLength: headers['content-length'],
    });
    
    // Extract query parameters
    const query: Record<string, string> = {};
    const transformedQueryParams: string[] = [];
    
    Object.entries(req.query).forEach(([key, value]) => {
      const transformedValue = Array.isArray(value) ? value.join(',') : String(value || '');
      query[key] = transformedValue;
      if (Array.isArray(value)) {
        transformedQueryParams.push(key);
      }
    });

    this.logger.debug({
      message: 'EXTRACT_REQUEST_DATA: Query parameters processed',
      originalQueryCount: Object.keys(req.query).length,
      finalQueryCount: Object.keys(query).length,
      transformedQueryParams,
      queryKeys: Object.keys(query),
    });

    // Get request body for POST/PUT/PATCH with detailed analysis
    const shouldHaveBody = ['POST', 'PUT', 'PATCH'].includes(req.method);
    const body = shouldHaveBody ? req.body : undefined;

    this.logger.debug({
      message: 'EXTRACT_REQUEST_DATA: Body extraction completed',
      method: req.method,
      shouldHaveBody,
      hasOriginalBody: !!req.body,
      extractedBodyType: typeof body,
      extractedBodyIsUndefined: body === undefined,
      extractedBodyIsNull: body === null,
      extractedBodyIsEmpty: body === '',
      extractedBodyKeys: body && typeof body === 'object' ? Object.keys(body) : [],
      extractedBodySize: body ? JSON.stringify(body).length : 0,
      bodyStringified: body ? JSON.stringify(body).substring(0, 200) + '...' : 'N/A',
    });

    const result = { body, headers, query };

    this.logger.debug({
      message: 'EXTRACT_REQUEST_DATA: Final result',
      resultKeys: Object.keys(result),
      hasResultBody: !!result.body,
      resultBodyType: typeof result.body,
      resultHeaderCount: Object.keys(result.headers).length,
      resultQueryCount: Object.keys(result.query).length,
      finalResult: {
        bodyPresent: !!result.body,
        bodyType: typeof result.body,
        headerCount: Object.keys(result.headers).length,
        queryCount: Object.keys(result.query).length,
      }
    });

    return result;
  }

  /**
   * Check if header is hop-by-hop and should not be forwarded
   */
  private isHopByHopHeader(header: string): boolean {
    const hopByHopHeaders = [
      'connection', 'keep-alive', 'proxy-authenticate', 'proxy-authorization',
      'te', 'trailers', 'transfer-encoding', 'upgrade'
    ];
    return hopByHopHeaders.includes(header.toLowerCase());
  }

  /**
   * Send successful proxy response
   */
  private async sendSuccessResponse(
    res: Response,
    proxyResponse: any,
    req: Request,
    serviceName: string,
    requestId: string,
    startTime: number
  ): Promise<void> {
    const duration = Date.now() - startTime;
    
    try {
      // Debug log the response structure
      this.logger.debug({
        message: 'Processing proxy response',
        requestId,
        serviceName,
        responseKeys: proxyResponse ? Object.keys(proxyResponse) : [],
        responseStatus: proxyResponse?.status,
        hasData: !!proxyResponse?.data,
        hasHeaders: !!proxyResponse?.headers,
        headersType: typeof proxyResponse?.headers,
        responseIsNull: !proxyResponse,
        responseType: typeof proxyResponse
      });

      // Handle null/undefined response
      if (!proxyResponse) {
        this.logger.error({
          message: 'Received null/undefined response from HttpClientService',
          requestId,
          serviceName,
        });
        throw new Error('No response received from backend service');
      }

      // Set response headers (filter hop-by-hop headers)
      if (proxyResponse.headers && typeof proxyResponse.headers === 'object') {
        Object.entries(proxyResponse.headers).forEach(([key, value]) => {
          if (!this.isHopByHopHeader(key) && value !== undefined) {
            res.setHeader(key, Array.isArray(value) ? value[0] : String(value));
          }
        });
      }

      // Send response
      res.status(proxyResponse.status || 200).json(proxyResponse.data);
    } catch (error: any) {
      this.logger.error({
        message: 'Error in sendSuccessResponse',
        requestId,
        serviceName,
        error: error.message,
        stack: error.stack,
        proxyResponseStructure: proxyResponse ? {
          keys: Object.keys(proxyResponse),
          status: proxyResponse.status,
          dataType: typeof proxyResponse.data,
          headersType: typeof proxyResponse.headers
        } : null
      });
      throw error; // Re-throw to trigger error handling
    }

    // Log successful proxy operation
    this.logProxyResponse(req, serviceName, requestId, proxyResponse.status || 200, duration, true);
    
    // Log business events
    this.businessLogger.logProxyEvent('success', requestId, {
      target_service: serviceName,
      response_time: duration,
      status_code: proxyResponse.status || 200,
      method: req.method,
      url: req.url,
      http_version: '2.0' // Got uses HTTP/2
    });
    
    this.businessLogger.logBackendAvailabilityEvent('available', serviceName, {
      response_time: duration,
      status_code: proxyResponse.status || 200,
      error_type: null
    });
  }

  /**
   * Handle proxy errors with comprehensive logging
   */
  private async handleProxyError(
    error: any,
    res: Response,
    req: Request,
    serviceName: string,
    requestId: string,
    startTime: number
  ): Promise<void> {
    const duration = Date.now() - startTime;
    
    // Determine error type and status code
    let statusCode = 500;
    let errorMessage = 'Internal proxy error';
    let errorType = 'unknown_error';

    if (error.status || error.statusCode) {
      statusCode = error.status || error.statusCode;
      errorMessage = error.message || 'Backend service error';
      errorType = 'backend_error';
    } else if (error.code === 'ETIMEDOUT' || error.name === 'TimeoutError') {
      statusCode = 504;
      errorMessage = 'Gateway timeout';
      errorType = 'timeout_error';
    } else if (error.code && ['ECONNREFUSED', 'ENOTFOUND', 'ENETUNREACH'].includes(error.code)) {
      statusCode = 502;
      errorMessage = 'Bad gateway - service unavailable';
      errorType = 'connection_error';
    }

    // Log detailed error information
    this.logger.error({
      message: 'Proxy request failed',
      requestId,
      serviceName,
      method: req.method,
      url: req.url,
      error: error.message,
      errorCode: error.code,
      statusCode,
      duration,
      errorType
    });

    // Send error response
    await this.sendErrorResponse(res, statusCode, errorMessage, requestId);

    // Log business events
    this.businessLogger.logProxyEvent('failure', requestId, {
      target_service: serviceName,
      response_time: duration,
      status_code: statusCode,
      method: req.method,
      url: req.url,
      error_type: errorType
    });
    
    this.businessLogger.logBackendAvailabilityEvent('unavailable', serviceName, {
      response_time: duration,
      status_code: statusCode,
      error_type: errorType
    });
  }

  /**
   * Send error response using centralized error handling
   */
  private async sendErrorResponse(
    res: Response,
    statusCode: number,
    message: string,
    requestId: string
  ): Promise<void> {
    // Create a standardized error for the response builder
    const error = new Error(message);
    (error as any).status = statusCode;
    
    const errorResponse = this.errorResponseBuilder.buildFromException(
      error,
      '/api/proxy', // Generic path since this is proxy service
      statusCode
    );

    // Override correlation ID with our request ID
    errorResponse.correlationId = requestId;

    res.status(statusCode).json(errorResponse);
  }

  /**
   * Log proxy request initiation
   */
  private logProxyRequest(
    req: Request,
    serviceName: string,
    requestId: string,
    transformedPath: string,
    startTime: number
  ): void {
    const originalPath = req.originalUrl || req.url;
    
    this.logger.log({
      message: 'Proxy request initiated',
      requestId,
      method: req.method,
      originalPath,
      transformedPath,
      targetService: serviceName,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.socket.remoteAddress,
      hasBody: req.body && Object.keys(req.body).length > 0,
      contentType: req.headers['content-type']
    });

    // Log business event for request routing
    this.businessLogger.logRequestRoutingEvent('success', requestId, {
      route: originalPath,
      target_service: serviceName,
      method: req.method,
      rewritten_path: transformedPath,
      ip: req.ip || req.socket.remoteAddress
    });
  }

  /**
   * Log proxy response
   */
  private logProxyResponse(
    req: Request,
    serviceName: string,
    requestId: string,
    statusCode: number,
    duration: number,
    isSuccess: boolean
  ): void {
    this.logger.log({
      message: 'Proxy response completed',
      requestId,
      targetService: serviceName,
      method: req.method,
      url: req.url,
      statusCode,
      duration,
      isSuccess,
      httpVersion: '2.0' // Got uses HTTP/2 by default
    });
  }

  /**
   * Generate unique request ID for correlation
   */
  private generateRequestId(): string {
    return `proxy_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}