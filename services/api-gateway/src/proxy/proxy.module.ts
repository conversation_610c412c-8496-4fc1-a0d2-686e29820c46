import { Module, NestModule, MiddlewareConsumer, Inject } from '@nestjs/common';
import { ProxyService } from './proxy.service';
import { ObservabilityLogger } from '@libs/observability';
import { ObservabilityModule } from '../observability/observability.module';
import { AuthCommonModule } from '@libs/auth-common';
import { DynamicProxyController } from '../routing/dynamic-proxy.controller';

@Module({
  imports: [
    ObservabilityModule,
    AuthCommonModule.forRoot(), // Import JWT validation capabilities
  ],
  controllers: [DynamicProxyController],
  providers: [ProxyService],
  exports: [ProxyService],
})
export class ProxyModule implements NestModule {
  private readonly logger: ObservabilityLogger;

  constructor(
    private readonly proxyService: ProxyService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    this.logger = this.loggerFactory.createLogger(ProxyModule.name);
  }

  configure(consumer: MiddlewareConsumer) {
    this.logger.log('Proxy module configuration started');
    
    // NOTE: Proxy middleware disabled - using controller-based approach with Guards instead
    // This provides better integration with NestJS authentication flow
    
    this.logger.log('Proxy module configuration completed - using controller-based routing');
  }
}
