import { Controller, Get, HttpException, HttpStatus } from '@nestjs/common';
import { DocsService } from './docs.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('documentation')
@Controller('docs')
export class DocsController {
  constructor(private readonly docsService: DocsService) {}

  @Get('swagger')
  @ApiOperation({ summary: 'Get aggregated Swagger documentation from all services' })
  @ApiResponse({ status: 200, description: 'Aggregated OpenAPI specification' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async getAggregatedSwagger() {
    try {
      return await this.docsService.getAggregatedDocs();
    } catch (error) {
      throw new HttpException(
        'Failed to aggregate documentation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('swagger-json')
  @ApiOperation({ summary: 'Get aggregated Swagger JSON from all services' })
  @ApiResponse({ status: 200, description: 'Aggregated OpenAPI JSON specification' })
  async getAggregatedSwaggerJson() {
    try {
      return await this.docsService.getAggregatedDocs();
    } catch (error) {
      throw new HttpException(
        'Failed to aggregate documentation JSON',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}