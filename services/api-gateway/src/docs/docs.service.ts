import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpClientService } from '@libs/http';
import { firstValueFrom } from 'rxjs';

interface OpenAPISpec {
  openapi: string;
  info: any;
  servers?: any[];
  paths: Record<string, any>;
  components?: {
    schemas?: Record<string, any>;
    securitySchemes?: Record<string, any>;
  };
  tags?: any[];
}

interface ServiceConfig {
  name: string;
  url: string;
  pathPrefix: string;
  tag: string;
}

@Injectable()
export class DocsService {
  private readonly logger = new Logger(DocsService.name);
  private readonly services: ServiceConfig[];

  constructor(
    private readonly configService: ConfigService,
    private readonly httpClient: HttpClientService,
  ) {
    this.services = [
      {
        name: 'Auth Service',
        url: this.configService.get<string>('AUTH_SERVICE_URL') || 'http://auth-service:3000',
        pathPrefix: '/api',
        tag: 'auth',
      },
      {
        name: 'User Service',
        url: this.configService.get<string>('USER_SERVICE_URL') || 'http://user-service:3000',
        pathPrefix: '/api',
        tag: 'users',
      },
    ];
  }

  async getAggregatedDocs(): Promise<OpenAPISpec> {
    this.logger.log('Starting documentation aggregation');
    
    const aggregatedSpec: OpenAPISpec = {
      openapi: '3.0.0',
      info: {
        title: 'Polyrepo API Gateway',
        description: 'Aggregated API documentation for all microservices',
        version: '1.0.0',
        contact: {
          name: 'Development Team',
          email: '<EMAIL>',
        },
      },
      servers: [
        {
          url: this.configService.get<string>('API_GATEWAY_URL') || 'http://localhost:3000',
          description: 'API Gateway',
        },
      ],
      paths: {},
      components: {
        schemas: {},
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      tags: [
        { name: 'gateway', description: 'API Gateway endpoints' },
      ],
    };

    // Add API Gateway's own endpoints
    aggregatedSpec.paths['/health'] = {
      get: {
        tags: ['gateway'],
        summary: 'API Gateway Health Check',
        responses: {
          '200': {
            description: 'Gateway is healthy',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'string', example: 'ok' },
                    timestamp: { type: 'string', format: 'date-time' },
                    uptime: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
    };

    aggregatedSpec.paths['/docs/swagger'] = {
      get: {
        tags: ['gateway'],
        summary: 'Get aggregated API documentation',
        responses: {
          '200': {
            description: 'Aggregated OpenAPI specification',
          },
        },
      },
    };

    // Fetch and merge service documentation
    for (const service of this.services) {
      try {
        this.logger.log(`Fetching docs from ${service.name} at ${service.url}`);
        const serviceSpec = await this.fetchServiceDocs(service);
        
        if (serviceSpec) {
          this.mergeServiceSpec(aggregatedSpec, serviceSpec, service);
          this.logger.log(`Successfully merged docs from ${service.name}`);
        }
      } catch (error) {
        this.logger.warn(`Failed to fetch docs from ${service.name}: ${error instanceof Error ? error.message : String(error)}`);
        // Continue with other services even if one fails
      }
    }

    this.logger.log('Documentation aggregation completed');
    return aggregatedSpec;
  }

  private async fetchServiceDocs(service: ServiceConfig): Promise<OpenAPISpec | null> {
    try {
      // Try multiple possible endpoints
      const endpoints = [
        `${service.url}/api/docs-json`,
        `${service.url}/api/docs/json`,
        `${service.url}/swagger-json`,
      ];

      for (const endpoint of endpoints) {
        try {
          this.logger.debug(`Trying endpoint: ${endpoint}`);
          const response = await this.httpClient.get(endpoint, {
            serviceName: `${service.name.toLowerCase().replace(' ', '-')}-docs`,
            operationName: 'fetch-swagger-docs',
            timeout: 5000, // 5s for docs fetch (business requirement)
            retries: 1, // Single retry for docs fetch
            headers: {
              'Accept': 'application/json',
            },
          });
          
          if (response.data && response.data.openapi) {
            this.logger.log(`Successfully fetched docs from ${endpoint}`);
            return response.data as OpenAPISpec;
          }
        } catch (endpointError) {
          this.logger.debug(`Endpoint ${endpoint} failed: ${endpointError instanceof Error ? endpointError.message : String(endpointError)}`);
          continue;
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Error fetching docs for ${service.name}:`, error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  private mergeServiceSpec(
    aggregatedSpec: OpenAPISpec,
    serviceSpec: OpenAPISpec,
    service: ServiceConfig,
  ): void {
    // Add service tag
    if (!aggregatedSpec.tags) {
      aggregatedSpec.tags = [];
    }
    aggregatedSpec.tags.push({
      name: service.tag,
      description: `${service.name} endpoints`,
    });

    // Merge paths with prefix
    if (serviceSpec.paths) {
      for (const [path, pathItem] of Object.entries(serviceSpec.paths)) {
        const prefixedPath = `${service.pathPrefix}${path}`;
        
        // Update tags in each method
        if (pathItem && typeof pathItem === 'object') {
          const updatedPathItem = { ...pathItem };
          
          for (const [method, operation] of Object.entries(updatedPathItem)) {
            if (operation && typeof operation === 'object' && 'tags' in operation) {
              operation.tags = [service.tag];
            }
          }
          
          aggregatedSpec.paths[prefixedPath] = updatedPathItem;
        }
      }
    }

    // Merge schemas
    if (serviceSpec.components?.schemas && aggregatedSpec.components?.schemas) {
      for (const [schemaName, schema] of Object.entries(serviceSpec.components.schemas)) {
        // Prefix schema names to avoid conflicts
        const prefixedSchemaName = `${service.tag}_${schemaName}`;
        aggregatedSpec.components.schemas[prefixedSchemaName] = schema;
      }
    }

    // Merge security schemes
    if (serviceSpec.components?.securitySchemes && aggregatedSpec.components?.securitySchemes) {
      Object.assign(aggregatedSpec.components.securitySchemes, serviceSpec.components.securitySchemes);
    }
  }
}