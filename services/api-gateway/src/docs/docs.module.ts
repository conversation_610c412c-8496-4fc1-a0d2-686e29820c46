import { Module } from '@nestjs/common';
import { DocsController } from './docs.controller';
import { DocsService } from './docs.service';

/**
 * Documentation aggregation module
 * Uses @libs/http for fetching service documentation with enterprise defaults
 * No separate HTTP configuration needed - uses library defaults + timeout overrides
 */
@Module({
  controllers: [DocsController],
  providers: [DocsService],
  exports: [DocsService],
})
export class DocsModule {}