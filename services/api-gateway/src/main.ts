import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as express from 'express'; // Import express
import { ObservabilityLogger } from '@libs/observability';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { DocsService } from './docs/docs.service';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Get logger for startup logging
  const loggerFactory = app.get('LOGGER_FACTORY');
  const logger = loggerFactory.createLogger('Bootstrap');

  // Trust proxy for correct IP detection behind load balancers
  app.set('trust proxy', true);
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT') || 3000;

  logger.log({
    message: 'API Gateway starting up',
    port,
    nodeEnv: process.env.NODE_ENV,
    trustProxy: true
  });

  // Add express.json middleware for body parsing
  app.use(express.json({ limit: '5mb' }));

  // Configure CORS with strict settings
  const allowedOrigins = configService.get<string>('ALLOWED_ORIGINS') || 'http://localhost:3000,http://localhost:4200';
  const origins = allowedOrigins.split(',').map(origin => origin.trim());

  app.enableCors({
    origin: origins,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
    maxAge: 3600, // 1 hour
  });

  logger.log({
    message: 'CORS configuration applied',
    allowedOrigins: origins,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: true,
    maxAge: 3600
  });

  // Set up API Gateway's own Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('API Gateway')
    .setDescription('Polyrepo API Gateway with aggregated service documentation')
    .setVersion('1.0')
    .addTag('gateway')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Set up aggregated documentation from all services
  const docsService = app.get(DocsService);
  try {
    const aggregatedDocs = await docsService.getAggregatedDocs();
    SwaggerModule.setup('docs', app, aggregatedDocs);
    logger.log({
      message: 'Aggregated Swagger documentation setup completed',
      endpoint: '/docs'
    });
  } catch (error) {
    logger.warn({
      message: 'Failed to setup aggregated documentation',
      error: error instanceof Error ? error.message : String(error),
      fallback: 'Gateway docs available at /api/docs'
    });
  }

  await app.listen(port, '0.0.0.0'); // Explicitly listen on 0.0.0.0
  
  const actualUrl = await app.getUrl();
  logger.log({
    message: 'API Gateway started successfully',
    port,
    bindAddress: '0.0.0.0',
    actualUrl,
    environment: process.env.NODE_ENV
  });
}
bootstrap();
