import { <PERSON>, Get, Header, Inject } from '@nestjs/common';
import { MetricsService, ObservabilityLogger } from '@libs/observability';

/**
 * Controller for exposing Prometheus metrics for the API Gateway service
 */
@Controller('metrics')
export class MetricsController {
  constructor(
    private readonly metricsService: MetricsService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory.createLogger('MetricsController');
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Expose Prometheus metrics endpoint for API Gateway
   * @returns Prometheus metrics in text format
   */
  @Get()
  @Header('Content-Type', 'text/plain')
  async getMetrics(): Promise<string> {
    this.logger.log('API Gateway metrics endpoint accessed');
    return this.metricsService.getMetrics();
  }
}
