import { Module } from '@nestjs/common';
import { MetricsController } from './metrics.controller';
import { MetricsModule as LibMetricsModule } from '@libs/observability';
import { ObservabilityModule as LocalObservabilityModule } from '../observability/observability.module';

@Module({
  imports: [
    // We don't need to import LibMetricsModule again as it's already imported in ObservabilityModule
    LocalObservabilityModule,
  ],
  controllers: [MetricsController],
})
export class MetricsModule {}
