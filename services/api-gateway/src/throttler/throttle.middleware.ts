import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ThrottlerException } from '@nestjs/throttler';

// Simple in-memory store for throttling
const store = new Map<string, { count: number, timestamp: number }>();

// Configure different limits for different route types
const throttleConfigs = {
  // Authentication endpoints (stricter limits)
  auth: {
    limit: 5,          // 5 requests per minute
    ttl: 60 * 1000     // 60 seconds in milliseconds
  },
  // Default for all other endpoints
  default: {
    limit: 10,         // 10 requests per minute
    ttl: 60 * 1000     // 60 seconds in milliseconds
  },
  // Add more configurations as needed
};

@Injectable()
export class ThrottleMiddleware implements NestMiddleware {
  private readonly logger = new Logger(ThrottleMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const ip = req.ips.length ? req.ips[0] : req.ip;
    const originalUrl = req.originalUrl || req.url;
    
    // Determine which throttle config to apply based on URL
    let config = throttleConfigs.default;
    let throttleType = 'default';
    
    // Apply stricter throttling for auth endpoints
    if (originalUrl.includes('/api/auth/login') || originalUrl.includes('/api/auth/register')) {
      config = throttleConfigs.auth;
      throttleType = 'auth';
    }
    
    // Always throttle all routes
    this.logger.debug(`Throttle middleware checking: ${originalUrl} from IP ${ip} using ${throttleType} config`);
    
    const now = Date.now();
    const key = `${ip}:${originalUrl}`;
    
    // Get or create record
    const record = store.get(key) || { count: 0, timestamp: now };
    
    // Reset if TTL has expired
    if (now - record.timestamp > config.ttl) {
      record.count = 0;
      record.timestamp = now;
    }
    
    // Increment count
    record.count++;
    
    // Update store
    store.set(key, record);
    
    // Log with appropriate level
    if (record.count > config.limit - 2) { // Warn when approaching limit
      this.logger.warn(`Request count for ${key}: ${record.count}/${config.limit}`);
    } else {
      this.logger.debug(`Request count for ${key}: ${record.count}/${config.limit}`);
    }
    
    // Check if limit exceeded
    if (record.count > config.limit) {
      this.logger.warn(`Throttling applied to ${key} - exceeded ${config.limit} requests per ${config.ttl/1000} seconds`);
      res.status(429).json({
        statusCode: 429,
        message: `Too Many Requests. Maximum of ${config.limit} requests allowed within ${config.ttl/1000} seconds.`,
        error: 'Too Many Requests'
      });
      return; // Stop processing
    }
    
    next();
  }
}
