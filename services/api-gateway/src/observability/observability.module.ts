import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ObservabilityModule as BaseObservabilityModule } from '@libs/observability';
import { ApiGatewayBusinessLogger } from './business-logger.service';
import { ObservabilityTestController } from './test.controller';

/**
 * This module integrates the API Gateway service with the observability infrastructure.
 * It configures logging, metrics, and tracing for the API Gateway and provides
 * gateway-specific business logging capabilities for request routing, authentication,
 * rate limiting, and proxy events.
 */
@Module({
  imports: [
    ConfigModule, // Ensure ConfigModule is imported
    BaseObservabilityModule.forRootAsync({
      inject: [], // No dependencies needed since we use process.env directly
      useFactory: () => ({
        logging: {
          service: 'api-gateway',
          defaultContext: 'ApiGateway',
          enableLoki: process.env.ENABLE_LOKI === 'true',
          lokiHost: process.env.LOKI_HOST || 'http://loki:3100',
          logLevel: process.env.LOG_LEVEL || 'info',
        },
        metrics: {
          prefix: 'api_gateway', // Use underscores for Prometheus compatibility
          defaultLabels: {
            service: 'api_gateway',
            environment: process.env.NODE_ENV || 'development',
          },
        },
        tracing: process.env.ENABLE_TRACING === 'true' ? {
          serviceName: 'api-gateway',
          environment: process.env.NODE_ENV || 'development',
          tempoEndpoint: process.env.TEMPO_ENDPOINT || 'http://localhost:4318/v1/traces',
        } : undefined,
      }),
    }),
  ],
  controllers: process.env.NODE_ENV === 'production' ? [] : [ObservabilityTestController],
  providers: [ApiGatewayBusinessLogger],
  exports: [ApiGatewayBusinessLogger, BaseObservabilityModule],
})
export class ObservabilityModule {}
