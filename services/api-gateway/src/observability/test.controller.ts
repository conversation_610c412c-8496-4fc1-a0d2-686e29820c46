import { Controller, Get, Inject, Post, Body } from '@nestjs/common';
import { ObservabilityLogger } from '@libs/observability';
import { ApiGatewayBusinessLogger } from './business-logger.service';

/**
 * Test controller for observability endpoints - only available in non-production environments
 * Provides endpoints for testing logging, metrics, and tracing functionality
 */
@Controller('observability-test')
export class ObservabilityTestController {
  private readonly logger: ObservabilityLogger;

  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly businessLogger: ApiGatewayBusinessLogger,
  ) {
    this.logger = this.loggerFactory.createLogger('ObservabilityTestController');
  }

  @Get('health')
  testHealth() {
    this.logger.log('Test health endpoint called - UPDATED FOR TESTING');
    this.businessLogger.logHealthCheckEvent('success', 'api-gateway', {
      endpoint: '/observability-test/health',
      timestamp: new Date().toISOString(),
    });
    return { 
      status: 'ok', 
      service: 'api-gateway', 
      timestamp: new Date().toISOString(),
      feedback_test: 'bundled-dev-workflow-test'
    };
  }

  @Post('business-event')
  testBusinessEvent(@Body() body: { event: string; status: string; metadata?: any }) {
    this.logger.log('Test business event endpoint called');
    
    // Test different business events based on the event type
    switch (body.event) {
      case 'request_routing':
        this.businessLogger.logRequestRoutingEvent(
          body.status as 'success' | 'failure',
          'test-request-' + Date.now(),
          body.metadata
        );
        break;
      case 'auth_validation':
        this.businessLogger.logAuthValidationEvent(
          body.status as 'success' | 'failure',
          'test-user-' + Date.now(),
          body.metadata
        );
        break;
      case 'rate_limit':
        this.businessLogger.logRateLimitEvent(
          body.status as 'rate_limited' | 'allowed',
          'test-ip-' + Date.now(),
          body.metadata
        );
        break;
      default:
        this.logger.warn('Unknown event type for testing');
    }

    return { message: 'Business event logged', event: body.event, status: body.status };
  }
}
