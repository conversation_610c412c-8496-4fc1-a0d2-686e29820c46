import { Injectable } from '@nestjs/common';
import { BusinessLogger } from '@libs/observability';

/**
 * Service wrapper for business logging in the API Gateway service
 * Provides methods for logging gateway-specific business events including
 * request routing, authentication, rate limiting, and proxy operations
 *
 * @see docs/business-events.md for the complete business events standard
 */
@Injectable()
export class ApiGatewayBusinessLogger {
  constructor(private readonly businessLogger: BusinessLogger) {}

  /**
   * Logs a request routing event.
   * @param status 'success' or 'failure'
   * @param requestId Unique identifier for the request
   * @param metadata Additional context (route, target_service, method, duration, etc.)
   */
  logRequestRoutingEvent(
    status: 'success' | 'failure',
    requestId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('request_routed', {
      status,
      requestId,
      service: 'api-gateway',
      ...metadata
    });
  }

  /**
   * Logs an authentication validation event at the gateway level.
   * @param status 'success' or 'failure'
   * @param userId ID of the user being validated (if available)
   * @param metadata Additional context (token_type, validation_method, ip, etc.)
   */
  logAuthValidationEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('auth_validation', {
      status,
      userId,
      service: 'api-gateway',
      ...metadata
    });
  }

  /**
   * Logs a rate limiting event.
   * @param status 'rate_limited' or 'allowed'
   * @param identifier IP address or user ID being rate limited
   * @param metadata Additional context (endpoint, limit, current_count, ttl, etc.)
   */
  logRateLimitEvent(
    status: 'rate_limited' | 'allowed',
    identifier: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('rate_limit', {
      status,
      identifier,
      service: 'api-gateway',
      ...metadata
    });
  }

  /**
   * Logs a proxy operation event.
   * @param status 'success' or 'failure'
   * @param requestId Unique identifier for the request
   * @param metadata Additional context (target_service, response_time, status_code, etc.)
   */
  logProxyEvent(
    status: 'success' | 'failure',
    requestId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('proxy_request', {
      status,
      requestId,
      service: 'api-gateway',
      ...metadata
    });
  }

  /**
   * Logs a health check event.
   * @param status 'success' or 'failure'
   * @param service Service being health checked
   * @param metadata Additional context (response_time, status_code, etc.)
   */
  logHealthCheckEvent(
    status: 'success' | 'failure',
    service: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('health_check', {
      status,
      target_service: service,
      service: 'api-gateway',
      ...metadata
    });
  }

  /**
   * Logs a backend service availability event.
   * @param status 'available' or 'unavailable'
   * @param service Backend service name
   * @param metadata Additional context (response_time, error_type, etc.)
   */
  logBackendAvailabilityEvent(
    status: 'available' | 'unavailable',
    service: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('backend_availability', {
      status,
      target_service: service,
      service: 'api-gateway',
      ...metadata
    });
  }

  /**
   * Logs a CORS validation event.
   * @param status 'allowed' or 'blocked'
   * @param origin Origin making the request
   * @param metadata Additional context (method, headers, etc.)
   */
  logCorsEvent(
    status: 'allowed' | 'blocked',
    origin: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logBusinessEvent('cors_validation', {
      status,
      origin,
      service: 'api-gateway',
      ...metadata
    });
  }
}
