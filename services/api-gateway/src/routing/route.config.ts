/**
 * Centralized route configuration for API Gateway
 * Single source of truth for routing, auth requirements, and service mapping
 */

export interface RouteConfig {
  /** Route pattern (supports wildcards and parameters) */
  pattern: string;
  /** Target service name (must match HttpModule service configuration) */
  serviceName: string;
  /** Whether this route requires JWT authentication */
  requiresAuth: boolean;
  /** Required realm roles (uses auth-common RolesGuard logic) */
  requiredRoles?: string[];
  /** Required resource roles (uses auth-common ResourceRolesGuard logic) */
  requiredResourceRoles?: Record<string, string[]>;
  /** Whether responses can be cached */
  cacheable?: boolean;
  /** Service-specific timeout override (ms) */
  timeout?: number;
  /** Service-specific retry limit override */
  retryLimit?: number;
  /** Description for documentation */
  description?: string;
}

/**
 * Centralized route configuration
 * Benefits:
 * - Single source of truth for all routes
 * - Declarative auth requirements
 * - Service-specific optimizations
 * - Easy to add new services
 * - Clear route-to-service mapping
 */
export const routeConfiguration: RouteConfig[] = [
  // ========================================
  // AUTH SERVICE ROUTES
  // ========================================
  
  // Public auth endpoints (no authentication required)
  {
    pattern: '/api/auth/login',
    serviceName: 'auth-service',
    requiresAuth: false,
    cacheable: false, // Never cache auth responses
    timeout: 2000, // Login MUST be super fast (2s max)
    retryLimit: 0, // Never retry auth (security)
    description: 'User login endpoint'
  },
  {
    pattern: '/api/auth/register',
    serviceName: 'auth-service',
    requiresAuth: false,
    cacheable: false,
    timeout: 3000, // Registration can be slightly slower
    retryLimit: 0, // Never retry registration
    description: 'User registration endpoint'
  },
  {
    pattern: '/api/auth/verify-email',
    serviceName: 'auth-service',
    requiresAuth: false,
    cacheable: false,
    timeout: 3000, // Email verification
    retryLimit: 0, // Never retry verification
    description: 'Email verification endpoint'
  },
  {
    pattern: '/api/auth/request-password-reset',
    serviceName: 'auth-service',
    requiresAuth: false,
    cacheable: false,
    timeout: 3000, // Password reset request
    retryLimit: 0, // Never retry password operations
    description: 'Password reset request endpoint'
  },
  {
    pattern: '/api/auth/execute-password-reset',
    serviceName: 'auth-service',
    requiresAuth: false,
    cacheable: false,
    timeout: 3000, // Password reset execution
    retryLimit: 0, // Never retry password operations
    description: 'Password reset execution endpoint'
  },
  {
    pattern: '/api/auth/refresh-token',
    serviceName: 'auth-service',
    requiresAuth: false, // Refresh tokens handle their own validation
    cacheable: false,
    timeout: 3000,
    retryLimit: 1,
    description: 'JWT token refresh endpoint'
  },

  // Protected auth endpoints (authentication required)
  {
    pattern: '/api/auth/me',
    serviceName: 'auth-service',
    requiresAuth: true,
    cacheable: true, // User info can be cached briefly
    timeout: 3000,
    retryLimit: 2,
    description: 'Get current user authentication info'
  },
  {
    pattern: '/api/auth/logout',
    serviceName: 'auth-service',
    requiresAuth: true,
    cacheable: false,
    timeout: 3000,
    retryLimit: 1,
    description: 'User logout endpoint'
  },

  // ========================================
  // USER SERVICE ROUTES  
  // ========================================
  // NOTE: More specific routes MUST come before parametrized routes

  // Health check routes (most specific first)
  {
    pattern: '/api/users/health',
    serviceName: 'user-service',
    requiresAuth: false,
    cacheable: false,
    timeout: 2000,
    retryLimit: 1,
    description: 'User service health check'
  },

  // Public user endpoints
  {
    pattern: '/api/users',
    serviceName: 'user-service',
    requiresAuth: false,
    cacheable: true, // Public user lists can be cached
    timeout: 5000,
    retryLimit: 3,
    description: 'Get users list (public)'
  },

  // Protected user endpoints (specific routes before parametrized)
  {
    pattern: '/api/users/me',
    serviceName: 'user-service',
    requiresAuth: true,
    cacheable: true, // Current user profile can be cached
    timeout: 5000,
    retryLimit: 3,
    description: 'Get current user profile'
  },
  {
    pattern: '/api/users/profile',
    serviceName: 'user-service',
    requiresAuth: true,
    cacheable: true, // User profiles can be cached
    timeout: 5000,
    retryLimit: 3,
    description: 'Get/update user profile'
  },
  {
    pattern: '/api/users/:id',
    serviceName: 'user-service',
    requiresAuth: true,
    cacheable: true,
    timeout: 5000,
    retryLimit: 3,
    description: 'Get/update/delete specific user'
  },

  // ========================================
  // HEALTH & MONITORING ROUTES
  // ========================================
  
  // Health check routes (always public)
  {
    pattern: '/api/auth/health',
    serviceName: 'auth-service',
    requiresAuth: false,
    cacheable: false,
    timeout: 2000,
    retryLimit: 1,
    description: 'Auth service health check'
  },

  // ========================================
  // FUTURE SERVICE ROUTES (Examples)
  // ========================================
  
  // When adding new services, just add routes here:
  // {
  //   pattern: '/api/notifications/*',
  //   serviceName: 'notification-service',
  //   requiresAuth: true,
  //   cacheable: false,
  //   timeout: 3000,
  //   retryLimit: 2,
  //   description: 'Notification service endpoints'
  // },
  // {
  //   pattern: '/api/admin/*',
  //   serviceName: 'admin-service', 
  //   requiresAuth: true,
  //   cacheable: false,
  //   timeout: 10000,
  //   retryLimit: 1,
  //   description: 'Admin service endpoints (requires special permissions)'
  // }
];

/**
 * Route matching utility
 * Matches incoming request paths against route patterns with support for:
 * - Exact matches: /api/auth/login
 * - Parameter matches: /api/users/:id
 * - Wildcard matches: /api/admin/*
 */
export class RouteConfigMatcher {
  /**
   * Find route configuration for a given path
   */
  static findRouteConfig(path: string): RouteConfig | undefined {
    return routeConfiguration.find(route => this.matchesPattern(path, route.pattern));
  }

  /**
   * Check if a path matches a route pattern
   */
  private static matchesPattern(path: string, pattern: string): boolean {
    // Exact match
    if (path === pattern) {
      return true;
    }

    // Wildcard match (e.g., /api/admin/* matches /api/admin/anything)
    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2); // Remove /*
      return path.startsWith(prefix + '/') || path === prefix;
    }

    // Parameter match (e.g., /api/users/:id matches /api/users/123)
    if (pattern.includes(':')) {
      const patternParts = pattern.split('/');
      const pathParts = path.split('/');

      if (patternParts.length !== pathParts.length) {
        return false;
      }

      return patternParts.every((part, index) => {
        return part.startsWith(':') || part === pathParts[index];
      });
    }

    return false;
  }

  /**
   * Get all routes for a specific service
   */
  static getRoutesForService(serviceName: string): RouteConfig[] {
    return routeConfiguration.filter(route => route.serviceName === serviceName);
  }

  /**
   * Get all public routes (no auth required)
   */
  static getPublicRoutes(): RouteConfig[] {
    return routeConfiguration.filter(route => !route.requiresAuth);
  }

  /**
   * Get all protected routes (auth required)
   */
  static getProtectedRoutes(): RouteConfig[] {
    return routeConfiguration.filter(route => route.requiresAuth);
  }
}