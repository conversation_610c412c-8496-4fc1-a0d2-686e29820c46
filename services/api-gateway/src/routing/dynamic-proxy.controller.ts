import { All, Controller, Req, Res, Injectable, UnauthorizedException, Logger, Inject, HttpException } from '@nestjs/common';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { RouteConfigMatcher, RouteConfig } from './route.config';
import { ProxyService } from '../proxy/proxy.service';
import { ObservabilityLogger } from '@libs/observability';
import { JwtPayload, UserContext, JwtStrategy } from '@libs/auth-common';
import { KeycloakClientService } from '@libs/keycloak-client';
import { ErrorResponseBuilderService, AUTH_ERRORS, ERROR_HTTP_STATUS } from '@libs/error-handling';

interface AuthenticatedRequest extends Request {
  user?: UserContext;
  routeConfig?: RouteConfig;
}

/**
 * Dynamic proxy controller that replaces multiple fragmented controllers
 * 
 * Benefits:
 * - Single controller handles all API routes
 * - Centralized route configuration
 * - Dynamic auth validation based on route requirements
 * - Service-specific optimizations
 * - Easy to add new services
 * 
 * Replaces:
 * - AuthProxyController (49 lines)
 * - AuthProtectedProxyController (63 lines) 
 * - UsersProxyController (26 lines)
 * Total: ~138 lines → Single dynamic controller
 */
@Controller('api')
@Injectable()
export class DynamicProxyController {
  private readonly logger = new Logger(DynamicProxyController.name);
  private readonly observabilityLogger: ObservabilityLogger;

  constructor(
    private readonly proxyService: ProxyService,
    private readonly keycloakClient: KeycloakClientService,
    private readonly configService: ConfigService,
    private readonly errorResponseBuilder: ErrorResponseBuilderService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    this.observabilityLogger = this.loggerFactory.createLogger(DynamicProxyController.name);
  }

  /**
   * Handle all API requests dynamically based on route configuration
   * This single method replaces multiple controllers and provides:
   * - Dynamic routing based on centralized configuration
   * - Conditional authentication based on route requirements
   * - Service-specific proxy handling
   */
  @All('*')
  async handleAllRequests(@Req() req: AuthenticatedRequest, @Res() res: Response): Promise<void> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();
    
    try {
      // 1. Find route configuration for this path
      const routeConfig = RouteConfigMatcher.findRouteConfig(req.path);
      
      if (!routeConfig) {
        this.observabilityLogger.warn({
          message: 'No route configuration found',
          path: req.path,
          method: req.method,
          requestId
        });
        
        return this.sendErrorResponse(res, 404, 'Route not found', requestId);
      }

      // Attach route config to request for downstream use
      req.routeConfig = routeConfig;

      // 2. Handle authentication and authorization based on route requirements
      if (routeConfig.requiresAuth) {
        const user = await this.validateAuthentication(req, requestId);
        req.user = user;
        
        // 2a. Validate realm roles if required (using auth-common logic)
        if (routeConfig.requiredRoles) {
          this.validateRealmRoles(user, routeConfig.requiredRoles, req.path, requestId);
        }
        
        // 2b. Validate resource roles if required (using auth-common logic)
        if (routeConfig.requiredResourceRoles) {
          this.validateResourceRoles(user, routeConfig.requiredResourceRoles, req.path, requestId);
        }
        
        // Add user context header for downstream services
        req.headers['x-user-context'] = JSON.stringify(user);
      }

      // 3. Log request with route context
      this.logProxyRequest(req, routeConfig, requestId, startTime);

      // 4. Proxy request to appropriate service
      await this.proxyService.handleRequest(req, res, routeConfig.serviceName);

    } catch (error: any) {
      await this.handleControllerError(error, req, res, requestId, startTime);
    }
  }


  /**
   * Validate JWT authentication for protected routes using centralized auth-common strategy
   */
  private async validateAuthentication(req: AuthenticatedRequest, requestId: string): Promise<UserContext> {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      this.observabilityLogger.warn({
        message: 'Missing or invalid authorization header',
        path: req.path,
        requestId
      });
      throw new UnauthorizedException('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7);

    try {
      // Extract kid from JWT header for JWKS resolution
      const kid = this.extractKidFromToken(token);
      if (!kid) {
        throw new Error('Unable to find kid in JWT header');
      }
      
      // Use centralized Keycloak client for key resolution (HTTP/2 performance boost)
      const signingKey = await this.keycloakClient.getSigningKey(kid);
      
      // Decode and validate JWT using jsonwebtoken
      const jwt = require('jsonwebtoken');
      const keycloakBaseUrl = this.configService.get<string>('KEYCLOAK_BASE_URL');
      const keycloakRealm = this.configService.get<string>('KEYCLOAK_REALM_NAME');
      
      const decoded = jwt.verify(token, signingKey, {
        audience: ['account', 'realm-management'],
        issuer: `${keycloakBaseUrl}/realms/${keycloakRealm}`,
        algorithms: ['RS256'],
      }) as JwtPayload;
      
      // Use the same transformation logic as JwtStrategy
      const userContext: UserContext = {
        userId: decoded.sub,
        email: decoded.email,
        name: decoded.name,
        username: decoded.preferred_username,
        roles: decoded.realm_access?.roles || [],
        resourceRoles: decoded.resource_access 
          ? Object.fromEntries(
              Object.entries(decoded.resource_access)
                .filter(([_, value]) => value?.roles)
                .map(([key, value]) => [key, value!.roles])
            )
          : {},
        isEmailVerified: decoded.email_verified,
      };

      this.observabilityLogger.debug({
        message: 'JWT validation successful using centralized Keycloak client',
        userId: userContext.userId,
        username: userContext.username,
        roles: userContext.roles,
        path: req.path,
        requestId
      });

      return userContext;

    } catch (error: any) {
      this.observabilityLogger.error({
        message: 'JWT validation failed',
        error: error.message,
        path: req.path,
        requestId
      });
      
      // Use error-handling library for consistent error responses
      const errorResponse = this.errorResponseBuilder.buildFromErrorCode(
        AUTH_ERRORS.TOKEN_INVALID,
        req.path
      );
      throw new HttpException(errorResponse, ERROR_HTTP_STATUS[AUTH_ERRORS.TOKEN_INVALID]);
    }
  }

  /**
   * Validate realm roles using auth-common RolesGuard logic with proper error handling
   */
  private validateRealmRoles(user: UserContext, requiredRoles: string[], path: string, requestId: string): void {
    const hasRole = requiredRoles.some((role) => user.roles?.includes(role));
    
    if (!hasRole) {
      this.observabilityLogger.warn({
        message: 'Access denied: insufficient realm roles',
        userId: user.userId,
        username: user.username,
        userRoles: user.roles,
        requiredRoles,
        path,
        requestId
      });
      
      // Use error-handling library for consistent RBAC error responses
      const errorResponse = this.errorResponseBuilder.buildFromErrorCode(
        AUTH_ERRORS.INSUFFICIENT_PERMISSIONS,
        path,
        'You do not have the required roles to access this resource'
      );
      throw new HttpException(errorResponse, ERROR_HTTP_STATUS[AUTH_ERRORS.INSUFFICIENT_PERMISSIONS]);
    }

    this.observabilityLogger.debug({
      message: 'Realm role validation successful',
      userId: user.userId,
      username: user.username,
      userRoles: user.roles,
      requiredRoles,
      path,
      requestId
    });
  }

  /**
   * Validate resource roles using auth-common ResourceRolesGuard logic with proper error handling
   */
  private validateResourceRoles(user: UserContext, requiredResourceRoles: Record<string, string[]>, path: string, requestId: string): void {
    if (!user.resourceRoles) {
      this.observabilityLogger.warn({
        message: 'Access denied: user has no resource roles',
        userId: user.userId,
        username: user.username,
        requiredResourceRoles,
        path,
        requestId
      });
      
      const errorResponse = this.errorResponseBuilder.buildFromErrorCode(
        AUTH_ERRORS.INSUFFICIENT_PERMISSIONS,
        path,
        'You do not have any resource permissions'
      );
      throw new HttpException(errorResponse, ERROR_HTTP_STATUS[AUTH_ERRORS.INSUFFICIENT_PERMISSIONS]);
    }

    // Check if user has any of the required resource roles
    const hasRequiredResourceRole = Object.entries(requiredResourceRoles).some(([resource, requiredRoles]) => {
      const userResourceRoles = user.resourceRoles?.[resource];
      if (!userResourceRoles) {
        return false;
      }
      return requiredRoles.some(role => userResourceRoles.includes(role));
    });

    if (!hasRequiredResourceRole) {
      this.observabilityLogger.warn({
        message: 'Access denied: insufficient resource roles',
        userId: user.userId,
        username: user.username,
        userResourceRoles: user.resourceRoles,
        requiredResourceRoles,
        path,
        requestId
      });
      
      const errorResponse = this.errorResponseBuilder.buildFromErrorCode(
        AUTH_ERRORS.INSUFFICIENT_PERMISSIONS,
        path,
        'You do not have the required resource permissions'
      );
      throw new HttpException(errorResponse, ERROR_HTTP_STATUS[AUTH_ERRORS.INSUFFICIENT_PERMISSIONS]);
    }

    this.observabilityLogger.debug({
      message: 'Resource role validation successful',
      userId: user.userId,
      username: user.username,
      userResourceRoles: user.resourceRoles,
      requiredResourceRoles,
      path,
      requestId
    });
  }

  /**
   * Extract kid from JWT header (reuse logic from JwtStrategy)
   */
  private extractKidFromToken(token: string): string | null {
    try {
      const header = token.split('.')[0];
      const decodedHeader = JSON.parse(Buffer.from(header, 'base64url').toString());
      return decodedHeader.kid || null;
    } catch (error: any) {
      this.observabilityLogger.error({
        message: 'Failed to extract kid from JWT header', 
        error: error?.message || error
      });
      return null;
    }
  }

  /**
   * Handle controller-level errors
   */
  private async handleControllerError(
    error: any,
    req: AuthenticatedRequest,
    res: Response,
    requestId: string,
    startTime: number
  ): Promise<void> {
    const duration = Date.now() - startTime;
    
    let statusCode = 500;
    let errorMessage = 'Internal server error';

    if (error instanceof UnauthorizedException) {
      statusCode = 401;
      errorMessage = error.message;
    } else if (error.status) {
      statusCode = error.status;
      errorMessage = error.message;
    }

    this.observabilityLogger.error({
      message: 'Dynamic proxy controller error',
      error: error.message,
      statusCode,
      path: req.path,
      method: req.method,
      duration,
      requestId,
      routeConfig: req.routeConfig?.pattern
    });

    await this.sendErrorResponse(res, statusCode, errorMessage, requestId);
  }

  /**
   * Send error response with consistent format
   */
  private async sendErrorResponse(
    res: Response,
    statusCode: number,
    message: string,
    requestId: string
  ): Promise<void> {
    res.status(statusCode).json({
      error: true,
      message,
      requestId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log proxy request with route context
   */
  private logProxyRequest(
    req: AuthenticatedRequest,
    routeConfig: RouteConfig,
    requestId: string,
    startTime: number
  ): void {
    this.observabilityLogger.log({
      message: 'Dynamic proxy request initiated',
      requestId,
      method: req.method,
      path: req.path,
      routePattern: routeConfig.pattern,
      targetService: routeConfig.serviceName,
      requiresAuth: routeConfig.requiresAuth,
      cacheable: routeConfig.cacheable,
      timeout: routeConfig.timeout,
      retryLimit: routeConfig.retryLimit,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.socket.remoteAddress,
      hasBody: req.body && Object.keys(req.body).length > 0,
      contentType: req.headers['content-type'],
      isAuthenticated: !!req.user
    });
  }

  /**
   * Generate unique request ID for correlation
   */
  private generateRequestId(): string {
    return `dynamic_proxy_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}