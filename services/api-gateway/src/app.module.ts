import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthController } from './health/health.controller';
import { ProxyModule } from './proxy/proxy.module';
import { ThrottleMiddleware } from './throttler/throttle.middleware';
// Comprehensive library integrations
import { ErrorHandlingModule, CorrelationIdMiddleware } from '@libs/error-handling';
import { AuthCommonModule } from '@libs/auth-common';
import { CircuitBreakerModule } from '@libs/resilience';
import { ObservabilityModule } from './observability/observability.module';
import { MetricsModule } from './metrics/metrics.module';
import { DocsModule } from './docs/docs.module';
import { HttpModule } from '@libs/http';

@Module({
  imports: [
    // Load .env files and make environment variables available via ConfigService
    ConfigModule.forRoot({
      isGlobal: true, // Make ConfigModule available globally
      envFilePath: (() => {
        const env = process.env.NODE_ENV;
        if (env === 'test') {
          return '.env.test';
        } else if (env === 'docker') {
          return '.env.docker';
        } else {
          // Default to .env.local for development or if NODE_ENV is not explicitly set to test/docker
          return '.env.local';
        }
      })(),
    }),
    CircuitBreakerModule.register(), // Single circuit breaker instance for entire API Gateway
    HttpModule.forRoot(), // Enterprise defaults: 3s timeout, 1 retry, HTTP/2
    AuthCommonModule.forRoot(), // Add auth-common module for JWT validation
    // Add comprehensive error handling with correlation ID support
    ErrorHandlingModule.forRoot({
      config: {
        serviceName: 'api-gateway',
        environment: process.env.NODE_ENV as 'development' | 'production' || 'development',
        lokiBaseUrl: process.env.LOKI_BASE_URL || 'http://localhost:3100',
        includeLokiLinks: true,
        includeSensitiveDetails: process.env.NODE_ENV !== 'production',
      },
      useGlobalFilter: true,
      useHttpInterceptor: true,
    }),
    ProxyModule,
    ObservabilityModule, // Add observability module for logging, metrics, and tracing
    MetricsModule, // Add metrics module for Prometheus endpoint
    DocsModule, // Add documentation aggregation module
  ],
  controllers: [AppController, HealthController],
  providers: [
    AppService,
    // AuthMiddleware removed - using Guards in controllers instead
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply correlation ID middleware first (before throttling)
    consumer
      .apply(CorrelationIdMiddleware)
      .forRoutes('*');
    
    // Authentication now handled by Guards in proxy controllers
    
    // Apply throttling middleware to all routes
    consumer
      .apply(ThrottleMiddleware)
      .forRoutes('*');
  }
}
