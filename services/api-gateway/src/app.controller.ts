import { Controller, Get, Post, HttpCode, HttpStatus, Inject } from '@nestjs/common';
import { AppService } from './app.service';
import { ObservabilityLogger } from '@libs/observability';
import { CorrelationId, RequestDuration } from '@libs/error-handling';

@Controller('app-status') 
export class AppController {
  private readonly logger: ObservabilityLogger;

  constructor(
    private readonly appService: AppService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory.createLogger(AppController.name);
  }

  @Get()
  getInfo(
    @CorrelationId() correlationId?: string,
    @RequestDuration() duration?: number
  ): any {
    this.logger.log(`App status endpoint called [${correlationId}]`);
    return {
      message: this.appService.getInfo(),
      correlationId,
      requestDuration: duration,
      timestamp: new Date().toISOString(),
    };
  }

  @Get('test-correlation')
  testCorrelation(
    @CorrelationId() correlationId?: string,
    @RequestDuration() duration?: number
  ): any {
    this.logger.log(`Test correlation endpoint called [${correlationId}]`);
    return {
      message: 'Correlation ID test endpoint',
      correlationId,
      requestDuration: duration,
      timestamp: new Date().toISOString(),
      service: 'api-gateway',
    };
  }

  // Removed the conflicting POST handler for /api/auth/register
  // @Post('auth/register')
  // @HttpCode(HttpStatus.I_AM_A_TEAPOT)
  // testAuthRegister() {
  //   console.log('>>> [API Gateway] /api/auth/register in AppController was hit!');
  //   return { message: 'AppController handled /api/auth/register' };
  // }
}
