{"name": "@polyrepo/api-gateway", "version": "1.0.0", "description": "API Gateway for polyrepo services", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc -p tsconfig.build.json", "build:webpack": "webpack --config webpack.config.js", "build:webpack:watch": "webpack --config webpack.config.js --watch", "bundle:rebuild:restart": "yarn build:webpack && cd ../../infrastructure/local-dev && docker restart polyrepo_api_gateway_volume", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "start:dev": "nest start --watch", "start:bundle": "node dist-webpack/main.bundle.js", "dev:bundle": "yarn build:webpack && yarn start:bundle", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --json --outputFile=e2e-test-results.json"}, "dependencies": {"@libs/auth-common": "file:../../libs/auth-common", "@libs/error-handling": "file:../../libs/error-handling", "@libs/http": "file:../../libs/http", "@libs/keycloak-client": "file:../../libs/keycloak-client", "@libs/observability": "file:../../libs/observability", "@libs/testing-utils": "file:../../libs/testing-utils", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@libs/testing-utils": "file:../../libs/testing-utils", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "copy-webpack-plugin": "^13.0.0", "dotenv-webpack": "^8.1.0", "eslint": "*", "jest": "^29.5.0", "rimraf": "^5.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "typescript": "*", "webpack": "^5.92.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": "test/unit/.*\\.spec\\.ts$", "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "coverage", "testEnvironment": "node", "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}