const path = require('path');
const webpack = require('webpack');
const DotenvWebpack = require('dotenv-webpack');
const nodeExternals = require('webpack-node-externals');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  // Target Node.js environment
  target: 'node',
  mode: 'development',
  
  // ===================================================================
  // ESM Support Configuration (Node.js 22)
  // ===================================================================
  // Enable ESM imports for pure ESM modules (like got v14)
  // Node.js 22 handles ESM natively, so we can import ESM modules directly
  externalsType: 'import',
  
  // Entry point
  entry: './src/main.ts',
  
  // Output configuration
  output: {
    path: path.resolve(__dirname, 'dist-webpack'),
    filename: 'main.bundle.js',
    libraryTarget: 'commonjs2',
  },
  
  // Module resolution
  resolve: {
    extensions: ['.ts', '.js', '.mjs', '.json'],
    alias: {
      // BUNDLE OPTIMIZATION: Use src paths for 6x faster library change feedback
      '@libs/auth-common': path.resolve(__dirname, '../../libs/auth-common/src'),
      '@libs/http': path.resolve(__dirname, '../../libs/http/src'),
      '@libs/observability': path.resolve(__dirname, '../../libs/observability/src'),
      '@libs/shared-types': path.resolve(__dirname, '../../libs/shared-types/src'),
      '@libs/caching': path.resolve(__dirname, '../../libs/caching/src'),
      '@libs/messaging': path.resolve(__dirname, '../../libs/messaging/src'),
      '@libs/resilience': path.resolve(__dirname, '../../libs/resilience/src'),
      '@libs/keycloak-client': path.resolve(__dirname, '../../libs/keycloak-client/src'),
      '@libs/error-handling': path.resolve(__dirname, '../../libs/error-handling/src'),
    },
    // Fallback for node modules
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },
  
  // Loader configuration with explicit resolution
  resolveLoader: {
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },

  // Module rules
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        include: [
          path.resolve(__dirname, 'src'),
          path.resolve(__dirname, '../../libs') // Include all library source
        ],
        use: [
          {
            // Use require.resolve for explicit loader path
            loader: require.resolve('ts-loader'),
            options: {
              configFile: path.resolve(__dirname, 'tsconfig.webpack.json'),
              // Skip type checking for faster builds - focus on bundling
              transpileOnly: true,
              // Important for NestJS decorators and metadata
              experimentalWatchApi: true,
              compilerOptions: {
                // Preserve decorator metadata for NestJS DI
                experimentalDecorators: true,
                emitDecoratorMetadata: true,
                // Enable source maps for debugging
                sourceMap: true,
              }
            }
          }
        ]
      },
      // Handle binary files by returning empty module
      {
        test: /\.(node|so|dylib)$/,
        use: 'null-loader'
      },
      // Handle other assets
      {
        test: /\.(txt|md)$/,
        type: 'asset/source',
      },
      // Handle thrift files for Jaeger
      {
        test: /\.thrift$/,
        type: 'asset/source',
      },
      
      // =================================================================
      // ESM Module Handling (Node.js 22 Compatible)
      // =================================================================
      // With Node.js 22, we can handle ESM modules natively.
      // This simplified rule handles .mjs files and modern JS modules
      // without complex transpilation since Node.js 22 supports ESM properly.
      {
        test: /\.mjs$/,
        type: 'javascript/esm',
      }
    ]
  },
  
  // External dependencies - use webpack-node-externals to exclude ALL node_modules
  externals: [
    nodeExternals({
      // Bundle only specific modules that need to be included
      allowlist: [
        // Allow shared libs to be bundled since they're local file dependencies
        /^@libs\//,
        
        // Critical runtime dependencies for TypeScript/NestJS
        'tslib',                      // TypeScript runtime helpers
        'reflect-metadata',           // Required for NestJS decorators and DI
        
        // ============================================================
        // ESM LIBRARIES: Bundle for Node.js 22 ESM compatibility
        // ============================================================
        // Core HTTP client (got ecosystem)
        'got',                        // Main got library - ESM module
        /got/,                        // Any got sub-modules
        /cacheable-request/,          // HTTP caching layer
        /keyv/,                       // Key-value storage
        /form-data-encoder/,          // Form data handling
        /decompress-response/,        // Response decompression
        /mimic-response/,             // Response mocking utilities
        /normalize-url/,              // URL normalization
        /responselike/,               // Response-like objects
        
        // Utility libraries (sindresorhus ecosystem - ESM modules)
        /@sindresorhus/,             // All @sindresorhus/* packages
        /type-fest/,                 // TypeScript type utilities
        /p-cancelable/,              // Cancelable promises
        /lowercase-keys/,            // Object key transformation
        /defer-to-connect/,          // Connection deferring
        /quick-lru/,                 // LRU cache implementation
        /escape-string-regexp/,      // RegExp escaping
        /is-plain-obj/,              // Object type checking
        
        // HTTP/2 and networking
        /http2-wrapper/,             // HTTP/2 client wrapper
        /@szmarczak/,                // HTTP utilities
        
        // Allow static assets to be bundled
        /\.(css|less|sass|scss)$/,
        /\.(png|jpg|jpeg|gif|svg)$/,
        /\.(woff|woff2|eot|ttf|otf)$/,
      ]
    })
  ],

  // Plugins
  plugins: [
    // Ignore optional NestJS modules that might not be installed
    new webpack.IgnorePlugin({
      resourceRegExp: /^@nestjs\/(microservices|websockets)/,
    }),
    
    // Ignore optional dependencies
    new webpack.IgnorePlugin({
      resourceRegExp: /^class-transformer\/storage$/,
    }),
    
    // Ignore native modules and binary files
    new webpack.IgnorePlugin({
      resourceRegExp: /\.node$/,
    }),
    
    // Ignore dynamic requires that NestJS might use
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
    
    // Use dotenv-webpack to inject environment variables correctly
    new DotenvWebpack({
      path: './.env.docker',
      systemvars: true,     // Load all system environment variables as well
      safe: false,         
      defaults: false,
      allowEmptyValues: true,
      // This ensures process.env remains intact during runtime to work with ConfigService
      ignoreStub: true
    }),
    
    // Copy Jaeger thrift files to dist-webpack directory
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, 'jaeger-idl/jaeger-idl'),
          to: path.resolve(__dirname, 'dist-webpack/jaeger-idl'),
          noErrorOnMissing: true
        }
      ]
    }),
    
    // Bundle optimization feedback
    new webpack.ProgressPlugin((percentage, message) => {
      if (percentage === 1) {
        console.log(`✅ API Gateway: Bundle optimization completed`);
      }
    }),
  ],
  
  // Development settings
  devtool: 'source-map',
  
  // Watch mode optimization for bundle optimization
  watchOptions: {
    aggregateTimeout: 300, // Wait 300ms before rebuilding
    poll: false, // Use native file watching
    followSymlinks: false,
    ignored: [
      '**/node_modules/**',
      '**/dist/**',
      '**/*.d.ts',
      '**/coverage/**'
    ]
  },
  
  // Stats configuration for better output
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    chunkModules: false,
    reasons: false,
    hash: false,
    version: false,
    timings: true,
    assets: true,
    warnings: true,
    errors: true,
    errorDetails: true
  },
  
  // Performance hints
  performance: {
    hints: false
  },
  
  // Node.js polyfills/mocks
  node: {
    // Don't mock these Node.js globals, use the real ones
    __dirname: false,
    __filename: false,
  }
};
