NODE_ENV=development
PORT=3000

# Service URLs (for bundling context - Docker internal networking)
AUTH_SERVICE_URL=http://auth-service:3000
USER_SERVICE_URL=http://user-service:3000

# Keycloak Configuration (for JWT validation - Docker internal networking)
KEYCLOAK_BASE_URL=http://keycloak:8080
KEYCLOAK_REALM_NAME=polyrepo-test

# Redis Configuration (for Docker environment)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
ENABLE_TRACING=true
TEMPO_ENDPOINT=http://localhost:4318/v1/traces