# Self-Contained Webpack Bundle Dockerfile for API Gateway - No Runtime Dependencies
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

WORKDIR /app

# Copy ONLY the webpack bundle and required files
COPY dist-webpack/main.bundle.js ./main.bundle.js

# Copy any other static files that might be needed at runtime
# (API Gateway may need config files or other static assets)

# Change ownership to non-root user  
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Run the self-contained webpack bundle
CMD ["node", "main.bundle.js"]
