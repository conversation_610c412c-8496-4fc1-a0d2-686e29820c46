{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist-webpack", "module": "commonjs", "target": "es2020", "sourceMap": true, "declaration": false, "removeComments": true, "noEmitOnError": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "dist-webpack", "test"]}