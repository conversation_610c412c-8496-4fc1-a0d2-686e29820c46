NODE_ENV=development
PORT=3000

# Upstream Service URLs (for local development, services running on host)
AUTH_SERVICE_URL=http://localhost:3001 # Corrected port
USER_SERVICE_URL=http://localhost:3002 # Corrected port
# Add other services as needed, e.g.:
# PRODUCT_SERVICE_URL=http://localhost:3004

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true # Assuming api-gateway will also integrate observability
LOKI_HOST=http://localhost:3100
JAEGER_ENDPOINT=http://localhost:14268/api/traces
