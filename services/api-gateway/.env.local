NODE_ENV=development
PORT=3000

# Keycloak Configuration (temporary until we centralize)
KEYCLOAK_BASE_URL=http://localhost:8080
KEYCLOAK_REALM_NAME=polyrepo-realm

# URLs for other services when running locally
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002

# Optional: Proxy timeout in milliseconds for http-proxy-middleware
PROXY_TIMEOUT=25000

# CORS configuration
# Comma-separated list of allowed origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:4200

# Rate limiting configuration
# These settings apply to sensitive endpoints like /api/auth/login and /api/auth/register
THROTTLE_TTL=60 # Time-to-live in seconds
THROTTLE_LIMIT=10 # Number of requests allowed in TTL

# Observability Configuration
SERVICE_NAME=api-gateway
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
ENABLE_METRICS=true
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces
