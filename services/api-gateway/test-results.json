{"numFailedTestSuites": 4, "numFailedTests": 0, "numPassedTestSuites": 0, "numPassedTests": 0, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 4, "numTodoTests": 0, "numTotalTestSuites": 4, "numTotalTests": 0, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1750194494188, "success": false, "testResults": [{"assertionResults": [], "coverage": {}, "endTime": 1750194503196, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/proxy.service.spec.ts\u001b[0m:\u001b[93m40\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getProxyMiddleware' does not exist on type 'ProxyService'.\n\n    \u001b[7m40\u001b[0m       const middleware = service.getProxyMiddleware('/auth');\n    \u001b[7m  \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/proxy.service.spec.ts\u001b[0m:\u001b[93m47\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getProxyMiddleware' does not exist on type 'ProxyService'.\n\n    \u001b[7m47\u001b[0m       const middleware = service.getProxyMiddleware('/users');\n    \u001b[7m  \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/proxy.service.spec.ts\u001b[0m:\u001b[93m55\u001b[0m:\u001b[93m17\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getProxyMiddleware' does not exist on type 'ProxyService'.\n\n    \u001b[7m55\u001b[0m         service.getProxyMiddleware('/unknown');\n    \u001b[7m  \u001b[0m \u001b[91m                ~~~~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/proxy.service.spec.ts\u001b[0m:\u001b[93m88\u001b[0m:\u001b[93m34\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getProxyMiddleware' does not exist on type 'ProxyService'.\n\n    \u001b[7m88\u001b[0m       const middleware = service.getProxyMiddleware('/auth');\n    \u001b[7m  \u001b[0m \u001b[91m                                 ~~~~~~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/services/api-gateway/test/unit/proxy.service.spec.ts", "startTime": 1750194503196, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750194503196, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/correlation.middleware.spec.ts\u001b[0m:\u001b[93m2\u001b[0m:\u001b[93m39\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../../src/correlation/correlation.middleware' or its corresponding type declarations.\n\n    \u001b[7m2\u001b[0m import { CorrelationMiddleware } from '../../src/correlation/correlation.middleware';\n    \u001b[7m \u001b[0m \u001b[91m                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/services/api-gateway/test/unit/correlation.middleware.spec.ts", "startTime": 1750194503196, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750194503196, "message": "  ● Test suite failed to run\n\n    <PERSON><PERSON> encountered an unexpected token\n\n    Jest failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when <PERSON><PERSON> is not configured to support such syntax.\n\n    Out of the box Je<PERSON> supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see https://jestjs.io/docs/ecmascript-modules for how to enable it.\n     • If you are trying to use TypeScript, see https://jestjs.io/docs/getting-started#using-typescript\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \"transformIgnorePatterns\" in your config.\n     • If you need a custom transformation specify a \"transform\" option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \"moduleNameMapper\" config option.\n\n    You'll find more details and examples of these config options in the docs:\n    https://jestjs.io/docs/configuration\n    For information about custom transformations, see:\n    https://jestjs.io/docs/code-transformation\n\n    Details:\n\n    /root/code/polyrepo/node_modules/got/dist/source/index.js:1\n    ({\"Object.<anonymous>\":function(module,exports,require,__dirname,__filename,jest){import create from './create.js';\n                                                                                      ^^^^^^\n\n    SyntaxError: Cannot use import statement outside a module\n\n      1 | import { Injectable, Logger, Optional, Inject } from '@nestjs/common';\n    > 2 | import got, { Got, Method, Options as GotOptions, Response, BeforeRetryHook } from 'got';\n        | ^\n      3 | import { \n      4 |   OBSERVABILITY_LOGGER, \n      5 |   METRICS_SERVICE, \n\n      at Runtime.createScriptFromCode (../../node_modules/jest-runtime/build/index.js:1505:14)\n      at Object.<anonymous> (../../libs/http/src/client/http-client.service.ts:2:1)\n      at Object.<anonymous> (../../libs/http/src/http.module.ts:19:1)\n      at Object.<anonymous> (../../libs/http/src/index.ts:2:1)\n      at Object.<anonymous> (src/docs/docs.service.ts:3:1)\n      at Object.<anonymous> (test/unit/docs.service.spec.ts:2:1)\n", "name": "/root/code/polyrepo/services/api-gateway/test/unit/docs.service.spec.ts", "startTime": 1750194503196, "status": "failed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1750194503196, "message": "  ● Test suite failed to run\n\n    \u001b[96mtest/unit/app.controller.spec.ts\u001b[0m:\u001b[93m39\u001b[0m:\u001b[93m33\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getHello' does not exist on type 'AppController'.\n\n    \u001b[7m39\u001b[0m       const result = controller.getHello();\n    \u001b[7m  \u001b[0m \u001b[91m                                ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/app.controller.spec.ts\u001b[0m:\u001b[93m42\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getHello' does not exist on type 'Mocked<AppService>'.\n\n    \u001b[7m42\u001b[0m       expect(appService.getHello).toHaveBeenCalledTimes(1);\n    \u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/app.controller.spec.ts\u001b[0m:\u001b[93m56\u001b[0m:\u001b[93m33\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getHealthStatus' does not exist on type 'AppController'.\n\n    \u001b[7m56\u001b[0m       const result = controller.getHealthStatus();\n    \u001b[7m  \u001b[0m \u001b[91m                                ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/app.controller.spec.ts\u001b[0m:\u001b[93m59\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getHealthStatus' does not exist on type 'Mocked<AppService>'.\n\n    \u001b[7m59\u001b[0m       expect(appService.getHealthStatus).toHaveBeenCalledTimes(1);\n    \u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~~~~~\u001b[0m\n    \u001b[96mtest/unit/app.controller.spec.ts\u001b[0m:\u001b[93m72\u001b[0m:\u001b[93m33\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getHealthStatus' does not exist on type 'AppController'.\n\n    \u001b[7m72\u001b[0m       const result = controller.getHealthStatus();\n    \u001b[7m  \u001b[0m \u001b[91m                                ~~~~~~~~~~~~~~~\u001b[0m\n", "name": "/root/code/polyrepo/services/api-gateway/test/unit/app.controller.spec.ts", "startTime": 1750194503196, "status": "failed", "summary": ""}], "wasInterrupted": false}