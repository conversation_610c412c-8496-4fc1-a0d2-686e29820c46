# --- Base Stage ---
# Uses the common base image where yarn install and shared lib builds are done.
FROM polyrepo-node-base:latest AS base

# --- Service Build Stage ---
# This stage is for building the specific service (api-gateway).
WORKDIR /app/services/api-gateway

# Copy service-specific package.json and tsconfig files from the build context
COPY services/api-gateway/package.json ./package.json
COPY services/api-gateway/tsconfig.json ./tsconfig.json
COPY services/api-gateway/tsconfig.build.json ./tsconfig.build.json

# Build the api-gateway application using its own tsconfig.build.json
# The 'yarn build' script in package.json (rimraf dist && tsc -p tsconfig.build.json)
# will handle cleaning and building.
RUN echo ">>> api-gateway: Running yarn build..."
RUN yarn build

# --- Final Stage ---
# Creates the lean production image.
FROM node:22.1.0-alpine AS final

# Set up user and group
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

WORKDIR /app

# Copy essential files from the 'base' stage (polyrepo-node-base)
COPY --from=base --chown=appuser:appgroup /app/package.json ./package.json
COPY --from=base --chown=appuser:appgroup /app/yarn.lock ./yarn.lock

# Copy all installed node_modules from the 'base' stage.
COPY --from=base --chown=appuser:appgroup /app/node_modules ./node_modules

# Copy compiled shared libraries from the 'base' stage (if api-gateway were to use them).
# COPY --from=base --chown=appuser:appgroup /app/libs ./libs

# --- API Gateway Specifics ---
WORKDIR /app/services/api-gateway

# Copy service-specific package.json
COPY --from=base --chown=appuser:appgroup /app/services/api-gateway/package.json ./package.json

# Copy built api-gateway application code from the 'base' stage (after its build)
COPY --from=base --chown=appuser:appgroup /app/services/api-gateway/dist ./dist

# Copy .env file from build context (services/api-gateway/.env) into the service's directory in the image
# For production, environment variables are typically injected, not copied via .env
# This line is often commented out or handled differently in production CI/CD pipelines.
# COPY --chown=appuser:appgroup services/api-gateway/.env ./.env

# Ensure all files in /app (especially service code) are owned by appuser
USER root
RUN chown -R appuser:appgroup /app
USER appuser

# Set NODE_ENV to production (can be overridden by docker-compose environment section)
ENV NODE_ENV production

# Expose port (defaulting to 3000 if not set in .env, as per main.ts)
EXPOSE 3000

# Define the command to run the application.
CMD ["node", "dist/main.js"]
