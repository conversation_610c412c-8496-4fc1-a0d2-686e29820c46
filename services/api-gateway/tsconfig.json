{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "module": "commonjs", "declaration": true, "removeComments": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "incremental": true, "skipLibCheck": true, "baseUrl": "./", "paths": {"@libs/*": ["../../libs/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}