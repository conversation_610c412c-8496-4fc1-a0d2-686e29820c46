import { Module } from '@nestjs/common';
import { HealthController } from './health.controller';
import { ObservabilityModule } from '../observability/observability.module';
import { MetricsModule as LibMetricsModule } from '@libs/observability';
import { CircuitBreakerModule } from '@libs/resilience';
import { AuthModule } from '../auth/auth.module';

/**
 * Module for health checks and service status
 */
@Module({
  imports: [
    ObservabilityModule,
    LibMetricsModule.forRoot(),
    CircuitBreakerModule.register(),
    AuthModule,
  ],
  controllers: [HealthController],
})
export class HealthModule {}
