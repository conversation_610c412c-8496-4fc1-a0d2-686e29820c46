import { Controller, Get, Inject } from '@nestjs/common';
import { ObservabilityLogger, MetricsService } from '@libs/observability';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { KeycloakService } from '../auth/services/keycloak.service';
import { UserServiceClient } from '../auth/services/user-service-client';
import { CircuitBreakerService } from '@libs/resilience';
import { CacheService } from '@libs/caching';
import { RedisStreamsPublisher } from '@libs/messaging/redis/redis-streams-publisher';
import { HealthCheckResponse } from '@libs/shared-types';

/**
 * Controller for health checks and service status
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly metricsService: MetricsService,
    private readonly keycloakService: KeycloakService,
    private readonly userServiceClient: UserServiceClient,
    private readonly circuitBreakerService: CircuitBreakerService,
    private readonly cacheService: CacheService,
    private readonly messagingPublisher: RedisStreamsPublisher,
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory.createLogger('HealthController');
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Basic health check endpoint
   * @returns Health status
   */
  @Get()
  @ApiOperation({ summary: 'Health check', description: 'Basic health check endpoint' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  healthCheck() {
    this.logger.log('Health check endpoint accessed');
    return {
      status: 'ok',
      service: 'auth-service',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Detailed health check including observability status
   * @returns Detailed health status
   */
  @Get('detailed')
  @ApiOperation({ summary: 'Detailed health check', description: 'Detailed health check including observability status' })
  @ApiResponse({ status: 200, description: 'Detailed health status' })
  async detailedHealthCheck(): Promise<HealthCheckResponse> {
    this.logger.log('Detailed health check endpoint accessed');

    // Get metrics to check if metrics service is working
    const metrics = await this.metricsService.getMetrics();
    const metricsStatus = metrics ? 'ok' : 'error';

    // Check if logger is working
    const loggerStatus = this.logger ? 'ok' : 'error';

    // Check Keycloak health
    const keycloakHealth = await this.keycloakService.checkHealth();

    // Check User Service health
    const userServiceHealth = await this.userServiceClient.checkHealth();

    // Get circuit breaker status
    const circuitBreakerStatus = this.circuitBreakerService.getStatus();

    // Check Redis infrastructure health
    const cacheHealth = await this.cacheService.getHealthStatus();
    const messagingHealth = await this.messagingPublisher.getHealthStatus();

    // Determine overall status
    const overallStatus =
      loggerStatus === 'ok' &&
      metricsStatus === 'ok' &&
      keycloakHealth.status === 'ok' &&
      userServiceHealth.status === 'ok' &&
      cacheHealth.status === 'ok' &&
      messagingHealth.status === 'ok' ? 'ok' : 'degraded';

    return {
      status: overallStatus,
      service: 'auth-service',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      observability: {
        logging: loggerStatus,
        metrics: metricsStatus,
      },
      dependencies: {
        keycloak: {
          status: keycloakHealth.status,
          responseTime: keycloakHealth.responseTime,
          ...(keycloakHealth.details && { details: keycloakHealth.details }),
        },
        userService: {
          status: userServiceHealth.status,
          responseTime: userServiceHealth.responseTime,
          ...(userServiceHealth.details && { details: userServiceHealth.details }),
        },
      },
      infrastructure: {
        cache: cacheHealth,
        messaging: messagingHealth,
        circuitBreakers: circuitBreakerStatus,
      },
    };
  }

  /**
   * Keycloak health check endpoint
   * @returns Keycloak health status
   */
  @Get('keycloak')
  @ApiOperation({ summary: 'Keycloak health check', description: 'Check Keycloak connectivity' })
  @ApiResponse({ status: 200, description: 'Keycloak health status' })
  async keycloakHealthCheck() {
    this.logger.log('Keycloak health check endpoint accessed');

    const keycloakHealth = await this.keycloakService.checkHealth();

    return {
      status: keycloakHealth.status,
      service: 'keycloak',
      timestamp: new Date().toISOString(),
      responseTime: keycloakHealth.responseTime,
      ...(keycloakHealth.details && { details: keycloakHealth.details }),
    };
  }

  /**
   * User Service health check endpoint
   * @returns User Service health status
   */
  @Get('user-service')
  @ApiOperation({ summary: 'User Service health check', description: 'Check User Service connectivity' })
  @ApiResponse({ status: 200, description: 'User Service health status' })
  async userServiceHealthCheck() {
    this.logger.log('User Service health check endpoint accessed');

    const userServiceHealth = await this.userServiceClient.checkHealth();

    return {
      status: userServiceHealth.status,
      service: 'user-service',
      timestamp: new Date().toISOString(),
      responseTime: userServiceHealth.responseTime,
      ...(userServiceHealth.details && { details: userServiceHealth.details }),
    };
  }

  /**
   * Circuit breaker status endpoint
   * @returns Status of all circuit breakers
   */
  @Get('circuit-breakers')
  @ApiOperation({ summary: 'Circuit breaker status', description: 'Get status of all circuit breakers' })
  @ApiResponse({ status: 200, description: 'Circuit breaker status' })
  async circuitBreakerStatus() {
    this.logger.log('Circuit breaker status endpoint accessed');

    const circuitBreakerStatus = this.circuitBreakerService.getStatus();

    return {
      status: 'ok',
      service: 'circuit-breakers',
      timestamp: new Date().toISOString(),
      circuitBreakers: circuitBreakerStatus,
    };
  }

  /**
   * Cache health check endpoint
   * @returns Redis cache health status
   */
  @Get('cache')
  @ApiOperation({ summary: 'Cache health check', description: 'Check Redis cache connectivity and performance' })
  @ApiResponse({ status: 200, description: 'Cache health status' })
  async cacheHealthCheck() {
    this.logger.log('Cache health check endpoint accessed');

    const cacheHealth = await this.cacheService.getHealthStatus();

    return {
      status: cacheHealth.status,
      service: 'cache',
      timestamp: new Date().toISOString(),
      responseTime: cacheHealth.responseTime,
      infrastructure: {
        cache: cacheHealth,
      },
    };
  }

  /**
   * Messaging health check endpoint
   * @returns Redis messaging health status
   */
  @Get('messaging')
  @ApiOperation({ summary: 'Messaging health check', description: 'Check Redis messaging connectivity and performance' })
  @ApiResponse({ status: 200, description: 'Messaging health status' })
  async messagingHealthCheck() {
    this.logger.log('Messaging health check endpoint accessed');

    const messagingHealth = await this.messagingPublisher.getHealthStatus();

    return {
      status: messagingHealth.status,
      service: 'messaging',
      timestamp: new Date().toISOString(),
      responseTime: messagingHealth.responseTime,
      infrastructure: {
        messaging: messagingHealth,
      },
    };
  }

  /**
   * Infrastructure health check endpoint
   * @returns Complete infrastructure status
   */
  @Get('infrastructure')
  @ApiOperation({ summary: 'Infrastructure health check', description: 'Check all infrastructure components' })
  @ApiResponse({ status: 200, description: 'Infrastructure health status' })
  async infrastructureHealthCheck() {
    this.logger.log('Infrastructure health check endpoint accessed');

    const cacheHealth = await this.cacheService.getHealthStatus();
    const messagingHealth = await this.messagingPublisher.getHealthStatus();
    const circuitBreakerStatus = this.circuitBreakerService.getStatus();

    const overallStatus = 
      cacheHealth.status === 'ok' && 
      messagingHealth.status === 'ok' ? 'ok' : 'degraded';

    return {
      status: overallStatus,
      service: 'infrastructure',
      timestamp: new Date().toISOString(),
      infrastructure: {
        cache: cacheHealth,
        messaging: messagingHealth,
        circuitBreakers: circuitBreakerStatus,
      },
    };
  }
}
