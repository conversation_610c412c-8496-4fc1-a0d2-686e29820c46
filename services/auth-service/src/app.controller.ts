﻿import { Controller, Get, Inject } from '@nestjs/common';
import { AppService } from './app.service';
import { ObservabilityLogger } from '@libs/observability';

@Controller()
export class AppController {
  private readonly logger: ObservabilityLogger;

  constructor(
    private readonly appService: AppService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any
  ) {
     // Get a logger instance specific to this controller
     this.logger = this.loggerFactory.createLogger(AppController.name);
  }

  @Get()
  getInfo(): string {
    this.logger.log('Root endpoint called');
    return this.appService.getHealth();
  }
}
