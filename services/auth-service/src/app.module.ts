import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config'; // Import config module
import { AuthModule } from './auth/auth.module';
import { ObservabilityModule } from './observability/observability.module'; // Import our custom observability module
import { MetricsModule } from './metrics/metrics.module'; // Import metrics module
import { HttpModule } from '@libs/http'; // Import HTTP module from libs
import { HealthModule } from './health/health.module'; // Import health module
import { ErrorHandlingModule } from '@libs/error-handling'; // Import error handling module

@Module({
  imports: [
    // Load .env files and make environment variables available via ConfigService
    ConfigModule.forRoot({
      isGlobal: true, // Make ConfigModule available globally
      envFilePath: (() => {
        const env = process.env.NODE_ENV;
        if (env === 'test') {
          return '.env.test';
        } else if (env === 'docker') {
          return '.env.docker';
        } else {
          // Default to .env.local for development or if NODE_ENV is not explicitly set to test/docker
          return '.env.local';
        }
      })(),
    }),
    ErrorHandlingModule.forRoot({
      config: {
        serviceName: 'auth-service',
        environment: process.env.NODE_ENV as 'development' | 'production' || 'development',
        lokiBaseUrl: process.env.LOKI_BASE_URL || 'http://localhost:3100',
        includeLokiLinks: true,
        includeSensitiveDetails: process.env.NODE_ENV !== 'production',
      },
      useGlobalFilter: true,
      useHttpInterceptor: false, // Services don't need HTTP interceptor (API Gateway handles it)
    }), // Add error handling module first for global exception handling
    AuthModule,
    ObservabilityModule, // Use our custom observability module
    MetricsModule, // Add metrics module
    HttpModule.forRoot(), // Enterprise defaults: 3s timeout, 1 retry, HTTP/2
    HealthModule, // Add health module
    // Add other modules like KeycloakAdminModule later
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
