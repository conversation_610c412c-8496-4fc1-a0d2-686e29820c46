import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { LoggerService, ValidationPipe } from '@nestjs/common'; 
// import { Logger } from '@nestjs/common'; // Import Logger
// HttpExceptionFilter is now handled by ErrorHandlingModule
import { ConfigService } from '@nestjs/config';
import { LOGGER_SERVICE } from '@libs/observability'; // Updated path
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

// Fix MaxListenersExceededWarning for winston logger
process.setMaxListeners(50);

async function bootstrap() {
  try {
    const app = await NestFactory.create(AppModule, {
      // logger: false, 
    });

    const appLogger = app.get<LoggerService>(LOGGER_SERVICE);
    app.useLogger(appLogger);

    // Apply global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));

    // Global exception filter is now handled by ErrorHandlingModule
    
    // Set up Swagger documentation
    const config = new DocumentBuilder()
      .setTitle('Auth Service API')
      .setDescription('Authentication and Authorization API for the Polyrepo Application')
      .setVersion('1.0')
      .addTag('auth')
      .addBearerAuth()
      .build();
    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document);
    
    // Expose JSON endpoint for API Gateway aggregation
    app.use('/api/docs-json', (req, res) => {
      res.json(document);
    });

    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT') || 3000;
    const host = '0.0.0.0';
    
    console.log(`Attempting to start server on ${host}:${port}...`);
    try {
      await app.listen(port, host);
      // If we reach here, app.listen() promise resolved successfully.
      console.log(`✅ app.listen() promise resolved. Server should be listening on ${host}:${port}`);
      appLogger.log(`AuthService is running on port ${port}, connect at http://${host}:${port} or http://localhost:${port}`, 'Bootstrap');
    } catch (listenError) {
      // This catch is specific to app.listen() failing.
      console.error(`❌ Error during app.listen(${port}, ${host}):`, listenError);
      // Ensure appLogger is available or use console.error for its message too
      if (appLogger && typeof appLogger.error === 'function') {
        const errorMessage = listenError instanceof Error ? listenError.message : String(listenError);
        const errorStack = listenError instanceof Error ? listenError.stack : undefined;
        appLogger.error(`Failed to listen on ${host}:${port}: ${errorMessage}`, errorStack, 'Bootstrap-ListenError');
      } else {
        console.error(`❌ appLogger not available or appLogger.error is not a function. Raw listenError:`, listenError);
      }
      // Re-throw to be caught by the outer try-catch for process.exit
      throw listenError;
    }
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    console.error('Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: error instanceof Error && 'code' in error ? (error as any).code : undefined
    });
    process.exit(1);
  }
}

// Add unhandled rejection handler
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Add uncaught exception handler  
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

bootstrap();
