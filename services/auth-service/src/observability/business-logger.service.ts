import { Injectable } from '@nestjs/common';
import { BusinessLogger } from '@libs/observability';

/**
 * Service wrapper for business logging in the auth service
 * Provides methods for logging auth-specific business events
 *
 * @see docs/business-events.md for the complete business events standard
 */
@Injectable()
export class AuthBusinessLogger {
  constructor(private readonly businessLogger: BusinessLogger) {}

  /**
   * Logs a user login event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user attempting to log in
   * @param metadata Additional context for the event (method, ip, etc.)
   */
  logUserLoginEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('login', status, userId, metadata);
  }

  /**
   * Logs a user logout event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user logging out
   * @param metadata Additional context for the event (sessionId, etc.)
   */
  logUserLogoutEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('logout', status, userId, metadata);
  }

  /**
   * Logs a token refresh event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user refreshing their token
   * @param metadata Additional context for the event
   */
  logUserTokenRefreshEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('token_refresh', status, userId, metadata);
  }

  /**
   * Logs a user registration event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user attempting to register
   * @param metadata Additional context for the event (email, etc.)
   */
  logUserRegistrationEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('register', status, userId, metadata);
  }

  /**
   * Logs a password reset request event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user requesting a password reset
   * @param metadata Additional context for the event (email, etc.)
   */
  logPasswordResetRequestEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('password_reset_request', status, userId, metadata);
  }

  /**
   * Logs a password reset completion event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user completing the password reset
   * @param metadata Additional context for the event
   */
  logPasswordResetEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>
  ) {
    this.businessLogger.logAuthEvent('password_reset', status, userId, metadata);
  }

  /**
   * Logs an email verification event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user verifying their email
   * @param metadata Additional context for the event
   */
  logEmailVerificationEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('email_verification', status, userId, metadata);
  }

  /**
   * Logs a token refresh event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user refreshing their token
   * @param metadata Additional context for the event
   */
  logTokenRefreshEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('token_refresh', status, userId, metadata);
  }

  /**
   * Logs an account lock event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user whose account is being locked
   * @param metadata Additional context for the event (reason, etc.)
   */
  logAccountLockEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('account_lock', status, userId, metadata);
  }

  /**
   * Logs an account unlock event.
   * @param status 'success' or 'failure'
   * @param userId ID of the user whose account is being unlocked
   * @param metadata Additional context for the event (reason, etc.)
   */
  logAccountUnlockEvent(
    status: 'success' | 'failure',
    userId: string,
    metadata?: Record<string, any>,
  ): void {
    this.businessLogger.logAuthEvent('account_unlock', status, userId, metadata);
  }
}
