import { Controller, Get, Post, Body, Query, Inject } from '@nestjs/common';
import { ObservabilityLogger, TracingService } from '@libs/observability';
import { AuthBusinessLogger } from './business-logger.service';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

/**
 * Controller for testing observability features
 * Only available in development and testing environments
 */
@ApiTags('observability')
@Controller('observability-test')
export class ObservabilityTestController {
  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly tracingService: TracingService,
    private readonly businessLogger: AuthBusinessLogger
  ) {
    // Get a logger instance specific to this controller with the proper context
    this.logger = this.loggerFactory.createLogger('ObservabilityTestController');
  }

  // Declare logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Test endpoint for logging
   */
  @Get('log')
  @ApiOperation({ summary: 'Test logging', description: 'Generates test log messages at different levels' })
  @ApiResponse({ status: 200, description: 'Logs generated successfully' })
  testLogging() {
    this.logger.log('Test log message from ObservabilityTestController');
    this.logger.debug(`Test debug message with object: ${JSON.stringify({ key: 'value', controller: 'ObservabilityTestController' })}`);
    this.logger.warn('Test warning message from ObservabilityTestController');
    this.logger.error('Test error message from ObservabilityTestController', new Error('Test error from ObservabilityTestController').stack);

    return { status: 'Logs generated successfully from ObservabilityTestController' };
  }

  /**
   * Test endpoint for tracing
   */
  @Get('trace')
  @ApiOperation({ summary: 'Test tracing', description: 'Generates test spans for tracing' })
  @ApiResponse({ status: 200, description: 'Tracing test completed successfully' })
  async testTracing() {
    return this.tracingService.traceAsyncFunction('ObservabilityTestController.testTracing-span', async (span) => {
      if (span && span.setAttribute) {
        span.setAttribute('test-attribute', 'test-value-ObservabilityTestController');
      }
      this.logger.log('Inside trace function for ObservabilityTestController');

      // Add a sub-span
      await this.tracingService.traceAsyncFunction('ObservabilityTestController.testTracing-sub-span', async (subSpan) => {
        if (subSpan && subSpan.setAttribute) {
          subSpan.setAttribute('sub-attribute', 'sub-value-ObservabilityTestController');
        }
        this.logger.log('Inside sub-span for ObservabilityTestController');

        // Simulate some work
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      return { status: 'Tracing test completed successfully for ObservabilityTestController' };
    });
  }

  /**
   * Test endpoint for business logging
   */
  @Get('business-event')
  @ApiOperation({ summary: 'Test business event logging', description: 'Generates test business events' })
  @ApiResponse({ status: 200, description: 'Business event logged successfully' })
  @ApiQuery({ name: 'status', enum: ['success', 'failure'], required: false, description: 'Event status' })
  @ApiQuery({ name: 'event', enum: ['login', 'registration', 'password-reset'], required: false, description: 'Event type' })
  testBusinessEventLogging(
    @Query('status') status: 'success' | 'failure' = 'success',
    @Query('event') eventType: string = 'login'
  ) {
    const userId = 'test-user-observability-event';

    if (eventType === 'login') {
      this.businessLogger.logUserLoginEvent(status, userId, { testField: 'test-value', controller: 'ObservabilityTestController' });
    } else if (eventType === 'registration') {
      this.businessLogger.logUserRegistrationEvent(status, userId, { testField: 'test-value', controller: 'ObservabilityTestController' });
    } else if (eventType === 'password-reset') {
      this.businessLogger.logPasswordResetRequestEvent(status, userId, { testField: 'test-value', controller: 'ObservabilityTestController' });
    } else {
      this.logger.warn(`Unknown business event type for testing in ObservabilityTestController: ${eventType}`);
      return `Unknown business event type: ${eventType}. Try 'login', 'registration', or 'password-reset'.`;
    }

    this.logger.log('Business event logged from ObservabilityTestController');
    return { status: 'Business event logged', event: eventType, userId };
  }
}
