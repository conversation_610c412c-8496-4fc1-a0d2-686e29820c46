import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ObservabilityModule as BaseObservabilityModule } from '@libs/observability';
import { AuthBusinessLogger } from './business-logger.service';
import { ObservabilityTestController } from './test.controller';

/**
 * This module integrates our auth service with the observability infrastructure.
 * It configures logging, metrics, and tracing for the auth service and provides
 * auth-specific business logging capabilities.
 */
@Module({
  imports: [
    ConfigModule, // Ensure ConfigModule is imported
    BaseObservabilityModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        logging: {
          service: 'auth-service',
          defaultContext: 'AuthService',
          enableLoki: configService.get<string>('ENABLE_LOKI') === 'true',
          lokiHost: configService.get<string>('LOKI_HOST') || 'http://loki:3100',
          logLevel: configService.get<string>('LOG_LEVEL') || 'info',
        },
        metrics: {
          prefix: 'auth_service',
          defaultLabels: {
            service: 'auth_service',
            environment: configService.get<string>('NODE_ENV') || 'development',
          },
        },
        tracing: {
          serviceName: 'auth-service',
          environment: configService.get<string>('NODE_ENV') || 'development',
          tempoEndpoint: configService.get<string>('TEMPO_ENDPOINT') || 'http://localhost:4318/v1/traces',
        },
      }),
    }),
  ],
  controllers: process.env.NODE_ENV === 'production' ? [] : [ObservabilityTestController],
  providers: [AuthBusinessLogger],
  exports: [AuthBusinessLogger, BaseObservabilityModule],
})
export class ObservabilityModule {}
