import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * DTO for the logout endpoint
 */
export class LogoutDto {
  /**
   * Refresh token to be invalidated during logout
   */
  @ApiProperty({
    description: 'Refresh token to be invalidated',
    example: 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...',
    required: false
  })
  @IsString({ message: 'Refresh token must be a string.' })
  @IsOptional()
  refresh_token?: string;
}
