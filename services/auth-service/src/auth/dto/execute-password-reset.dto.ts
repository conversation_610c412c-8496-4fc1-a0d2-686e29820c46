import { IsNotEmpty, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for executing a password reset with the provided token and new password
 */
export class ExecutePasswordResetDto {
  /**
   * Password reset token sent to the user's email
   */
  @ApiProperty({
    description: 'Password reset token received via email',
    example: '9f8d7c6b-5a4e-3b2c-1d0e-9f8d7c6b5a4e',
    required: true
  })
  @IsString({ message: 'Token must be a string.' })
  @IsNotEmpty({ message: 'Token is required.' })
  token!: string;

  /**
   * New password to set for the user
   */
  @ApiProperty({
    description: 'New password for the user account',
    example: 'SecurePass123',
    required: true,
    minLength: 8
  })
  @IsString({ message: 'Password must be a string.' })
  @IsNotEmpty({ message: 'Password is required.' })
  @MinLength(8, { message: 'Password must be at least 8 characters long.' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: 'Password must include at least one lowercase letter, one uppercase letter, and one number.',
  })
  password!: string;
}
