import { IsBoolean, IsEmail, IsN<PERSON>Empty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for updating user status (activate/deactivate)
 */
export class UpdateUserStatusDto {
  /**
   * Email address or Keycloak ID of the user to update
   */
  @ApiProperty({
    description: 'Email address or Keycloak ID of the user to update',
    example: '<EMAIL>',
    required: true
  })
  @IsString({ message: 'User identifier must be a string.' })
  @IsNotEmpty({ message: 'User identifier is required.' })
  userIdentifier!: string;

  /**
   * Whether the user is using an email (true) or Keycloak ID (false)
   */
  @ApiProperty({
    description: 'Whether the user identifier is an email (true) or Keycloak ID (false)',
    example: true,
    required: false,
    default: true
  })
  @IsBoolean({ message: 'isEmail must be a boolean value.' })
  @IsOptional()
  isEmail?: boolean = true;

  /**
   * Whether to enable or disable the user account
   */
  @ApiProperty({
    description: 'Whether to enable (true) or disable (false) the user account',
    example: true,
    required: true
  })
  @IsBoolean({ message: 'enabled must be a boolean value.' })
  @IsNotEmpty({ message: 'Account status is required.' })
  enabled!: boolean;

  /**
   * Optional reason for the status change (for audit logs)
   */
  @ApiProperty({
    description: 'Optional reason for the status change (for audit logs)',
    example: 'User requested account deactivation',
    required: false
  })
  @IsString({ message: 'Reason must be a string.' })
  @IsOptional()
  reason?: string;
}
