import { IsE<PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * D<PERSON> for requesting a password reset
 */
export class RequestPasswordResetDto {
  /**
   * Email address of the user requesting a password reset
   */
  @ApiProperty({
    description: 'Email address of the user requesting password reset',
    example: '<EMAIL>',
    required: true
  })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  @IsNotEmpty({ message: 'Email should not be empty.' })
  email!: string;
}
