import { IsEmail, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for requesting an email verification
 */
export class RequestEmailVerificationDto {
  /**
   * Email address of the user requesting verification
   */
  @ApiProperty({
    description: 'Email address of the user requesting email verification',
    example: '<EMAIL>',
    required: true
  })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  @IsNotEmpty({ message: 'Email should not be empty.' })
  email!: string;
}
