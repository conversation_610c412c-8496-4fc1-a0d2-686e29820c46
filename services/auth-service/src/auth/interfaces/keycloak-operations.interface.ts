import { JwtPayload } from './jwt-payload.interface';
import { KeycloakTokenResponse } from './keycloak-token-response.interface';

/**
 * Interface for Keycloak operations
 */
export interface KeycloakOperations {
  /**
   * Request a password reset for a user
   * @param email User email
   * @returns The user ID if found, or null if not found
   */
  requestPasswordReset(email: string): Promise<string | null>;

  /**
   * Execute a password reset
   * @param token Reset token
   * @param newPassword New password
   * @returns The user ID if the reset was successful, or null if not
   */
  executePasswordReset(token: string, newPassword: string): Promise<string | null>;

  /**
   * Send email verification
   * @param email User email
   * @returns The user ID if found, or null if not found
   */
  sendEmailVerification(email: string): Promise<string | null>;

  /**
   * Update user account status
   * @param userIdentifier User email or ID
   * @param enabled Whether to enable or disable the account
   * @param isEmail Whether userIdentifier is an email
   * @param reason Optional reason for status change
   * @param admin Optional admin who made the change
   * @returns The user ID if the update was successful, or null if not
   */
  updateUserStatus(
    userIdentifier: string,
    enabled: boolean,
    isEmail?: boolean,
    reason?: string,
    admin?: JwtPayload
  ): Promise<string | null>;

  /**
   * Refresh an access token using a refresh token
   * @param refreshToken Refresh token
   * @returns New token response
   */
  refreshToken(refreshToken: string): Promise<KeycloakTokenResponse>;

  /**
   * Revoke a refresh token
   * @param refreshToken Refresh token to revoke
   */
  revokeRefreshToken(refreshToken: string): Promise<void>;

  /**
   * Invalidate all user sessions
   * @param userId User ID
   */
  invalidateUserSessions(userId: string): Promise<void>;

  /**
   * Check if a user exists in Keycloak
   * @param email User email to check
   * @returns Whether the user exists
   */
  checkUserExists(email: string): Promise<boolean>;

  /**
   * Authenticate a user with Keycloak and get access tokens
   * @param email User email
   * @param password User password
   * @returns Keycloak token response
   */
  authenticateUser(email: string, password: string): Promise<KeycloakTokenResponse>;

  /**
   * Create a new user in Keycloak
   * @param user User details
   * @returns ID of the created user
   */
  createUser(user: { email: string; password: string; firstName: string; lastName: string; }): Promise<string>;

  /**
   * Decode a reset token to extract the user ID
   * @param token Reset token
   * @returns User ID or null if invalid
   */
  decodeResetToken(token: string): string | null;

  /**
   * Delete a user from Keycloak
   * @param userId User ID to delete
   * @param adminToken Admin access token
   * @returns True if successful, false otherwise
   */
  deleteUser(userId: string, adminToken: string): Promise<boolean>;
}
