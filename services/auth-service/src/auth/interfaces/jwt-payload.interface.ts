export interface JwtPayload {
  exp?: number;
  iat?: number;
  auth_time?: number;
  jti?: string;
  iss?: string;
  aud?: string | string[];
  sub: string; // Subject (usually the user ID)
  typ?: string;
  azp?: string;
  nonce?: string;
  session_state?: string;
  acr?: string;
  'allowed-origins'?: string[];
  realm_access?: {
    roles: string[];
  };
  resource_access?: {
    [key: string]: { roles: string[] } | undefined; // Value can be undefined
    account?: { roles: string[] }; // This should now be compatible
  };
  scope?: string;
  sid?: string;
  email_verified?: boolean;
  name?: string;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  email: string;
}
