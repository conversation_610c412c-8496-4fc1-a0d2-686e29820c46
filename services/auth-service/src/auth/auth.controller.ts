import { Controller, Post, Body, ValidationPipe, UsePipes, HttpCode, HttpStatus, Logger, Get, Req, UseGuards, UnauthorizedException, InternalServerErrorException, BadRequestException, NotFoundException, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { KeycloakTokenResponse } from './interfaces/keycloak-token-response.interface';
import { RegisterUserDto, LoginUserDto } from '@libs/shared-types';
import { ObservabilityLogger } from '@libs/observability';
import { AuthBusinessLogger } from '../observability/business-logger.service';
import { UserContextGuard, User as CurrentUser, UserContext, Roles, RolesGuard } from '@libs/auth-common';
import type { Request } from 'express';
import { RequestPasswordResetDto } from './dto/request-password-reset.dto';
import { ExecutePasswordResetDto } from './dto/execute-password-reset.dto';
import { RequestEmailVerificationDto } from './dto/request-email-verification.dto';
import { UpdateUserStatusDto } from './dto/user-status.dto';
import { LogoutDto } from './dto/logout.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
// Test import of new error-handling library
import { ERROR_CODES, ErrorResponse, lokiQueryBuilder, ApiErrorResponse } from '@libs/error-handling';



@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private logger: ObservabilityLogger;

  constructor(
    private readonly authService: AuthService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
    private readonly businessLogger: AuthBusinessLogger,
  ) {
    // Get a logger instance specific to this controller
    this.logger = this.loggerFactory?.createLogger(AuthController.name) || {
      log: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {},
      verbose: () => {},
    };
  }

  /**
   * User registration endpoint
   */
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({ summary: 'Register new user', description: 'Creates a new user account in the system' })
  @ApiBody({ type: RegisterUserDto })
  @ApiResponse({ status: 201, description: 'User successfully registered', schema: { properties: { id: { type: 'string' }, email: { type: 'string' } } } })
  @ApiErrorResponse('VALIDATION_FAILED', 'Request validation failed')
  @ApiErrorResponse('EMAIL_ALREADY_EXISTS', 'User with this email already exists')
  @ApiErrorResponse('KEYCLOAK_CONNECTION_ERROR', 'Registration service unavailable')
  async register(
    @Body() registerUserDto: RegisterUserDto,
  ): Promise<{ id: string; email: string }> {
    this.logger.log(`Registration attempt for email: ${registerUserDto.email}`);

    try {
      const result = await this.authService.registerUser(registerUserDto);

      this.logger.log(`Registration successful for email: ${registerUserDto.email}`);

      // Log business event directly from controller
      this.businessLogger.logUserRegistrationEvent(
        'success',
        result.id,
        { email: result.email }
      );

      return result;
    } catch (error) {
      // Log business event for failure
      this.businessLogger.logUserRegistrationEvent(
        'failure',
        'unknown',
        { email: registerUserDto.email, reason: error instanceof Error ? error.message : String(error) }
      );

      // Rethrow the error to be handled by NestJS
      throw error;
    }
  }

  /**
   * User login endpoint
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({ summary: 'User login', description: 'Authenticates a user and returns access and refresh tokens' })
  @ApiBody({ type: LoginUserDto })
  @ApiResponse({ status: 200, description: 'Login successful', schema: { properties: { access_token: { type: 'string' }, refresh_token: { type: 'string' }, expires_in: { type: 'number' }, refresh_expires_in: { type: 'number' }, token_type: { type: 'string' } } } })
  @ApiErrorResponse('VALIDATION_FAILED', 'Request validation failed')
  @ApiErrorResponse('INVALID_CREDENTIALS', 'Invalid email or password')
  @ApiErrorResponse('KEYCLOAK_CONNECTION_ERROR', 'Authentication service unavailable')
  async login(@Body() loginUserDto: LoginUserDto, @Req() req: Request): Promise<KeycloakTokenResponse> {
    this.logger.log(`Login attempt for email: ${loginUserDto.email}`);

    try {
      const result = await this.authService.loginUser(loginUserDto);

      this.logger.log(`Login successful for email: ${loginUserDto.email}`);

      // Extract userId from token for business logging
      const tokenPayload = this.authService.decodeToken(result.access_token);
      const userId = tokenPayload?.sub || 'unknown';

      // Log business event directly from controller
      this.businessLogger.logUserLoginEvent(
        'success',
        userId,
        { email: loginUserDto.email }
      );

      return result;
    } catch (error: unknown) {
      // If it's already an HttpException, just rethrow it
      if (error && typeof error === 'object' && 'status' in error && 'response' in error) {
        const httpError = error as { status: number; response: { message?: string } };

        // Log business event for failure
        this.businessLogger.logUserLoginEvent(
          'failure',
          'unknown',
          {
            email: loginUserDto.email,
            reason: httpError.response?.message || 'Authentication failed',
            status: httpError.status
          }
        );

        // If it's a 401 error, make sure it's properly formatted
        if (httpError.status === 401) {
          throw new UnauthorizedException(
            httpError.response?.message || 'Invalid credentials',
          );
        }
        throw error;
      }

      // For other errors, log them and throw a generic error
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Login failed for email: ${loginUserDto.email}: ${errorMessage}`, errorStack);

      // Log business event for failure
      this.businessLogger.logUserLoginEvent(
        'failure',
        'unknown',
        { email: loginUserDto.email, reason: errorMessage }
      );

      throw new InternalServerErrorException('An error occurred during login');
    }
  }

  /**
   * Token refresh endpoint
   */
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({ summary: 'Refresh access token', description: 'Refreshes an access token using a refresh token' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResponse({ status: 200, description: 'Token refresh successful', schema: { properties: { access_token: { type: 'string' }, refresh_token: { type: 'string' }, expires_in: { type: 'number' }, refresh_expires_in: { type: 'number' }, token_type: { type: 'string' } } } })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid or expired refresh token' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async refresh(@Body() refreshTokenDto: RefreshTokenDto): Promise<KeycloakTokenResponse> {
    this.logger.log('Token refresh attempt');

    try {
      const result = await this.authService.refreshToken(refreshTokenDto.refresh_token);

      this.logger.log('Token refresh successful');

      return result;
    } catch (error: unknown) {
      // If it's already an HttpException, just rethrow it
      if (error && typeof error === 'object' && 'status' in error && 'response' in error) {
        const httpError = error as { status: number; response: { message?: string } };

        // If it's a 401 error, make sure it's properly formatted
        if (httpError.status === 401) {
          throw new UnauthorizedException(
            httpError.response?.message || 'Invalid or expired refresh token',
          );
        }
        throw error;
      }

      // For other errors, log them and throw a generic error
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Token refresh failed: ${errorMessage}`, errorStack);

      throw new InternalServerErrorException('An error occurred during token refresh');
    }
  }

  /**
   * Admin access check endpoint
   * Requires admin role
   */
  @Get('admin-check')
  @UseGuards(UserContextGuard, RolesGuard)
  @Roles('admin')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Admin access check', description: 'Verifies if the authenticated user has admin privileges' })
  @ApiResponse({ status: 200, description: 'User has admin access', schema: { properties: { message: { type: 'string' }, user: { type: 'object', properties: { id: { type: 'string' }, email: { type: 'string' }, roles: { type: 'array', items: { type: 'string' } } } } } } })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid or missing token' })
  @ApiResponse({ status: 403, description: 'Forbidden - user does not have admin role' })
  adminCheck(@CurrentUser() userContext: UserContext) {
    this.logger.log(
      `Admin check endpoint accessed by user: ${userContext.userId} (email: ${userContext.email})`,
      AuthController.name + '.adminCheck',
    );
    return {
      message: 'Welcome, Admin!',
      user: {
        id: userContext.userId,
        email: userContext.email,
        roles: userContext.roles,
      },
    };
  }

  /**
   * Logout endpoint to invalidate user's session and tokens in Keycloak
   */
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  @UseGuards(UserContextGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'User logout', description: 'Invalidates the user session and revokes tokens' })
  @ApiBody({ type: LogoutDto })
  @ApiResponse({ status: 200, description: 'Logout successful', schema: { properties: { message: { type: 'string', example: 'Logout successful' } } } })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid or missing token' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async logout(@CurrentUser() userContext: UserContext, @Body() logoutDto: LogoutDto) {
    this.logger.log(`Logout attempt for user: ${userContext.userId} (email: ${userContext.email})`);

    try {
      await this.authService.logoutUser(userContext.userId, logoutDto.refresh_token);

      // Log business event for successful logout
      this.businessLogger.logUserLogoutEvent(
        'success',
        userContext.userId,
        { email: userContext.email }
      );

      return {
        message: 'Logout successful',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Logout failed for user: ${userContext.userId} (email: ${userContext.email}): ${errorMessage}`, errorStack);

      // Log business event for logout (still marking as success since we return success to client)
      this.businessLogger.logUserLogoutEvent(
        'success',
        userContext.userId,
        {
          email: userContext.email,
          hadErrors: true,
          errorType: error instanceof Error ? error.constructor.name : 'Unknown'
        }
      );

      // Even if there's an issue with Keycloak, we still want to indicate success to the client
      // as they are effectively logged out from the application's perspective
      return {
        message: 'Logout successful',
      };
    }
  }

  /**
   * Request a password reset email to be sent to the user's email address
   */
  @Post('request-password-reset')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({ summary: 'Request password reset', description: 'Sends a password reset link to the user email' })
  @ApiBody({ type: RequestPasswordResetDto })
  @ApiResponse({ status: 200, description: 'Password reset request successful', schema: { properties: { message: { type: 'string', example: 'If your email is registered, you will receive password reset instructions.' } } } })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async requestPasswordReset(@Body() requestPasswordResetDto: RequestPasswordResetDto) {
    this.logger.log(`Password reset requested for email: ${requestPasswordResetDto.email}`);

    try {
      const userId = await this.authService.requestPasswordReset(requestPasswordResetDto.email);

      // Log business event for successful password reset request
      if (userId) {
        this.businessLogger.logPasswordResetRequestEvent(
          'success',
          userId,
          { email: requestPasswordResetDto.email }
        );
      }

      // Always return success to prevent email enumeration attacks
      return {
        message: 'If your email is registered, you will receive password reset instructions.',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Password reset request failed for email: ${requestPasswordResetDto.email}: ${errorMessage}`, errorStack);

      // Log business event for failed password reset request
      // We don't have a userId here, but we can log the email
      this.businessLogger.logPasswordResetRequestEvent(
        'failure',
        'unknown',
        {
          email: requestPasswordResetDto.email,
          reason: errorMessage
        }
      );

      // Always return success to prevent email enumeration attacks
      return {
        message: 'If your email is registered, you will receive password reset instructions.',
      };
    }
  }

  /**
   * Execute a password reset using the token received in the email
   */
  @Post('execute-password-reset')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({ summary: 'Reset password', description: 'Resets user password using the reset token' })
  @ApiBody({ type: ExecutePasswordResetDto })
  @ApiResponse({ status: 200, description: 'Password reset successful', schema: { properties: { message: { type: 'string', example: 'Password has been successfully reset. You can now log in with your new password.' } } } })
  @ApiResponse({ status: 400, description: 'Bad request - validation error or invalid token' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async executePasswordReset(@Body() executePasswordResetDto: ExecutePasswordResetDto) {
    this.logger.log(`Executing password reset with token`);

    try {
      const userId = await this.authService.executePasswordReset(
        executePasswordResetDto.token,
        executePasswordResetDto.password
      );

      // Log business event for successful password reset
      if (userId) {
        this.businessLogger.logPasswordResetEvent(
          'success',
          userId,
          { tokenUsed: true }
        );
      }

      return {
        message: 'Password has been successfully reset. You can now log in with your new password.',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Password reset execution failed: ${errorMessage}`, errorStack);

      // Log business event for failed password reset
      this.businessLogger.logPasswordResetEvent(
        'failure',
        'unknown',
        {
          reason: errorMessage,
          tokenProvided: true
        }
      );

      // Use a more specific error message based on the error type
      if (error instanceof BadRequestException) {
        throw error; // Forward validation errors
      }

      throw new BadRequestException(
        'Unable to reset password. The reset token may be invalid or expired.'
      );
    }
  }

  /**
   * Send or resend email verification link to the user's email
   */
  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiOperation({ summary: 'Request email verification', description: 'Sends an email verification link to the user' })
  @ApiBody({ type: RequestEmailVerificationDto })
  @ApiResponse({ status: 200, description: 'Email verification request successful', schema: { properties: { message: { type: 'string', example: 'If your email is registered, you will receive a verification link.' } } } })
  @ApiResponse({ status: 400, description: 'Bad request - validation error' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async requestEmailVerification(@Body() requestEmailVerificationDto: RequestEmailVerificationDto) {
    this.logger.log(`Email verification requested for: ${requestEmailVerificationDto.email}`);

    try {
      const userId = await this.authService.sendEmailVerification(requestEmailVerificationDto.email);

      // Log business event for successful email verification request
      if (userId) {
        this.businessLogger.logEmailVerificationEvent(
          'success',
          userId,
          { email: requestEmailVerificationDto.email }
        );
      }

      // Always return success to prevent email enumeration attacks
      return {
        message: 'If your email is registered, you will receive a verification link.',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Email verification request failed for: ${requestEmailVerificationDto.email}: ${errorMessage}`, errorStack);

      // Log business event for failed email verification request
      this.businessLogger.logEmailVerificationEvent(
        'failure',
        'unknown',
        {
          email: requestEmailVerificationDto.email,
          reason: errorMessage
        }
      );

      // Always return success to prevent email enumeration attacks
      return {
        message: 'If your email is registered, you will receive a verification link.',
      };
    }
  }

  /**
   * Analytics access check endpoint
   * Requires analytics role - separate from admin hierarchy
   */
  @Get('analytics-check')
  @UseGuards(UserContextGuard, RolesGuard)
  @Roles('analytics')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Analytics access check', description: 'Verifies if the authenticated user has analytics access (separate from admin roles)' })
  @ApiResponse({ status: 200, description: 'User has analytics access', schema: { properties: { message: { type: 'string' }, user: { type: 'object', properties: { id: { type: 'string' }, email: { type: 'string' }, roles: { type: 'array', items: { type: 'string' } } } } } } })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid or missing token' })
  @ApiResponse({ status: 403, description: 'Forbidden - user does not have analytics role' })
  analyticsCheck(@CurrentUser() userContext: UserContext) {
    this.logger.log(
      `Analytics check endpoint accessed by user: ${userContext.userId} (email: ${userContext.email})`,
      AuthController.name + '.analyticsCheck',
    );
    return {
      message: 'Welcome to Analytics!',
      user: {
        id: userContext.userId,
        email: userContext.email,
        roles: userContext.roles,
      },
    };
  }

  /**
   * Activate or deactivate a user account
   * Requires admin privileges
   */
  @Post('user-status')
  @HttpCode(HttpStatus.OK)
  @UseGuards(UserContextGuard, RolesGuard)
  @Roles('admin')
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user status', description: 'Activates or deactivates a user account (admin only)' })
  @ApiBody({ type: UpdateUserStatusDto })
  @ApiResponse({ status: 200, description: 'User status updated successfully', schema: { properties: { message: { type: 'string' }, userId: { type: 'string' }, status: { type: 'string' } } } })
  @ApiResponse({ status: 401, description: 'Unauthorized - invalid or missing token' })
  @ApiResponse({ status: 403, description: 'Forbidden - user does not have admin role' })
  @ApiResponse({ status: 404, description: 'Not Found - user not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async updateUserStatus(
    @Body() updateUserStatusDto: UpdateUserStatusDto,
    @CurrentUser() adminContext: UserContext
  ) {
    this.logger.log(`User status change requested by admin ${adminContext.userId} (${adminContext.email}): User ${updateUserStatusDto.userIdentifier} to ${updateUserStatusDto.enabled ? 'ACTIVE' : 'DISABLED'}`);

    try {
      const userId = await this.authService.updateUserStatus(
        updateUserStatusDto.userIdentifier,
        updateUserStatusDto.enabled,
        updateUserStatusDto.isEmail || true,
        updateUserStatusDto.reason,
        adminContext
      );

      // Log business event for account lock/unlock
      if (userId) {
        if (updateUserStatusDto.enabled) {
          this.businessLogger.logAccountUnlockEvent(
            'success',
            userId,
            {
              adminId: adminContext.userId,
              adminEmail: adminContext.email,
              reason: updateUserStatusDto.reason || 'Admin action'
            }
          );
        } else {
          this.businessLogger.logAccountLockEvent(
            'success',
            userId,
            {
              adminId: adminContext.userId,
              adminEmail: adminContext.email,
              reason: updateUserStatusDto.reason || 'Admin action'
            }
          );
        }
      }

      return {
        message: `User has been ${updateUserStatusDto.enabled ? 'activated' : 'deactivated'} successfully.`,
        userId: updateUserStatusDto.userIdentifier,
        status: updateUserStatusDto.enabled ? 'active' : 'disabled',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Failed to ${updateUserStatusDto.enabled ? 'activate' : 'deactivate'} user ${updateUserStatusDto.userIdentifier}: ${errorMessage}`, errorStack);

      // Log business event for failed account lock/unlock
      const eventMethod = updateUserStatusDto.enabled ?
        this.businessLogger.logAccountUnlockEvent.bind(this.businessLogger) :
        this.businessLogger.logAccountLockEvent.bind(this.businessLogger);

      eventMethod(
        'failure',
        updateUserStatusDto.userIdentifier, // Use identifier as userId since we don't have the real one
        {
          adminId: adminContext.userId,
          adminEmail: adminContext.email,
          reason: updateUserStatusDto.reason || 'Admin action',
          error: errorMessage
        }
      );

      if (error instanceof NotFoundException) {
        throw error; // Forward not found errors
      }

      if (error instanceof UnauthorizedException) {
        throw error; // Forward unauthorized errors
      }

      throw new InternalServerErrorException('Failed to update user status');
    }
  }

  /**
   * Get current authentication information
   * Returns token details, session info, and authentication state
   */
  @Get('me')
  @UseGuards(UserContextGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get current authentication information',
    description: 'Returns JWT token details, session information, and authentication state'
  })
  @ApiResponse({
    status: 200,
    description: 'Authentication information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        user: {
          type: 'object',
          properties: {
            userId: { type: 'string' },
            email: { type: 'string' },
            username: { type: 'string' },
            name: { type: 'string' },
            roles: { type: 'array', items: { type: 'string' } },
            isEmailVerified: { type: 'boolean' }
          }
        },
        token: {
          type: 'object',
          properties: {
            issuedAt: { type: 'number' },
            expiresAt: { type: 'number' },
            issuer: { type: 'string' },
            audience: { type: 'array', items: { type: 'string' } }
          }
        },
        session: {
          type: 'object',
          properties: {
            sessionId: { type: 'string' },
            sessionState: { type: 'string' },
            isActive: { type: 'boolean' }
          }
        }
      }
    }
  })
  @ApiErrorResponse('UNAUTHORIZED', 'Invalid or expired authentication token')
  @ApiErrorResponse('INTERNAL_SERVER_ERROR', 'Internal server error')
  async getCurrentAuthInfo(@CurrentUser() userContext: UserContext, @Req() request: Request): Promise<any> {
    this.logger.log(`Getting auth info for user: ${userContext.userId}`);

    try {
      // Extract the token from the Authorization header
      const authHeader = request.headers.authorization;
      const token = authHeader?.replace('Bearer ', '');
      
      let tokenInfo = {};
      let sessionInfo = {};
      
      if (token) {
        try {
          // Decode the JWT token to get token-specific information
          const payload = this.authService.decodeToken(token);
          
          tokenInfo = {
            issuedAt: payload.iat,
            expiresAt: payload.exp,
            issuer: payload.iss,
            audience: payload.aud,
            tokenId: payload.jti,
            azp: payload.azp,
            scope: payload.scope
          };
          
          sessionInfo = {
            sessionId: payload.sid,
            sessionState: payload.session_state,
            isActive: true,
            authTime: payload.auth_time
          };
        } catch (decodeError) {
          this.logger.warn(`Failed to decode token for additional info: ${decodeError}`);
          // Continue with basic info if token decode fails
        }
      }

      // Get authentication information
      const authInfo = {
        user: {
          userId: userContext.userId,
          email: userContext.email,
          username: userContext.username,
          name: userContext.name,
          roles: userContext.roles,
          resourceRoles: userContext.resourceRoles,
          isEmailVerified: userContext.isEmailVerified
        },
        token: tokenInfo,
        session: sessionInfo
      };

      // Log business event using the general auth event logger
      this.businessLogger.businessLogger.logAuthEvent(
        'auth_info_access',
        'success',
        userContext.userId,
        {
          email: userContext.email,
          sessionId: sessionInfo.sessionId || 'unknown'
        }
      );

      return authInfo;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Failed to get auth info for user: ${userContext.userId}: ${errorMessage}`, errorStack);

      // Log business event for failure
      this.businessLogger.businessLogger.logAuthEvent(
        'auth_info_access',
        'failure',
        userContext.userId,
        {
          error: errorMessage
        }
      );

      throw new InternalServerErrorException('Failed to retrieve authentication information');
    }
  }
}
