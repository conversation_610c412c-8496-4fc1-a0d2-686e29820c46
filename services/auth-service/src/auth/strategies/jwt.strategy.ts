import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { passportJwtSecret } from 'jwks-rsa';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') { // Named strategy 'jwt'
  private readonly logger: Logger; // Declare logger, initialize after super()

  constructor(private readonly configService: ConfigService) {
    const keycloakBaseUrl = configService.get<string>('KEYCLOAK_BASE_URL');
    const keycloakRealm = configService.get<string>('KEYCLOAK_REALM_NAME');
    const keycloakAudience = configService.get<string>('KEYCLOAK_CLIENT_ID'); // Audience for this service

    if (!keycloakBaseUrl || !keycloakRealm) {
      // Cannot use this.logger before super(), so log statically or throw immediately
      Logger.error(
        'Keycloak URL or Realm not configured for JwtStrategy. Check KEYCLOAK_BASE_URL and KEYCLOAK_REALM_NAME env variables.', 
        JwtStrategy.name
      );
      throw new Error('Keycloak URL or Realm not configured for JwtStrategy.');
    }

    super({
      secretOrKeyProvider: passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: `${keycloakBaseUrl}/realms/${keycloakRealm}/protocol/openid-connect/certs`,
        handleSigningKeyError: (err, cb) => { // More detailed error logging for JWKS
          // Cannot use this.logger here if super() hasn't finished its async part or if it's not bound yet.
          // Static logging is safer here or ensure logger is initialized before this callback could be hit.
          // For simplicity, let's assume this.logger is available if super() is synchronous for setup.
          // However, the TS error implies 'this' context issues with 'super'.
          // Let's use a static logger temporarily for the handleSigningKeyError or ensure 'this' is safe.
          // The 'this' in this callback might not be the JwtStrategy instance 'this'.
          const loggerInstance = new Logger(JwtStrategy.name + ':JWKS');
          if (err) {
            loggerInstance.error(`Error fetching JWKS: ${err.message}`, err.stack);
          } else {
            loggerInstance.error('Unknown error fetching JWKS (err object is null)');
          }
          cb(err || new Error('Unknown error fetching JWKS')); // Pass an error to callback if err is null
        },
      }),
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      audience: ['account', 'realm-management'], // Accept standard Keycloak audiences
      issuer: `${keycloakBaseUrl}/realms/${keycloakRealm}`, // Validate the issuer claim
      algorithms: ['RS256'], // Keycloak typically uses RS256 for signed JWTs
      ignoreExpiration: false, // Ensure tokens are not expired
    });

    // Initialize logger after super() call
    this.logger = new Logger(JwtStrategy.name);
    this.logger.log(`Initializing JwtStrategy for issuer: ${keycloakBaseUrl}/realms/${keycloakRealm} and standard Keycloak audiences: account, realm-management`);
  }

  async validate(payload: any): Promise<any> {
    this.logger.verbose(`Validating JWT payload for user: ${payload.sub}`);
    // passport-jwt has already validated the signature, expiration, audience, and issuer based on super() config.
    // The payload is the decoded JWT.
    if (!payload) {
      this.logger.warn('Invalid token payload received after initial validation.');
      throw new UnauthorizedException('Invalid token payload.');
    }

    // You can add more validation here if needed, e.g., check if user is active in your DB (if you sync users).
    // For now, if Keycloak issued it and it's valid, we trust it.

    // The returned value will be attached to the request object as `request.user`
    return payload;
  }
}
