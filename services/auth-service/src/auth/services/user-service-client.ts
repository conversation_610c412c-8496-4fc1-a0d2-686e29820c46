import { Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateUserInternalDto } from '@libs/shared-types';
import { BaseServiceClient, HttpClientService } from '@libs/http';
import { ObservabilityLogger } from '@libs/observability';

/**
 * User Service HTTP client using BaseServiceClient pattern
 * Eliminates boilerplate while maintaining circuit breaker protection and observability
 * Reduced from ~161 lines to ~60 lines
 */
@Injectable()
export class UserServiceClient extends BaseServiceClient {
  private readonly logger: ObservabilityLogger;

  constructor(
    configService: ConfigService,
    httpClient: HttpClientService,
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    const baseUrl = configService.get<string>('USER_SERVICE_URL') || '';
    
    if (!baseUrl) {
      throw new Error('Required USER_SERVICE_URL configuration variable is missing.');
    }

    // Initialize base class with service configuration
    super(httpClient, 'user-service', baseUrl);

    // Create logger instance
    this.logger = this.loggerFactory.createLogger(UserServiceClient.name);
    this.logger.log(`User Service client configured for URL: ${baseUrl}`);
  }

  /**
   * Creates a user record in the User Service
   * Circuit breaker protection is automatically handled by BaseServiceClient
   */
  async createUser(userDto: CreateUserInternalDto): Promise<any> {
    this.logger.log(`Creating user record in User Service for Keycloak ID: ${userDto.keycloakId}`);

    try {
      const response = await this.post('/users/internal', userDto, {
        operationName: 'create-user',
        headers: { 'Content-Type': 'application/json' }
      });

      this.logger.log(`Successfully created user record in User Service for Keycloak ID: ${userDto.keycloakId}`);
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      this.logger.error(`Failed to create user in User Service: ${errorMessage}`);
      throw error; // Re-throw for caller to handle
    }
  }

  /**
   * Gets a user by their Keycloak ID
   */
  async getUserByKeycloakId(keycloakId: string): Promise<any> {
    this.logger.debug(`Fetching user from User Service by Keycloak ID: ${keycloakId}`);

    try {
      const response = await this.get(`/users/keycloak/${keycloakId}`, {
        operationName: 'get-user-by-keycloak-id',
        headers: { 'Content-Type': 'application/json' }
      });

      return response.data;
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      this.logger.error(`Failed to fetch user from User Service: ${errorMessage}`);
      throw error; // Re-throw for caller to handle
    }
  }

  /**
   * Check User Service health using the base class helper
   * Automatic circuit breaker protection and standardized health check format
   */
  async checkHealth(): Promise<{ status: string; responseTime: number; details?: any }> {
    try {
      const healthResult = await super.checkHealth('/health');
      this.logger.log(`User Service health check successful (${healthResult.responseTime}ms)`);
      return healthResult;
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      this.logger.error(`User Service health check failed: ${errorMessage}`);
      
      return {
        status: 'error',
        responseTime: 0,
        details: {
          message: errorMessage,
          code: error?.code || 'UNKNOWN',
        },
      };
    }
  }

  /**
   * Update user profile
   */
  async updateUser(userId: string, updateData: any): Promise<any> {
    this.logger.log(`Updating user profile for user ID: ${userId}`);

    try {
      const response = await this.put(`/users/${userId}`, updateData, {
        operationName: 'update-user'
      });

      this.logger.log(`Successfully updated user profile for user ID: ${userId}`);
      return response.data;
    } catch (error: any) {
      this.logger.error(`Failed to update user profile for ${userId}: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Delete user
   */
  async deleteUser(userId: string): Promise<void> {
    this.logger.log(`Deleting user with ID: ${userId}`);

    try {
      await this.delete(`/users/${userId}`, {
        operationName: 'delete-user'
      });

      this.logger.log(`Successfully deleted user with ID: ${userId}`);
    } catch (error: any) {
      this.logger.error(`Failed to delete user ${userId}: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  }
}