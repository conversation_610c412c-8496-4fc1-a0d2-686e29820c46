import { Injectable, UnauthorizedException, Inject } from '@nestjs/common';
import { LOGGER_SERVICE, ObservabilityLogger } from '@libs/observability';
import { UseCache } from '@libs/caching';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import * as jwt from 'jsonwebtoken';

/**
 * Service for handling JWT token operations
 */
@Injectable()
export class TokenService {
  constructor(
    @Inject('LOGGER_FACTORY') private readonly loggerFactory: any,
  ) {
    // Create a logger instance specific to this service
    this.logger = this.loggerFactory.createLogger(TokenService.name);
  }

  // Declare the logger as a class property
  private readonly logger: ObservabilityLogger;

  /**
   * Decodes a JWT without validation (for internal use)
   * @param token JWT token
   * @returns Decoded JWT payload or null if invalid
   */
  decodeJwt(token: string): JwtPayload {
    try {
      const decoded = jwt.decode(token);
      
      if (!decoded || typeof decoded !== 'object') {
        this.logger.warn('Invalid token format, could not decode JWT');
        throw new UnauthorizedException('Invalid token format');
      }
      
      return decoded as JwtPayload;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error decoding JWT: ${errorMessage}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Validates a JWT and extracts the payload with caching
   * @param token JWT token
   * @returns Decoded JWT payload
   */
  @UseCache({
    key: (token: string) => `jwt:validation:${token.slice(-10)}`, // Use last 10 chars to avoid full token in cache key
    ttl: 60, // 1 minute TTL for security
    namespace: 'auth',
    condition: (payload) => payload !== null && !payload.exp || payload.exp > Math.floor(Date.now() / 1000)
  })
  validateJwt(token: string): JwtPayload {
    try {
      // Decode the token (in a production app, we would verify the signature)
      const payload = this.decodeJwt(token);

      // Check if the token has expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < currentTime) {
        this.logger.warn(`Token has expired. Expiry: ${new Date(payload.exp * 1000).toISOString()}, Current: ${new Date().toISOString()}`);
        throw new UnauthorizedException('Token has expired');
      }

      this.logger.debug(`JWT validated successfully for subject: ${payload.sub}`);
      return payload;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`JWT validation failed: ${errorMessage}`);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Extracts user roles from a JWT payload
   * @param payload JWT payload
   * @returns Array of role names
   */
  extractRoles(payload: JwtPayload): string[] {
    try {
      const roles = payload.realm_access?.roles || [];
      this.logger.debug(`Extracted roles from token: ${roles.join(', ')}`);
      return roles;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.warn(`Could not extract roles from token: ${errorMessage}`);
      return [];
    }
  }

  /**
   * Extracts the user ID (subject) from a JWT payload
   * @param payload JWT payload
   * @returns User ID
   */
  extractUserId(payload: JwtPayload): string {
    return payload.sub;
  }
}
