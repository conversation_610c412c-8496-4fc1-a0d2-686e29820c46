import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  private readonly logger = new Logger(RolesGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No specific roles required, access granted
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      // This should ideally be caught by an AuthGuard (e.g., JwtAuthGuard) before RolesGuard
      this.logger.warn('User object not found in request. Ensure AuthGuard runs before RolesGuard.');
      return false; // Or throw UnauthorizedException, but JwtAuthGuard should handle this
    }

    // Keycloak typically stores realm roles in user.realm_access.roles
    // Client-specific roles might be in user.resource_access[client_id].roles
    // We'll check realm_access.roles first, then consider client roles if needed or configured.
    const userRoles: string[] = user?.realm_access?.roles || [];
    
    // For more complex scenarios, you might also check client roles:
    // const clientId = this.configService.get<string>('KEYCLOAK_CLIENT_ID'); // If you need client roles
    // const clientRoles = user?.resource_access?.[clientId]?.roles || [];
    // const allUserRoles = [...new Set([...realmRoles, ...clientRoles])];

    const hasRequiredRole = requiredRoles.some((role) => userRoles.includes(role));

    if (!hasRequiredRole) {
      this.logger.warn(`User ${user.sub} (roles: ${userRoles.join(', ')}) does not have required roles: ${requiredRoles.join(', ')}`);
      // Throwing ForbiddenException is more appropriate here than just returning false
      throw new ForbiddenException('You do not have the right roles to access this resource.');
    }
    
    this.logger.verbose(`User ${user.sub} has required roles: ${requiredRoles.join(', ')}`);
    return true;
  }
}
