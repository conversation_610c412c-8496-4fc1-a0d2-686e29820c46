import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthCommonModule } from '@libs/auth-common';
import { KeycloakClientModule } from '@libs/keycloak-client';
import { ObservabilityModule } from '../observability/observability.module';
import { KeycloakService } from './services/keycloak.service';
import { UserServiceClient } from './services/user-service-client';
import { TokenService } from './services/token.service';
import { CircuitBreakerModule } from '@libs/resilience';
import { CacheModule } from '@libs/caching';
import { MessagingModule } from '@libs/messaging';

@Module({
  imports: [
    // Import our custom ObservabilityModule for logging, metrics, and tracing
    ObservabilityModule,
    ConfigModule, // Ensure ConfigModule is available for shared auth library
    // Import centralized Keycloak client with HTTP/2 performance
    KeycloakClientModule.forRoot(),
    // Import shared auth library (replaces local JWT strategy)
    AuthCommonModule.forRoot(),
    // Import circuit breaker module for resilience
    CircuitBreakerModule.register(),
    // Import caching module for performance improvements
    CacheModule.registerAsync({
      useFactory: () => ({
        // Use process.env directly as ConfigService may not work properly with webpack bundled apps
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
        db: parseInt(process.env.REDIS_DB || '0', 10),
        defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || '300', 10),
        keyPrefix: 'auth-service:',
        connectTimeout: 5000,
        commandTimeout: 5000,
      }),
      inject: [], // No dependencies needed since we use process.env directly
    }),
    // Import messaging module for auth event publishing
    MessagingModule.forRootAsync({
      useFactory: () => ({
        isGlobal: true,
        redis: {
          redis: {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT || '6379', 10),
            db: parseInt(process.env.REDIS_DB || '0', 10),
          },
        },
      }),
      inject: [], // No dependencies needed since we use process.env directly
    }),
  ],
  controllers: [AuthController],
  providers: [
    // Core services
    AuthService,
    // New refactored services
    KeycloakService,
    UserServiceClient,
    TokenService,
    // The ObservabilityModule already provides the LOGGER_FACTORY and LOGGER_SERVICE tokens,
    // AuthCommonModule provides JWT validation, guards, and decorators
  ],
  exports: [
    AuthService,
    KeycloakService,
    UserServiceClient,
    TokenService
  ], // Export services needed by other modules
})
export class AuthModule {}
