import {
  Injectable,
  ConflictException,
  InternalServerErrorException,
  UnauthorizedException,
  HttpException,
  Inject,
  BadRequestException,
  NotFoundException
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OBSERVABILITY_LOGGER, METRICS_SERVICE, TRACING_SERVICE, ObservabilityLogger, TracingService, MetricsService } from '@libs/observability';
import { AuthBusinessLogger } from '../observability/business-logger.service';
import { RegisterUserDto, LoginUserDto, CreateUserInternalDto } from '@libs/shared-types';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { UserContext } from '@libs/auth-common';
import { KeycloakTokenResponse } from './interfaces/keycloak-token-response.interface';
import { KeycloakService } from './services/keycloak.service';
import { UserServiceClient } from './services/user-service-client';
import { TokenService } from './services/token.service';
import { CacheService, UseCache, InvalidateCache } from '@libs/caching';
import { EventPublisher, EventFactory } from '@libs/messaging';

@Injectable()
export class AuthService {
  constructor(
    private readonly keycloakService: KeycloakService,
    private readonly userServiceClient: UserServiceClient,
    private readonly tokenService: TokenService,
    private readonly cacheService: CacheService,
    @Inject(OBSERVABILITY_LOGGER) private readonly logger: ObservabilityLogger,
    @Inject(METRICS_SERVICE) private readonly metricsService: MetricsService,
    @Inject(TRACING_SERVICE) private readonly tracingService: TracingService,
    @Inject('EVENT_PUBLISHER') private readonly eventPublisher: EventPublisher,
    private readonly businessLogger: AuthBusinessLogger,
  ) {
    this.logger.log('Auth Service initialized with caching and messaging capabilities');
  }



  /**
   * Register a new user in the system
   * @param registerUserDto User registration data
   * @returns Object with user ID and email
   */
  async registerUser(registerUserDto: RegisterUserDto): Promise<{ id: string; email: string }> {
    const { email, password, firstName, lastName } = registerUserDto;

    this.logger.log(`Processing registration for user: ${email}`);

    try {
      // Step 1: Check if user already exists in Keycloak
      const userExists = await this.keycloakService.checkUserExists(email);
      if (userExists) {
        this.logger.warn(`User with email ${email} already exists in Keycloak`);
        throw new ConflictException(`User with email ${email} already exists.`);
      }

      // Step 2: Create user in Keycloak
      const keycloakUserId = await this.keycloakService.createUser({
        email,
        password,
        firstName,
        lastName
      });

      // Step 3: Create user in User Service
      const userInternalDto: CreateUserInternalDto = {
        keycloakId: keycloakUserId,
        email,
        firstName,
        lastName,
      };

      try {
        // Circuit breaker protection is automatic in UserServiceClient via HttpClientService
        await this.userServiceClient.createUser(userInternalDto);
      } catch (userServiceError: any) {
        // Non-blocking error - log but don't fail the registration
        const errorMessage = userServiceError?.message || 'Unknown error';
        this.logger.warn(`User Service record creation failed (non-critical): ${errorMessage}`);
      }

      this.businessLogger.logUserRegistrationEvent(
        'success',
        keycloakUserId,
        { email }
      );

      // Publish user registration success event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.registration.success', {
          userId: keycloakUserId,
          email,
          firstName,
          lastName,
          registeredAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.registration.success'
      );

      return { id: keycloakUserId, email };
    } catch (error: any) {
      // Handle specific error types
      if (error instanceof ConflictException) {
        // Publish registration failure event for conflict (non-blocking)
        this.publishEventSafely(
          EventFactory.create('auth.registration.failure', {
            email,
            firstName,
            lastName,
            failureReason: 'User already exists',
            failedAt: new Date().toISOString(),
            errorCode: 'CONFLICT',
          }, { source: 'auth-service' }),
          'auth.registration.failure'
        );
        throw error;
      }

      // Log the error with appropriate context
      const errorMessage = error?.response?.data?.message || error?.message || 'Unknown error during user registration';
      this.logger.error(`Registration failed for ${email}: ${errorMessage}`);

      // Publish registration failure event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.registration.failure', {
          email,
          firstName,
          lastName,
          failureReason: errorMessage,
          failedAt: new Date().toISOString(),
          errorCode: error?.status || 'UNKNOWN',
        }, { source: 'auth-service' }),
        'auth.registration.failure'
      );

      // Re-throw with appropriate exception
      if (error?.status === 409) {
        throw new ConflictException(`User with email ${email} already exists.`);
      }

      if (error?.status >= 500) {
        throw new InternalServerErrorException('User registration service is currently unavailable');
      }

      throw new InternalServerErrorException('An unexpected error occurred during registration');
    }
  }

  /**
   * Authenticates a user and returns Keycloak tokens
   * @param loginUserDto User credentials
   * @returns Keycloak token response
   */
  async loginUser(loginUserDto: LoginUserDto): Promise<KeycloakTokenResponse> {
    const { email, password } = loginUserDto;

    this.logger.log(`Attempting login for user: ${email}`);

    try {
      // Circuit breaker protection is automatic in KeycloakClientService via HttpClientService
      const tokenResponse = await this.keycloakService.authenticateUser(email, password);

      this.logger.log(`Login successful for user: ${email}`);

      // Extract userId from token for business logging
      const tokenPayload = this.tokenService.decodeJwt(tokenResponse.access_token);
      const userId = tokenPayload?.sub || 'unknown';

      // Cache user session data for quick access
      if (userId !== 'unknown') {
        const sessionData = {
          userId,
          email: tokenPayload.email || email,
          roles: tokenPayload.realm_access?.roles || [],
          lastLoginAt: new Date().toISOString(),
        };
        
        try {
          await this.cacheService.set(
            this.generateUserCacheKey(userId, 'session'),
            sessionData,
            600 // 10 minutes TTL
          );
          this.logger.debug(`Cached session data for user: ${userId}`);
        } catch (cacheError) {
          this.logger.error(`Failed to cache session data for user ${userId}: ${(cacheError as Error)?.message || 'Unknown error'}`);
          // Continue - caching failure doesn't break login
        }
      }

      this.businessLogger.logUserLoginEvent(
        'success',
        userId,
        { email }
      );

      // Publish login success event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.login.success', {
          userId,
          email,
          loginAt: new Date().toISOString(),
          userAgent: 'not-available', // Could be passed from request headers
        }, { source: 'auth-service' }),
        'auth.login.success'
      );

      return tokenResponse;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.error_description || error?.message || 'Unknown error during login';
      this.logger.error(`Login failed for ${email}: ${errorMessage}`);

      // Publish login failure event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.login.failure', {
          email,
          failureReason: errorMessage,
          failedAt: new Date().toISOString(),
          errorCode: error?.response?.status || 'unknown',
        }, { source: 'auth-service' }),
        'auth.login.failure'
      );

      // Let Keycloak error handler exceptions bubble up (they're already properly formatted)
      // The keycloakService.authenticateUser method handles error transformation
      throw error;
    }
  }

  /**
   * Validates a JWT and extracts the payload with caching
   * @param token JWT token
   * @returns Decoded JWT payload
   */
  async validateJwt(token: string): Promise<JwtPayload> {
    // Create a hash of the token for cache key (for security)
    const crypto = require('crypto');
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
    const cacheKey = this.generateJwtCacheKey(tokenHash);

    try {
      // Try to get validation result from cache first
      const cachedResult = await this.cacheService.get<JwtPayload>(cacheKey);
      if (cachedResult.hit && cachedResult.value) {
        this.logger.debug(`JWT validation cache hit for token hash: ${tokenHash}`);
        return cachedResult.value;
      }

      // Cache miss - validate token
      this.logger.debug(`JWT validation cache miss for token hash: ${tokenHash}`);
      const payload = this.tokenService.validateJwt(token);

      // Cache the validation result for a short time (reducing repeated validations)
      await this.cacheService.set(cacheKey, payload, 60); // 1 minute TTL
      
      return payload;
    } catch (error) {
      this.logger.error(`JWT validation failed for token hash: ${tokenHash}: ${(error as Error)?.message || 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Validates a JWT synchronously (non-cached version for compatibility)
   * @param token JWT token
   * @returns Decoded JWT payload
   */
  validateJwtSync(token: string): JwtPayload {
    return this.tokenService.validateJwt(token);
  }

  /**
   * Decodes a JWT token without validation
   * @param token JWT token
   * @returns Decoded JWT payload or null if invalid
   */
  decodeToken(token: string): JwtPayload | null {
    return this.tokenService.decodeJwt(token);
  }

  /**
   * Extract roles from a JWT payload
   * @param payload JWT payload
   * @returns Array of role names
   */
  extractRoles(payload: JwtPayload): string[] {
    return this.tokenService.extractRoles(payload);
  }

  /**
   * Extract user ID from a JWT payload
   * @param payload JWT payload
   * @returns User ID
   */
  extractUserId(payload: JwtPayload): string {
    return this.tokenService.extractUserId(payload);
  }

  /**
   * Logs out a user by invalidating their session and tokens in Keycloak
   * @param userId The user ID from user context
   * @param refreshToken Optional refresh token to revoke (if not provided, only the session is invalidated)
   */
  async logoutUser(userId: string, refreshToken?: string): Promise<void> {
    this.logger.log(`Logging out user: ${userId}`);

    try {
      // Two steps to logout completely:
      // 1. Revoke the refresh token if provided
      if (refreshToken) {
        await this.keycloakService.revokeRefreshToken(refreshToken);
      }

      // 2. Invalidate the Keycloak session(s)
      await this.keycloakService.invalidateUserSessions(userId);

      // 3. Invalidate cached session data
      await this.invalidateUserAuthCache(userId);

      this.logger.log(`Successfully logged out user: ${userId}`);

      // Publish logout event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.logout.success', {
          userId,
          logoutAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.logout.success'
      );

    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error';
      this.logger.error(`Failed to log out user ${userId}: ${errorMessage}`);

      // Publish logout failure event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.logout.failure', {
          userId,
          failureReason: errorMessage,
          failedAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.logout.failure'
      );

      throw new InternalServerErrorException('Failed to log out user');
    }
  }

  /**
   * Refreshes an access token using a refresh token
   * @param refreshToken The refresh token to use for refreshing
   * @returns New token response with fresh access and refresh tokens
   */
  async refreshToken(refreshToken: string): Promise<KeycloakTokenResponse> {
    this.logger.log('Processing token refresh request');

    try {
      const tokenResponse = await this.keycloakService.refreshToken(refreshToken);

      this.logger.log('Token refresh successful');

      // Extract userId from the new token for business logging
      const tokenPayload = this.tokenService.decodeJwt(tokenResponse.access_token);
      const userId = tokenPayload?.sub || 'unknown';

      this.businessLogger.logUserTokenRefreshEvent(
        'success',
        userId,
        { /* no additional metadata needed */ }
      );

      return tokenResponse;
    } catch (error: any) {
      const errorMessage = error?.message || 'Unknown error during token refresh';
      this.logger.error(`Token refresh failed: ${errorMessage}`);

      // Log business event for failed refresh
      this.businessLogger.logUserTokenRefreshEvent(
        'failure',
        'unknown',
        { reason: errorMessage }
      );

      // Re-throw the error (it's already properly typed from KeycloakService)
      throw error;
    }
  }

  /**
   * Requests a password reset for a user with the given email
   * @param email The email address of the user requesting a password reset
   * @returns The user ID if found, or null if not found
   */
  async requestPasswordReset(email: string): Promise<string | null> {
    this.logger.log(`Delegating password reset request for user: ${email} to KeycloakService`);
    
    try {
      const userId = await this.keycloakService.requestPasswordReset(email);
      
      // Publish password reset request event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.password_reset.requested', {
          userId: userId || 'unknown',
          email,
          requestedAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.password_reset.requested'
      );
      
      return userId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Password reset request failed for ${email}`, errorMessage);
      
      // Publish password reset request failure event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.password_reset.request_failed', {
          email,
          failureReason: errorMessage || 'Unknown error',
          failedAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.password_reset.request_failed'
      );
      
      throw error;
    }
  }

  /**
   * Executes a password reset using the token from the email
   * @param token The reset token from the email link
   * @param newPassword The new password to set
   * @returns The user ID if the reset was successful, or null if not
   */
  async executePasswordReset(token: string, newPassword: string): Promise<string | null> {
    this.logger.log(`Delegating password reset execution to KeycloakService`);
    
    try {
      const userId = await this.keycloakService.executePasswordReset(token, newPassword);
      
      if (userId) {
        // Invalidate cached session data for this user
        await this.invalidateUserAuthCache(userId);
        
        // Publish password reset success event (non-blocking)
        this.publishEventSafely(
          EventFactory.create('auth.password_reset.completed', {
            userId,
            completedAt: new Date().toISOString(),
          }, { source: 'auth-service' }),
          'auth.password_reset.completed'
        );
      }
      
      return userId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Password reset execution failed`, errorMessage);
      
      // Publish password reset failure event (non-blocking)
      this.publishEventSafely(
        EventFactory.create('auth.password_reset.execution_failed', {
          failureReason: errorMessage || 'Unknown error',
          failedAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.password_reset.execution_failed'
      );
      
      throw error;
    }
  }

  /**
   * Sends an email verification link to the user's email
   * @param email The email address to send the verification link to
   * @returns The user ID if found, or null if not found
   */
  async sendEmailVerification(email: string): Promise<string | null> {
    this.logger.log(`Delegating email verification for user: ${email} to KeycloakService`);
    return this.keycloakService.sendEmailVerification(email);
  }

  /**
   * Update user account status (active/disabled)
   * @param userIdentifier Email or userId of the user to update
   * @param enabled Whether to enable or disable the account
   * @param isEmail Whether userIdentifier is an email (true) or userId (false)
   * @param reason Optional reason for status change
   * @param admin UserContext of the admin making the change (for audit logs)
   * @returns The user ID if the update was successful, or null if not
   */
  async updateUserStatus(
    userIdentifier: string,
    enabled: boolean,
    isEmail: boolean = true,
    reason?: string,
    admin?: UserContext
  ): Promise<string | null> {
    this.logger.log(`Delegating user status update for: ${userIdentifier} to KeycloakService`);
    return this.keycloakService.updateUserStatus(userIdentifier, enabled, isEmail, reason, admin);
  }

  /**
   * Decodes a password reset token to extract the user ID
   * Note: In a real implementation, you would validate the token signature, expiration, etc.
   * @param token The password reset token
   * @returns The user ID if the token is valid, null otherwise
   */
  private decodeResetToken(token: string): string | null {
    return this.keycloakService.decodeResetToken(token);
  }

  /**
   * Logs out a user and invalidates all cached sessions
   * @param userId User ID
   * @returns Promise<void>
   */
  @InvalidateCache({
    pattern: (userId: string) => `auth:user:${userId}:*`,
    namespace: 'auth'
  })
  async logoutUser(userId: string): Promise<void> {
    this.logger.log(`Logging out user: ${userId}`);

    try {
      // Invalidate all JWT validation cache entries (pattern-based)
      await this.cacheService.invalidate({
        pattern: 'jwt:validation:*',
        namespace: 'auth'
      });

      // Publish logout event
      this.publishEventSafely(
        EventFactory.create('auth.logout', {
          userId,
          loggedOutAt: new Date().toISOString(),
        }, { source: 'auth-service' }),
        'auth.logout'
      );

      this.logger.log(`Successfully logged out user: ${userId}`);
    } catch (error: any) {
      this.logger.error(`Failed to logout user ${userId}: ${error?.message || 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Publishes an auth event in a non-blocking way with error handling
   * Failed event publishing doesn't break the auth operation
   * @param event Event to publish
   * @param eventDescription Description for logging
   */
  private publishEventSafely(event: any, eventDescription: string): void {
    // Publish event asynchronously without blocking the main operation
    setImmediate(async () => {
      try {
        await this.eventPublisher.publish(event);
        this.logger.debug(`Successfully published ${eventDescription} event`);
      } catch (error) {
        this.logger.error(`Failed to publish ${eventDescription} event: ${(error as Error)?.message || 'Unknown error'}`);
        // Don't throw - events are non-critical for auth operations
      }
    });
  }

  /**
   * Generates a cache key for user session data
   * @param userId User ID
   * @param sessionType Type of session data (jwt, keycloak, permissions)
   * @returns Cache key string
   */
  private generateUserCacheKey(userId: string, sessionType: string): string {
    return `auth:user:${userId}:${sessionType}`;
  }

  /**
   * Generates a cache key for JWT token validation
   * @param tokenHash Hash of the token
   * @returns Cache key string
   */
  private generateJwtCacheKey(tokenHash: string): string {
    return `auth:jwt:${tokenHash}`;
  }

  /**
   * Invalidates all cache entries for a specific user
   * @param userId User ID
   */
  private async invalidateUserAuthCache(userId: string): Promise<void> {
    try {
      // Invalidate all cache entries for this user
      await this.cacheService.invalidate({
        pattern: `auth:user:${userId}:*`
      });
      this.logger.debug(`Invalidated auth cache for user: ${userId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to invalidate auth cache for user ${userId}`, errorMessage);
      // Don't throw - cache invalidation failures are non-critical
    }
  }
}
