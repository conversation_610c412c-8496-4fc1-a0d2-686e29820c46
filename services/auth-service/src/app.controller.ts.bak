import { Controller, Get, Inject } from '@nestjs/common';
import { AppService } from './app.service';
import { ObservabilityLogger } from '@libs/observability';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    @Inject('LOGGER_FACTORY') private readonly logger: ObservabilityLogger
  ) {
     this.logger.setContext(AppController.name); // Set context for logger
  }

  @Get('health')
  getHealth(): string {
    this.logger.log('Health check endpoint called');
    return this.appService.getHealth();
  }
}
