import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe, LoggerService } from '@nestjs/common';
import request from 'supertest';
import { AppModule } from '../../src/app.module';
import { HttpExceptionFilter } from '@libs/nestjs-common';
import { KeycloakService } from '../../src/auth/services/keycloak.service';
import { ConfigService } from '@nestjs/config';
import { LOGGER_SERVICE } from '@libs/observability';
import { KeycloakTestUtils, createTestUser, cleanupTestUser, AppRole } from '@libs/testing-utils';

/**
 * End-to-end tests for Role-Based Access Control (RBAC)
 *
 * These tests verify that:
 * 1. Protected endpoints require authentication
 * 2. Role-protected endpoints require specific roles
 * 3. Users with correct roles can access protected endpoints
 * 4. Users without required roles are denied access
 */
describe('RBAC (e2e)', () => {
  let app: INestApplication;
  let keycloakService: KeycloakService;
  let configService: ConfigService;
  let keycloakTestUtils: KeycloakTestUtils;

  // Store created test users with roles
  let adminUser: any;
  let regularUser: any; 
  let superAdminUser: any;
  let moderatorUser: any;
  let analyticsUser: any;
  
  // Store tokens
  let adminAccessToken: string;
  let regularAccessToken: string;
  let superAdminAccessToken: string;
  let moderatorAccessToken: string;
  let analyticsAccessToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();

    // Apply global pipes and filters
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }));
    
    // Get logger service and apply global exception filter
    const logger = app.get<LoggerService>(LOGGER_SERVICE);
    app.useGlobalFilters(new HttpExceptionFilter(logger));

    await app.init();

    // Get services
    keycloakService = moduleFixture.get<KeycloakService>(KeycloakService);
    configService = moduleFixture.get<ConfigService>(ConfigService);
    
    // Initialize Keycloak test utilities (static method)
    await KeycloakTestUtils.initialize();

    console.log('🔧 Creating test users with roles in Keycloak...');

    // Create test users with specific roles using the KeycloakTestUtils
    adminUser = await KeycloakTestUtils.createTestUser(AppRole.ADMIN);
    regularUser = await KeycloakTestUtils.createTestUser(AppRole.USER);
    superAdminUser = await KeycloakTestUtils.createTestUser(AppRole.SUPERADMIN);
    moderatorUser = await KeycloakTestUtils.createTestUser(AppRole.MODERATOR);
    analyticsUser = await KeycloakTestUtils.createTestUser(AppRole.ANALYTICS);

    console.log('✅ Created all test users with roles');

    // Get authentication tokens for each user
    console.log('🔑 Getting authentication tokens from Keycloak...');

    adminAccessToken = (await KeycloakTestUtils.authenticateTestUser(adminUser.email, adminUser.password)).access_token;
    regularAccessToken = (await KeycloakTestUtils.authenticateTestUser(regularUser.email, regularUser.password)).access_token;
    superAdminAccessToken = (await KeycloakTestUtils.authenticateTestUser(superAdminUser.email, superAdminUser.password)).access_token;
    moderatorAccessToken = (await KeycloakTestUtils.authenticateTestUser(moderatorUser.email, moderatorUser.password)).access_token;
    analyticsAccessToken = (await KeycloakTestUtils.authenticateTestUser(analyticsUser.email, analyticsUser.password)).access_token;

    console.log('✅ All RBAC test users and tokens ready');
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up RBAC test users...');
    
    // Cleanup all test users using KeycloakTestUtils
    const users = [
      { user: adminUser, name: 'admin' },
      { user: regularUser, name: 'regular' },
      { user: superAdminUser, name: 'superadmin' },
      { user: moderatorUser, name: 'moderator' },
      { user: analyticsUser, name: 'analytics' }
    ];

    for (const { user, name } of users) {
      if (user?.email) {
        try {
          const userId = await KeycloakTestUtils.getUserIdByEmail(user.email);
          await KeycloakTestUtils.deleteUser(userId);
          console.log(`✅ Cleaned up ${name} test user: ${user.email}`);
        } catch (error) {
          console.error(`❌ Failed to clean up ${name} test user:`, error);
        }
      }
    }

    await app.close();
    console.log('✅ RBAC test cleanup completed');
  });

  describe('Unauthenticated access', () => {
    it('should allow access to public endpoints', async () => {
      // Auth health check endpoint is public
      await request(app.getHttpServer())
        .get('/health')
        .expect(200);
    });

    it('should deny access to protected endpoints without authentication', async () => {
      // Admin check endpoint requires authentication
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .expect(401); // Unauthorized
    });
  });

  describe('Role-based access control', () => {
    it('should allow admin user to access admin-only endpoints', async () => {
      // Admin check endpoint requires admin role
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(200);
    });

    it('should deny regular user access to admin-only endpoints', async () => {
      // Admin check endpoint requires admin role
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${regularAccessToken}`)
        .expect(403); // Forbidden
    });

    it('should allow admin to update user status', async () => {
      // User status endpoint requires admin role
      await request(app.getHttpServer())
        .post('/auth/user-status')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .send({
          userIdentifier: regularUser.email,
          enabled: false,
          isEmail: true,
          reason: 'Testing RBAC'
        })
        .expect(200);
    });

    it('should deny regular user from updating user status', async () => {
      // User status endpoint requires admin role
      await request(app.getHttpServer())
        .post('/auth/user-status')
        .set('Authorization', `Bearer ${regularAccessToken}`)
        .send({
          userIdentifier: adminUser.email,
          enabled: false,
          isEmail: true,
          reason: 'Testing RBAC'
        })
        .expect(403); // Forbidden
    });
  });

  describe('Token validation', () => {
    it('should reject expired tokens', async () => {
      // Use KeycloakTestUtils to create an expired token
      const expiredToken = await KeycloakTestUtils.createExpiredToken();

      // Try to access admin endpoint with expired token
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401); // Unauthorized
    });

    it('should reject malformed tokens', async () => {
      // Use KeycloakTestUtils to create a malformed token
      const malformedToken = KeycloakTestUtils.createInvalidToken();

      // Try to access admin endpoint with malformed token
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${malformedToken}`)
        .expect(401); // Unauthorized
    });
  });

  describe('Role Hierarchy Testing', () => {
    it('should allow superadmin to access admin endpoints', async () => {
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${superAdminAccessToken}`)
        .expect(200);
    });

    it('should deny moderator access to admin endpoints', async () => {
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${moderatorAccessToken}`)
        .expect(403); // Forbidden - moderator role not sufficient
    });

    it('should allow all roles to access user endpoints', async () => {
      // All users should be able to access basic endpoints
      // This would test a @Roles('user') endpoint if we had one
      
      // For now, test that all tokens are structurally valid
      expect(superAdminAccessToken).toBeDefined();
      expect(adminAccessToken).toBeDefined();
      expect(moderatorAccessToken).toBeDefined();
      expect(analyticsAccessToken).toBeDefined();
      expect(regularAccessToken).toBeDefined();
    });

    it('should properly separate analytics access from admin hierarchy', async () => {
      // Superadmin should have analytics access
      await request(app.getHttpServer())
        .get('/auth/analytics-check')
        .set('Authorization', `Bearer ${superAdminAccessToken}`)
        .expect(200);

      // Analytics user should have analytics access
      await request(app.getHttpServer())
        .get('/auth/analytics-check')
        .set('Authorization', `Bearer ${analyticsAccessToken}`)
        .expect(200);

      // Admin should NOT have analytics access (analytics is separate)
      await request(app.getHttpServer())
        .get('/auth/analytics-check')
        .set('Authorization', `Bearer ${adminAccessToken}`)
        .expect(403);

      // Moderator should NOT have analytics access
      await request(app.getHttpServer())
        .get('/auth/analytics-check')
        .set('Authorization', `Bearer ${moderatorAccessToken}`)
        .expect(403);

      // Regular user should NOT have analytics access
      await request(app.getHttpServer())
        .get('/auth/analytics-check')
        .set('Authorization', `Bearer ${regularAccessToken}`)
        .expect(403);
    });

    it('should prevent analytics users from accessing admin endpoints', async () => {
      // Analytics user should NOT be able to access admin endpoints
      await request(app.getHttpServer())
        .get('/auth/admin-check')
        .set('Authorization', `Bearer ${analyticsAccessToken}`)
        .expect(403);

      // Analytics user should NOT be able to update user status
      await request(app.getHttpServer())
        .post('/auth/user-status')
        .set('Authorization', `Bearer ${analyticsAccessToken}`)
        .send({
          userIdentifier: regularUser.email,
          enabled: false,
          isEmail: true,
          reason: 'Testing RBAC separation'
        })
        .expect(403);
    });

    it('should have correct role hierarchy in token claims', () => {
      // Verify that higher-level roles include lower-level roles with proper separation
      const superAdminPayload = JSON.parse(Buffer.from(superAdminAccessToken.split('.')[1], 'base64').toString());
      const adminPayload = JSON.parse(Buffer.from(adminAccessToken.split('.')[1], 'base64').toString());
      const moderatorPayload = JSON.parse(Buffer.from(moderatorAccessToken.split('.')[1], 'base64').toString());
      const analyticsPayload = JSON.parse(Buffer.from(analyticsAccessToken.split('.')[1], 'base64').toString());
      const userPayload = JSON.parse(Buffer.from(regularAccessToken.split('.')[1], 'base64').toString());

      // SuperAdmin should have all roles (system-wide access)
      expect(superAdminPayload.realm_access.roles).toContain('superadmin');
      expect(superAdminPayload.realm_access.roles).toContain('admin');
      expect(superAdminPayload.realm_access.roles).toContain('moderator');
      expect(superAdminPayload.realm_access.roles).toContain('analytics');
      expect(superAdminPayload.realm_access.roles).toContain('user');

      // Admin should have admin, moderator, and user roles (but NOT analytics)
      expect(adminPayload.realm_access.roles).toContain('admin');
      expect(adminPayload.realm_access.roles).toContain('moderator');
      expect(adminPayload.realm_access.roles).toContain('user');
      expect(adminPayload.realm_access.roles).not.toContain('analytics'); // Analytics is separate

      // Moderator should have moderator and user roles (but NOT analytics or admin)
      expect(moderatorPayload.realm_access.roles).toContain('moderator');
      expect(moderatorPayload.realm_access.roles).toContain('user');
      expect(moderatorPayload.realm_access.roles).not.toContain('admin');
      expect(moderatorPayload.realm_access.roles).not.toContain('analytics'); // Analytics is separate

      // Analytics user should have analytics and user roles (but no admin/moderator)
      expect(analyticsPayload.realm_access.roles).toContain('analytics');
      expect(analyticsPayload.realm_access.roles).toContain('user');
      expect(analyticsPayload.realm_access.roles).not.toContain('admin');
      expect(analyticsPayload.realm_access.roles).not.toContain('moderator');

      // Regular user should only have user role
      expect(userPayload.realm_access.roles).toContain('user');
      expect(userPayload.realm_access.roles).not.toContain('admin');
      expect(userPayload.realm_access.roles).not.toContain('moderator');
      expect(userPayload.realm_access.roles).not.toContain('analytics');
    });
  });
});
