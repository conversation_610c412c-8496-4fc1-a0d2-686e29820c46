# Auth Service Test Documentation

## Overview

The Auth Service test suite provides comprehensive testing coverage for authentication, authorization, and RBAC functionality. Tests are organized into three categories with real infrastructure integration for validation.

## Current Test Results ✅

**All Tests Passing**: 46/46 tests across all categories
- **Unit Tests**: 16/16 passing (AuthService, AuthController, RolesGuard, Observability)
- **Integration Tests**: 8/8 passing (Keycloak integration, service connectivity)
- **E2E Tests**: 22/22 passing (Auth flows, RBAC, token management)

## Test Categories

### Unit Tests (`test/unit/`)
- **Purpose**: Test individual components in isolation with mocked dependencies
- **Coverage**: Services, controllers, guards, and utilities
- **Dependencies**: All external services mocked using `@libs/testing-utils`
- **Execution**: `yarn test`

### Integration Tests (`test/integration/`)
- **Purpose**: Test service integration with real external dependencies
- **Coverage**: Keycloak integration, database operations, caching
- **Dependencies**: Real Keycloak, Redis, PostgreSQL
- **Execution**: `yarn test:integration`

### End-to-End Tests (`test/e2e/`)
- **Purpose**: Test complete user flows through HTTP endpoints
- **Coverage**: Authentication flows, RBAC, token management
- **Dependencies**: Full infrastructure stack
- **Execution**: `yarn test:e2e`

## Infrastructure Requirements

### Required Services
All integration and e2e tests require these services to be running:

```bash
# Start full development stack
cd /root/code/polyrepo
yarn start:dev:bundled:watch

# Verify services are healthy
docker ps | grep -E "(keycloak|redis|postgres)"
curl http://localhost:8080/realms/polyrepo-test/.well-known/openid_configuration
```

### Service Dependencies
- **Keycloak**: Authentication provider (port 8080)
- **PostgreSQL**: User data storage (via User Service)
- **Redis**: Caching and session management
- **User Service**: User management API (port 3002)
- **Observability Stack**: Loki, Prometheus for event validation

## Key Testing Patterns

### 1. RBAC Testing with Real JWT Tokens

```typescript
// Example from rbac.e2e-spec.ts
import { KeycloakTestUtils } from '@libs/testing-utils';

describe('Role-based access control', () => {
  it('should allow admin user to access admin-only endpoints', async () => {
    // Get real JWT token from Keycloak
    const adminToken = await KeycloakTestUtils.authenticateTestUser(
      '<EMAIL>',
      'testpassword123'
    );
    
    return request(app.getHttpServer())
      .get('/auth/admin/test')
      .set('Authorization', `Bearer ${adminToken.access_token}`)
      .expect(200);
  });
});
```

### 2. Observability Validation

```typescript
// Business event validation with unique markers
const testId = TestEnvironment.createTestId();
const userData = TestDataGenerator.createRegistrationData({
  email: `test-${testId}@example.com`
});

// Perform operation
await request(app.getHttpServer())
  .post('/auth/register')
  .send(userData)
  .expect(201);

// Validate business event was logged
await TestEnvironment.waitForProcessing(1000);
const logs = await queryLokiLogs({
  datasourceUid: 'P8E80F9AEF21F6940',
  logql: `{service="auth-service"} |= "USER_REGISTERED" |= "${testId}"`
});
expect(logs.length).toBeGreaterThan(0);
```

### 3. Error Handling Validation

```typescript
// Test both HTTP response and Keycloak integration
it('should fail with duplicate email', async () => {
  const userData = TestDataGenerator.createRegistrationData();
  
  // First registration should succeed
  await request(app.getHttpServer())
    .post('/auth/register')
    .send(userData)
    .expect(201);
  
  // Second registration should fail
  await request(app.getHttpServer())
    .post('/auth/register')
    .send(userData)
    .expect(409)
    .expect(res => {
      expect(res.body.message).toContain('User already exists');
    });
});
```

## Environment Configuration

### Test Environment Files
```
test/.env.test.unit       # Unit test configuration
test/.env.test.integration # Integration test configuration  
test/.env.test.e2e        # E2E test configuration
```

### Key Environment Variables
```bash
# Keycloak Configuration
KEYCLOAK_BASE_URL=http://localhost:8080
KEYCLOAK_REALM_NAME=polyrepo-test
KEYCLOAK_CLIENT_ID=auth-service-test-client
KEYCLOAK_CLIENT_SECRET=TestClientSecretDev

# Service URLs
USER_SERVICE_URL=http://localhost:3002

# Test Configuration
NODE_ENV=test
LOG_LEVEL=debug
METRICS_ENABLED=false
TRACING_ENABLED=false
```

## Test File Structure

```
test/
├── README.md                           # This documentation
├── setup.ts                            # Global test setup
├── .env.test.unit                      # Unit test environment
├── .env.test.integration               # Integration test environment
├── .env.test.e2e                      # E2E test environment
├── .env.test.e2e.example               # E2E environment template
├── unit/                               # Unit tests (mocked dependencies)
│   ├── auth/
│   │   ├── auth.controller.spec.ts     # AuthController unit tests
│   │   └── auth.service.spec.ts        # AuthService unit tests
│   ├── rbac.spec.ts                    # RolesGuard unit tests
│   └── observability.spec.ts           # Observability integration tests
├── integration/                        # Integration tests (real services)
│   └── auth-keycloak.integration.spec.ts # Keycloak integration tests
├── e2e/                                # End-to-end tests (full stack)
│   ├── auth.e2e-spec.ts               # Authentication flow tests
│   └── rbac.e2e-spec.ts               # RBAC and role hierarchy tests
└── manual/                             # Manual testing scripts
    ├── check-keycloak-config.sh        # Keycloak configuration validation
    ├── test-auth-flow.js               # Manual auth flow testing
    └── test-user-management.sh         # User management testing
```

## Running Tests

### All Tests
```bash
cd services/auth-service
yarn test:all              # Run all test categories
```

### Individual Categories
```bash
yarn test                   # Unit tests only
yarn test:integration       # Integration tests only
yarn test:e2e               # E2E tests only
```

### With Coverage
```bash
yarn test --coverage        # Unit tests with coverage report
```

### Debug Mode
```bash
yarn test --detectOpenHandles --forceExit  # Debug hanging tests
```

## Key Test Utilities

### From @libs/testing-utils

```typescript
// Mock creation
const mocks = MockFactory.createCommonMocks({
  KEYCLOAK_REALM_NAME: 'polyrepo-test'
});

// Test data generation
const user = TestDataGenerator.createTestUser();
const regData = TestDataGenerator.createRegistrationData();

// Keycloak authentication
const token = await KeycloakTestUtils.authenticateTestUser(email, password);

// Observability testing
const testId = TestEnvironment.createTestId();
await TestEnvironment.waitForProcessing(1000);
```

## Debugging Failed Tests

### 1. Infrastructure Issues
```bash
# Check service health
curl http://localhost:3001/health
curl http://localhost:3002/health  
curl http://localhost:8080/realms/polyrepo-test/.well-known/openid_configuration

# Check Docker containers
docker ps | grep -E "(keycloak|redis|postgres)"
docker-compose logs keycloak
```

### 2. Authentication Issues
```bash
# Test Keycloak directly
node test/manual/keycloak-direct-test.js

# Check user creation
./test/manual/test-user-management.sh
```

### 3. Database/Cache Issues
```bash
# Check Redis connection
redis-cli -h localhost -p 6379 ping

# Check PostgreSQL connection
docker exec -it polyrepo_postgres_1 psql -U postgres -d polyrepo_test -c "\dt"
```

### 4. Log Analysis
```bash
# View service logs
docker-compose logs -f auth-service
docker-compose logs -f user-service

# Check for errors in specific time range
docker-compose logs --since="1m" auth-service | grep ERROR
```

## RBAC Role Hierarchy

The test suite validates this role hierarchy:

```
Security Roles:
├── superadmin (highest privileges)
├── admin 
├── moderator
└── user (base privileges)

Analytics Roles (separate hierarchy):
├── analytics_admin
├── analytics_user
└── analytics_viewer
```

**Critical Security Note**: Analytics roles are intentionally separated from the admin hierarchy. Analytics users cannot access admin endpoints, ensuring proper security boundaries.

## Test Data Management

### Unique Test Identifiers
All tests use unique identifiers to prevent conflicts:

```typescript
const testId = TestEnvironment.createTestId(); // Generates UUID
const email = `test-${testId}@example.com`;
```

### Test User Cleanup
Tests create users with predictable patterns for easy cleanup:
- Email format: `test-<uuid>@example.com`
- Usernames: `testuser-<uuid>`
- Automated cleanup occurs after test completion

## Observability Integration

### Business Event Validation
Tests validate that business events are properly logged:

```typescript
// Check for user registration events
const logs = await queryLokiLogs({
  datasourceUid: 'P8E80F9AEF21F6940',
  logql: `{service="auth-service"} |= "USER_REGISTERED" |= "${testId}"`
});
```

### Metrics Validation
Integration tests can verify metrics collection:

```typescript
// Check authentication metrics
const metrics = await queryPrometheus({
  datasourceUid: 'PBFA97CFB590B2093',
  expr: 'auth_service_login_attempts_total'
});
```

## Real Infrastructure Validation ✅

### Confirmed Working with Live Services

**Infrastructure Setup:**
- Keycloak running at localhost:8080 (healthy)
- User Service running at localhost:3002 (healthy)
- PostgreSQL databases for both Keycloak and User Service
- Redis, Loki, Prometheus, and Jaeger for observability

**Confirmed Working Authentication Flows:**
1. **User Registration** (`POST /auth/register`):
   - ✅ Creates user in both User Service and Keycloak
   - ✅ Returns user ID and email confirmation
   - ✅ Validates input data and handles duplicates

2. **User Login** (`POST /auth/login`):
   - ✅ Authenticates against Keycloak
   - ✅ Returns valid JWT access and refresh tokens
   - ✅ Tokens contain proper user claims and roles

3. **Protected Endpoints**:
   - ✅ Correctly reject requests without valid JWT tokens
   - ✅ Authentication guards working properly

4. **Service Communication**:
   - ✅ Auth Service ↔ Keycloak communication (6ms response time)
   - ✅ Auth Service ↔ User Service communication (4ms response time)
   - ✅ Circuit breakers functioning correctly

## Common Issues and Solutions

### 1. "Network was found but has incorrect label"
```bash
docker network rm polyrepo_default
docker-compose up -d
```

### 2. "ECONNREFUSED" on Keycloak
```bash
# Wait for Keycloak to fully start
docker-compose logs keycloak | grep "started"
# Typically takes 30-60 seconds
```

### 3. Test timeouts
```bash
# Increase Jest timeout in jest.config.js
module.exports = {
  testTimeout: 30000  // 30 seconds
};
```

### 4. Token validation failures
```bash
# Verify Keycloak realm configuration
./test/manual/check-keycloak-config.sh
```

## Best Practices

1. **Use Real Infrastructure**: Integration/E2E tests use real services for validation
2. **Unique Test Data**: Always use `TestEnvironment.createTestId()` for uniqueness
3. **Proper Cleanup**: Tests clean up created resources automatically
4. **Observability Validation**: Verify business events and metrics in tests
5. **Role Hierarchy Testing**: Test complete RBAC scenarios with real JWT tokens
6. **Error Scenarios**: Test both success and failure cases thoroughly

## Contributing

When adding new tests:

1. **Follow naming conventions**: `*.spec.ts` for unit, `*.integration.spec.ts` for integration, `*.e2e-spec.ts` for e2e
2. **Use testing utilities**: Leverage `@libs/testing-utils` for consistency
3. **Include observability**: Validate business events and metrics where applicable
4. **Test both success and error cases**: Ensure comprehensive coverage
5. **Update this documentation**: Keep test documentation current

## Test Performance

Current execution times (approximate):
- **Unit Tests**: ~5-10 seconds
- **Integration Tests**: ~15-30 seconds  
- **E2E Tests**: ~30-60 seconds
- **Total Suite**: ~60-90 seconds

Performance optimizations:
- Parallel test execution within categories
- Shared test infrastructure setup
- Efficient cleanup strategies
- Targeted test scoping