#!/bin/bash

# Script to run integration tests for auth-service

# Check if Keycloak is running
echo "Checking if Keycloak is running..."
if ! curl -s http://localhost:8080 > /dev/null; then
  echo "Keycloak is not running. Starting infrastructure services..."
  
  # Navigate to infrastructure directory
  cd ../../infrastructure/local-dev
  
  # Start infrastructure services
  ./start-local-services.sh
  
  # Wait for Keycloak to be ready
  echo "Waiting for Keycloak to be ready..."
  while ! curl -s http://localhost:8080 > /dev/null; do
    echo "Waiting for Keycloak to start..."
    sleep 5
  done
  
  # Navigate back to auth-service directory
  cd ../../services/auth-service
else
  echo "Keycloak is already running."
fi

# Run the integration tests
echo "Running integration tests..."
yarn test:integration

# Display test results
echo "Integration tests completed."
