/**
 * Test setup file for Auth Service
 *
 * This file is executed before running tests to set up the test environment.
 */

import { config } from 'dotenv';
import { join } from 'path';
import * as fs from 'fs';

// Determine test type based on Jest config
const isIntegrationTest = process.argv.some(arg => arg.includes('jest-integration.json'));
const isE2ETest = process.argv.some(arg => arg.includes('jest-e2e.json'));
const testType = isIntegrationTest ? 'integration' : isE2ETest ? 'e2e' : 'unit';

// Load environment variables from the appropriate .env file in the test directory
const envFile = join(process.cwd(), 'test', `.env.test.${testType}`);
if (fs.existsSync(envFile)) {
  console.log(`Loading environment from ${envFile}`);
  config({ path: envFile });
} else {
  console.warn(`Warning: ${envFile} not found. Looking for example file...`);
  // Load from example file if the specific .env file doesn't exist
  const exampleFile = join(process.cwd(), 'test', `.env.test.${testType}.example`);
  if (fs.existsSync(exampleFile)) {
    console.log(`Loading environment from example file: ${exampleFile}`);
    config({ path: exampleFile });
  } else {
    console.warn(`Warning: No suitable environment file found for ${testType} tests.`);
  }
}

// Set default environment variables for tests
process.env.NODE_ENV = 'test';

// Configure Jest global setup
global.beforeEach(() => {
  jest.resetAllMocks();
});

// Configure Jest global teardown
global.afterAll(() => {
  // Clean up any resources created during tests
});

// Configure Jest timeout
jest.setTimeout(30000); // 30 seconds

// Configure console output during tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

if (process.env.JEST_HIDE_CONSOLE_OUTPUT === 'true') {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
}

// Restore console output after tests
afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
