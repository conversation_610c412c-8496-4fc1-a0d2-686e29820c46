import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from '../../../src/auth/auth.controller';
import { AuthService } from '../../../src/auth/auth.service';
import { RegisterUserDto } from '../../../src/auth/dto/register-user.dto';
import { LoginUserDto } from '../../../src/auth/dto/login-user.dto';
import { HttpStatus } from '@nestjs/common';
import { JwtPayload } from '../../../src/auth/interfaces/jwt-payload.interface';
import { AuthGuard } from '@nestjs/passport';
import { RolesGuard } from '../../../src/auth/guards/roles.guard';
import { MockFactory } from '@libs/testing-utils';
import { AuthBusinessLogger } from '../../../src/observability/business-logger.service';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: jest.Mocked<AuthService>;

  const mockAuthService = {
    registerUser: jest.fn(),
    loginUser: jest.fn(),
    decodeToken: jest.fn().mockReturnValue({ sub: 'user-123', email: '<EMAIL>' }),
  };

  const mockBusinessLogger = MockFactory.createBusinessLogger();
  const mockLoggerFactory = MockFactory.createLoggerFactory();

  const mockRequest = (user?: Partial<JwtPayload>) => ({
    user: {
      sub: 'user-123',
      email: '<EMAIL>',
      realm_access: { roles: ['admin'] },
      ...user,
    },
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
        {
          provide: 'LOGGER_FACTORY',
          useValue: mockLoggerFactory,
        },
        {
          provide: AuthBusinessLogger,
          useValue: mockBusinessLogger,
        },
      ],
    })
      .overrideGuard(AuthGuard('jwt'))
      .useValue({ canActivate: () => true })
      .overrideGuard(RolesGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get(AuthService) as jest.Mocked<AuthService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // Note: healthCheck method is in HealthController, not AuthController

  describe('register', () => {
    it('should register a new user', async () => {
      const registerDto: RegisterUserDto = {
        email: '<EMAIL>',
        password: 'Test@1234',
        firstName: 'Test',
        lastName: 'User',
      };

      const expectedResult = {
        id: 'user-123',
        email: '<EMAIL>',
      };

      authService.registerUser.mockResolvedValue(expectedResult);

      const result = await controller.register(registerDto);
      expect(result).toEqual(expectedResult);
      expect(authService.registerUser).toHaveBeenCalledWith(registerDto);
    });
  });

  describe('login', () => {
    it('should return tokens on successful login', async () => {
      const loginDto: LoginUserDto = {
        email: '<EMAIL>',
        password: 'Test@1234',
      };

      const mockTokenResponse = {
        access_token: 'test-access-token',
        expires_in: 300,
        refresh_token: 'test-refresh-token',
        token_type: 'bearer',
      };

      authService.loginUser.mockResolvedValue(mockTokenResponse as any);

      const result = await controller.login(loginDto, {} as any);
      expect(result).toEqual(mockTokenResponse);
      expect(authService.loginUser).toHaveBeenCalledWith(loginDto);
    });
  });

  describe('adminCheck', () => {
    it('should return admin welcome message with user info', () => {
      const mockUser = {
        sub: 'user-123',
        email: '<EMAIL>',
        realm_access: { roles: ['admin'] },
      };

      const req = mockRequest(mockUser);
      const result = controller.adminCheck(req as any);

      expect(result).toEqual({
        message: 'Welcome, Admin!',
        user: {
          id: mockUser.sub,
          email: mockUser.email,
          roles: mockUser.realm_access.roles,
        },
      });
    });

    it('should throw UnauthorizedException if user is not authenticated', () => {
      const req = { user: null };

      expect(() => controller.adminCheck(req as any)).toThrow('User not authenticated.');
    });
  });
});
