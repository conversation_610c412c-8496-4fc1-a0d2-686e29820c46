import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from '../../../src/auth/auth.service';
import { KeycloakService } from '../../../src/auth/services/keycloak.service';
import { UserServiceClient } from '../../../src/auth/services/user-service-client';
import { TokenService } from '../../../src/auth/services/token.service';
import { AuthBusinessLogger } from '../../../src/observability/business-logger.service';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { MockFactory } from '@libs/testing-utils';
import { TracingService } from '@libs/observability';
import { CacheService } from '@libs/caching';
import { EventPublisher } from '@libs/messaging';

describe('AuthService', () => {
  let service: AuthService;
  let keycloakService: jest.Mocked<KeycloakService>;
  let userServiceClient: jest.Mocked<UserServiceClient>;
  let tokenService: jest.Mocked<TokenService>;
  let businessLogger: jest.Mocked<AuthBusinessLogger>;

  const mockKeycloakService = {
    createUser: jest.fn(),
    authenticateUser: jest.fn(),
    checkUserExists: jest.fn(),
    requestPasswordReset: jest.fn(),
    executePasswordReset: jest.fn(),
    sendEmailVerification: jest.fn(),
    updateUserStatus: jest.fn(),
    revokeRefreshToken: jest.fn(),
    invalidateUserSessions: jest.fn(),
  };

  const mockUserServiceClient = {
    createUser: jest.fn(),
    getUserByKeycloakId: jest.fn(),
  };

  const mockTokenService = {
    decodeJwt: jest.fn(),
    validateJwt: jest.fn(),
  };

  const mockBusinessLogger = {
    logUserRegistrationEvent: jest.fn(),
    logUserLoginEvent: jest.fn(),
    logUserLogoutEvent: jest.fn(),
    logPasswordResetEvent: jest.fn(),
    logPasswordResetRequestEvent: jest.fn(),
    logEmailVerificationEvent: jest.fn(),
    logAccountLockEvent: jest.fn(),
    logAccountUnlockEvent: jest.fn(),
  };

  const mockTracingService = MockFactory.createTracingService();
  const mockLoggerFactory = MockFactory.createLoggerFactory();

  const mockConfigService = {
    get: jest.fn().mockImplementation((key: string) => {
      if (key === 'KEYCLOAK_REALM_NAME') return 'test-realm';
      return null;
    }),
  };

  const mockCacheService = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    invalidatePattern: jest.fn(),
  };

  const mockEventPublisher = {
    publish: jest.fn(),
    publishBatch: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: KeycloakService,
          useValue: mockKeycloakService,
        },
        {
          provide: UserServiceClient,
          useValue: mockUserServiceClient,
        },
        {
          provide: TokenService,
          useValue: mockTokenService,
        },
        {
          provide: AuthBusinessLogger,
          useValue: mockBusinessLogger,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'LOGGER_FACTORY',
          useValue: mockLoggerFactory,
        },
        {
          provide: TracingService,
          useValue: mockTracingService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
        {
          provide: 'EVENT_PUBLISHER',
          useValue: mockEventPublisher,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    keycloakService = module.get(KeycloakService) as jest.Mocked<KeycloakService>;
    userServiceClient = module.get(UserServiceClient) as jest.Mocked<UserServiceClient>;
    tokenService = module.get(TokenService) as jest.Mocked<TokenService>;
    businessLogger = module.get(AuthBusinessLogger) as jest.Mocked<AuthBusinessLogger>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('registerUser', () => {
    const registerDto = {
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User',
    };

    it('should register a new user successfully', async () => {
      // Mock user doesn't exist
      keycloakService.checkUserExists.mockResolvedValue(false);

      // Mock Keycloak user creation
      keycloakService.createUser.mockResolvedValue('mock-keycloak-id');

      // Mock User Service user creation
      userServiceClient.createUser.mockResolvedValue({
        id: 1,
        email: registerDto.email,
        keycloakId: 'mock-keycloak-id',
      });

      const result = await service.registerUser(registerDto);

      expect(result).toBeDefined();
      expect(result.email).toBe(registerDto.email);
      expect(result.id).toBe('mock-keycloak-id');
      expect(keycloakService.checkUserExists).toHaveBeenCalledWith(registerDto.email);
      expect(keycloakService.createUser).toHaveBeenCalledWith(registerDto);
      expect(userServiceClient.createUser).toHaveBeenCalledWith({
        email: registerDto.email,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
        keycloakId: 'mock-keycloak-id',
      });
      expect(businessLogger.logUserRegistrationEvent).toHaveBeenCalledWith(
        'success',
        'mock-keycloak-id',
        expect.any(Object)
      );
    });

    it('should throw an error if user already exists', async () => {
      // Mock user already exists
      keycloakService.checkUserExists.mockResolvedValue(true);

      await expect(service.registerUser(registerDto)).rejects.toThrow();
      expect(keycloakService.createUser).not.toHaveBeenCalled();
      expect(userServiceClient.createUser).not.toHaveBeenCalled();
    });
  });

  describe('loginUser', () => {
    const loginDto = {
      email: '<EMAIL>',
      password: 'Password123!',
    };

    it('should login a user successfully', async () => {
      // Mock Keycloak authentication
      keycloakService.authenticateUser.mockResolvedValue({
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expires_in: 300,
        token_type: 'bearer',
      });

      // Mock token decoding
      tokenService.decodeJwt.mockReturnValue({
        sub: 'mock-keycloak-id',
        email: loginDto.email,
      });

      const result = await service.loginUser(loginDto);

      expect(result).toBeDefined();
      expect(result.access_token).toBe('mock-access-token');
      expect(result.refresh_token).toBe('mock-refresh-token');
      expect(keycloakService.authenticateUser).toHaveBeenCalledWith(
        loginDto.email,
        loginDto.password
      );
      expect(businessLogger.logUserLoginEvent).toHaveBeenCalledWith(
        'success',
        'mock-keycloak-id',
        expect.any(Object)
      );
    });

    it('should throw an error if authentication fails', async () => {
      // Mock authentication failure
      keycloakService.authenticateUser.mockRejectedValue({
        message: 'Invalid credentials',
        response: { status: 401 }
      });

      await expect(service.loginUser(loginDto)).rejects.toThrow(UnauthorizedException);
      // The business logger is not called for failed logins in the current implementation
      expect(businessLogger.logUserLoginEvent).not.toHaveBeenCalled();
    });
  });
});
