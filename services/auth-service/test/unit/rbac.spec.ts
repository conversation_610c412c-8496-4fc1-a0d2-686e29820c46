import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesGuard } from '../../src/auth/guards/roles.guard';
import { ROLES_KEY } from '../../src/auth/decorators/roles.decorator';

/**
 * Tests for Role-Based Access Control (RBAC)
 * 
 * These tests verify that:
 * 1. The RolesGuard correctly checks user roles against required roles
 * 2. Users with required roles are granted access
 * 3. Users without required roles are denied access
 */
describe('RBAC', () => {
  let reflector: Reflector;
  let rolesGuard: RolesGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
      ],
    }).compile();

    reflector = module.get<Reflector>(Reflector);
    rolesGuard = module.get<RolesGuard>(RolesGuard);
  });

  describe('RolesGuard', () => {
    it('should allow access when no roles are required', () => {
      // Mock the reflector to return no roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(null);

      // Create a mock execution context
      const mockContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: {
              sub: 'user-123',
              email: '<EMAIL>',
              realm_access: {
                roles: ['user'],
              },
            },
          }),
        }),
      } as unknown as ExecutionContext;

      // Verify that access is granted
      expect(rolesGuard.canActivate(mockContext)).toBe(true);
    });

    it('should allow access when user has required role', () => {
      // Mock the reflector to return required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

      // Create a mock execution context with a user that has the admin role
      const mockContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: {
              sub: 'admin-123',
              email: '<EMAIL>',
              realm_access: {
                roles: ['admin', 'user'],
              },
            },
          }),
        }),
      } as unknown as ExecutionContext;

      // Verify that access is granted
      expect(() => rolesGuard.canActivate(mockContext)).not.toThrow();
    });

    it('should deny access when user does not have required role', () => {
      // Mock the reflector to return required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

      // Create a mock execution context with a user that does not have the admin role
      const mockContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: {
              sub: 'user-123',
              email: '<EMAIL>',
              realm_access: {
                roles: ['user'],
              },
            },
          }),
        }),
      } as unknown as ExecutionContext;

      // Verify that access is denied with a ForbiddenException
      expect(() => rolesGuard.canActivate(mockContext)).toThrow();
    });

    it('should handle missing user object', () => {
      // Mock the reflector to return required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

      // Create a mock execution context with no user object
      const mockContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({}),
        }),
      } as unknown as ExecutionContext;

      // Verify that access is denied
      expect(rolesGuard.canActivate(mockContext)).toBe(false);
    });

    it('should handle missing roles in user object', () => {
      // Mock the reflector to return required roles
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(['admin']);

      // Create a mock execution context with a user that has no roles
      const mockContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: jest.fn().mockReturnValue({
            user: {
              sub: 'user-123',
              email: '<EMAIL>',
              // No realm_access property
            },
          }),
        }),
      } as unknown as ExecutionContext;

      // Verify that access is denied with a ForbiddenException
      expect(() => rolesGuard.canActivate(mockContext)).toThrow();
    });
  });
});
