{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "..", "testEnvironment": "node", "testMatch": ["<rootDir>/test/integration/**/*.integration.spec.ts"], "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/main.ts", "!src/**/*.module.ts", "!src/**/*.dto.ts", "!src/**/*.entity.ts", "!src/**/*.interface.ts"], "coverageDirectory": "./coverage-integration", "testTimeout": 30000, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1", "^@libs/nestjs-common$": "<rootDir>/../../libs/nestjs-common/src", "^@libs/nestjs-common/(.*)$": "<rootDir>/../../libs/nestjs-common/src/$1", "^@libs/shared-types$": "<rootDir>/../../libs/shared-types/src", "^@libs/shared-types/(.*)$": "<rootDir>/../../libs/shared-types/src/$1", "^@libs/observability$": "<rootDir>/../../libs/observability/src", "^@libs/observability/(.*)$": "<rootDir>/../../libs/observability/src/$1", "^@libs/resilience$": "<rootDir>/../../libs/resilience/src", "^@libs/resilience/(.*)$": "<rootDir>/../../libs/resilience/src/$1", "^@libs/testing-utils$": "<rootDir>/../../libs/testing-utils/src", "^@libs/testing-utils/(.*)$": "<rootDir>/../../libs/testing-utils/src/$1", "^@libs/messaging$": "<rootDir>/../../libs/messaging/src", "^@libs/messaging/(.*)$": "<rootDir>/../../libs/messaging/src/$1", "^@libs/caching$": "<rootDir>/../../libs/caching/src", "^@libs/caching/(.*)$": "<rootDir>/../../libs/caching/src/$1"}, "setupFilesAfterEnv": ["<rootDir>/test/setup.ts"], "verbose": true, "detectOpenHandles": true, "forceExit": true}