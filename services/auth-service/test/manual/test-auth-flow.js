/**
 * JavaScript script to test the auth service flows
 * This script creates a new user and tests all auth flows
 */

// Using built-in fetch API
const crypto = require('crypto');

// Configuration
const AUTH_SERVICE_URL = 'http://localhost:3001';
const KEYCLOAK_URL = 'http://localhost:8080';
const KEYCLOAK_REALM = 'polyrepo-test';
const KEYCLOAK_CLIENT_ID = 'auth-service-test-client';
const KEYCLOAK_CLIENT_SECRET = 'TestClientSecretDev';
const KEYCLOAK_ADMIN_USER = 'admin';
const KEYCLOAK_ADMIN_PASSWORD = 'admin';

// Generate a unique test user
const timestamp = Date.now();
const randomSuffix = crypto.randomBytes(4).toString('hex');
const TEST_USER = {
  email: `test.user.${timestamp}.${randomSuffix}@example.com`,
  password: 'Test@1234',
  firstName: 'Test',
  lastName: 'User'
};

// Store tokens and IDs
let accessToken;
let refreshToken;
let userId;
let keycloakId;

// Helper function to print section headers
function printHeader(title) {
  console.log('\n==== ' + title + ' ====\n');
}

// Helper function to check if a service is running
async function checkService(url, name) {
  console.log(`Checking if ${name} is running at ${url}...`);
  try {
    const response = await fetch(`${url}/health`);
    if (response.ok) {
      console.log(`${name} is running.`);
      return true;
    } else {
      console.error(`${name} is not running. Please start it before running this script.`);
      return false;
    }
  } catch (error) {
    console.error(`Error checking ${name}: ${error.message}`);
    return false;
  }
}

// Helper function to get admin token from Keycloak
async function getAdminToken() {
  try {
    const response = await fetch(`${KEYCLOAK_URL}/realms/master/protocol/openid-connect/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        client_id: 'admin-cli',
        username: KEYCLOAK_ADMIN_USER,
        password: KEYCLOAK_ADMIN_PASSWORD,
        grant_type: 'password'
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to get admin token: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.access_token;
  } catch (error) {
    console.error(`Error getting admin token: ${error.message}`);
    throw error;
  }
}

// Test registration
async function testRegistration() {
  printHeader('1. Testing Registration');
  console.log(`Registering user with email: ${TEST_USER.email}`);

  try {
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_USER)
    });

    const data = await response.json();
    console.log('Registration response:');
    console.log(JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.error(`Registration failed: ${data.message}`);
      return false;
    }

    userId = data.id;
    keycloakId = data.keycloakId;
    console.log(`Registration successful. User ID: ${userId}, Keycloak ID: ${keycloakId}`);
    return true;
  } catch (error) {
    console.error(`Error during registration: ${error.message}`);
    return false;
  }
}

// Test login
async function testLogin() {
  printHeader('2. Testing Login');
  console.log(`Logging in with email: ${TEST_USER.email}`);

  try {
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password
      })
    });

    const data = await response.json();
    console.log('Login response:');
    console.log(JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.error(`Login failed: ${data.message}`);
      return false;
    }

    accessToken = data.access_token;
    refreshToken = data.refresh_token;
    console.log('Login successful. Tokens received.');
    return true;
  } catch (error) {
    console.error(`Error during login: ${error.message}`);
    return false;
  }
}

// Test protected endpoint
async function testProtectedEndpoint() {
  printHeader('3. Testing Protected Endpoint');
  console.log('Accessing protected endpoint with access token...');

  try {
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/admin-check`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    const data = await response.json();
    console.log('Protected endpoint response:');
    console.log(JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.error(`Protected endpoint access failed: ${data.message}`);
      return false;
    }

    console.log('Protected endpoint access successful.');
    return true;
  } catch (error) {
    console.error(`Error accessing protected endpoint: ${error.message}`);
    return false;
  }
}

// Test user status update
async function testUserStatusUpdate() {
  printHeader('4. Testing User Status Update');
  console.log(`Updating status for user: ${userId}`);

  try {
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/user-status`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        userId: keycloakId,
        enabled: false
      })
    });

    const data = await response.json();
    console.log('Status update response:');
    console.log(JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.error(`Status update failed: ${data.message}`);
      return false;
    }

    console.log('Status update successful.');
    return true;
  } catch (error) {
    console.error(`Error updating user status: ${error.message}`);
    return false;
  }
}

// Test logout
async function testLogout() {
  printHeader('5. Testing Logout');
  console.log('Logging out user...');

  try {
    const response = await fetch(`${AUTH_SERVICE_URL}/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        refreshToken: refreshToken
      })
    });

    const data = await response.json();
    console.log('Logout response:');
    console.log(JSON.stringify(data, null, 2));

    if (!response.ok) {
      console.error(`Logout failed: ${data.message}`);
      return false;
    }

    console.log('Logout successful.');
    return true;
  } catch (error) {
    console.error(`Error during logout: ${error.message}`);
    return false;
  }
}

// Clean up test user
async function cleanupTestUser() {
  printHeader('Cleaning Up Test User');
  console.log(`Deleting test user: ${TEST_USER.email}`);

  try {
    const adminToken = await getAdminToken();
    
    // Get user ID from Keycloak
    const userResponse = await fetch(`${KEYCLOAK_URL}/admin/realms/${KEYCLOAK_REALM}/users?email=${encodeURIComponent(TEST_USER.email)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    const users = await userResponse.json();
    if (users.length === 0) {
      console.log('No user found to delete.');
      return;
    }
    
    const keycloakUserId = users[0].id;
    
    // Delete user from Keycloak
    const deleteResponse = await fetch(`${KEYCLOAK_URL}/admin/realms/${KEYCLOAK_REALM}/users/${keycloakUserId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    if (deleteResponse.ok) {
      console.log(`Test user deleted successfully from Keycloak.`);
    } else {
      console.error(`Failed to delete test user from Keycloak: ${deleteResponse.status} ${deleteResponse.statusText}`);
    }
  } catch (error) {
    console.error(`Error cleaning up test user: ${error.message}`);
  }
}

// Main function to run all tests
async function runTests() {
  printHeader('Starting Auth Service Tests');
  
  // Check if services are running
  const authServiceRunning = await checkService(AUTH_SERVICE_URL, 'Auth Service');
  if (!authServiceRunning) {
    return;
  }
  
  try {
    // Run tests in sequence
    const registrationSuccess = await testRegistration();
    if (!registrationSuccess) {
      console.error('Registration failed. Cannot continue with tests.');
      return;
    }
    
    const loginSuccess = await testLogin();
    if (!loginSuccess) {
      console.error('Login failed. Cannot continue with tests.');
      return;
    }
    
    await testProtectedEndpoint();
    await testUserStatusUpdate();
    await testLogout();
    
    // Clean up
    await cleanupTestUser();
    
    printHeader('All Tests Completed');
  } catch (error) {
    console.error(`Test execution failed: ${error.message}`);
  }
}

// Run the tests
runTests();
