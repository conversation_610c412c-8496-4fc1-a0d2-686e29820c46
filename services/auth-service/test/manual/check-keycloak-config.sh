#!/bin/bash
# Script to check Keycloak configuration

# Set the base URL for Keycloak
KEYCLOAK_URL="http://localhost:8080"
REALM_NAME="polyrepo-test"
ADMIN_USER="admin"
ADMIN_PASSWORD="admin"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${YELLOW}==== $1 ====${NC}\n"
}

# Check if Keycloak is running
print_header "Checking Keycloak"
echo "Checking if Keycloak is running at $KEYCLOAK_URL..."

response=$(curl -s -o /dev/null -w "%{http_code}" $KEYCLOAK_URL)

if [ "$response" == "200" ]; then
  echo -e "${GREEN}Keycloak is running.${NC}"
else
  echo -e "${RED}Keycloak is not running. Please start it before running this script.${NC}"
  exit 1
fi

# Get admin token
print_header "Getting Admin Token"
echo "Getting admin token from Keycloak..."

TOKEN_RESPONSE=$(curl -s -X POST "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=admin-cli" \
  -d "username=$ADMIN_USER" \
  -d "password=$ADMIN_PASSWORD" \
  -d "grant_type=password")

echo "Token response:"
echo $TOKEN_RESPONSE

# Extract access token
ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | cut -d':' -f2 | tr -d '"')

if [ -z "$ACCESS_TOKEN" ]; then
  echo -e "${RED}Failed to get admin token. Cannot continue with tests.${NC}"
  exit 1
else
  echo -e "${GREEN}Successfully got admin token.${NC}"
fi

# Check if realm exists
print_header "Checking Realm"
echo "Checking if realm '$REALM_NAME' exists..."

REALM_RESPONSE=$(curl -s -X GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "Realm response:"
echo $REALM_RESPONSE

if [[ $REALM_RESPONSE == *"not found"* ]]; then
  echo -e "${RED}Realm '$REALM_NAME' does not exist. Please create it before running this script.${NC}"
  exit 1
else
  echo -e "${GREEN}Realm '$REALM_NAME' exists.${NC}"
fi

# Check if test user exists
print_header "Checking Test User"
echo "Checking if test user '<EMAIL>' exists..."

USER_RESPONSE=$(curl -s -X GET "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users?email=<EMAIL>" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "User response:"
echo $USER_RESPONSE

if [[ $USER_RESPONSE == "[]" ]]; then
  echo -e "${RED}Test user '<EMAIL>' does not exist. Please create it before running this script.${NC}"
  
  # Create test user
  print_header "Creating Test User"
  echo "Creating test user '<EMAIL>'..."
  
  CREATE_USER_RESPONSE=$(curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "username": "<EMAIL>",
      "email": "<EMAIL>",
      "enabled": true,
      "emailVerified": true,
      "firstName": "Integration",
      "lastName": "Test",
      "credentials": [
        {
          "type": "password",
          "value": "testpassword",
          "temporary": false
        }
      ]
    }')
  
  echo "Create user response:"
  echo $CREATE_USER_RESPONSE
  
  if [[ $CREATE_USER_RESPONSE == *"error"* ]]; then
    echo -e "${RED}Failed to create test user. Cannot continue with tests.${NC}"
    exit 1
  else
    echo -e "${GREEN}Successfully created test user.${NC}"
  fi
else
  echo -e "${GREEN}Test user '<EMAIL>' exists.${NC}"
fi

print_header "Keycloak Configuration Check Complete"
echo -e "${GREEN}Keycloak is properly configured for testing.${NC}"
