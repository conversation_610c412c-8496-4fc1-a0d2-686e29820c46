#!/bin/bash
# Manual test script for auth service flows

# Set the base URL for the auth service
AUTH_SERVICE_URL="http://localhost:3001"

# Test user credentials - using the test realm (polyrepo-test) configuration
# These should match the test users created in configure-keycloak-test.sh
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="testpassword"
TEST_FIRST_NAME="Test"
TEST_LAST_NAME="User2"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Function to extract value from JSON response using grep and cut
# Usage: extract_json_value "response" "key"
extract_json_value() {
  local json="$1"
  local key="$2"
  echo "$json" | grep -o "\"$key\":[^,}]*" | cut -d':' -f2- | tr -d ' ",'
}

# Function to print section headers
print_header() {
  echo -e "\n${YELLOW}==== $1 ====${NC}\n"
}

# Function to check if a service is running
check_service() {
  local service_url=$1
  local service_name=$2
  local health_endpoint=${3:-/health}

  echo -e "Checking if $service_name is running at $service_url..."

  response=$(curl -s -o /dev/null -w "%{http_code}" $service_url$health_endpoint)

  if [ "$response" == "200" ]; then
    echo -e "${GREEN}$service_name is running.${NC}"
    return 0
  else
    echo -e "${RED}$service_name is not running. Please start it before running this script.${NC}"
    return 1
  fi
}

# Check if services are running
check_service "$AUTH_SERVICE_URL" "Auth Service" || exit 1
check_service "http://localhost:8080" "Keycloak" "" || exit 1

# Check if test realm is configured
print_header "Checking Keycloak Configuration"
echo "This script requires the 'polyrepo-test' realm to be configured in Keycloak."
echo "Please run the configure-keycloak-test.sh script to set up the test realm."
echo "Proceeding with tests assuming the test realm is properly configured..."

# Use the predefined test user or generate a unique one
# Uncomment the following lines to generate a unique user instead of using the predefined one
# TEST_EMAIL="test.user.$(date +%s)@example.com"
# TEST_PASSWORD="Password123!"
# TEST_FIRST_NAME="Test"
# TEST_LAST_NAME="User"

print_header "1. Testing Login (Skipping Registration)"
echo "Using predefined test user: $TEST_EMAIL"

# Skip registration since we're using a predefined test user
# The test user should already exist in Keycloak from the configure-keycloak-test.sh script
USER_ID="predefined-test-user"
KEYCLOAK_ID="predefined-test-user"

echo -e "${GREEN}Using predefined test user. Proceeding to login.${NC}"

print_header "2. Testing Login"
echo "Logging in with email: $TEST_EMAIL"

LOGIN_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "'$TEST_EMAIL'",
    "password": "'$TEST_PASSWORD'"
  }')

echo "Login response:"
echo $LOGIN_RESPONSE

# Extract tokens from login response
ACCESS_TOKEN=$(extract_json_value "$LOGIN_RESPONSE" "accessToken")
REFRESH_TOKEN=$(extract_json_value "$LOGIN_RESPONSE" "refreshToken")

if [ "$ACCESS_TOKEN" == "null" ] || [ -z "$ACCESS_TOKEN" ]; then
  echo -e "${RED}Login failed. Cannot continue with tests.${NC}"
  exit 1
else
  echo -e "${GREEN}Login successful. Access token obtained.${NC}"
fi

print_header "3. Testing Protected Endpoint"
echo "Accessing protected endpoint with access token"

ADMIN_CHECK_RESPONSE=$(curl -s -X GET "$AUTH_SERVICE_URL/auth/admin-check" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "Protected endpoint response:"
echo $ADMIN_CHECK_RESPONSE

print_header "4. Testing User Status Update"
echo "Updating user status"

STATUS_UPDATE_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/auth/user-status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "userId": "'$USER_ID'",
    "status": "ACTIVE"
  }')

echo "Status update response:"
echo $STATUS_UPDATE_RESPONSE

print_header "5. Testing Logout"
echo "Logging out user"

LOGOUT_RESPONSE=$(curl -s -X POST "$AUTH_SERVICE_URL/auth/logout" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "refreshToken": "'$REFRESH_TOKEN'"
  }')

echo "Logout response:"
echo $LOGOUT_RESPONSE

print_header "6. Testing Metrics Endpoint"
echo "Accessing metrics endpoint"

METRICS_RESPONSE=$(curl -s "$AUTH_SERVICE_URL/metrics")

echo "First 10 lines of metrics response:"
echo "$METRICS_RESPONSE" | head -n 10

print_header "7. Testing Health Endpoint"
echo "Accessing detailed health endpoint"

HEALTH_RESPONSE=$(curl -s "$AUTH_SERVICE_URL/health/detailed")

echo "Health response:"
echo $HEALTH_RESPONSE

print_header "Test Summary"
echo -e "${GREEN}All tests completed.${NC}"
echo "User ID: $USER_ID"
echo "Keycloak ID: $KEYCLOAK_ID"
echo "Test Email: $TEST_EMAIL"
