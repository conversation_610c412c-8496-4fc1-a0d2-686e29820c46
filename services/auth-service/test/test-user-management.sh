#!/bin/bash
# Test script for the user management features
# Usage: ./test-user-management.sh

# Base URL of the auth service
BASE_URL="http://localhost:3001"
# Email to use for testing (change as needed)
TEST_EMAIL="<EMAIL>"
# Admin credentials for testing user deactivation
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="adminpassword"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing User Management Features${NC}"
echo "=================================="

# Function to make HTTP requests and display results
function make_request() {
  local method=$1
  local endpoint=$2
  local data=$3
  local description=$4
  
  echo -e "\n${YELLOW}Testing: ${description}${NC}"
  echo "Endpoint: ${method} ${endpoint}"
  if [ -n "$data" ]; then
    echo "Request data: ${data}"
  fi
  
  response=$(curl -s -X ${method} \
    -H "Content-Type: application/json" \
    -d "${data}" \
    ${BASE_URL}${endpoint})
  
  echo "Response: ${response}"
  
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Request successful${NC}"
  else
    echo -e "${RED}Request failed${NC}"
  fi
  
  return $?
}

# Function to get admin token for protected endpoints
function get_admin_token() {
  echo -e "\n${YELLOW}Getting admin token for testing protected endpoints${NC}"
  
  response=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "{\"email\":\"${ADMIN_EMAIL}\",\"password\":\"${ADMIN_PASSWORD}\"}" \
    ${BASE_URL}/auth/login)
  
  # Extract access token from response
  access_token=$(echo $response | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
  
  if [ -n "$access_token" ]; then
    echo -e "${GREEN}Admin token obtained successfully${NC}"
  else
    echo -e "${RED}Failed to get admin token${NC}"
    echo "Response: ${response}"
  fi
  
  echo $access_token
}

# Test: Password Reset Request
make_request "POST" "/auth/request-password-reset" \
  "{\"email\":\"${TEST_EMAIL}\"}" \
  "Request Password Reset"

# For testing purposes, you'd now check your email or logs to get the reset token
# Here we'll use a dummy token
RESET_TOKEN="dummy-reset-token"

# Test: Execute Password Reset
make_request "POST" "/auth/execute-password-reset" \
  "{\"token\":\"${RESET_TOKEN}\",\"password\":\"newPassword123\"}" \
  "Execute Password Reset"

# Test: Email Verification Request
make_request "POST" "/auth/verify-email" \
  "{\"email\":\"${TEST_EMAIL}\"}" \
  "Request Email Verification"

# Test: User Logout (requires a valid refresh token)
make_request "POST" "/auth/logout" \
  "{\"refresh_token\":\"dummy-refresh-token\"}" \
  "User Logout"

# Get admin token for protected endpoints
ADMIN_TOKEN=$(get_admin_token)

if [ -n "$ADMIN_TOKEN" ]; then
  # Test: Deactivate User (Admin only)
  make_request "POST" "/auth/user-status" \
    "{\"userIdentifier\":\"${TEST_EMAIL}\",\"isEmail\":true,\"enabled\":false,\"reason\":\"Testing deactivation\"}" \
    "Deactivate User" \
    "${ADMIN_TOKEN}"
  
  # Test: Reactivate User (Admin only)
  make_request "POST" "/auth/user-status" \
    "{\"userIdentifier\":\"${TEST_EMAIL}\",\"isEmail\":true,\"enabled\":true,\"reason\":\"Testing reactivation\"}" \
    "Reactivate User" \
    "${ADMIN_TOKEN}"
else
  echo -e "${RED}Skipping admin-only tests due to missing admin token${NC}"
fi

echo -e "\n${GREEN}Testing completed!${NC}"
