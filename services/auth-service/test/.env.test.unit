# Auth Service Unit Test Environment Variables

# Node Environment
NODE_ENV=test

# Server Configuration
PORT=3001

# Keycloak Configuration - Mocked in unit tests
KEYCLOAK_BASE_URL=http://localhost:8080
KEYCLOAK_REALM_NAME=polyrepo-test
KEYCLOAK_CLIENT_ID=auth-service-test-client
KEYCLOAK_CLIENT_SECRET=TestClientSecretDev
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_ADMIN_CLIENT_ID=admin-cli

# User Service Configuration - Mocked in unit tests
USER_SERVICE_URL=http://localhost:3002

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json

# Observability Configuration
METRICS_ENABLED=false
TRACING_ENABLED=false

# JWT Settings for JwtStrategy
KEYCLOAK_AUTH_SERVER_URL=http://localhost:8080
KEYCLOAK_TOKEN_AUDIENCE=account
JWT_SECRET=test-secret
