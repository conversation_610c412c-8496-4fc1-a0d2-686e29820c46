#!/bin/bash

# Script to run Auth Service tests with Docker environment

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Auth Service Test Runner${NC}"
echo "=============================="
echo ""

# Function to clean up resources
cleanup() {
  echo -e "${YELLOW}Cleaning up resources...${NC}"
  docker-compose -f test/docker-compose.test.yml down
  echo -e "${GREEN}Cleanup complete.${NC}"
}

# Handle script interruption
trap cleanup EXIT

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Error: Docker is not running. Please start Docker and try again.${NC}"
  exit 1
fi

# Parse command line arguments
TEST_TYPE="all"
if [ $# -gt 0 ]; then
  TEST_TYPE=$1
fi

# Start Docker environment
echo -e "${YELLOW}Starting test environment...${NC}"
docker-compose -f test/docker-compose.test.yml up -d

# Wait for Keycloak to be ready
echo -e "${YELLOW}Waiting for Keycloak to be ready...${NC}"
attempt=0
max_attempts=30
until $(curl --output /dev/null --silent --fail http://localhost:8080/health/ready); do
  if [ ${attempt} -eq ${max_attempts} ]; then
    echo -e "${RED}Keycloak failed to start after ${max_attempts} attempts.${NC}"
    cleanup
    exit 1
  fi
  
  printf '.'
  attempt=$(($attempt+1))
  sleep 2
done

echo -e "\n${GREEN}Keycloak is ready!${NC}"

# Wait for Keycloak setup to complete
echo -e "${YELLOW}Waiting for Keycloak setup to complete...${NC}"
sleep 10
echo -e "${GREEN}Keycloak setup complete!${NC}"

# Run tests based on the specified type
case $TEST_TYPE in
  "unit")
    echo -e "${YELLOW}Running unit tests...${NC}"
    yarn test
    ;;
  "integration")
    echo -e "${YELLOW}Running integration tests...${NC}"
    yarn test:integration
    ;;
  "e2e")
    echo -e "${YELLOW}Running E2E tests...${NC}"
    yarn test:e2e
    ;;
  "all")
    echo -e "${YELLOW}Running all tests...${NC}"
    echo -e "${YELLOW}1. Unit Tests${NC}"
    yarn test
    
    echo -e "${YELLOW}2. Integration Tests${NC}"
    yarn test:integration
    
    echo -e "${YELLOW}3. E2E Tests${NC}"
    yarn test:e2e
    ;;
  *)
    echo -e "${RED}Invalid test type: $TEST_TYPE${NC}"
    echo "Valid options: unit, integration, e2e, all"
    cleanup
    exit 1
    ;;
esac

echo -e "${GREEN}Tests completed!${NC}"
