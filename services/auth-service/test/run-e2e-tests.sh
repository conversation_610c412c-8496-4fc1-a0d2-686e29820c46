#!/bin/bash

# Script to run end-to-end tests for auth-service

# Check if Keycloak is running
echo "Checking if Keycloak is running..."
if ! curl -s http://localhost:8080 > /dev/null; then
  echo "Keycloak is not running. Starting infrastructure services..."
  
  # Navigate to infrastructure directory
  cd ../../infrastructure/local-dev
  
  # Start infrastructure services
  ./start-local-services.sh
  
  # Wait for Keycloak to be ready
  echo "Waiting for Keycloak to be ready..."
  while ! curl -s http://localhost:8080 > /dev/null; do
    echo "Waiting for Keycloak to start..."
    sleep 5
  done
  
  # Navigate back to auth-service directory
  cd ../../services/auth-service
else
  echo "Keycloak is already running."
fi

# Check if User Service is running
echo "Checking if User Service is running..."
if ! curl -s http://localhost:3002/health > /dev/null; then
  echo "User Service is not running. Starting User Service..."
  
  # Start User Service in a new terminal
  cd ../user-service
  gnome-terminal -- bash -c "yarn start:dev" || xterm -e "yarn start:dev" || start "User Service" yarn start:dev
  
  # Wait for User Service to be ready
  echo "Waiting for User Service to be ready..."
  while ! curl -s http://localhost:3002/health > /dev/null; do
    echo "Waiting for User Service to start..."
    sleep 5
  done
  
  # Navigate back to auth-service directory
  cd ../auth-service
else
  echo "User Service is already running."
fi

# Run the end-to-end tests
echo "Running end-to-end tests..."
yarn test:e2e

# Display test results
echo "End-to-end tests completed."
