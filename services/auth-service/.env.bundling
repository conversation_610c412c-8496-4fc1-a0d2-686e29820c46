NODE_ENV=development
PORT=3000

# Keycloak Configuration (for Docker bundling environment)
KEYCLOAK_BASE_URL=http://keycloak:8080
KEYCLOAK_REALM_NAME=polyrepo-test
KEYCLOAK_CLIENT_ID=auth-service-test-client
KEYCLOAK_CLIENT_SECRET=TestClientSecretDev

# Keycloak Admin Configuration (use service account for admin operations)
KEYCLOAK_ADMIN_CLIENT_ID=auth-service-test-client
KEYCLOAK_ADMIN_CLIENT_SECRET=TestClientSecretDev

# Service URLs (for bundling context)
USER_SERVICE_URL=http://user-service:3000

# Redis Configuration (for Docker environment)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
ENABLE_TRACING=true
TEMPO_ENDPOINT=http://localhost:4318/v1/traces