NODE_ENV=docker
PORT=3000 # Internal port for the container

# Keycloak Configuration (for Keycloak service in Docker network)
KEYCLOAK_BASE_URL=http://keycloak:8080
KEYCLOAK_REALM_NAME=polyrepo-realm
KEYCLOAK_CLIENT_ID=auth-service-client
KEYCLOAK_CLIENT_SECRET=SuperSecretDevPasswordChangeMe # This should match what keycloak-setup configures

# URL for User Service (when running in Docker, using service name)
USER_SERVICE_URL=http://user-service:3000

# Optional JWT settings
# JWT_SECRET=your-docker-development-secret
# JWT_EXPIRATION_TIME=3600s

# Optional: Keycloak Admin Client configuration
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_ADMIN_CLIENT_ID=admin-cli

# HTTP Client Configuration
HTTP_TIMEOUT=5000
HTTP_MAX_REDIRECTS=5

# Redis Configuration (for caching and messaging)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://loki:3100
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://host.docker.internal:14268/api/traces