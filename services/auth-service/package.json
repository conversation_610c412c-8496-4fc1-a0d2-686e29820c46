{"name": "@polyrepo/auth-service", "version": "1.0.0", "description": "Authentication and Authorization Service", "private": true, "scripts": {"prebuild": "rimraf dist tsconfig.build.tsbuildinfo", "build": "rimraf dist tsconfig.build.tsbuildinfo && tsc -p tsconfig.build.json", "build:webpack": "webpack --config webpack.config.js", "build:webpack:watch": "webpack --config webpack.config.js --watch", "start:bundle": "node dist-webpack/main.bundle.js", "dev:bundle": "yarn build:webpack && yarn start:bundle", "bundle:rebuild:restart": "yarn build:webpack && cd ../../infrastructure/local-dev && docker restart polyrepo_auth_service_volume", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "start:dev": "rimraf tsconfig.build.tsbuildinfo && nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --json --outputFile=test-results.json", "test:watch": "jest --watch", "test:cov": "jest --coverage --json --outputFile=coverage-results.json", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --runInBand --json --outputFile=e2e-test-results.json", "test:integration": "jest --config ./test/jest-integration.json --runInBand --json --outputFile=integration-test-results.json", "test:integration:ci": "cross-env CI=true jest --config ./test/jest-integration.json --runInBand --json --outputFile=integration-test-results.json", "dev": "nest start --watch"}, "dependencies": {"@libs/auth-common": "file:../../libs/auth-common", "@libs/caching": "file:../../libs/caching", "@libs/http": "file:../../libs/http", "@libs/keycloak-client": "file:../../libs/keycloak-client", "@libs/messaging": "file:../../libs/messaging", "@libs/observability": "file:../../libs/observability", "@libs/resilience": "file:../../libs/resilience", "@libs/shared-types": "file:../../libs/shared-types", "@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^11.2.0", "axios": "^1.6.8", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "jwks-rsa": "^3.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "cross-env": "^7.0.3", "dotenv-webpack": "^8.1.0", "eslint": "*", "jest": "^29.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "typescript": "*", "webpack": "^5.92.1", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}