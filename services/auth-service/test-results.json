{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 4, "numPassedTests": 16, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 4, "numTotalTests": 16, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1749463918900, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["AuthService"], "duration": 37, "failureDetails": [], "failureMessages": [], "fullName": "AuthService should be defined", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be defined"}, {"ancestorTitles": ["AuthService", "registerUser"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "AuthService registerUser should register a new user successfully", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should register a new user successfully"}, {"ancestorTitles": ["AuthService", "registerUser"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "AuthService registerUser should throw an error if user already exists", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should throw an error if user already exists"}, {"ancestorTitles": ["AuthService", "loginUser"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "AuthService loginUser should login a user successfully", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should login a user successfully"}, {"ancestorTitles": ["AuthService", "loginUser"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "AuthService loginUser should throw an error if authentication fails", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should throw an error if authentication fails"}], "endTime": 1749463924094, "message": "", "name": "/root/code/polyrepo/services/auth-service/test/unit/auth/auth.service.spec.ts", "startTime": 1749463919329, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["RBAC", "<PERSON>s<PERSON><PERSON>"], "duration": 24, "failureDetails": [], "failureMessages": [], "fullName": "RBAC RolesGuard should allow access when no roles are required", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow access when no roles are required"}, {"ancestorTitles": ["RBAC", "<PERSON>s<PERSON><PERSON>"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "RBAC RolesGuard should allow access when user has required role", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should allow access when user has required role"}, {"ancestorTitles": ["RBAC", "<PERSON>s<PERSON><PERSON>"], "duration": 30, "failureDetails": [], "failureMessages": [], "fullName": "RBAC RolesGuard should deny access when user does not have required role", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should deny access when user does not have required role"}, {"ancestorTitles": ["RBAC", "<PERSON>s<PERSON><PERSON>"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "RBAC RolesGuard should handle missing user object", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle missing user object"}, {"ancestorTitles": ["RBAC", "<PERSON>s<PERSON><PERSON>"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "RBAC RolesGuard should handle missing roles in user object", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should handle missing roles in user object"}], "endTime": 1749463924741, "message": "", "name": "/root/code/polyrepo/services/auth-service/test/unit/rbac.spec.ts", "startTime": 1749463924108, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["AuthController", "register"], "duration": 30, "failureDetails": [], "failureMessages": [], "fullName": "AuthController register should register a new user", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should register a new user"}, {"ancestorTitles": ["AuthController", "login"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "AuthController login should return tokens on successful login", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return tokens on successful login"}, {"ancestorTitles": ["AuthController", "admin<PERSON><PERSON><PERSON>"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "AuthController admin<PERSON><PERSON><PERSON> should return admin welcome message with user info", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return admin welcome message with user info"}, {"ancestorTitles": ["AuthController", "admin<PERSON><PERSON><PERSON>"], "duration": 57, "failureDetails": [], "failureMessages": [], "fullName": "AuthController ad<PERSON><PERSON><PERSON><PERSON> should throw UnauthorizedException if user is not authenticated", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should throw UnauthorizedException if user is not authenticated"}], "endTime": 1749463926845, "message": "", "name": "/root/code/polyrepo/services/auth-service/test/unit/auth/auth.controller.spec.ts", "startTime": 1749463924785, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Observability", "LoggerService"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Observability LoggerService should be properly exported and usable", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be properly exported and usable"}, {"ancestorTitles": ["Observability", "HttpClientService"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "Observability HttpClientService should be properly exported and usable", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should be properly exported and usable"}], "endTime": 1749463927872, "message": "", "name": "/root/code/polyrepo/services/auth-service/test/unit/observability.spec.ts", "startTime": 1749463926856, "status": "passed", "summary": ""}], "wasInterrupted": false}