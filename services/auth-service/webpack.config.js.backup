const path = require('path');
const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');
const DotenvWebpack = require('dotenv-webpack');

module.exports = {
  // Target Node.js environment
  target: 'node',
  mode: 'development',
  
  // Entry point
  entry: './src/main.ts',
  
  // Output configuration
  output: {
    path: path.resolve(__dirname, 'dist-webpack'),
    filename: 'main.bundle.js',
    libraryTarget: 'commonjs2',
  },
  
  // Module resolution
  resolve: {
    extensions: ['.ts', '.js', '.json'],
    alias: {
      '@libs/nestjs-common': path.resolve(__dirname, '../../libs/nestjs-common/src'),
      '@libs/observability': path.resolve(__dirname, '../../libs/observability/src'),
      '@libs/resilience': path.resolve(__dirname, '../../libs/resilience/src'),
      '@libs/shared-types': path.resolve(__dirname, '../../libs/shared-types/src'),
    },
    // Fallback for node modules
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },
  
  // Loader configuration with explicit resolution
  resolveLoader: {
    modules: [
      'node_modules',
      path.resolve(__dirname, 'node_modules'),
      path.resolve(__dirname, '../../node_modules'),
    ]
  },
  
  // Module rules
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          {
            // Use require.resolve for explicit loader path
            loader: require.resolve('ts-loader'),
            options: {
              configFile: path.resolve(__dirname, 'tsconfig.webpack.json'),
              // Skip type checking for faster builds - focus on bundling
              transpileOnly: true,
              // Important for NestJS decorators and metadata
              experimentalWatchApi: true,
              compilerOptions: {
                // Preserve decorator metadata for NestJS DI
                experimentalDecorators: true,
                emitDecoratorMetadata: true,
                // Enable source maps for debugging
                sourceMap: true,
              }
            }
          }
        ]
      },
      // Handle binary files by returning empty module
      {
        test: /\.(node|so|dylib)$/,
        use: 'null-loader'
      },
      // Handle native modules
      {
        test: /\/build\/Release\//,
        use: 'null-loader'
      },
      // JS files will be handled by ts-loader as well if needed
      {
        test: /\.js$/,
        exclude: [
          /node_modules/,
          /\.(node|so|dylib)$/,
          /\/build\/Release\//
        ],
        use: [
          {
            loader: require.resolve('ts-loader'),
            options: {
              allowTsInNodeModules: true,
              configFile: path.resolve(__dirname, 'tsconfig.webpack.json'),
              transpileOnly: true,
            }
          }
        ]
      }
    ]
  },
  
  // External dependencies - minimize externals for self-contained bundle
  externals: [
    // Only externalize truly problematic native modules and large packages
    function({ context, request }, callback) {
      // Exclude .node files (native binaries)
      if (/\.node$/.test(request)) {
        return callback(null, 'commonjs ' + request);
      }
      // Exclude native build directories
      if (/\/build\/Release\//.test(request)) {
        return callback(null, 'commonjs ' + request);
      }
      // Externalize only truly problematic packages
      const problematicPackages = [
        'fsevents',           // macOS-specific native module
        'cpu-features',       // Native CPU detection
        '@swc/core',          // Native compiler
        'esbuild',            // Native bundler
        'node-gyp',           // Native build tools
        'canvas',             // Native graphics
        'sharp',              // Native image processing
        'sqlite3',            // Native database
        'bcrypt',             // Native crypto (if causing issues)
        '@mapbox/node-pre-gyp' // Native module installer
      ];
      
      // CRITICAL: Externalize OpenTelemetry packages for webpack compatibility
      // OpenTelemetry requires native require() function and module patching that doesn't work with bundling
      const openTelemetryPackages = [
        '@opentelemetry/sdk-node',
        '@opentelemetry/auto-instrumentations-node',
        '@opentelemetry/resources',
        '@opentelemetry/exporter-jaeger',
        '@opentelemetry/exporter-trace-otlp-http',
        '@opentelemetry/sdk-trace-base',
        '@opentelemetry/sdk-trace-node',
        '@opentelemetry/instrumentation',
        '@opentelemetry/semantic-conventions',
        '@opentelemetry/api'
      ];
      
      if (openTelemetryPackages.some(pkg => request === pkg || request.startsWith(pkg + '/'))) {
        return callback(null, 'commonjs ' + request);
      }
      
      // Always include critical dependencies in the bundle
      const criticalDependencies = [
        'tslib',             // TypeScript runtime
        'dotenv',            // Environment variable handling
        '@nestjs/config',    // NestJS config module
        'jwks-rsa'           // JWT handling
      ];
      
      if (criticalDependencies.some(pkg => request === pkg || request.startsWith(pkg + '/'))) {
        // These packages MUST be bundled, not externalized
        return callback();
      }
      
      // Only externalize if it's in our problematic list
      if (problematicPackages.some(pkg => request === pkg || request.startsWith(pkg + '/'))) {
        return callback(null, 'commonjs ' + request);
      }
      
      // Bundle everything else
      callback();
    }
  ],
  
  // Plugins
  plugins: [
    // Ignore optional NestJS modules that might not be installed
    new webpack.IgnorePlugin({
      resourceRegExp: /^@nestjs\/(microservices|websockets)/,
    }),
    
    // Ignore optional dependencies
    new webpack.IgnorePlugin({
      resourceRegExp: /^class-transformer\/storage$/,
    }),
    
    // Ignore native modules and binary files
    new webpack.IgnorePlugin({
      resourceRegExp: /\.node$/,
    }),
    
    // Ignore bcrypt native modules (if any)
    new webpack.IgnorePlugin({
      resourceRegExp: /^bcrypt$/,
    }),
    
    // Ignore dynamic requires that NestJS might use
    new webpack.IgnorePlugin({
      resourceRegExp: /^\.\/locale$/,
      contextRegExp: /moment$/,
    }),
    
    // Use dotenv-webpack to inject environment variables correctly
    new DotenvWebpack({
      path: './.env.docker',
      systemvars: true,     // Load all system environment variables as well
      safe: false,         
      defaults: false,
      allowEmptyValues: true,
      // This ensures process.env remains intact during runtime to work with ConfigService
      ignoreStub: true
    })
  ],
  
  // Development settings
  devtool: 'source-map',
  
  // Stats configuration for better output
  stats: {
    colors: true,
    modules: false,
    chunks: false,
    chunkModules: false,
    reasons: false,
    hash: false,
    version: false,
    timings: true,
    assets: true,
    warnings: true,
    errors: true,
    errorDetails: true
  },
  
  // Performance hints
  performance: {
    hints: false
  },
  
  // Node.js polyfills
  node: {
    __dirname: false,
    __filename: false,
  },
  
  // Optimization
  optimization: {
    minimize: false, // Keep readable for development
    nodeEnv: false,  // Don't override NODE_ENV
  }
};
