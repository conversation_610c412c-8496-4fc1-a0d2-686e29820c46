# Auth Service specific environment variables
NODE_ENV=development
PORT=3001

# Keycloak Configuration (for Keycloak service running in Docker, accessed from localhost)
KEYCLOAK_BASE_URL=http://localhost:8080
KEYCLOAK_REALM_NAME=polyrepo-realm
KEYCLOAK_CLIENT_ID=auth-service-client
KEYCLOAK_CLIENT_SECRET=YOUR_KEYCLOAK_CLIENT_SECRET # Replace with actual secret or reference .env.local

# Keycloak Admin Client configuration (if auth-service needs to manage Keycloak)
KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=YOUR_KEYCLOAK_ADMIN_PASSWORD # Replace with actual secret or reference .env.local

# HTTP Client Configuration
HTTP_TIMEOUT=5000
HTTP_MAX_REDIRECTS=5

# URL for User Service (if auth-service needs to call it)
# Assumes user-service is accessible via its Docker port mapping (e.g., localhost:3002)
USER_SERVICE_URL=http://localhost:3002

# Observability Configuration
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
TEMPO_ENDPOINT=http://localhost:4318/v1/traces
