# --- Base Stage ---
# Uses the common base image where yarn install and shared lib builds are done.
# The polyrepo-node-base already contains:
# - Root package.json, yarn.lock, tsconfig.base.json
# - All source code from ./libs and ./services
# - All node_modules installed via yarn install
# - Built shared libraries (@libs/*)
FROM polyrepo-node-base:latest AS base

# --- Service Build Stage ---
# This stage is for building the specific service (auth-service).
# It leverages the pre-installed node_modules and built libs from the 'base' stage.

WORKDIR /app/services/auth-service

# Copy service-specific package.json and tsconfig.json from the build context
# This ensures we use the latest versions of these files from the host for the build.
# REMOVED - these files should be present from polyrepo-node-base's COPY services ./services
# COPY services/auth-service/package.json ./package.json 
# COPY services/auth-service/tsconfig.json ./tsconfig.json
# COPY services/auth-service/tsconfig.build.json ./tsconfig.build.json

# Build the auth-service application using its own tsconfig.build.json
RUN echo ">>> auth-service: Removing previous build artifacts (dist and tsbuildinfo)..."
RUN rm -rf dist && rm -f tsconfig.build.tsbuildinfo tsconfig.tsbuildinfo
RUN echo ">>> auth-service: Running Build (tsc -p tsconfig.build.json)..."
RUN echo "Listing contents of /app/services/auth-service/src before build:" && ls -R /app/services/auth-service/src
# 'yarn build' in auth-service/package.json should be "tsc -p tsconfig.build.json"
RUN yarn build
# RUN yarn tsc src/main.ts --project tsconfig.json --outDir dist --noEmitOnError false && cat dist/main.js || echo "main.js not found or tsc failed for main.ts"
# RUN yarn rimraf dist && yarn tsc src/main.ts --target ES2021 --module commonjs --moduleResolution NodeNext --esModuleInterop --experimentalDecorators --emitDecoratorMetadata --sourceMap --outDir dist --skipLibCheck --diagnostics --listEmittedFiles
RUN ls -R dist || echo 'dist directory not found or is empty'
RUN if [ ! -f dist/main.js ]; then echo "ERROR: dist/main.js not found after yarn build!"; exit 1; fi

# --- Final Stage ---
# Creates the lean production image.
FROM node:22.1.0-alpine AS final

# Set up user and group
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

WORKDIR /app

# Copy essential files from the 'base' stage (polyrepo-node-base)
# Root package.json and yarn.lock might be needed for some runtime tooling or context.
COPY --from=base --chown=appuser:appgroup /app/package.json ./package.json
COPY --from=base --chown=appuser:appgroup /app/yarn.lock ./yarn.lock

# Copy all installed node_modules from the 'base' stage.
COPY --from=base --chown=appuser:appgroup /app/node_modules ./node_modules

# Copy compiled shared libraries from the 'base' stage.
COPY --from=base --chown=appuser:appgroup /app/libs ./libs

# --- Auth Service Specifics ---
WORKDIR /app/services/auth-service

# Copy service-specific package.json (potentially for runtime, though often not needed if just running node dist/main.js)
COPY --from=base --chown=appuser:appgroup /app/services/auth-service/package.json ./package.json

# Copy built auth-service application code from the 'Service Build Stage' (effectively 'base' after its build)
COPY --from=base --chown=appuser:appgroup /app/services/auth-service/dist ./dist

# Ensure all files in /app (especially service code) are owned by appuser
USER root
RUN chown -R appuser:appgroup /app
USER appuser

# Set NODE_ENV to production
ENV NODE_ENV=production

# Expose port (as per existing Dockerfile and likely docker-compose.yml)
EXPOSE 3000

# Define the command to run the application.
# WORKDIR is /app/services/auth-service, so path to main.js is relative (dist/main.js)
CMD ["node", "dist/main.js"]
