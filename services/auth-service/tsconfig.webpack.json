{"extends": "./tsconfig.build.json", "compilerOptions": {"rootDir": "../..", "baseUrl": "../..", "outDir": "./dist-webpack", "module": "commonjs", "target": "ES2020", "declaration": false, "declarationMap": false, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": false, "isolatedModules": false, "paths": {"@libs/*": ["libs/*/src"]}}, "include": ["src/**/*", "../../libs/*/src/**/*"], "exclude": ["node_modules", "dist", "dist-webpack", "test", "**/*.spec.ts", "**/*.test.ts"]}