# Auth Service (@polyrepo/auth-service)

This service is responsible for user authentication and authorization within the Polyrepo application. It integrates with Keycloak for identity and access management.

## Features

- User registration
- User login (password grant type with Keycloak)
- JWT-based authentication
- Role-Based Access Control (RBAC)

## Environment Variables

The following environment variables are required to run this service:

### Development Environment (`.env.bundling`)
- `NODE_ENV=development`
- `PORT=3000` (internal container port)
- `KEYCLOAK_BASE_URL=http://keycloak:8080` (container-to-container networking)
- `KEYCLOAK_REALM_NAME=polyrepo-test` (test realm with preconfigured users)
- `KEYCLOAK_CLIENT_ID=auth-service-test-client`
- `KEYCLOAK_CLIENT_SECRET=TestClientSecretDev`
- `KEYCLOAK_ADMIN_CLIENT_ID=auth-service-test-client` (same as client ID for service account)
- `KEYCLOAK_ADMIN_CLIENT_SECRET=TestClientSecretDev`
- `USER_SERVICE_URL=http://user-service:3000`
- `REDIS_HOST=redis`, `REDIS_PORT=6379`, `REDIS_DB=0`

### Production Environment
- `KEYCLOAK_REALM_NAME=polyrepo-realm` (production realm)
- `KEYCLOAK_CLIENT_ID=auth-service-client`
- `KEYCLOAK_CLIENT_SECRET=<production-secret>`
- Other variables configured per deployment environment

## API Endpoints

- `POST /auth/register`: Register a new user.
  - **Request Body:** `RegisterUserDto` (`{ email, password, firstName, lastName }`)
  - **Response:** `201 Created` with user details (excluding password).
- `POST /auth/login`: Log in an existing user.
  - **Request Body:** `LoginUserDto` (`{ email, password }`)
  - **Response:** `200 OK` with Keycloak token response (access token, refresh token, etc.).
- `GET /auth/admin-check`: (Protected) Example endpoint to check for 'admin' role.
  - **Authorization:** `Bearer <access_token>`
  - **Response:** `200 OK` if user has 'admin' role, `403 Forbidden` otherwise, `401 Unauthorized` if token is missing/invalid.

## Role-Based Access Control (RBAC)

This service implements RBAC to control access to its endpoints based on user roles defined in Keycloak.

### 1. `@Roles()` Decorator

To protect an endpoint with specific roles, use the `@Roles()` decorator above the controller method.

**Example:**
```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Roles } from './decorators/roles.decorator';
import { RolesGuard } from './guards/roles.guard';

@Controller('some-feature')
export class SomeFeatureController {
  @Get('admin-only')
  @UseGuards(AuthGuard('jwt'), RolesGuard)
  @Roles('admin', 'superadmin') // Requires either 'admin' OR 'superadmin' role
  getAdminResource() {
    return { message: 'This is an admin-only resource!' };
  }
}
```

### 2. `RolesGuard`

The `RolesGuard` is responsible for:
1. Extracting the roles required by the `@Roles()` decorator for a given endpoint.
2. Retrieving the user's roles from the validated JWT payload (specifically from `req.user.realm_access.roles`).
3. Comparing the user's roles against the required roles.
4. Allowing access if the user has at least one of the required roles, otherwise throwing a `ForbiddenException` (403).

It is typically used in conjunction with `AuthGuard('jwt')` to ensure the user is authenticated before checking roles.

### 3. JWT Role Structure

The `RolesGuard` expects the user's roles to be present in the JWT access token within the `realm_access.roles` array. This is a common way Keycloak includes realm-level roles in tokens.

**Example JWT Payload Snippet:**
```json
{
  // ... other claims
  "realm_access": {
    "roles": [
      "default-roles-polyrepo-realm",
      "offline_access",
      "admin",
      "uma_authorization"
    ]
  },
  // ... other claims
}
```

### 4. Keycloak Configuration for Roles

- Ensure that the roles you intend to use (e.g., `admin`, `user`, `moderator`) are defined as **Realm Roles** within your Keycloak realm.
- Assign these roles to users or groups as needed via the Keycloak Admin Console.

## Running the Service

Refer to the main project README for instructions on how to build and run services using Docker Compose.

## Testing

**Current Status**: 46/46 tests passing across all categories ✅

### Quick Testing Commands

```bash
# Run all tests
yarn test:all

# Individual test categories
yarn test                   # Unit tests (16/16 passing)
yarn test:integration       # Integration tests (8/8 passing)  
yarn test:e2e               # E2E tests (22/22 passing)
```

### Test Categories

1. **Unit Tests** (`test/unit/`): Test individual components with mocked dependencies
   - AuthController, AuthService, RolesGuard testing
   - Uses `@libs/testing-utils` for standardized mocks
   - Fast execution (~5-10 seconds)

2. **Integration Tests** (`test/integration/`): Test real service integration
   - Real Keycloak authentication and user management
   - Service connectivity validation
   - Business event validation
   - Uses test realm (`polyrepo-test`) with preconfigured test users

3. **End-to-End Tests** (`test/e2e/`): Test complete user flows
   - Full authentication flows with real JWT tokens
   - RBAC testing with role hierarchy validation
   - Observability and business event verification
   - Tests against live test users (<EMAIL>, <EMAIL>, etc.)

### Key Testing Features

- **Real Infrastructure Testing**: Integration/E2E tests use real Keycloak, Redis, PostgreSQL
- **RBAC with Real JWT Tokens**: Tests validate actual role-based access control
- **Observability Validation**: Business events and metrics verification
- **Role Hierarchy Security**: Validates separation of analytics from admin roles
- **Comprehensive Coverage**: Authentication, authorization, error handling, edge cases

### Infrastructure Requirements

Tests require the following services (automatically started with development stack):

```bash
# Start full development environment with authentication
cd /root/code/polyrepo
yarn dev:cli start
```

**Required Services:**
- Keycloak (localhost:8080) - Authentication provider with test realm configured
- User Service (localhost:3002) - User management  
- PostgreSQL - Data persistence
- Redis - Caching and sessions
- Observability stack (Loki, Prometheus) - Event validation

**Pre-configured Test Users:**
- `<EMAIL>` / `testpassword` (role: user)
- `<EMAIL>` / `testpassword` (role: admin)
- `<EMAIL>` / `testpassword` (role: superadmin)

### Detailed Documentation

For comprehensive testing documentation, patterns, and troubleshooting:
- **See**: `test/README.md` - Complete testing guide
- **See**: `docs/testing-standards.md` - Project-wide testing standards

### Test Output Files

```
test-results.json              # Unit test results
integration-test-results.json  # Integration test results  
e2e-test-results.json          # E2E test results
```

## Setup for Local Development

**RECOMMENDED: Use Enhanced Development CLI**

```bash
# Quick start - starts everything including Keycloak setup
cd /root/code/polyrepo
yarn dev:cli start

# Monitor service status
yarn dev:cli status
yarn dev:cli health

# View auth service logs
yarn dev:cli logs webpack-auth
```

**Manual Setup (if needed):**

1. Install dependencies: `yarn install`
2. Build shared libraries: `yarn build:libs`
3. Start with Docker: Uses `.env.bundling` configuration automatically
4. Access endpoints:
   - Auth Service: http://localhost:3001
   - Keycloak Admin: http://localhost:8080 (admin/admin)

**Testing Authentication:**

```bash
# Test login with preconfigured user
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}'

# Test registration
curl -X POST http://localhost:3001/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","firstName":"New","lastName":"User"}'
```
