services:
  # --- Build Stage for Base Image ---
  node-base: # Service definition to ensure base image is built
    image: polyrepo-node-base:latest # Tag the built image
    build:
      context: ../.. # Monorepo root
      dockerfile: infrastructure/docker/Dockerfile.node-base # Path to base Dockerfile
    # No need to run this service, just build it

  # --- Core Infrastructure ---
  postgres_keycloak: # Renamed from postgres, now dedicated to Keycloak
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_postgres_keycloak_dev
    environment:
      POSTGRES_USER: ${KEYCLOAK_POSTGRES_USER}
      POSTGRES_PASSWORD: ${KEYCLOAK_POSTGRES_PASSWORD}
      POSTGRES_DB: ${KEYCLOAK_POSTGRES_DB}
    volumes:
      - postgres_keycloak_data:/var/lib/postgresql/data # Dedicated data volume
    ports:
      - "${KEYCLOAK_POSTGRES_PORT_EXTERNAL:-5432}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  postgres_user_service: # New Postgres instance for user-service
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_postgres_user_service_dev
    environment:
      POSTGRES_USER: ${USER_SERVICE_POSTGRES_USER}
      POSTGRES_PASSWORD: ${USER_SERVICE_POSTGRES_PASSWORD}
      POSTGRES_DB: ${USER_SERVICE_POSTGRES_DB} # This will be 'polyrepo_user_db'
    volumes:
      - postgres_user_service_data:/var/lib/postgresql/data # Dedicated data volume
    ports:
      - "${USER_SERVICE_POSTGRES_PORT_EXTERNAL:-5433}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  redis:
    image: redis:7-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_redis_dev # Use project name
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  keycloak:
    image: quay.io/keycloak/keycloak:24.0
    container_name: polyrepo_keycloak_dev # Adjusted name for polyrepo
    environment:
      # Database connection settings
      KC_DB: postgres
      KC_DB_URL_HOST: postgres_keycloak # Updated to Keycloak's dedicated PG instance
      KC_DB_URL_PORT: 5432
      KC_DB_URL_DATABASE: ${KEYCLOAK_POSTGRES_DB} # Uses Keycloak's specific DB name
      KC_DB_USERNAME: ${KEYCLOAK_POSTGRES_USER} # Uses Keycloak's specific DB user
      KC_DB_PASSWORD: ${KEYCLOAK_POSTGRES_PASSWORD} # Uses Keycloak's specific DB password
      # Keycloak Admin Credentials (needed for kcadm.sh login)
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER:-admin} # Default to 'admin' if not in .env
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD:-admin} # Default to 'admin' if not in .env
      # Development/Proxy Settings
      KC_PROXY: edge
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_HTTP_ENABLED: "true"
      KC_HEALTH_ENABLED: "true"
      # Explicitly set Java Heap Size (adjust as needed)
      JAVA_OPTS_APPEND: "-Xms512m -Xmx1024m" # Example: 512MB initial, 1GB max heap

    ports:
      - "8080:8080" # Map Keycloak default port
    command: ["start-dev"] # Start in dev mode without import flag
    depends_on:
      postgres_keycloak: # Updated dependency
        condition: service_healthy # Wait for postgres to be healthy
    networks:
      - local_network
    healthcheck:
      test: ["CMD-SHELL", "exec 3<>/dev/tcp/127.0.0.1/8080;echo -e 'GET /health/ready HTTP/1.1\r\nhost: localhost\r\nConnection: close\r\n\r\n' >&3;grep 'HTTP/1.1 200 OK' <&3"]
      interval: 10s # Check more frequently
      timeout: 5s
      retries: 10 # Allow more retries
      start_period: 120s # Increase start period significantly
    restart: unless-stopped

  keycloak-setup:
    container_name: polyrepo_keycloak_setup # Consistent naming
    build:
      context: ./keycloak-setup # Path to the directory containing the Dockerfile
      dockerfile: Dockerfile.keycloak-setup # Specify the Dockerfile name
    depends_on:
      keycloak:
        condition: service_started # Script polls internally for full readiness
    environment:
      # Pass admin credentials and client secret to the script (used by configure-keycloak.sh)
      - KEYCLOAK_ADMIN_USER=${KEYCLOAK_ADMIN_USER:-admin}
      - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD:-admin}
      - AUTH_SERVICE_CLIENT_SECRET=${AUTH_SERVICE_CLIENT_SECRET:-SuperSecretDevPasswordChangeMe}
    networks:
      - local_network
    # entrypoint and command are now handled by the Dockerfile
    restart: 'no' # Do not restart setup job automatically

  auth-service:
    container_name: polyrepo_auth_service
    build:
      context: ../.. # Monorepo root context
      dockerfile: services/auth-service/Dockerfile # Path relative to context
    env_file:
      - ../../services/auth-service/.env.docker # Changed from .env.development
    environment:
      NODE_ENV: development
    depends_on:
      redis:
        condition: service_healthy
      keycloak:
        condition: service_healthy
      user-service: # Add dependency on user-service
        condition: service_started # Wait for user-service to start (or service_healthy if it has a check)
    networks:
      - local_network
    restart: unless-stopped

  user-service:
    container_name: polyrepo_user_service
    build:
      context: ../.. # Monorepo root context
      dockerfile: services/user-service/Dockerfile # Path relative to context
    image: polyrepo-user-service:latest
    env_file:
      - ../../services/user-service/.env.docker
    ports:
      - "${USER_SERVICE_PORT_EXTERNAL:-3002}:${USER_SERVICE_PORT_INTERNAL:-3000}"
    environment:
      NODE_ENV: development
      DATABASE_URL: postgresql://${USER_SERVICE_POSTGRES_USER}:${USER_SERVICE_POSTGRES_PASSWORD}@postgres_user_service:5432/${USER_SERVICE_POSTGRES_DB}?schema=public # Updated to use user_service PG
    depends_on:
      postgres_user_service: # Updated dependency
        condition: service_healthy # Wait for postgres
    networks:
      - local_network
    restart: unless-stopped

  api-gateway:
    container_name: polyrepo_api_gateway
    build:
      context: ../.. # Monorepo root context
      dockerfile: services/api-gateway/Dockerfile # Path relative to context
    env_file:
      - ../../services/api-gateway/.env.docker # Added env_file
    ports:
      - "${API_GATEWAY_PORT_EXTERNAL:-3000}:3000"
    environment:
      NODE_ENV: development
    networks:
      - local_network
    restart: unless-stopped

volumes:
  postgres_keycloak_data: # New volume for Keycloak's PG
    driver: local
  postgres_user_service_data: # New volume for User service's PG
    driver: local

networks:
  local_network:
    driver: bridge
