version: '3.8' # Specify a version for clarity

services:
  # --- Build Stage for Base Image ---
  node-base: # Service definition to ensure base image is built
    image: polyrepo-node-base:latest # Tag the built image
    build:
      context: ../.. # Monorepo root
      dockerfile: infrastructure/docker/Dockerfile.node-base # Path to base Dockerfile

  # --- Core Infrastructure ---
  postgres_keycloak:
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_postgres_keycloak_dev
    environment:
      POSTGRES_USER: ${KEYCLOAK_POSTGRES_USER}
      POSTGRES_PASSWORD: ${KEYCLOAK_POSTGRES_PASSWORD}
      POSTGRES_DB: ${KEYCLOAK_POSTGRES_DB}
    volumes:
      - postgres_keycloak_data:/var/lib/postgresql/data
    ports:
      - "${KEYCLOAK_POSTGRES_PORT_EXTERNAL:-5432}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  postgres_user_service:
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_postgres_user_service_dev
    environment:
      POSTGRES_USER: ${USER_SERVICE_POSTGRES_USER}
      POSTGRES_PASSWORD: ${USER_SERVICE_POSTGRES_PASSWORD}
      POSTGRES_DB: ${USER_SERVICE_POSTGRES_DB}
    volumes:
      - postgres_user_service_data:/var/lib/postgresql/data
    ports:
      - "${USER_SERVICE_POSTGRES_PORT_EXTERNAL:-5433}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  redis:
    image: redis:7-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_redis_dev
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  keycloak:
    image: quay.io/keycloak/keycloak:24.0
    container_name: polyrepo_keycloak_dev
    environment:
      KC_DB: postgres
      KC_DB_URL_HOST: postgres_keycloak
      KC_DB_URL_PORT: 5432
      KC_DB_URL_DATABASE: ${KEYCLOAK_POSTGRES_DB}
      KC_DB_USERNAME: ${KEYCLOAK_POSTGRES_USER}
      KC_DB_PASSWORD: ${KEYCLOAK_POSTGRES_PASSWORD}
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER:-admin}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD:-admin}
      KC_PROXY: edge
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_HTTP_ENABLED: "true"
      KC_HEALTH_ENABLED: "true"
      JAVA_OPTS_APPEND: "-Xms512m -Xmx1024m"
    ports:
      - "8080:8080"
    command: ["start-dev"]
    depends_on:
      postgres_keycloak:
        condition: service_healthy
    networks:
      - local_network
    healthcheck:
      test: ["CMD-SHELL", "exec 3<>/dev/tcp/127.0.0.1/8080;echo -e 'GET /health/ready HTTP/1.1\r\nhost: localhost\r\nConnection: close\r\n\r\n' >&3;grep 'HTTP/1.1 200 OK' <&3"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 120s
    restart: unless-stopped

  keycloak-setup:
    container_name: polyrepo_keycloak_setup
    build:
      context: ./keycloak-setup
      dockerfile: Dockerfile.keycloak-setup
    depends_on:
      keycloak:
        condition: service_started
    environment:
      - KEYCLOAK_ADMIN_USER=${KEYCLOAK_ADMIN_USER:-admin}
      - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD:-admin}
      - AUTH_SERVICE_CLIENT_SECRET=${AUTH_SERVICE_CLIENT_SECRET:-SuperSecretDevPasswordChangeMe}
    networks:
      - local_network
    restart: 'no'

volumes:
  postgres_keycloak_data:
    driver: local
  postgres_user_service_data:
    driver: local

networks:
  local_network:
    driver: bridge
