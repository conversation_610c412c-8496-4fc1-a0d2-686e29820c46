COMPOSE_PROJECT_NAME=polyrepo

KE<PERSON><PERSON><PERSON>K_POSTGRES_USER=devuser_keycloak
KEYCLOAK_POSTGRES_PASSWORD=devpassword_keycloak
KEYCLOAK_POSTGRES_DB=keycloak_db
KEY<PERSON>OAK_POSTGRES_PORT_EXTERNAL=5432 # Optional, if you need to override the default in docker-compose

USER_SERVICE_POSTGRES_USER=devuser_user_service
USER_SERVICE_POSTGRES_PASSWORD=devpassword_user_service
USER_SERVICE_POSTGRES_DB=polyrepo_user_db
USER_SERVICE_POSTGRES_PORT_EXTERNAL=5433 # Optional

KEYCLOAK_ADMIN_USER=admin
KEYCLOAK_ADMIN_PASSWORD=admin

# You might also need these if api-gateway proxies to other services:
# AUTH_SERVICE_URL=http://auth-service:3000
# USER_SERVICE_URL=http://user-service:3000