#!/bin/bash
# Starts all services (infrastructure and applications) as Docker containers,
# mimicking a production build by using production Dockerfiles and no local volume mounts for code.

# Exit immediately if a command exits with a non-zero status.
set -e

# Navigate to the project root directory
cd "$(dirname "$0")/../.." || exit

echo "-------------------------------------------------"
echo "Building shared libraries..."
echo "-------------------------------------------------"
yarn build:libs

# Navigate to the infrastructure directory
cd infrastructure/local-dev || exit

echo ""
echo "-------------------------------------------------"
echo "Starting all services (infrastructure and applications) in Docker containers (Production-like Build)..."
echo "This uses docker-compose.yml and production Dockerfiles."
echo "-------------------------------------------------"

# Use the .env file in this directory for docker-compose variables
LOG_FILE="../../logs/docker-compose-prod-like-$(date +%Y%m%d-%H%M%S).log"
mkdir -p ../../logs

echo "Docker Compose output will be logged to: ${LOG_FILE}"

docker-compose --env-file .env -f docker-compose.yml up -d --build --remove-orphans 2>&1 | tee "${LOG_FILE}"

if [ ${PIPESTATUS[0]} -ne 0 ]; then
  echo "Error starting Docker services for prod-like build. Please check Docker logs in ${LOG_FILE} and 'docker-compose -f docker-compose.yml logs'." >&2
  exit 1
fi

echo ""
echo "-------------------------------------------------"
echo "All services started successfully in Docker (Production-like Build)."
echo "-------------------------------------------------"
echo "Keycloak should be accessible at http://localhost:8080 (after it fully initializes)."
echo "API Gateway should be accessible at http://localhost:3000 (or as per API_GATEWAY_PORT_EXTERNAL in .env)."
echo "(Application services use their respective .env.docker files for configuration)."
echo ""
echo "To view logs: docker-compose -f docker-compose.yml logs -f [service_name]"
echo "To stop all services: docker-compose -f docker-compose.yml down"
echo "To also remove volumes (deletes data): docker-compose -f docker-compose.yml down -v"
