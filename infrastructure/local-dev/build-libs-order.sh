#!/bin/bash
# Script to build the libraries in the correct order

set -e

# Color definitions for better output
RED="\033[0;31m"
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Helper functions
log_info() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
  echo -e "${RED}[ERROR]${NC} $1" >&2
}

# Navigate to project root
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
cd "$PROJECT_ROOT" || exit 1

# Create logs directory
LOGS_DIR="$PROJECT_ROOT/logs"
mkdir -p "$LOGS_DIR"

TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
LOG_FILE="$LOGS_DIR/library-build-$TIMESTAMP.log"

log_info "Building libraries in order..."
log_info "Log file: $LOG_FILE"

# Define the libraries in the correct build order
# Based on dependencies: observability → shared-types/shared-utils → keycloak-client → auth-common → error-handling → resilience/http → caching/messaging
LIBRARIES=(
  "observability"
  "shared-types"
  "shared-utils"
  "keycloak-client"
  "auth-common"
  "error-handling"
  "resilience"
  "http"
  "caching"
  "messaging"
  "testing-utils"
)

# Build each library in order
for lib in "${LIBRARIES[@]}"; do
  if [ -d "$PROJECT_ROOT/libs/$lib" ]; then
    log_info "Building @libs/$lib..."
    
    # Check if the library has a build script
    if grep -q '"build"' "$PROJECT_ROOT/libs/$lib/package.json"; then
      (cd "$PROJECT_ROOT/libs/$lib" && yarn build) 2>&1 | tee -a "$LOG_FILE" || {
        log_error "Failed to build @libs/$lib"
        exit 1
      }
      log_success "Successfully built @libs/$lib"
    else
      log_warning "No build script found for @libs/$lib, skipping"
    fi
  else
    log_warning "Library @libs/$lib not found, skipping"
  fi
done

log_success "All libraries built successfully!"
