# Infrastructure-Level Environment Variables for Local Development
# These variables are shared across all services when running locally

# Centralized Keycloak Configuration
KE<PERSON><PERSON>OAK_BASE_URL=http://localhost:8080
KEYCLOAK_REALM_NAME=polyrepo-realm
KEYCLOAK_CLIENT_ID=auth-service-client
KEYCLOAK_CLIENT_SECRET=your-client-secret-here
K<PERSON><PERSON>CLOAK_ADMIN_CLIENT_ID=admin-cli
<PERSON>EYCLOAK_ADMIN_CLIENT_SECRET=

# Service URLs for Local Development
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002

# Observability Configuration (shared across all services)
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://localhost:3100
ENABLE_METRICS=true
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Database Configuration for Local Development
POSTGRES_KEYCLOAK_HOST=localhost
POSTGRES_KEYCLOAK_PORT=5432
POSTGRES_USER_SERVICE_HOST=localhost
POSTGRES_USER_SERVICE_PORT=5433

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379