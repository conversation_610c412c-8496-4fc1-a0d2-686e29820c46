#!/bin/bash
# This script prepares the local development environment and starts core infrastructure.
# Application services (api-gateway, auth-service, user-service) should be run locally by the user.

# Exit immediately if a command exits with a non-zero status.
set -e

# Navigate to the project root directory
cd "$(dirname "$0")/../.." || exit

echo "-------------------------------------------------"
echo "Building shared libraries..."
echo "-------------------------------------------------"
yarn build:libs

# Navigate to the infrastructure directory
cd infrastructure/local-dev || exit

echo "
-------------------------------------------------"
echo "Starting core infrastructure (Keycloak, PostgreSQL databases, Redis)..."
echo "This will also run Keycloak setup."
echo "-------------------------------------------------"
# Use the .env file in this directory for docker-compose variables
# The --build flag ensures keycloak-setup is built if it changed
docker-compose --env-file .env -f docker-compose.infra.yml up -d --build --remove-orphans

if [ $? -ne 0 ]; then
  echo "Error starting core infrastructure. Please check Docker logs." >&2
  exit 1
fi

echo "
-------------------------------------------------"
echo "Waiting for infrastructure services to be healthy..."
echo "(This might take a couple of minutes, especially for Keycloak setup)"
echo "-------------------------------------------------"

# Wait for Keycloak to be healthy (adjust timeout as needed)
# A simple sleep is used here, more robust checks can be added if needed
# Checking logs for keycloak-setup completion
TIMEOUT_SECONDS=300 # 5 minutes
INTERVAL_SECONDS=10
ELAPSED_SECONDS=0

until docker-compose -f docker-compose.infra.yml logs keycloak-setup | grep -q "Keycloak setup completed successfully"; do
  if [ "$ELAPSED_SECONDS" -ge "$TIMEOUT_SECONDS" ]; then
    echo "Timeout waiting for Keycloak setup to complete. Please check 'docker-compose -f docker-compose.infra.yml logs keycloak-setup'" >&2
    # Optionally, you might want to stop services here or provide further instructions
    # exit 1 # Uncomment to exit if timeout is critical
    break # Break the loop and proceed, user can check logs
  fi
  echo "Waiting for Keycloak setup... ($ELAPSED_SECONDS/$TIMEOUT_SECONDS s)"
  sleep "$INTERVAL_SECONDS"
  ELAPSED_SECONDS=$((ELAPSED_SECONDS + INTERVAL_SECONDS))
done

if docker-compose -f docker-compose.infra.yml logs keycloak-setup | grep -q "Keycloak setup completed successfully"; then
  echo "Keycloak setup completed."
else
  echo "Keycloak setup might not have completed successfully or timed out. Please check logs: 'docker-compose -f docker-compose.infra.yml logs keycloak-setup'"
fi

echo "
-------------------------------------------------"
echo "Preparing User Service (Prisma)..."
echo "-------------------------------------------------"
cd ../../services/user-service || exit

echo "Generating Prisma client for User Service..."
npx prisma generate

echo "Running Prisma migrations for User Service..."
# Using 'reset --force' for development to ensure a clean state. Change if needed.
npx prisma migrate reset --force 
# If you prefer not to reset, use: npx prisma migrate deploy or npx prisma migrate dev

cd ../../infrastructure/local-dev || exit

echo "
-------------------------------------------------"
echo "Local Development Setup Complete!"
echo "-------------------------------------------------"
echo "Core infrastructure is running in Docker."
echo "Shared libraries have been built."
echo "User service Prisma client generated and migrations applied."
echo ""

echo "You can now start your application services locally in SEPARATE TERMINALS."
echo "Navigate to the project root (d:\Code\polyrepo) in each new terminal."
echo ""

echo "  1. API Gateway:"
echo "     cd services/api-gateway"
echo "     yarn start:dev"
echo "     (Service will be available at http://localhost:3000, debug on 9231)"
echo ""

echo "  2. Auth Service:"
echo "     cd services/auth-service"
echo "     yarn start:dev"
echo "     (Service will be available at http://localhost:3001, debug on 9229)"
echo ""

echo "  3. User Service:"
echo "     cd services/user-service"
echo "     yarn start:dev"
echo "     (Service will be available at http://localhost:3002, debug on 9230)"
echo ""

echo "To stop the core infrastructure, run from 'd:\Code\polyrepo\infrastructure\local-dev':"
echo "  docker-compose -f docker-compose.infra.yml down"
