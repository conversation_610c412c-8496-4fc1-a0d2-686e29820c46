# Traefik Dynamic Configuration (dynamic_conf.yml)
# This file can be used to define routers, services, middleware, and TLS options
# that are not easily configured via Docker labels, or for non-Docker services.

http:
  routers:
    auth-service:
      rule: "PathPrefix(`/api/auth`)"
      service: "auth-service"
      middlewares:
        - "strip-api-prefix"
        - "cors-headers"
      tls: {}

  services:
    auth-service:
      loadBalancer:
        servers:
          - url: "http://auth-service:3001/"

  middlewares:
    strip-api-prefix:
      stripPrefix:
        prefixes:
          - "/api"
        
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
          - PUT
          - POST
          - DELETE
        accessControlAllowHeaders:
          - "Authorization"
          - "Content-Type"
        accessControlAllowOriginList:
          - "*"  # For development only
        accessControlMaxAge: 100
        addVaryHeader: true
        
    rate-limit:
      rateLimit:
        average: 100
        burst: 50

tls:
  certificates:
    - certFile: /etc/traefik/certs/local.pem
      keyFile: /etc/traefik/certs/local-key.pem
  options:
    default:
      minVersion: VersionTLS12
      cipherSuites:
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305
        - TLS_AES_128_GCM_SHA256
        - TLS_AES_256_GCM_SHA384
        - TLS_CHACHA20_POLY1305_SHA256
