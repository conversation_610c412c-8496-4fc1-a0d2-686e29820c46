# Traefik Static Configuration (traefik.yml)

global:
  checkNewVersion: true
  sendAnonymousUsage: false # Optional

# -- (1) API and Dashboard
api:
  dashboard: true
  insecure: true # For local dev only, accessible at http://traefik.localhost:8080. Secure this in production.

# -- (2) EntryPoints
entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
          permanent: true
  websecure:
    address: ":443"
    http:
      tls: 
        certResolver: "selfsigned"
# -- (3) Providers
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false # Only expose services with traefik.enable=true
    network: "local_network" # Ensure Traefik uses the correct Docker network
  file:
    filename: "/etc/traefik/dynamic_conf.yml"
    watch: true

# -- (4) Certificate Resolvers (for self-signed initially)
certificatesResolvers:
  selfsigned:
    acme:
      email: "<EMAIL>" # Required by ACME, even for self-signed if using acme for it.
      storage: "/etc/traefik/acme.json"
      # For actual Let's Encrypt, you'd use httpChallenge or dnsChallenge
      # For local self-signed, we'll primarily provide certs directly in dynamic_conf or via labels
      # However, Traefik can generate self-signed if no certs are found for a TLS-enabled router.
      # We will provide our own self-signed certs via file provider in dynamic_conf.yml for more control.
      caServer: "https://acme-staging-v02.api.letsencrypt.org/directory" # Staging for testing LE

log:
  level: INFO # (DEBUG, INFO, WARNING, ERROR, CRITICAL)
  filePath: "/etc/traefik/traefik.log"
accessLog:
  filePath: "/etc/traefik/access.log"
  bufferingSize: 100
