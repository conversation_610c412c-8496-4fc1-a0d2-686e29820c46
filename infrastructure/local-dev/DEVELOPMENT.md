# Polyrepo Development Guide

This document provides comprehensive instructions for setting up and running the polyrepo application in different development environments. Choose the mode that best suits your needs:

1. **Local Services Development** - Run services locally with hot-reload, infrastructure in Docker
2. **Enhanced Bundled Volume Development** - **[RECOMMENDED]** Ultra-fast containerized development with webpack watch + volume mounting
3. **Production-like Docker Build** - Full Docker build simulation for production testing  
4. **Bundled Docker Volume** - Current main development mode using pre-built bundles with volume mounting

## Prerequisites

- **Docker and Docker Compose** - Required for all modes
- **Node.js 18+ with Yarn** - Required for local development and bundling
- **Prisma CLI** - For database migrations
- **Git** - For version control

## Development Modes

### 1. Local Services Development

Run application services locally while using Docker for infrastructure services (Keycloak, databases, Redis). This mode offers the fastest development cycle with hot-reloading and direct debugging.

**When to use this mode:**
- When actively developing a specific service with debugging
- When you need direct access to debugger and dev tools
- When you need the fastest possible iteration cycle for service code only

**Steps to start:**

```bash
# Start infrastructure and local services
yarn start:dev:local
```

This will:
- Build all shared libraries
- Start infrastructure (Keycloak, databases, Redis) in Docker
- Run Keycloak setup and database migrations
- Provide instructions for starting application services locally

**Endpoints:**
- API Gateway: Not running (start services individually)
- Auth Service: http://localhost:3001 (when started)
- User Service: http://localhost:3002 (when started)
- Keycloak Admin: http://localhost:8080 (admin/admin)
- Grafana: http://localhost:3200 (admin/admin)

---

### 2. Enhanced Bundled Volume Development **[RECOMMENDED]**

Ultra-fast development workflow with automatic library watching, webpack bundling, and volume mounting. This provides the fastest possible iteration cycle while maintaining full containerization and observability.

**When to use this mode:**
- **PRIMARY development mode** for daily work
- When working across multiple services and libraries
- When you need full observability and monitoring
- When you want containerized consistency with maximum speed

**Key Features:**
- **5-10 second library change feedback** with automatic rebuilds
- **3-5 second service change feedback** with webpack watch
- **Automatic Prisma client generation** via webpack plugins
- **Automatic database migrations** via container startup
- **Service-prefixed HTTP modules** for precise observability
- **Centralized CLI** for process management and monitoring
- **Real-time health checks** and log aggregation

**Quick Start:**

```bash
# Start the enhanced development workflow
yarn dev:cli start

# Monitor status
yarn dev:cli status
yarn dev:cli health

# View logs for specific processes
yarn dev:cli logs webpack-auth
yarn dev:cli logs library-watcher

# Stop everything
yarn dev:cli stop
```

**Common Development Workflow:**
1. `yarn dev:cli start` - Start all processes (includes Prisma setup)
2. Make changes to libraries (`libs/*/src/`) or services (`services/*/src/`)
3. Changes are automatically detected and rebuilt
4. Prisma schema changes trigger automatic client generation
5. Containers pick up changes via volume mounting
6. Database migrations run automatically on container restart
7. Test changes in browser/API
8. `yarn dev:cli stop` - Clean shutdown

**Environment Configuration:**
- Each service uses `.env.bundling` for Docker container environment
- Containers use internal port 3000 with external port mapping
- Database connections automatically configured for Docker networking
- Prisma client generated to workspace root for multi-service compatibility
- Keycloak configured with test realm (`polyrepo-test`) and test users
- Auth service uses container-to-container networking (`keycloak:8080`)

**Process Management:**
- All processes tracked with PIDs in `/infrastructure/local-dev/watcher/pid/`
- Centralized logging in `/infrastructure/local-dev/watcher/logs/`
- Graceful shutdown and cleanup with `yarn dev:cli cleanup`

**Endpoints:**
- API Gateway: http://localhost:3000
- Auth Service: http://localhost:3001 (direct)
- User Service: http://localhost:3002 (direct)
- Keycloak Admin: http://localhost:8080 (admin/admin)
- Grafana: http://localhost:3200 (admin/admin)

**Keycloak Test Users (realm: polyrepo-test):**
- `<EMAIL>` / `testpassword` (role: user)
- `<EMAIL>` / `testpassword` (role: admin)
- `<EMAIL>` / `testpassword` (role: superadmin)

---

### 3. Production-like Docker Build **[NEEDS RESTORATION]**

Simulate a production environment by building and running all services in Docker using proper Node.js base images with npm/yarn install inside containers. This mode replicates production deployment most closely.

**When to use this mode:**
- Final testing before production deployment
- Verifying production build processes
- Testing deployment configurations
- Validating containerization without host dependencies

**Current Status:** 
⚠️ **NEEDS RESTORATION** - This mode hasn't been maintained recently and may be broken. Required for production deployment preparation.

**Expected Features (when restored):**
- Full production-like Docker builds using `Dockerfile` (not `Dockerfile.webpack`)
- Services install dependencies inside containers
- No volume mounting - complete isolation
- Proper Node.js base images with production configurations
- Would be suitable for Fly.io deployment with adjustments

**Restoration Priority:**
- **HIGH** when approaching production deployment
- Currently not blocking development workflow

---

### 4. Bundled Docker Build **[PRODUCTION-READY]**

Self-contained Docker containers using webpack bundles built and copied into images (not volume mounted). This mode closely resembles production deployment and is suitable for Fly.io-style platforms.

**How it works:**
- Libraries are bundled with services using webpack
- Services are built using `Dockerfile.webpack` with complete bundle copying
- Containers are self-contained with no host dependencies
- No volume mounting - everything copied into container image

**Production Characteristics:**
- **Self-contained**: No volume dependencies, suitable for cloud deployment
- **Fly.io Compatible**: Perfect for platforms without persistent volumes
- **Build Process**: Full Docker build with bundle copying
- **Isolation**: Complete container isolation from host

**Current Status:**
- **File**: `docker-compose.bundled-dev.yml` 
- **Build Mode**: Uses `Dockerfile.webpack` for all services
- **Container Names**: `polyrepo_*_bundled` (different from volume mode)

**Commands:**
```bash
# Build libraries first (required)
yarn build:libs

# Start bundled build mode (self-contained containers)
docker-compose -f infrastructure/local-dev/docker-compose.bundled-dev.yml up -d --build
```

**Performance Characteristics:**
- **Service changes**: ~30-60 seconds (full Docker rebuild)
- **Library changes**: ~60+ seconds (full rebuild required)
- **Cold start**: ~60+ seconds (Docker build time)
- **Memory usage**: Higher (complete bundle per container)
- **Production parity**: Highest - exactly like production deployment

**Use Cases:**
- **Pre-production testing** with exact deployment simulation
- **Fly.io deployment preparation** 
- **Performance testing** under production-like conditions
- **CI/CD pipeline validation**

---

## Development Mode Comparison

| Mode | Speed | Debugging | Prod-like | Library Changes | Service Changes | Use Case |
|------|-------|-----------|-----------|----------------|----------------|----------|
| **Local Services** | ⚡⚡⚡ | ✅ Full | ❌ No | ~5s | ~1s | Heavy debugging |
| **Enhanced Bundled Volume** | ⚡⚡⚡ | ✅ Good | ✅ Good | ~5-10s | ~3-5s | **Daily development** |
| **Production Docker** | ⚡ | ❌ Limited | ✅ Yes | ~60s+ | ~60s+ | Pre-deployment testing |
| **Bundled Docker Build** | ⚡ | ✅ Limited | ✅ Highest | ~60s+ | ~30-60s | **Fly.io preparation** |

## Recommended Workflow

**For daily development:**
1. Use **Enhanced Bundled Volume Development** (`yarn dev:cli start`)
2. Make changes to services and libraries
3. Monitor with `yarn dev:cli status` and `yarn dev:cli logs <service>`
4. Use `yarn dev:cli stop` for clean shutdown

**For production preparation:**
1. Restore **Production-like Docker Build** mode
2. Test complete build and deployment process
3. Validate all containerization and dependency management

**For debugging specific issues:**
1. Use **Local Services Development** for direct debugging access
2. Run specific services locally while keeping infrastructure in Docker

## Development CLI Reference

The enhanced development workflow provides a comprehensive CLI for process and workflow management:

```bash
# Core workflow commands
yarn dev:cli start [--auto]     # Start development workflow
yarn dev:cli stop               # Stop all processes and containers
yarn dev:cli restart            # Restart entire workflow
yarn dev:cli status             # Show process status
yarn dev:cli health             # Comprehensive health check

# Monitoring and debugging
yarn dev:cli logs [service]     # View logs (library-watcher, webpack-auth, etc.)
yarn dev:cli cleanup            # Clean up dead processes

# Advanced features
yarn dev:cli bundle-test        # Test bundle optimization framework
```

**Process Management:**
- PIDs tracked in `/infrastructure/local-dev/watcher/pid/`
- Logs centralized in `/infrastructure/local-dev/watcher/logs/`
- Graceful shutdown and cleanup capabilities
- Real-time status monitoring

## Infrastructure Services

All development modes include these infrastructure services:

- **Keycloak**: http://localhost:8080 (admin/admin) - Authentication server
  - **Production realm**: `polyrepo-realm` with client `auth-service-client`
  - **Test realm**: `polyrepo-test` with client `auth-service-test-client`
  - **Auto-configured**: Test users, roles, and service account permissions
- **PostgreSQL (User Service)**: localhost:5433 - User data
- **PostgreSQL (Keycloak)**: localhost:5432 - Keycloak data  
- **Redis**: localhost:6379 - Caching and messaging
- **Grafana**: http://localhost:3200 (admin/admin) - Monitoring and logs
- **Prometheus**: http://localhost:9090 - Metrics collection
- **Loki**: http://localhost:3100 - Log aggregation
- **Tempo**: http://localhost:3201 - Distributed tracing
- **Pyroscope**: http://localhost:4040 - Continuous profiling

## Library Development

All development modes support shared library development in `/libs/`:

- **Observability**: Logging, metrics, tracing
- **HTTP**: Modern HTTP/2 client with caching and circuit breakers
- **Auth**: JWT validation and Keycloak integration
- **Messaging**: Event-driven architecture with Redis Streams
- **Caching**: Redis-based caching with decorators
- **Error Handling**: Centralized error management with correlation IDs
- **Resilience**: Circuit breaker patterns for failure isolation

**Library build order** (managed automatically):
`observability` → `shared-types` → `shared-utils` → `keycloak-client` → `auth-common` → `error-handling` → `resilience` → `http` → `caching` → `messaging`

## Authentication Testing

**Testing Auth Flows:**

```bash
# Test user registration
curl -X POST http://localhost:3001/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User"}'

# Test user login
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpassword"}'

# Test protected endpoint via API Gateway
curl -X GET http://localhost:3000/auth/profile \
  -H "Authorization: Bearer <jwt_token>"
```

**Available Test Users:**
- `<EMAIL>` - Basic user role
- `<EMAIL>` - Admin role with user management
- `<EMAIL>` - Full administrative access
- All test users have password: `testpassword`

## Next Steps

1. **Continue using Enhanced Bundled Volume Development** as the primary mode
2. **Authentication integration complete** - ready for full application development
3. **Restore Production-like Docker Build** when approaching deployment
4. **Consider Fly.io deployment** using volumes for production scaling