#!/usr/bin/env node

/**
 * Development Workflow CLI
 * Unified command-line interface for managing development processes
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

const PROJECT_ROOT = path.resolve(__dirname, '../../../..');
const WATCHER_DIR = path.join(__dirname, '..');
const ProcessManager = require('../daemon/process-manager');

class DevCLI {
  constructor() {
    this.manager = new ProcessManager();
  }

  async executeCommand(command, args) {
    switch (command) {
      case 'start':
        return this.start(args);
      case 'stop':
        return this.stop();
      case 'restart':
        return this.restart();
      case 'status':
        return this.status();
      case 'health':
        return this.health();
      case 'logs':
        return this.logs(args);
      case 'cleanup':
        return this.cleanup();
      case 'bundle-test':
        return this.bundleTest();
      default:
        this.showHelp();
    }
  }

  async start(args) {
    const mode = args.includes('--auto') ? 'auto' : 'manual';
    console.log(`🚀 Starting development workflow in ${mode} mode...`);
    
    const daemonPath = path.join(WATCHER_DIR, 'daemon/dev-workflow-daemon.js');
    const child = spawn('node', [daemonPath, mode], {
      cwd: PROJECT_ROOT,
      stdio: 'inherit'
    });
    
    return new Promise((resolve) => {
      child.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Development workflow started successfully');
        } else {
          console.log(`❌ Failed to start (exit code: ${code})`);
        }
        resolve(code);
      });
    });
  }

  async stop() {
    console.log('🛑 Stopping all development processes...');
    
    // Stop managed processes
    this.manager.killAll();
    
    // Stop Docker containers
    return new Promise((resolve) => {
      const dockerCompose = path.join(PROJECT_ROOT, 'infrastructure/local-dev/docker-compose.bundled-dev-volume.yml');
      exec(`docker-compose -f ${dockerCompose} down`, { cwd: PROJECT_ROOT }, (error, stdout, stderr) => {
        if (error) {
          console.log(`❌ Error stopping containers: ${error.message}`);
        } else {
          console.log('✅ All processes and containers stopped');
        }
        resolve(error ? 1 : 0);
      });
    });
  }

  async restart() {
    console.log('🔄 Restarting development workflow...');
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2s
    return this.start([]);
  }

  async status() {
    console.log('📊 Development Process Status');
    console.log('=============================');
    
    const status = this.manager.getStatus();
    
    if (status.length === 0) {
      console.log('No managed processes running');
      return 0;
    }
    
    console.log('Name                   PID      Status    Log File');
    console.log('─'.repeat(65));
    
    status.forEach(proc => {
      const statusIcon = proc.running ? '✅ Running' : '❌ Dead';
      const logPath = proc.logFile ? path.basename(proc.logFile) : 'none';
      const nameCol = proc.name.padEnd(20);
      const pidCol = proc.pid.toString().padEnd(8);
      const statusCol = statusIcon.padEnd(10);
      
      console.log(`${nameCol} ${pidCol} ${statusCol} ${logPath}`);
    });
    
    return 0;
  }

  async health() {
    console.log('🏥 Running comprehensive health check...');
    
    return new Promise((resolve) => {
      const healthScript = path.join(PROJECT_ROOT, 'infrastructure/local-dev/dev-health-check.sh');
      const child = spawn('bash', [healthScript], {
        cwd: path.join(PROJECT_ROOT, 'infrastructure/local-dev'),
        stdio: 'inherit'
      });
      
      child.on('close', (code) => {
        resolve(code);
      });
    });
  }

  async logs(args) {
    const service = args[0];
    const logsDir = path.join(WATCHER_DIR, 'logs');
    
    if (service) {
      const logFile = path.join(logsDir, `${service}.log`);
      if (fs.existsSync(logFile)) {
        console.log(`📋 Last 20 lines from ${service}:`);
        const { execSync } = require('child_process');
        try {
          const output = execSync(`tail -20 "${logFile}"`, { encoding: 'utf8' });
          console.log(output);
        } catch (error) {
          console.log(`❌ Error reading log: ${error.message}`);
        }
      } else {
        console.log(`❌ Log file not found: ${logFile}`);
        return 1;
      }
    } else {
      console.log('📋 Available log files:');
      if (fs.existsSync(logsDir)) {
        const logFiles = fs.readdirSync(logsDir).filter(f => f.endsWith('.log'));
        logFiles.forEach(file => {
          console.log(`  - ${file.replace('.log', '')}`);
        });
        console.log('\\nUsage: dev-cli logs <service-name>');
      } else {
        console.log('No log files found');
      }
    }
    
    return 0;
  }

  async cleanup() {
    console.log('🧹 Cleaning up development processes...');
    const cleaned = await this.manager.cleanup();
    console.log(`✅ Cleaned up ${cleaned} dead processes`);
    return 0;
  }

  async bundleTest() {
    console.log('📦 Testing Bundle Optimization Framework');
    console.log('=======================================');
    
    const BundleOptimizer = require('../daemon/bundle-optimizer');
    const optimizer = new BundleOptimizer();
    
    try {
      await optimizer.testOptimization();
      console.log('✅ Bundle optimization framework test completed');
      return 0;
    } catch (error) {
      console.log(`❌ Bundle optimization test failed: ${error.message}`);
      return 1;
    }
  }

  showHelp() {
    console.log(`
🛠️  Development Workflow CLI

Usage: dev-cli <command> [options]

Commands:
  start [--auto]    Start development workflow (manual or auto-restart mode)
  stop              Stop all processes and containers
  restart           Restart the entire development workflow
  status            Show status of all managed processes
  health            Run comprehensive health check
  logs [service]    Show available logs or tail specific service logs
  cleanup           Clean up dead processes
  bundle-test       Test bundle optimization features

Examples:
  dev-cli start --auto     # Start with auto-restart
  dev-cli logs api-gateway # Tail API gateway logs
  dev-cli health           # Full health check
  dev-cli status           # Process status overview

For more information, see: infrastructure/local-dev/watcher/README.md
`);
  }
}

// CLI entry point
if (require.main === module) {
  const cli = new DevCLI();
  const [,, command, ...args] = process.argv;
  
  if (!command) {
    cli.showHelp();
    process.exit(0);
  }
  
  cli.executeCommand(command, args)
    .then(code => process.exit(code || 0))
    .catch(error => {
      console.error('❌ CLI Error:', error.message);
      process.exit(1);
    });
}

module.exports = DevCLI;