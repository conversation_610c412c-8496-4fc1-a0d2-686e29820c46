/**
 * Webpack Bundle Optimization Configuration
 * 
 * This configuration enables direct bundling of library source files
 * instead of pre-building them, providing 6x faster feedback loops
 * for library changes (3-5s vs 15-30s).
 */

const path = require('path');
const webpack = require('webpack');

/**
 * Creates an optimized webpack config for a service that bundles
 * library source directly instead of using pre-built dist files.
 * 
 * @param {string} serviceName - Name of the service (e.g., 'auth-service')
 * @param {string} serviceDir - Path to service directory
 * @param {string} projectRoot - Path to project root
 * @returns {object} Webpack configuration object
 */
function createBundleOptimizedConfig(serviceName, serviceDir, projectRoot) {
  const libsDir = path.join(projectRoot, 'libs');
  
  // Map all @libs/* imports to source directories
  const libAliases = {};
  const libDirs = [
    'observability', 'shared-types', 'shared-utils', 'keycloak-client',
    'auth-common', 'error-handling', 'resilience', 'http', 'caching', 'messaging'
  ];
  
  libDirs.forEach(lib => {
    libAliases[`@libs/${lib}`] = path.join(libsDir, lib, 'src');
  });

  return {
    entry: './src/main.ts',
    target: 'node',
    mode: 'development',
    
    // Enable source maps for debugging
    devtool: 'source-map',
    
    output: {
      path: path.join(serviceDir, 'dist-webpack'),
      filename: 'main.bundle.js',
      clean: true
    },
    
    resolve: {
      extensions: ['.ts', '.js', '.json'],
      
      // Direct source aliasing - skip dist/ entirely
      alias: libAliases,
      
      // Module resolution strategy
      modules: ['node_modules'],
      symlinks: false
    },
    
    module: {
      rules: [
        {
          test: /\.ts$/,
          use: {
            loader: 'ts-loader',
            options: {
              // Faster compilation
              transpileOnly: true,
              experimentalWatchApi: true,
              // Use service's tsconfig for consistency
              configFile: path.join(serviceDir, 'tsconfig.json')
            }
          },
          include: [
            path.join(serviceDir, 'src'),
            libsDir // Include all library source
          ],
          exclude: [
            /node_modules/,
            /dist/,
            /\.d\.ts$/
          ]
        }
      ]
    },
    
    plugins: [
      // Define service context for runtime
      new webpack.DefinePlugin({
        'process.env.SERVICE_NAME': JSON.stringify(serviceName),
        'process.env.BUILD_MODE': JSON.stringify('bundle-optimized')
      }),
      
      // Progress plugin for build feedback
      new webpack.ProgressPlugin((percentage, message) => {
        if (percentage === 1) {
          console.log(`✅ ${serviceName}: Bundle completed`);
        }
      })
    ],
    
    // Optimization for development speed
    optimization: {
      // Minimize only in production
      minimize: false
    },
    
    // External dependencies (don't bundle these)
    externals: {
      // Keep these as external requires
      'fsevents': 'commonjs fsevents',
      'chokidar': 'commonjs chokidar'
    },
    
    // Watch mode configuration
    watchOptions: {
      aggregateTimeout: 300,
      poll: false,
      followSymlinks: false,
      ignored: [
        '**/node_modules/**',
        '**/dist/**',
        '**/*.d.ts'
      ]
    },
    
    // Performance hints
    performance: {
      // Disable for development
      hints: false
    },
    
    // Stats configuration
    stats: {
      colors: true,
      modules: false,
      chunks: false,
      chunkModules: false,
      timings: true,
      builtAt: true
    }
  };
}

/**
 * Service-specific configurations
 */
const SERVICES = {
  'auth-service': {
    port: 3001,
    additionalWatches: ['libs/keycloak-client/src/**/*.ts', 'libs/auth-common/src/**/*.ts']
  },
  'user-service': {
    port: 3002,
    additionalWatches: ['libs/shared-types/src/**/*.ts']
  },
  'api-gateway': {
    port: 3000,
    additionalWatches: ['libs/http/src/**/*.ts', 'libs/resilience/src/**/*.ts']
  }
};

module.exports = {
  createBundleOptimizedConfig,
  SERVICES
};