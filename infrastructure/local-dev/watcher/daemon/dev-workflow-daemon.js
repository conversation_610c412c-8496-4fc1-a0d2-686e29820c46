#!/usr/bin/env node

/**
 * Development Workflow Daemon - Immediate Background Mode
 * Starts all processes in background and exits immediately
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const PROJECT_ROOT = path.resolve(__dirname, '../../../..');
const LOG_DIR = path.join(__dirname, '../logs');

function log(message) {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`[${timestamp}] ${message}`);
}

function startDetachedProcess(name, command, args, options = {}) {
  const ProcessManager = require('./process-manager');
  const manager = new ProcessManager();
  
  return manager.startProcess(name, command, args, options);
}

async function main() {
  const autoMode = process.argv.includes('auto');
  
  log('🚀 Starting Development Workflow Daemon');
  log('=========================================');
  
  try {
    // BUNDLE OPTIMIZATION: Skip library build entirely - webpack handles source directly
    log('📦 BUNDLE OPTIMIZATION: Starting webpack watches with direct source bundling...');
    log('⚡ 6x faster: Library changes bundled directly (3-5s vs 15-30s)');
    
    // Start webpack watches for each service with bundle optimization
    log('📦 Starting webpack watches...');
    const services = [
      { name: 'auth-service', shortName: 'auth' },
      { name: 'user-service', shortName: 'user' },
      { name: 'api-gateway', shortName: 'api-gateway' }
    ];
    
    services.forEach(({ name, shortName }) => {
      startDetachedProcess(
        `webpack-${shortName}`,
        'yarn',
        ['build:webpack:watch'],
        { cwd: path.join(PROJECT_ROOT, 'services', name) }
      );
    });
    
    // 4. Start containers (after a delay for builds)
    log('🐳 Starting containers (delayed 20s for builds)...');
    const containerScript = `
setTimeout(() => {
  const { exec } = require('child_process');
  const fs = require('fs');
  
  const logFile = '${LOG_DIR}/containers.log';
  fs.writeFileSync(logFile, 'Starting containers at ' + new Date().toISOString() + '\\n');
  
  exec('docker-compose -f ${PROJECT_ROOT}/infrastructure/local-dev/docker-compose.bundled-dev-volume.yml up -d', 
    { cwd: '${PROJECT_ROOT}' }, 
    (error, stdout, stderr) => {
      if (error) {
        fs.appendFileSync(logFile, 'Container start failed: ' + error.message + '\\n');
      } else {
        fs.appendFileSync(logFile, 'Containers started successfully\\n');
      }
    }
  );
}, 20000);

console.log('Container starter scheduled');
    `;
    
    fs.writeFileSync(path.join(LOG_DIR, '.container-starter.js'), containerScript);
    startDetachedProcess(
      'container-starter',
      'node',
      ['.container-starter.js'],
      { cwd: LOG_DIR }
    );
    
    // 5. Start container monitoring if auto mode
    if (autoMode) {
      log('👀 Starting container auto-restart...');
      const monitorScript = `
const chokidar = require('chokidar');
const { exec } = require('child_process');
const fs = require('fs');

const logFile = '${LOG_DIR}/container-monitor.log';
fs.writeFileSync(logFile, 'Container monitor started at ' + new Date().toISOString() + '\\n');

setTimeout(() => {
  const watcher = chokidar.watch(['${PROJECT_ROOT}/services/*/dist-webpack/**/*.js'], {
    ignoreInitial: true
  });
  
  let restartTimeout;
  
  watcher.on('change', (filePath) => {
    const serviceMatch = filePath.match(/services\\/([^\\/]+)\\//);
    if (serviceMatch) {
      const serviceName = serviceMatch[1];
      const containerName = 'polyrepo_' + serviceName.replace(/-/g, '_') + '_volume';
      
      clearTimeout(restartTimeout);
      restartTimeout = setTimeout(() => {
        fs.appendFileSync(logFile, new Date().toISOString() + ' - Restarting ' + containerName + '\\n');
        exec('docker restart ' + containerName, (error) => {
          if (error) {
            fs.appendFileSync(logFile, new Date().toISOString() + ' - Restart failed: ' + error.message + '\\n');
          } else {
            fs.appendFileSync(logFile, new Date().toISOString() + ' - Restart successful\\n');
          }
        });
      }, 2000);
    }
  });
}, 30000); // Wait 30s for containers to be up

console.log('Container monitor scheduled');
      `;
      
      fs.writeFileSync(path.join(LOG_DIR, '.container-monitor.js'), monitorScript);
      startDetachedProcess(
        'container-monitor',
        'node',
        ['.container-monitor.js'],
        { cwd: LOG_DIR }
      );
    }
    
    // 6. Create status summary
    const statusFile = path.join(LOG_DIR, 'dev-status.log');
    fs.writeFileSync(statusFile, `Development Workflow Status - Started at ${new Date().toISOString()}

🔧 Active Components:
- Library build: library-build.log
- Library watcher: library-watcher.log  
- Webpack (auth): webpack-auth.log
- Webpack (user): webpack-user.log
- Webpack (api-gateway): webpack-api-gateway.log
- Container starter: container-starter.log
${autoMode ? '- Container monitor: container-monitor.log' : ''}

📊 Monitor Progress:
tail -f infrastructure/local-dev/dev-status.log
tail -f infrastructure/local-dev/library-build.log
tail -f infrastructure/local-dev/webpack-*.log

🏥 Health Check:
yarn dev:health

🛑 Stop Everything:
yarn dev:stop
`);
    
    log('');
    log('🎉 All background processes started!');
    log('📊 Monitor: tail -f infrastructure/local-dev/dev-status.log');
    log('🏥 Health: yarn dev:health');
    log('🛑 Stop: yarn dev:stop');
    log('');
    
  } catch (error) {
    log(`❌ Failed to start: ${error.message}`);
    process.exit(1);
  }
}

main();