#!/usr/bin/env node

/**
 * Development Process Manager
 * Centralized management for all development background processes
 */

const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

const PROJECT_ROOT = path.resolve(__dirname, '../../../..');
const PID_DIR = path.join(__dirname, '../pid');
const LOG_DIR = path.join(__dirname, '../logs');

class ProcessManager {
  constructor() {
    this.processes = new Map();
    this.ensurePidDir();
  }

  ensurePidDir() {
    if (!fs.existsSync(PID_DIR)) {
      fs.mkdirSync(PID_DIR, { recursive: true });
    }
    if (!fs.existsSync(LOG_DIR)) {
      fs.mkdirSync(LOG_DIR, { recursive: true });
    }
  }

  log(message) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] ${message}`);
  }

  // Start a managed process
  startProcess(name, command, args, options = {}) {
    const logFile = path.join(LOG_DIR, `${name}.log`);
    
    // Kill existing process if running
    this.killProcess(name);
    
    // Create log file
    fs.writeFileSync(logFile, `Starting ${name} at ${new Date().toISOString()}\n`);
    fs.appendFileSync(logFile, `Command: ${command} ${args.join(' ')}\n`);
    fs.appendFileSync(logFile, `CWD: ${options.cwd || PROJECT_ROOT}\n`);
    fs.appendFileSync(logFile, `PATH: ${process.env.PATH}\n`);
    
    const childProcess = spawn(command, args, {
      cwd: options.cwd || PROJECT_ROOT,
      detached: true,
      stdio: ['ignore', fs.openSync(logFile, 'a'), fs.openSync(logFile, 'a')],
      env: { ...process.env } // Inherit full environment including PATH and NVM vars
    });

    // Handle process errors
    childProcess.on('error', (error) => {
      this.log(`❌ Failed to start ${name}: ${error.message}`);
      throw error;
    });
    
    // Save PID (with retry for slow-starting processes like yarn)
    const pidFile = path.join(PID_DIR, `${name}.pid`);
    if (childProcess.pid) {
      fs.writeFileSync(pidFile, childProcess.pid.toString());
    } else {
      // For yarn/npm processes, wait a moment for PID assignment
      setTimeout(() => {
        if (childProcess.pid) {
          fs.writeFileSync(pidFile, childProcess.pid.toString());
        } else {
          this.log(`⚠️  Warning: ${name} started but PID not immediately available`);
        }
      }, 100);
    }
    
    this.processes.set(name, {
      pid: childProcess.pid || 'pending',
      name,
      command: `${command} ${args.join(' ')}`,
      logFile,
      startTime: new Date()
    });
    
    childProcess.unref(); // Don't keep parent alive
    
    this.log(`✅ Started ${name} (PID: ${childProcess.pid || 'pending'})`);
    return childProcess.pid || 'pending';
  }

  // Kill a specific process
  killProcess(name) {
    const pidFile = path.join(PID_DIR, `${name}.pid`);
    
    if (fs.existsSync(pidFile)) {
      try {
        const pid = parseInt(fs.readFileSync(pidFile, 'utf8'));
        process.kill(pid, 'SIGTERM');
        fs.unlinkSync(pidFile);
        this.processes.delete(name);
        this.log(`🛑 Stopped ${name} (PID: ${pid})`);
        return true;
      } catch (err) {
        // Process already dead, clean up PID file
        fs.unlinkSync(pidFile);
        this.processes.delete(name);
        return false;
      }
    }
    return false;
  }

  // Kill all managed processes
  killAll() {
    this.log('🧹 Stopping all managed processes...');
    
    const pidFiles = fs.readdirSync(PID_DIR).filter(f => f.endsWith('.pid'));
    let stopped = 0;
    
    for (const pidFile of pidFiles) {
      const name = path.basename(pidFile, '.pid');
      if (this.killProcess(name)) {
        stopped++;
      }
    }
    
    // Also kill webpack processes (they might not be tracked)
    try {
      exec('pkill -f webpack', () => {});
      exec('pkill -f "node.*library-watcher"', () => {});
      exec('pkill -f "node.*container-monitor"', () => {});
    } catch (err) {
      // Ignore errors
    }
    
    this.log(`✅ Stopped ${stopped} processes`);
    return stopped;
  }

  // Check if a process is running
  isRunning(name) {
    const pidFile = path.join(PID_DIR, `${name}.pid`);
    
    if (!fs.existsSync(pidFile)) {
      return false;
    }
    
    try {
      const pid = parseInt(fs.readFileSync(pidFile, 'utf8'));
      process.kill(pid, 0); // Check if process exists
      return true;
    } catch (err) {
      // Process is dead, clean up
      fs.unlinkSync(pidFile);
      this.processes.delete(name);
      return false;
    }
  }

  // Get status of all processes
  getStatus() {
    const pidFiles = fs.readdirSync(PID_DIR).filter(f => f.endsWith('.pid'));
    const status = [];
    
    for (const pidFile of pidFiles) {
      const name = path.basename(pidFile, '.pid');
      const pid = parseInt(fs.readFileSync(path.join(PID_DIR, pidFile), 'utf8'));
      const isRunning = this.isRunning(name);
      const logFile = path.join(LOG_DIR, `${name}.log`);
      
      status.push({
        name,
        pid,
        running: isRunning,
        logFile: fs.existsSync(logFile) ? logFile : null
      });
    }
    
    return status;
  }

  // Display status
  showStatus() {
    const status = this.getStatus();
    
    if (status.length === 0) {
      this.log('📭 No managed processes running');
      return;
    }
    
    this.log('📊 Process Status:');
    console.log('');
    console.log('Name                   PID      Status    Log File');
    console.log('─'.repeat(65));
    
    for (const proc of status) {
      const statusIcon = proc.running ? '✅ Running' : '❌ Dead';
      const logPath = proc.logFile ? path.basename(proc.logFile) : 'none';
      console.log(`${proc.name.padEnd(20)} ${proc.pid.toString().padEnd(8)} ${statusIcon.padEnd(9)} ${logPath}`);
    }
    
    console.log('');
    this.log(`📈 Total: ${status.filter(p => p.running).length}/${status.length} running`);
  }

  // Restart a specific process
  restartProcess(name) {
    this.log(`🔄 Restarting ${name}...`);
    
    // This is a simple restart - kill and let the user restart
    // For more complex restart, we'd need to store the original command
    this.killProcess(name);
    this.log(`ℹ️  Process ${name} stopped. Use the original start command to restart.`);
  }

  // Tail logs for a process
  tailLogs(name, lines = 20) {
    const logFile = path.join(LOG_DIR, `${name}.log`);
    
    if (!fs.existsSync(logFile)) {
      this.log(`❌ No log file found for ${name}`);
      return;
    }
    
    exec(`tail -n ${lines} "${logFile}"`, (error, stdout) => {
      if (error) {
        this.log(`❌ Error reading logs: ${error.message}`);
        return;
      }
      
      console.log(`\n📋 Last ${lines} lines from ${name}:`);
      console.log('─'.repeat(50));
      console.log(stdout);
    });
  }

  // Clean up dead processes
  cleanup() {
    const status = this.getStatus();
    let cleaned = 0;
    for (const proc of status) {
      if (!proc.running) {
        this.killProcess(proc.name); // This will clean up the PID file
        cleaned++;
      }
    }
    this.log(`🧹 Cleaned up ${cleaned} dead processes`);
    return cleaned;
  }
}

// CLI Interface
function main() {
  const manager = new ProcessManager();
  const command = process.argv[2];
  const arg = process.argv[3];

  switch (command) {
    case 'start':
      console.log('Use specific start commands (yarn start:dev:bundled:watch:enhanced)');
      break;
      
    case 'stop':
      if (arg) {
        manager.killProcess(arg);
      } else {
        manager.killAll();
      }
      break;
      
    case 'status':
      manager.showStatus();
      break;
      
    case 'restart':
      if (arg) {
        manager.restartProcess(arg);
      } else {
        console.log('Specify process name: node process-manager.js restart <name>');
      }
      break;
      
    case 'logs':
      if (arg) {
        manager.tailLogs(arg);
      } else {
        console.log('Specify process name: node process-manager.js logs <name>');
      }
      break;
      
    case 'cleanup':
      // Clean up dead PID files
      const status = manager.getStatus();
      let cleaned = 0;
      for (const proc of status) {
        if (!proc.running) {
          manager.killProcess(proc.name); // This will clean up the PID file
          cleaned++;
        }
      }
      manager.log(`🧹 Cleaned up ${cleaned} dead processes`);
      break;
      
    default:
      console.log(`
Development Process Manager

Usage: node process-manager.js <command> [args]

Commands:
  status              Show all managed processes
  stop [name]         Stop specific process or all processes  
  restart <name>      Restart specific process
  logs <name>         Show recent logs for process
  cleanup             Remove dead process entries

Examples:
  node process-manager.js status
  node process-manager.js stop library-watcher
  node process-manager.js stop                    # Stop all
  node process-manager.js logs webpack-auth
      `);
  }
}

if (require.main === module) {
  main();
}

module.exports = ProcessManager;