# Development Workflow Watcher

Centralized development workflow management system with process tracking, health monitoring, and bundle optimization for the polyrepo project.

## Structure

```
/watcher/
├── daemon/           # Core daemon scripts and process management
├── pid/             # Process ID files for tracking
├── logs/            # Centralized logging for all processes  
├── config/          # Configuration files and templates
└── cli/             # Command-line interface tools
```

## Quick Start

```bash
# Start enhanced development workflow
yarn dev:cli start

# Check system health  
yarn dev:cli health

# View process status
yarn dev:cli status

# Stop everything
yarn dev:cli stop
```

## Common Development Scenarios

### 🔄 When You Change Library Code (`libs/*/src/**/*.ts`)

**What happens automatically:**
1. Library watcher detects the change
2. Libraries are rebuilt automatically (shared dependencies)
3. Service webpack watches are triggered to rebuild
4. Containers pick up new bundles via volume mounting

**Manual verification:**
```bash
# Check if your changes were detected and rebuilt
yarn dev:cli logs library-watcher
yarn dev:cli logs webpack-auth  # or webpack-user, webpack-api-gateway

# Check overall status
yarn dev:cli status
```

**If automatic rebuild fails:**
```bash
# Manual rebuild and restart
yarn build:libs
yarn dev:restart
```

### 🔄 When You Change Service Code (`services/*/src/**/*.ts`)

**What happens automatically:**
1. Webpack watch detects the change
2. Service bundle is rebuilt (~3-5 seconds)
3. Container picks up new bundle immediately

**Manual verification:**
```bash
# Check if webpack rebuilt your service
yarn dev:cli logs webpack-auth  # Replace with your service

# Check service health
curl http://localhost:3000/health  # or 3001/3002 for direct service access
```

**If changes aren't reflected:**
```bash
# Force container restart
yarn dev:restart

# Or restart specific container
docker restart polyrepo_auth_service_volume
```

### 📊 Monitoring Changes

**Quick status check:**
```bash
yarn dev:cli status    # Shows all managed processes
yarn dev:cli health    # Comprehensive health check
```

**Detailed monitoring:**
```bash
# Watch specific logs
yarn dev:cli logs library-watcher
yarn dev:cli logs webpack-auth
yarn dev:cli logs webpack-user
yarn dev:cli logs webpack-api-gateway

# Check container logs
docker logs polyrepo_api_gateway_volume --tail=20
```

**Verify your specific changes were picked up:**
1. Check the log timestamps match when you made changes
2. Look for "Build completed" or "Bundle completed" messages
3. Verify container restart timestamps if using auto-restart mode

## CLI Commands

| Command | Description | Use Case |
|---------|-------------|----------|
| `yarn dev:cli start [--auto]` | Start development workflow | Beginning work session |
| `yarn dev:cli status` | Show all running processes | Check what's running |
| `yarn dev:cli health` | Comprehensive health check | Troubleshoot issues |
| `yarn dev:cli stop` | Stop all processes + containers | End work session |
| `yarn dev:cli restart` | Restart entire workflow | Major changes/fixes |
| `yarn dev:cli logs [service]` | View/tail service logs | Debug specific issues |
| `yarn dev:cli cleanup` | Clean up dead processes | Cleanup after errors |
| `yarn dev:cli bundle-test` | Test bundle optimization | Performance testing |

## Manual Rebuild & Restart Scenarios

**When automatic rebuilds aren't enough:**

```bash
# Full clean rebuild (when in doubt)
yarn dev:cli stop
yarn build:libs
yarn dev:cli start

# Quick restart (containers only)
yarn dev:restart

# Specific service restart
docker restart polyrepo_auth_service_volume
docker restart polyrepo_user_service_volume  
docker restart polyrepo_api_gateway_volume
```

**When to use manual commands:**
- Major library interface changes
- TypeScript compilation errors
- Container networking issues
- After pulling major changes from git
- When automatic detection seems stuck

## Troubleshooting

**Changes not reflected:**
1. `yarn dev:cli status` - check process health
2. `yarn dev:cli logs <service>` - check for build errors
3. `yarn dev:restart` - restart containers
4. `yarn build:libs && yarn dev:restart` - full rebuild

**Processes stuck/dead:**
1. `yarn dev:cli cleanup` - remove dead processes
2. `yarn dev:cli stop` - clean shutdown
3. `yarn dev:cli start` - fresh start

**Performance issues:**
1. `yarn dev:cli bundle-test` - test optimization
2. Check disk space and Docker resources
3. Consider using manual restart mode vs auto-restart

## Development Flow Summary

**Typical workflow:**
1. **Start:** `yarn dev:cli start`
2. **Code:** Make changes in `libs/` or `services/`
3. **Monitor:** `yarn dev:cli logs <service>` (optional)
4. **Test:** Browser refresh or API test
5. **Iterate:** Repeat steps 2-4
6. **End:** `yarn dev:cli stop`

**Expected timing:**
- Library changes: ~5-10 seconds to live
- Service changes: ~3-5 seconds to live  
- Manual restarts: ~10-15 seconds

This provides the fastest possible development feedback loop while maintaining full observability and containerization.