#!/bin/sh

# Script to list users in Keycloak realm

TARGET_REALM_NAME="polyrepo-realm"
KC_ADMIN_REALM="master" # Admin user is in the master realm
KC_ADMIN_USER="admin"
KC_ADMIN_PASSWORD="admin"
KC_SERVER_URL="http://localhost:8080/"

KCADM_CMD="/opt/keycloak/bin/kcadm.sh"

echo "Attempting to login to Keycloak server ($KC_SERVER_URL) in realm '$KC_ADMIN_REALM' as user '$KC_ADMIN_USER'..."

# Login to the master realm (or the realm where the admin user exists)
$KCADM_CMD config credentials --realm $KC_ADMIN_REALM --user $KC_ADMIN_USER --password $KC_ADMIN_PASSWORD --server $KC_SERVER_URL --protocol openid-connect

LOGIN_EXIT_CODE=$?
if [ $LOGIN_EXIT_CODE -ne 0 ]; then
  echo "Error during Keycloak login. kcadm.sh exited with code $LOGIN_EXIT_CODE"
  exit $LOGIN_EXIT_CODE
fi

echo "Successfully logged in. Attempting to list users in realm '$TARGET_REALM_NAME'..."

# Now get users from the target realm
$KCADM_CMD get users -r $TARGET_REALM_NAME --fields username,email,enabled

GET_USERS_EXIT_CODE=$?
if [ $GET_USERS_EXIT_CODE -ne 0 ]; then
  echo "Error listing users from realm '$TARGET_REALM_NAME'. kcadm.sh exited with code $GET_USERS_EXIT_CODE"
  exit $GET_USERS_EXIT_CODE
fi

echo "Successfully listed users from realm '$TARGET_REALM_NAME'."
exit 0
