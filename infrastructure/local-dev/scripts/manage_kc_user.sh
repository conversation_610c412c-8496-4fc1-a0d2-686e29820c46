#!/bin/sh

# Script to manage Keycloak users (get ID, reset password, assign roles)

set -e # Exit immediately if a command exits with a non-zero status.

TARGET_REALM_NAME="polyrepo-realm"
KC_ADMIN_REALM="master"
KC_ADMIN_USER="admin"
KC_ADMIN_PASSWORD="admin"
KC_SERVER_URL="http://localhost:8080/"
KCADM_CMD="/opt/keycloak/bin/kcadm.sh"

# --- Login --- 
login() {
  echo "Attempting to login to Keycloak server ($KC_SERVER_URL) in realm '$KC_ADMIN_REALM' as user '$KC_ADMIN_USER'..."
  $KCADM_CMD config credentials --realm $KC_ADMIN_REALM --user $KC_ADMIN_USER --password $KC_ADMIN_PASSWORD --server $KC_SERVER_URL --protocol openid-connect
  echo "Successfully logged in."
}

# --- Get User ID by <PERSON>rna<PERSON> ---
get_user_id() {
  USERNAME=$1
  if [ -z "$USERNAME" ]; then
    echo "Usage: $0 get-user-id <username>"
    exit 1
  fi
  echo "Getting ID for user '$USERNAME' in realm '$TARGET_REALM_NAME'..."
  USER_ID=$($KCADM_CMD get users -r $TARGET_REALM_NAME -q username=$USERNAME --fields id --format csv --noquotes | head -n 1)
  if [ -z "$USER_ID" ]; then
    echo "Error: User '$USERNAME' not found in realm '$TARGET_REALM_NAME'."
    exit 1
  fi
  echo $USER_ID
}

# --- Reset User Password ---
reset_password() {
  USER_ID=$1
  NEW_PASSWORD=$2
  if [ -z "$USER_ID" ] || [ -z "$NEW_PASSWORD" ]; then
    echo "Usage: $0 reset-password <user-id> <new-password>"
    exit 1
  fi
  echo "Resetting password for user ID '$USER_ID' in realm '$TARGET_REALM_NAME'..."
  $KCADM_CMD update users/$USER_ID/reset-password -r $TARGET_REALM_NAME -s type=password -s value=$NEW_PASSWORD -s temporary=false -n
  echo "Password reset successfully for user ID '$USER_ID'."
}

# --- Get Role Representation (for adding to user) ---
get_role_representation() {
  ROLE_NAME=$1
  if [ -z "$ROLE_NAME" ]; then
    echo "Usage: $0 get-role <role-name>"
    exit 1
  fi
  echo "Getting representation for role '$ROLE_NAME' in realm '$TARGET_REALM_NAME'..."
  # This command gets the full JSON. We need name and id primarily.
  ROLE_REP=$($KCADM_CMD get roles -r $TARGET_REALM_NAME --rname $ROLE_NAME --fields name,id --format csv --noquotes | head -n 1)
  if [ -z "$ROLE_REP" ]; then
    echo "Error: Role '$ROLE_NAME' not found in realm '$TARGET_REALM_NAME'."
    exit 1
  fi
  echo $ROLE_REP # Outputs comma-separated: name,id
}

# --- Assign Realm Role to User ---
assign_realm_role() {
  USER_ID=$1
  ROLE_NAME=$2 # We need the role name, kcadm will find its representation
  if [ -z "$USER_ID" ] || [ -z "$ROLE_NAME" ]; then
    echo "Usage: $0 assign-role <user-id> <role-name>"
    exit 1
  fi
  echo "Assigning realm role '$ROLE_NAME' to user ID '$USER_ID' in realm '$TARGET_REALM_NAME'..."
  # kcadm add-roles expects the role representation (name and id). Simpler to just use role name directly.
  $KCADM_CMD add-roles --uusername $($KCADM_CMD get users/$USER_ID -r $TARGET_REALM_NAME --fields username --format csv --noquotes) --rolename $ROLE_NAME -r $TARGET_REALM_NAME
  echo "Role '$ROLE_NAME' assigned successfully to user ID '$USER_ID'."
}

# --- List Realm Roles for a User ---
list_user_realm_roles() {
  USER_ID=$1
  if [ -z "$USER_ID" ]; then
    echo "Usage: $0 list-user-roles <user-id>"
    exit 1
  fi
  echo "Listing realm roles for user ID '$USER_ID' in realm '$TARGET_REALM_NAME'..."
  $KCADM_CMD get-roles --uusername $($KCADM_CMD get users/$USER_ID -r $TARGET_REALM_NAME --fields username --format csv --noquotes) -r $TARGET_REALM_NAME --effective
}

# --- Create Realm Role if it doesn't exist ---
create_realm_role() {
  ROLE_NAME=$1
  if [ -z "$ROLE_NAME" ]; then
    echo "Usage: $0 create-realm-role <role-name>"
    exit 1
  fi
  echo "Checking if realm role '$ROLE_NAME' exists in realm '$TARGET_REALM_NAME'..."
  # Try to get the role, suppress error output if it doesn't exist
  ROLE_EXISTS=$($KCADM_CMD get roles -r $TARGET_REALM_NAME --rname $ROLE_NAME --fields name --format csv --noquotes 2>/dev/null | head -n 1)

  if [ "$ROLE_EXISTS" = "$ROLE_NAME" ]; then
    echo "Realm role '$ROLE_NAME' already exists in realm '$TARGET_REALM_NAME'."
  else
    echo "Realm role '$ROLE_NAME' does not exist. Creating it..."
    $KCADM_CMD create roles -r $TARGET_REALM_NAME -s name=$ROLE_NAME
    echo "Realm role '$ROLE_NAME' created successfully in realm '$TARGET_REALM_NAME'."
  fi
}

# --- Main Execution Logic ---
ACTION=$1

if [ -z "$ACTION" ]; then
  echo "Usage: $0 <action> [args...]"
  echo "Actions:"
  echo "  get-user-id <username>"
  echo "  reset-password <user-id> <new-password>"
  echo "  get-role <role-name>"
  echo "  create-realm-role <role-name>"
  echo "  assign-role <user-id> <role-name>"
  echo "  list-user-roles <user-id>"
  exit 1
fi

login # Ensure we are logged in before any action

shift # Remove action from arguments, rest are for the function

case $ACTION in
  get-user-id)
    get_user_id $1
    ;;
  reset-password)
    reset_password $1 $2
    ;;
  get-role)
    get_role_representation $1
    ;;
  create-realm-role)
    create_realm_role $1
    ;;
  assign-role)
    assign_realm_role $1 $2
    ;;
  list-user-roles)
    list_user_realm_roles $1
    ;;
  *)
    echo "Error: Unknown action '$ACTION'"
    exit 1
    ;;
esac

exit 0
