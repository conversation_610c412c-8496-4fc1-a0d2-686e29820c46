#!/bin/bash

# Development Environment Health Check
# Standalone script for checking development environment status

# Colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_status $BLUE "🏥 Development Environment Health Check"
print_status $BLUE "======================================"

# Check container status
print_status $BLUE "📊 Container Status:"
if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q polyrepo; then
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep polyrepo
    print_status $GREEN "✅ Containers are running"
else
    print_status $RED "❌ No polyrepo containers found"
fi

echo ""

# Check webpack builds
print_status $BLUE "📦 Webpack Build Status:"
for service in auth user api-gateway; do
    log_file="webpack-watch-$service.log"
    if [[ -f "$log_file" ]]; then
        if tail -n 5 "$log_file" | grep -q "compiled successfully"; then
            print_status $GREEN "   ✅ $service: Built successfully"
        elif tail -n 5 "$log_file" | grep -q "Error"; then
            print_status $RED "   ❌ $service: Build errors detected"
        else
            print_status $YELLOW "   ⚠️  $service: Build in progress"
        fi
    else
        print_status $RED "   ❌ $service: No build log found"
    fi
done

echo ""

# Check library status
print_status $BLUE "📚 Library Status:"
cd ../../
for lib_dir in libs/*/; do
    lib_name=$(basename "$lib_dir")
    if [[ -d "$lib_dir/dist" ]] && [[ -n "$(ls -A "$lib_dir/dist" 2>/dev/null)" ]]; then
        print_status $GREEN "   ✅ $lib_name: Built"
    else
        print_status $RED "   ❌ $lib_name: Not built"
    fi
done
cd infrastructure/local-dev

echo ""

# Check service health endpoints
print_status $BLUE "🌐 Service Health:"
services=("localhost:3000" "localhost:3001" "localhost:3002")
for service_url in "${services[@]}"; do
    if curl -s --max-time 2 "$service_url/health" > /dev/null 2>&1; then
        print_status $GREEN "   ✅ $service_url: Healthy"
    else
        print_status $RED "   ❌ $service_url: Unhealthy or not responding"
    fi
done

echo ""