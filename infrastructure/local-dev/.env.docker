# Infrastructure-Level Environment Variables for Docker Development
# These variables are shared across all services when running in Docker

# Centralized Keycloak Configuration
K<PERSON><PERSON><PERSON>OAK_BASE_URL=http://keycloak:8080
KEYCLOAK_REALM_NAME=polyrepo-realm
KEYCLOAK_CLIENT_ID=auth-service-client
KE<PERSON><PERSON>OAK_CLIENT_SECRET=your-client-secret-here
KEYCLOAK_ADMIN_CLIENT_ID=admin-cli
<PERSON><PERSON><PERSON><PERSON><PERSON>K_ADMIN_CLIENT_SECRET=

# Service URLs for Docker Development
AUTH_SERVICE_URL=http://auth-service:3000
USER_SERVICE_URL=http://user-service:3000

# Observability Configuration (shared across all services)
LOG_LEVEL=debug
ENABLE_LOKI=true
LOKI_HOST=http://loki:3100
ENABLE_METRICS=true
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://jaeger:14268/api/traces

# Database Configuration for Docker Development
POSTGRES_KEYCLOAK_HOST=postgres_keycloak
POSTGRES_KEYCLOAK_PORT=5432
POSTGRES_USER_SERVICE_HOST=postgres_user_service
POSTGRES_USER_SERVICE_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379