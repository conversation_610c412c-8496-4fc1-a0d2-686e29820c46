# Experimental: Bundled Development Mode with Volume Mounting
# This approach mounts webpack bundles as volumes to avoid Docker rebuilds on code changes

version: '3.8'

services:
  # --- Core Infrastructure (same as bundled-dev.yml) ---
  postgres_keycloak:
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_postgres_keycloak_dev
    environment:
      POSTGRES_USER: ${KEYCLOAK_POSTGRES_USER}
      POSTGRES_PASSWORD: ${KEYCLOAK_POSTGRES_PASSWORD}
      POSTGRES_DB: ${KEYCLOAK_POSTGRES_DB}
    volumes:
      - postgres_keycloak_data:/var/lib/postgresql/data
    ports:
      - "${KEYCLOAK_POSTGRES_PORT_EXTERNAL:-5432}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  redis:
    image: redis:7-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_redis_dev
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

  keycloak:
    image: quay.io/keycloak/keycloak:24.0
    container_name: polyrepo_keycloak_dev
    environment:
      # Database connection settings
      KC_DB: postgres
      KC_DB_URL_HOST: postgres_keycloak
      KC_DB_URL_PORT: 5432
      KC_DB_URL_DATABASE: ${KEYCLOAK_POSTGRES_DB}
      KC_DB_USERNAME: ${KEYCLOAK_POSTGRES_USER}
      KC_DB_PASSWORD: ${KEYCLOAK_POSTGRES_PASSWORD}
      # Keycloak Admin Credentials
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN_USER:-admin}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD:-admin}
      # Development/Proxy Settings
      KC_PROXY: edge
      KC_HOSTNAME_STRICT: "false"
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_HTTP_ENABLED: "true"
      KC_HEALTH_ENABLED: "true"
      # Java Heap Size
      JAVA_OPTS_APPEND: "-Xms512m -Xmx1024m"
    ports:
      - "8080:8080"
    command: ["start-dev"]
    depends_on:
      postgres_keycloak:
        condition: service_healthy
    networks:
      - local_network
    healthcheck:
      test: ["CMD-SHELL", "exec 3<>/dev/tcp/127.0.0.1/8080;echo -e 'GET /health/ready HTTP/1.1\r\nhost: localhost\r\nConnection: close\r\n\r\n' >&3;grep 'HTTP/1.1 200 OK' <&3"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 120s
    restart: unless-stopped

  keycloak-setup:
    container_name: polyrepo_keycloak_setup
    build:
      context: ./keycloak-setup
      dockerfile: Dockerfile.keycloak-setup
    depends_on:
      keycloak:
        condition: service_started
    environment:
      - KEYCLOAK_ADMIN_USER=${KEYCLOAK_ADMIN_USER:-admin}
      - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD:-admin}
      - AUTH_SERVICE_CLIENT_SECRET=${AUTH_SERVICE_CLIENT_SECRET:-SuperSecretDevPasswordChangeMe}
    networks:
      - local_network
    restart: 'no'

  # --- Webpack Bundled Services with Volume Mounting ---
  
  # Base Node.js image with volume-mounted webpack bundles
  auth-service:
    container_name: polyrepo_auth_service_volume
    image: node:22-alpine
    working_dir: /app
    ports:
      - "${AUTH_SERVICE_PORT_EXTERNAL:-3001}:3000"
    env_file:
      - ../../services/auth-service/.env.bundling
    environment:
      NODE_ENV: docker
    volumes:
      # Mount the webpack bundle directory directly
      - ../../services/auth-service/dist-webpack:/app/dist-webpack:ro
      # Mount root node_modules for externalized dependencies (OpenTelemetry, etc.)
      # Yarn workspaces hoists dependencies to root level
      - ../../node_modules:/app/node_modules:ro
    # Run the webpack bundle directly  
    command: ["node", "dist-webpack/main.bundle.js"]
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - local_network
    restart: unless-stopped
    # Health check using node directly
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://0.0.0.0:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  user-service:
    container_name: polyrepo_user_service_volume
    image: node:22-alpine
    working_dir: /app
    ports:
      - "${USER_SERVICE_PORT_EXTERNAL:-3002}:3000"
    env_file:
      - ../../services/user-service/.env.bundling
    environment:
      NODE_ENV: docker
    volumes:
      # Mount the webpack bundle directory directly
      - ../../services/user-service/dist-webpack:/app/dist-webpack:ro
      # Mount root node_modules for externalized dependencies (Prisma, OpenTelemetry, etc.)
      # Yarn workspaces hoists dependencies to root level
      - ../../node_modules:/app/node_modules:ro
      # Mount Prisma files
      - ../../services/user-service/prisma:/app/prisma:ro
    # Run migrations then start the bundle (client generation handled on host)
    command: ["sh", "-c", "npx prisma migrate deploy && node dist-webpack/main.bundle.js"]
    depends_on:
      postgres_user_service:
        condition: service_healthy
    networks:
      - local_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-gateway:
    container_name: polyrepo_api_gateway_volume
    image: node:22-alpine
    working_dir: /app
    ports:
      - "${API_GATEWAY_PORT_EXTERNAL:-3000}:3000"
    env_file:
      - ../../services/api-gateway/.env.bundling
    environment:
      NODE_ENV: docker
    volumes:
      # Mount the webpack bundle directory directly
      - ../../services/api-gateway/dist-webpack:/app/dist-webpack:ro
      # Mount root node_modules for externalized dependencies (OpenTelemetry, etc.)
      # Yarn workspaces hoists dependencies to root level
      - ../../node_modules:/app/node_modules:ro
    # Run the webpack bundle directly
    command: ["node", "dist-webpack/main.bundle.js"]
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - local_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres_user_service:
    image: postgres:15-alpine
    container_name: ${COMPOSE_PROJECT_NAME}_postgres_user_service_dev
    environment:
      POSTGRES_USER: ${USER_SERVICE_POSTGRES_USER}
      POSTGRES_PASSWORD: ${USER_SERVICE_POSTGRES_PASSWORD}
      POSTGRES_DB: ${USER_SERVICE_POSTGRES_DB}
    volumes:
      - postgres_user_service_data:/var/lib/postgresql/data
    ports:
      - "${USER_SERVICE_POSTGRES_PORT_EXTERNAL:-5433}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER} -d $${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - local_network

volumes:
  postgres_keycloak_data:
    driver: local
  postgres_user_service_data:
    driver: local

networks:
  local_network:
    driver: bridge
