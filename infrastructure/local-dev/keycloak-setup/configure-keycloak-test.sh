#!/bin/bash

set -e # Exit immediately if a command exits with a non-zero status.

# Variables for Test Environment
KEYCLOAK_URL="http://keycloak:8080" # Use service name inside docker network
TEST_REALM_NAME="polyrepo-test"      # Dedicated Test Realm
ADMIN_USER="${KEYCLOAK_ADMIN_USER:-admin}"
ADMIN_PASSWORD="${KEYCLOAK_ADMIN_PASSWORD:-admin}"
TEST_CLIENT_ID="auth-service-test-client" # Dedicated Test Client ID
TEST_CLIENT_SECRET="${AUTH_SERVICE_TEST_CLIENT_SECRET:-TestClientSecretDev}"

KCADM="/opt/keycloak/bin/kcadm.sh"
SERVICE_ACCOUNT_USER_CLIENT_ROLES=("manage-users" "view-users" "query-users" "realm-admin") # For the service client itself

# Test Users with Different Roles
TEST_USER_USERNAME="<EMAIL>"
TEST_USER_PASSWORD="testpassword"
TEST_ADMIN_USERNAME="<EMAIL>"
TEST_ADMIN_PASSWORD="testpassword"
TEST_MODERATOR_USERNAME="<EMAIL>"
TEST_MODERATOR_PASSWORD="testpassword"
TEST_ANALYTICS_USERNAME="<EMAIL>"
TEST_ANALYTICS_PASSWORD="testpassword"
TEST_TECH_ADMIN_USERNAME="<EMAIL>"
TEST_TECH_ADMIN_PASSWORD="testpassword"
TEST_SUPERADMIN_USERNAME="<EMAIL>"
TEST_SUPERADMIN_PASSWORD="testpassword"

# Application Roles Hierarchy (highest to lowest privilege)
APP_ROLES=("superadmin" "tech-admin" "admin" "moderator" "analytics" "user")

# --- Helper Functions (Copied and adapted from original) ---

# Login to master realm
login_kcadm() {
  echo "Attempting kcadm login to $KEYCLOAK_URL (Realm: master) as user '$ADMIN_USER' for TEST setup..."
  local attempt=0
  until $KCADM config credentials --server $KEYCLOAK_URL --realm master --user "$ADMIN_USER" --password "$ADMIN_PASSWORD" --client admin-cli || [ $attempt -ge 5 ]; do
      attempt=$((attempt+1))
      echo "kcadm login failed for TEST setup, retrying in 5s... (Attempt $attempt/5)"
      sleep 5
  done

  if [ $attempt -ge 5 ]; then
      echo "ERROR (TEST setup): Failed to authenticate kcadm.sh. Check URL, credentials, and Keycloak status."
      exit 1
  fi
  echo "kcadm login successful for TEST setup."
}

# Function to check if realm exists
realm_exists() {
  local realm_to_check="$1"
  echo "Checking if realm '$realm_to_check' exists for TEST setup..."
  if $KCADM get realms/$realm_to_check > /dev/null 2>&1; then
      echo "Realm '$realm_to_check' found for TEST setup."
      return 0 # Realm exists
  else
      echo "Realm '$realm_to_check' not found for TEST setup."
      return 1 # Realm does not exist
  fi
}

# Function to get internal ID of a client
get_client_id() {
  local realm_name="$1"
  local client_clientId="$2"
  $KCADM get clients -r "$realm_name" --fields id,clientId | jq -r --arg clientId "$client_clientId" '.[] | select(.clientId==$clientId) | .id'
}

# Function to get Service Account User ID for a client
get_service_account_user_id() {
  local realm_name="$1"
  local client_internal_id="$2"
  # Note: kcadm uses clientId for --query with service accounts, not the internal client ID
  $KCADM get users -r "$realm_name" --query "username=service-account-$client_internal_id" --fields id | jq -r '.[0].id'
}

# Function to create application roles if they don't exist
create_application_roles() {
    local realm="$1"
    echo "Creating application roles in realm '$realm'..."
    
    for role in "${APP_ROLES[@]}"; do
        echo "Checking if role '$role' exists..."
        if $KCADM get roles/$role -r "$realm" > /dev/null 2>&1; then
            echo "Role '$role' already exists."
        else
            echo "Role '$role' not found, creating it..."
            if $KCADM create roles -r "$realm" -s name="$role" -s description="Application role: $role" 2>/dev/null; then
                echo "Role '$role' created successfully."
            else
                echo "WARNING: Failed to create role '$role' (may already exist)."
            fi
        fi
    done
    echo "Application roles creation completed."
}

# Function to assign role hierarchy according to business logic
assign_role_hierarchy() {
    local realm="$1"
    local username="$2"
    local primary_role="$3"
    
    echo "Assigning role hierarchy for '$primary_role' to user '$username'..."
    
    # Define role hierarchy (matches KeycloakTestUtils logic)
    local roles_to_assign=()
    case "$primary_role" in
        "superadmin")
            roles_to_assign=("superadmin" "tech-admin" "admin" "moderator" "analytics" "user")
            ;;
        "tech-admin")
            roles_to_assign=("tech-admin" "admin" "moderator" "user")
            ;;
        "admin")
            roles_to_assign=("admin" "moderator" "user")
            ;;
        "moderator")
            roles_to_assign=("moderator" "user")
            ;;
        "analytics")
            roles_to_assign=("analytics" "user")
            ;;
        "user")
            roles_to_assign=("user")
            ;;
        *)
            echo "WARNING: Unknown role '$primary_role', assigning only this role"
            roles_to_assign=("$primary_role")
            ;;
    esac
    
    # Assign all roles in the hierarchy
    for role in "${roles_to_assign[@]}"; do
        echo "  Assigning role '$role' to user '$username'..."
        
        # Check if role exists
        if ! $KCADM get roles/$role -r "$realm" > /dev/null 2>&1; then
            echo "  ERROR: Role '$role' does not exist in realm '$realm'. Skipping."
            continue
        fi
        
        # Assign the role
        if $KCADM add-roles --uusername "$username" --rolename "$role" -r "$realm" 2>/dev/null; then
            echo "  ✅ Role '$role' assigned successfully."
        else
            echo "  ⚠️  Failed to assign role '$role' (may already be assigned)."
        fi
    done
    
    echo "Role hierarchy assignment completed for '$username'."
}

# Function to create a user if not exists with proper role hierarchy
create_user_if_not_exists() {
    local realm="$1"
    local username="$2"
    local password="$3"
    local user_role="$4" # Primary role: superadmin, tech-admin, admin, moderator, analytics, user

    echo "Checking if user '$username' exists in realm '$realm'..."
    USER_EXISTS_ID=$($KCADM get users -r "$realm" -q username="$username" --fields id | jq -r '.[0].id')

    if [ -z "$USER_EXISTS_ID" ] || [ "$USER_EXISTS_ID" == "null" ]; then
        echo "User '$username' does not exist. Creating..."
        $KCADM create users -r "$realm" -s username="$username" -s email="$username" -s firstName="Test" -s lastName="User" -s enabled=true -s emailVerified=true
        USER_ID=$($KCADM get users -r "$realm" -q username="$username" --fields id | jq -r '.[0].id')
        if [ -z "$USER_ID" ] || [ "$USER_ID" == "null" ]; then
            echo "ERROR: Failed to create or retrieve ID for user '$username'."
            return 1
        fi
        echo "Setting password for user '$username' (ID: $USER_ID)..."
        $KCADM set-password -r "$realm" --userid "$USER_ID" --new-password "$password" --temporary=false
        echo "User '$username' created with ID $USER_ID."

        # Assign role hierarchy based on primary role
        assign_role_hierarchy "$realm" "$username" "$user_role"
    else
        echo "User '$username' already exists with ID $USER_EXISTS_ID. Skipping creation."
    fi
}

# --- Main Script for TEST Environment ---

echo "Waiting for Keycloak at $KEYCLOAK_URL to be ready (TEST script)..."
attempt=0
max_attempts=36
until curl -s -o /dev/null -w "%{http_code}" "$KEYCLOAK_URL/health/ready" | grep -q "200" || [ $attempt -ge $max_attempts ]; do
    attempt=$((attempt+1))
    echo "Keycloak not ready (TEST script), retrying in 5s... (Attempt $attempt/$max_attempts)"
    sleep 5
done

if [ $attempt -ge $max_attempts ]; then
    echo "ERROR (TEST script): Keycloak did not become ready. Exiting."
    exit 1
fi
echo "Keycloak is ready (TEST script)."

login_kcadm

# Attempt to delete the TEST_REALM_NAME first to ensure a clean slate
echo "Attempting to delete existing TEST realm '$TEST_REALM_NAME' for a clean setup..."
if realm_exists "$TEST_REALM_NAME"; then
  $KCADM delete realms/$TEST_REALM_NAME
  if [ $? -eq 0 ]; then
    echo "TEST Realm '$TEST_REALM_NAME' deleted successfully."
  else
    echo "WARNING: Failed to delete existing TEST realm '$TEST_REALM_NAME'. It might not have existed or another issue occurred."
  fi
else
  echo "TEST Realm '$TEST_REALM_NAME' does not exist, no need to delete."
fi

# Create Test Realm (it should not exist at this point if deletion was successful or it never existed)
echo "Creating TEST realm '$TEST_REALM_NAME'..."
$KCADM create realms -s realm=$TEST_REALM_NAME -s enabled=true
if [ $? -ne 0 ]; then echo "ERROR: Failed to create TEST realm '$TEST_REALM_NAME'"; exit 1; fi
echo "TEST Realm '$TEST_REALM_NAME' created successfully."

# --- Configure Brute Force Detection and Account Lockout for Test Realm ---
echo "Configuring brute force detection and account lockout policies for TEST realm '$TEST_REALM_NAME'..."

# Configure brute force detection settings
$KCADM update realms/$TEST_REALM_NAME -s "bruteForceProtected=true" \
  -s "failureFactor=10" \
  -s "maxFailureWaitSeconds=900" \
  -s "minimumQuickLoginWaitSeconds=60" \
  -s "waitIncrementSeconds=60" \
  -s "quickLoginCheckMilliSeconds=1000" \
  -s "maxDeltaTimeSeconds=43200" \
  -s "permanentLockout=false"

echo "Brute force detection and account lockout policies configured successfully for TEST realm."

# Create Test Client
echo "Attempting to create/update TEST client '$TEST_CLIENT_ID' in realm '$TEST_REALM_NAME'..."
# Attempt to get Test Client internal ID first
CLIENT_INTERNAL_ID_EXISTING=$(get_client_id "$TEST_REALM_NAME" "$TEST_CLIENT_ID")
# Trim potential whitespace
CLIENT_INTERNAL_ID_EXISTING=$(echo "$CLIENT_INTERNAL_ID_EXISTING" | tr -d '[:space:]')

if [ -n "$CLIENT_INTERNAL_ID_EXISTING" ]; then
    echo "Test client '$TEST_CLIENT_ID' already exists with internal ID '$CLIENT_INTERNAL_ID_EXISTING'. Updating..."
    $KCADM update clients/$CLIENT_INTERNAL_ID_EXISTING -r $TEST_REALM_NAME \
        -s enabled=true \
        -s clientAuthenticatorType=client-secret \
        -s secret="$TEST_CLIENT_SECRET" \
        -s 'serviceAccountsEnabled=true' \
        -s 'publicClient=false' \
        -s 'directAccessGrantsEnabled=true' \
        -s 'standardFlowEnabled=true' \
        -s 'redirectUris=["http://localhost:*/callback", "http://localhost/*"]' \
        -s 'webOrigins=["+"]' || echo "WARNING: Failed to update existing test client '$TEST_CLIENT_ID'."
else
    echo "Test client '$TEST_CLIENT_ID' does not exist in realm '$TEST_REALM_NAME'. Creating..."
    $KCADM create clients -r $TEST_REALM_NAME -s clientId="$TEST_CLIENT_ID" \
        -s enabled=true \
        -s clientAuthenticatorType=client-secret \
        -s secret="$TEST_CLIENT_SECRET" \
        -s 'serviceAccountsEnabled=true' \
        -s 'publicClient=false' \
        -s 'directAccessGrantsEnabled=true' \
        -s 'standardFlowEnabled=true' \
        -s 'redirectUris=["http://localhost:*/callback", "http://localhost/*"]' \
        -s 'webOrigins=["+"]' || echo "WARNING: Failed to create test client '$TEST_CLIENT_ID'."
fi

if ! $KCADM get clients -r $TEST_REALM_NAME -q clientId=$TEST_CLIENT_ID > /dev/null 2>&1; then
  echo "ERROR: Test Client '$TEST_CLIENT_ID' was not configured. Exiting."
  exit 1
fi
echo "Test Client '$TEST_CLIENT_ID' configured in realm '$TEST_REALM_NAME'."

# --- Assign Service Account Roles for TEST_CLIENT_ID ---
echo "Assigning service account roles for TEST client '$TEST_CLIENT_ID' in realm '$TEST_REALM_NAME'..."
CLIENT_INTERNAL_ID=$(get_client_id "$TEST_REALM_NAME" "$TEST_CLIENT_ID")
if [ -z "$CLIENT_INTERNAL_ID" ]; then
  echo "ERROR: Could not retrieve internal ID for TEST client '$TEST_CLIENT_ID'."; exit 1;
fi

SERVICE_ACCOUNT_USER_ID=$(get_service_account_user_id "$TEST_REALM_NAME" "$TEST_CLIENT_ID")
if [ -z "$SERVICE_ACCOUNT_USER_ID" ]; then
  echo "ERROR: Could not retrieve Service Account User ID for TEST client '$TEST_CLIENT_ID'."; exit 1;
fi

REALM_MANAGEMENT_CLIENT_ID=$(get_client_id "$TEST_REALM_NAME" "realm-management")
if [ -z "$REALM_MANAGEMENT_CLIENT_ID" ]; then
  echo "ERROR: Could not retrieve internal ID for 'realm-management' client in TEST realm."; exit 1;
fi

for role_name in "${SERVICE_ACCOUNT_USER_CLIENT_ROLES[@]}"; do
  echo "Assigning role '$role_name' from 'realm-management' to service account of '$TEST_CLIENT_ID'..."
  if $KCADM add-roles --uusername "service-account-$TEST_CLIENT_ID" --cclientid realm-management --rolename "$role_name" -r "$TEST_REALM_NAME"; then
    echo "Successfully assigned role '$role_name' to service account of $TEST_CLIENT_ID."
  else
    echo "WARNING: Failed to assign role '$role_name' to service account of $TEST_CLIENT_ID or it was already assigned."
  fi
done
echo "Keycloak service account role assignment for TEST client attempted."

# --- Create Application Roles ---
echo "Creating application roles in test realm '$TEST_REALM_NAME'..."
create_application_roles "$TEST_REALM_NAME"

# --- Create Test Users with Different Roles ---
echo "Creating test users with different roles in realm '$TEST_REALM_NAME'..."
create_user_if_not_exists "$TEST_REALM_NAME" "$TEST_USER_USERNAME" "$TEST_USER_PASSWORD" "user"
create_user_if_not_exists "$TEST_REALM_NAME" "$TEST_ANALYTICS_USERNAME" "$TEST_ANALYTICS_PASSWORD" "analytics"
create_user_if_not_exists "$TEST_REALM_NAME" "$TEST_MODERATOR_USERNAME" "$TEST_MODERATOR_PASSWORD" "moderator"
create_user_if_not_exists "$TEST_REALM_NAME" "$TEST_ADMIN_USERNAME" "$TEST_ADMIN_PASSWORD" "admin"
create_user_if_not_exists "$TEST_REALM_NAME" "$TEST_TECH_ADMIN_USERNAME" "$TEST_TECH_ADMIN_PASSWORD" "tech-admin"
create_user_if_not_exists "$TEST_REALM_NAME" "$TEST_SUPERADMIN_USERNAME" "$TEST_SUPERADMIN_PASSWORD" "superadmin"
echo "Test user creation process completed."

# --- Validation: Verify Role Assignments ---
echo ""
echo "🔍 Validating role assignments..."

validate_user_roles() {
    local username="$1"
    local expected_primary_role="$2"
    
    echo "Checking roles for user '$username' (expected primary: $expected_primary_role):"
    
    # Get user's roles
    local user_roles
    user_roles=$($KCADM get-roles --uusername "$username" -r "$TEST_REALM_NAME" 2>/dev/null | jq -r '.[].name' | tr '\n' ' ' || echo "")
    
    if [ -n "$user_roles" ]; then
        echo "  Assigned roles: $user_roles"
        
        # Check if user has the expected primary role
        if echo "$user_roles" | grep -q "$expected_primary_role"; then
            echo "  ✅ Primary role '$expected_primary_role' found"
        else
            echo "  ❌ Primary role '$expected_primary_role' MISSING"
        fi
        
        # Check if user has 'user' role (all users should have this)
        if echo "$user_roles" | grep -q "user"; then
            echo "  ✅ Base 'user' role found"
        else
            echo "  ❌ Base 'user' role MISSING"
        fi
    else
        echo "  ❌ Failed to retrieve roles for user '$username'"
    fi
    echo ""
}

# Validate each test user
validate_user_roles "$TEST_USER_USERNAME" "user"
validate_user_roles "$TEST_ANALYTICS_USERNAME" "analytics"
validate_user_roles "$TEST_MODERATOR_USERNAME" "moderator"
validate_user_roles "$TEST_ADMIN_USERNAME" "admin"
validate_user_roles "$TEST_TECH_ADMIN_USERNAME" "tech-admin"
validate_user_roles "$TEST_SUPERADMIN_USERNAME" "superadmin"

echo "✅ Keycloak TEST configuration script completed successfully with role validation."
exit 0
