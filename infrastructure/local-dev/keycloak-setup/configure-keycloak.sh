#!/bin/bash

set -e # Exit immediately if a command exits with a non-zero status.

# === Configuration Variables ===
# Allow environment variable overrides for all key configuration

# Keycloak Connection
KEYCLOAK_URL="${KEYCLOAK_BASE_URL:-http://keycloak:8080}"
ADMIN_USER="${K<PERSON><PERSON><PERSON>OAK_ADMIN_USER:-admin}"
ADMIN_PASSWORD="${KEYCLOAK_ADMIN_PASSWORD:-admin}"

# Realm Configuration  
REALM_NAME="${KEYCLOAK_REALM_NAME:-polyrepo-realm}"
CLIENT_ID="${KEYCLOAK_CLIENT_ID:-auth-service-client}"

# Security (IMPORTANT: Use secure secrets in production)
CLIENT_SECRET="${AUTH_SERVICE_CLIENT_SECRET:-SuperSecretDevPasswordChangeMe}"

# Validation
if [ -z "$CLIENT_SECRET" ] || [ "$CLIENT_SECRET" = "SuperSecretDevPasswordChangeMe" ]; then
    echo "⚠️  WARNING: Using default client secret. Set AUTH_SERVICE_CLIENT_SECRET environment variable for production!"
fi

KCADM="/opt/keycloak/bin/kcadm.sh"
SERVICE_ACCOUNT_USER_CLIENT_ROLES=("manage-users" "view-users" "query-users")

# Application Roles Hierarchy (highest to lowest privilege)
APP_ROLES=("superadmin" "tech-admin" "admin" "moderator" "analytics" "user")

# --- Helper Functions ---

# Login to master realm
login_kcadm() {
  echo "Attempting kcadm login to $KEYCLOAK_URL (Realm: master) as user '$ADMIN_USER'..."
  # Try login with retries as well, sometimes initial login fails briefly
  local attempt=0
  until $KCADM config credentials --server $KEYCLOAK_URL --realm master --user "$ADMIN_USER" --password "$ADMIN_PASSWORD" --client admin-cli || [ $attempt -ge 5 ]; do
      attempt=$((attempt+1))
      echo "kcadm login failed, retrying in 5s... (Attempt $attempt/5)"
      sleep 5
  done

  if [ $attempt -ge 5 ]; then
      echo "ERROR: Failed to authenticate kcadm.sh after retries. Check URL, credentials, and Keycloak status."
      exit 1
  fi
  echo "kcadm login successful."
}

# Function to check if realm exists
realm_exists() {
  echo "Checking if realm '$REALM_NAME' exists..."
  if $KCADM get realms/$REALM_NAME > /dev/null 2>&1; then
      echo "Realm '$REALM_NAME' found."
      return 0 # Realm exists
  else
      echo "Realm '$REALM_NAME' not found."
      return 1 # Realm does not exist
  fi
}

# Function to get internal ID of a client
get_client_id() {
  local realm_name="$1"
  local client_clientId="$2"
  $KCADM get clients -r "$realm_name" --fields id,clientId | jq -r --arg clientId "$client_clientId" '.[] | select(.clientId==$clientId) | .id'
}

# Function to get Service Account User ID for a client
get_service_account_user_id() {
  local realm_name="$1"
  local client_internal_id="$2"
  $KCADM get users -r "$realm_name" --query "username=service-account-$client_internal_id" --fields id | jq -r '.[0].id'
}

# Function to create application roles if they don't exist
create_application_roles() {
  local realm="$1"
  echo "Creating application roles in realm '$realm'..."
  
  for role in "${APP_ROLES[@]}"; do
    echo "Checking if role '$role' exists..."
    if $KCADM get roles/$role -r "$realm" > /dev/null 2>&1; then
      echo "Role '$role' already exists."
    else
      echo "Role '$role' not found, creating it..."
      if $KCADM create roles -r "$realm" -s name="$role" -s description="Application role: $role" 2>/dev/null; then
        echo "Role '$role' created successfully."
      else
        echo "WARNING: Failed to create role '$role' (may already exist)."
      fi
    fi
  done
  echo "Application roles creation completed."
}

# --- Main Script ---

# Wait for Keycloak to be ready (using curl against /health/ready)
echo "Waiting for Keycloak at $KEYCLOAK_URL to be ready..."
attempt=0
max_attempts=36 # Try for 3 minutes (36 * 5s = 180s)
until curl -s -o /dev/null -w "%{http_code}" "$KEYCLOAK_URL/health/ready" | grep -q "200" || [ $attempt -ge $max_attempts ]; do
    attempt=$((attempt+1))
    echo "Keycloak not ready (or /health/ready not 200), retrying in 5s... (Attempt $attempt/$max_attempts)"
    sleep 5
done

if [ $attempt -ge $max_attempts ]; then
    echo "ERROR: Keycloak did not become ready (or /health/ready did not return 200) after $max_attempts attempts."
    exit 1
fi
echo "Keycloak is ready."

login_kcadm

# Create Realm if not exists
if ! realm_exists; then
  echo "Creating realm '$REALM_NAME'..."
  $KCADM create realms -s realm=$REALM_NAME -s enabled=true
  if [ $? -ne 0 ]; then
    echo "ERROR: Failed to create realm '$REALM_NAME'"
    exit 1
  fi
  echo "Realm '$REALM_NAME' created successfully."
fi

# --- Create Application Roles ---
echo "Creating application roles in realm '$REALM_NAME'..."
create_application_roles "$REALM_NAME"

# Create or Update Client
CLIENT_INTERNAL_ID_EXISTING=$(get_client_id "$REALM_NAME" "$CLIENT_ID")
# Trim potential whitespace
CLIENT_INTERNAL_ID_EXISTING=$(echo "$CLIENT_INTERNAL_ID_EXISTING" | tr -d '[:space:]')

if [ -n "$CLIENT_INTERNAL_ID_EXISTING" ]; then
    echo "Client '$CLIENT_ID' already exists with internal ID '$CLIENT_INTERNAL_ID_EXISTING' in realm '$REALM_NAME'. Updating..."
    $KCADM update clients/$CLIENT_INTERNAL_ID_EXISTING -r "$REALM_NAME" \
        -s enabled=true \
        -s clientAuthenticatorType=client-secret \
        -s secret="$CLIENT_SECRET" \
        -s 'serviceAccountsEnabled=true' \
        -s 'publicClient=false' \
        -s 'directAccessGrantsEnabled=true' \
        -s 'standardFlowEnabled=true' \
        -s 'redirectUris=["http://localhost:*/callback", "http://localhost/*"]' \
        -s 'webOrigins=["+"]' || echo "WARNING: Failed to update existing client '$CLIENT_ID'."
else
    echo "Client '$CLIENT_ID' does not exist in realm '$REALM_NAME'. Creating..."
    $KCADM create clients -r "$REALM_NAME" -s clientId="$CLIENT_ID" \
        -s enabled=true \
        -s clientAuthenticatorType=client-secret \
        -s secret="$CLIENT_SECRET" \
        -s 'serviceAccountsEnabled=true' \
        -s 'publicClient=false' \
        -s 'directAccessGrantsEnabled=true' \
        -s 'standardFlowEnabled=true' \
        -s 'redirectUris=["http://localhost:*/callback", "http://localhost/*"]' \
        -s 'webOrigins=["+"]' || echo "WARNING: Failed to create client '$CLIENT_ID'."
fi

# --- Configure Brute Force Detection and Account Lockout ---
echo "Configuring brute force detection and account lockout policies for realm '$REALM_NAME'..."

# Configure brute force detection settings
$KCADM update realms/$REALM_NAME -s "bruteForceProtected=true" \
  -s "failureFactor=10" \
  -s "maxFailureWaitSeconds=900" \
  -s "minimumQuickLoginWaitSeconds=60" \
  -s "waitIncrementSeconds=60" \
  -s "quickLoginCheckMilliSeconds=1000" \
  -s "maxDeltaTimeSeconds=43200" \
  -s "permanentLockout=false"

echo "Brute force detection and account lockout policies configured successfully."

# --- Assign Service Account Roles ---
echo "Assigning service account roles for client '$CLIENT_ID' in realm '$REALM_NAME'..."

# 1. Get the internal ID of our client ('auth-service-client')
CLIENT_INTERNAL_ID=$(get_client_id "$REALM_NAME" "$CLIENT_ID")
if [ -z "$CLIENT_INTERNAL_ID" ]; then
  echo "ERROR: Could not retrieve internal ID for client '$CLIENT_ID'." >&2
  exit 1
fi
echo "Internal ID for client '$CLIENT_ID' is '$CLIENT_INTERNAL_ID'"

# 2. Get the Service Account User's ID for our client
SERVICE_ACCOUNT_USER_ID=$(get_service_account_user_id "$REALM_NAME" "$CLIENT_ID") # kcadm uses clientId here for query
if [ -z "$SERVICE_ACCOUNT_USER_ID" ]; then
  echo "ERROR: Could not retrieve Service Account User ID for client '$CLIENT_ID'." >&2
  exit 1
fi
echo "Service Account User ID for client '$CLIENT_ID' is '$SERVICE_ACCOUNT_USER_ID'"

# 3. Get the 'realm-management' client's internal ID (this client holds the roles we want to assign)
REALM_MANAGEMENT_CLIENT_ID=$(get_client_id "$REALM_NAME" "realm-management")
if [ -z "$REALM_MANAGEMENT_CLIENT_ID" ]; then
  echo "ERROR: Could not retrieve internal ID for 'realm-management' client." >&2
  exit 1
fi
echo "Internal ID for 'realm-management' client is '$REALM_MANAGEMENT_CLIENT_ID'"

# 4. Add roles to the service account user
for role_name in "${SERVICE_ACCOUNT_USER_CLIENT_ROLES[@]}"; do
  echo "Assigning role '$role_name' from 'realm-management' to service account of '$CLIENT_ID'..."
  if $KCADM add-roles --uusername "service-account-$CLIENT_ID" --cclientid realm-management --rolename "$role_name" -r "$REALM_NAME"; then
    echo "Successfully assigned role '$role_name'."
  else
    echo "WARNING: Failed to assign role '$role_name' or it was already assigned."
    # Check if role is already assigned (optional, kcadm might error if already assigned)
    # $KCADM get-roles --uusername "service-account-$CLIENT_ID" --cclientid realm-management -r "$REALM_NAME" | jq -e --arg ROLE_NAME "$role_name" '.[] | select(.name==$ROLE_NAME)' > /dev/null
    # if [ $? -eq 0 ]; then echo "Role '$role_name' was already assigned."; else echo "Failed to assign role '$role_name'."; fi
  fi
done

echo "Keycloak service account role assignment attempted."

echo "Keycloak configuration script completed."
exit 0
