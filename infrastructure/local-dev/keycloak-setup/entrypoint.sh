#!/bin/bash

set -e

MAIN_CONFIG_SCRIPT="/opt/keycloak/configure-keycloak.sh"
TEST_CONFIG_SCRIPT="/opt/keycloak/configure-keycloak-test.sh"

echo "Keycloak Setup Entrypoint: Starting configuration process."

# Run main configuration script
if [ -f "${MAIN_CONFIG_SCRIPT}" ]; then
  echo "Keycloak Setup Entrypoint: Executing main configuration script: ${MAIN_CONFIG_SCRIPT}"
  chmod +x "${MAIN_CONFIG_SCRIPT}"
  "${MAIN_CONFIG_SCRIPT}"
  if [ $? -ne 0 ]; then
    echo "Keycloak Setup Entrypoint: ERROR - Main configuration script failed!" >&2
    exit 1
  fi
  echo "Keycloak Setup Entrypoint: Main configuration script completed."
else
  echo "Keycloak Setup Entrypoint: WARNING - Main configuration script '${MAIN_CONFIG_SCRIPT}' not found!" >&2
  # Decide if this is a fatal error. For now, we'll let it proceed to test config.
fi

# Run test configuration script
if [ -f "${TEST_CONFIG_SCRIPT}" ]; then
  echo "Keycloak Setup Entrypoint: Executing test configuration script: ${TEST_CONFIG_SCRIPT}"
  chmod +x "${TEST_CONFIG_SCRIPT}"
  "${TEST_CONFIG_SCRIPT}"
  if [ $? -ne 0 ]; then
    echo "Keycloak Setup Entrypoint: ERROR - Test configuration script failed!" >&2
    exit 1
  fi
  echo "Keycloak Setup Entrypoint: Test configuration script completed."
else
  echo "Keycloak Setup Entrypoint: WARNING - Test configuration script '${TEST_CONFIG_SCRIPT}' not found!" >&2
fi

echo "Keycloak Setup Entrypoint: All specified configurations complete."

# Original script logic based on KEYCLOAK_CONFIG_SCRIPT_NAME is now superseded.
# If you need to select scripts dynamically in the future, this entrypoint would need to be more complex.

# # Default to the standard configuration script if not specified
# CONFIG_SCRIPT_TO_RUN="${KEYCLOAK_CONFIG_SCRIPT_NAME:-configure-keycloak.sh}"
# 
# SCRIPT_PATH="/opt/keycloak/${CONFIG_SCRIPT_TO_RUN}"
# 
# echo "Keycloak Setup Entrypoint: Checking for script ${SCRIPT_PATH}"
# 
# if [ -f "${SCRIPT_PATH}" ]; then
#   echo "Keycloak Setup Entrypoint: Executing ${SCRIPT_PATH}"
#   # Ensure the script is executable (should be set in Dockerfile, but good practice)
#   chmod +x "${SCRIPT_PATH}"
#   exec "${SCRIPT_PATH}"
# else
#   echo "Keycloak Setup Entrypoint: ERROR - Configuration script '${SCRIPT_PATH}' not found!"
#   exit 1
# fi
