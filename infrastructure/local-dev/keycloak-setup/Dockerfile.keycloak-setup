# Stage 1: Use UBI9 to install required tools into a temporary rootfs
FROM registry.access.redhat.com/ubi9 AS ubi-builder

ARG PACKAGES="jq curl"

RUN mkdir -p /mnt/rootfs && \
    echo "Installing packages ($PACKAGES) into /mnt/rootfs using dnf..." && \
    # Install packages into the temporary rootfs, disable weak dependencies and docs to keep size down
    dnf install --installroot /mnt/rootfs $PACKAGES --releasever 9 --setopt install_weak_deps=false --nodocs -y && \
    echo "Cleaning dnf cache in /mnt/rootfs..." && \
    dnf --installroot /mnt/rootfs clean all && \
    echo "Packages installed and cleaned up in /mnt/rootfs."

# Stage 2: Final image based on Keycloak
FROM quay.io/keycloak/keycloak:24.0

USER root

# Copy the installed packages and their dependencies from the builder stage's rootfs
COPY --from=ubi-builder /mnt/rootfs /

# Add Keycloak bin to PATH so kcadm.sh can be called directly
ENV PATH="${PATH}:/opt/keycloak/bin"

# Verify tools are present and executable
RUN echo "Verifying installed tools..." && \
    echo "jq path: $(command -v jq)" && \
    echo "jq version: $(jq --version)" && \
    echo "curl path: $(command -v curl)" && \
    echo "curl version: $(curl --version | head -n 1)" && \
    echo "Tools verified."

# Copy the configuration scripts and the new entrypoint script into the image
COPY ./configure-keycloak.sh /opt/keycloak/configure-keycloak.sh
COPY ./configure-keycloak-test.sh /opt/keycloak/configure-keycloak-test.sh
COPY ./entrypoint.sh /opt/keycloak/entrypoint.sh

# Make all scripts executable
RUN chmod +x /opt/keycloak/configure-keycloak.sh && \
    chmod +x /opt/keycloak/configure-keycloak-test.sh && \
    chmod +x /opt/keycloak/entrypoint.sh

# Set the new entrypoint
ENTRYPOINT ["/opt/keycloak/entrypoint.sh"]
