# Fly.io Keycloak Configuration

Connection details for the main Keycloak instance hosted on Fly.io should be provided via environment variables during deployment and for backend services running locally that need to connect to it.

Required Environment Variables (example names):

*   `KEYCLOAK_AUTH_URL=https://<your-fly-keycloak-app>.fly.dev`
*   `<PERSON><PERSON><PERSON>CLOAK_REALM=<your-realm-name>`
*   `<PERSON><PERSON><PERSON>CLOAK_CLIENT_ID_API_GATEWAY=<your-client-id>`
*   `KEYCLOAK_CLIENT_SECRET_API_GATEWAY=<your-client-secret>` (If applicable)
*   etc. for other clients...

These variables will be consumed by the respective backend services.
