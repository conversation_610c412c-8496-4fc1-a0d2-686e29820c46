# Base image for Node.js services in the polyrepo
ARG NODE_ALPINE_VERSION=22.12-alpine # LTS version - required for Got HTTP/2 client (minimum Node 20+)

FROM node:${NODE_ALPINE_VERSION} AS base
# RUN npm install -g yarn@${YARN_VERSION} # Removed as yarn is likely pre-installed

# Set working directory
WORKDIR /app

# Create a non-root user and group.
# This makes 'appuser' and 'appgroup' available for 'COPY --chown' in subsequent stages
# that use this base, even though commands in this base image itself run as root.
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# This stage installs all dependencies and builds shared libraries.
# It will become the polyrepo-node-base:latest image.
COPY package.json yarn.lock ./
COPY tsconfig.base.json ./

# Copy all library and service sources.
# These are needed for 'yarn install' to correctly resolve all workspaces
# and for building the libraries.
# Paths are relative to the Docker build context root (monorepo root).
COPY libs ./libs
COPY services ./services

# Install ALL dependencies from all package.json files.
# Using --production=false to ensure devDependencies (needed by workspace build scripts or linting in CI) are available.
# This creates a single large node_modules at /app/node_modules.
RUN yarn install --frozen-lockfile --production=false

# At this point, node_modules is populated.
# Source code for libraries is present from 'COPY libs ./libs'.
# Now, build the actual shared libraries in the correct dependency order.
RUN echo "Building @libs/observability..." && \
    yarn workspace @libs/observability build && \
    echo "Building @libs/shared-types..." && \
    yarn workspace @libs/shared-types build && \
    echo "Building @libs/shared-utils..." && \
    yarn workspace @libs/shared-utils build && \
    echo "Building @libs/nestjs-common..." && \
    yarn workspace @libs/nestjs-common build && \
    echo "Building @libs/resilience..." && \
    yarn workspace @libs/resilience build && \
    echo "Building @libs/http..." && \
    yarn workspace @libs/http build

# Default EXPOSE, intended to be overridden by consuming service Dockerfiles.
EXPOSE 3000