# Monitoring Infrastructure

This directory contains the monitoring stack configuration for the polyrepo application. The stack includes:

- **Prometheus**: For metrics collection and storage
- **<PERSON><PERSON>**: For visualization and dashboards
- **Loki**: For centralized log aggregation
- **Promtail**: For log collection and forwarding to Loki
- **Tempo**: For distributed tracing
- **Pyroscope**: For continuous profiling
- **OpenTelemetry Collector**: For telemetry data collection and forwarding

## Getting Started

To start the monitoring stack:

```bash
# From the root of the repository
cd infrastructure/monitoring
docker-compose up -d
```

## Accessing the Services

- Grafana: http://grafana.localhost (Default credentials: admin/admin)
- Prometheus: http://prometheus.localhost
- Tempo: http://localhost:3201 (accessed via Grafana datasource)
- Pyroscope: http://localhost:4040

## Integration with Application Services

Services need to be configured to:

1. Expose metrics for Prometheus
2. Send logs to Loki (either directly or via Promtail)
3. Instrument code with OpenTelemetry tracing (exported to Tempo)
4. Enable continuous profiling with Pyroscope

## Recommended Dashboard Imports

For Grafana, we recommend importing the following dashboards:

- Node.js Application Dashboard (ID: 11159)
- Traefik Dashboard (ID: 4475)
- Loki Logs Panel (ID: 12019)
