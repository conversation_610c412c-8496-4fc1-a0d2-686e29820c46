global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8080']

  - job_name: 'auth-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['host.docker.internal:3001']

  - job_name: 'api-gateway'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['host.docker.internal:3000']

  - job_name: 'user-service'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['host.docker.internal:3002']
