auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096
  log_level: debug

common:
  instance_addr: 127.0.0.1
  path_prefix: /loki 
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

limits_config:
  reject_old_samples: true
  reject_old_samples_max_age: 168h

schema_config:
  configs:
    - from: "2020-10-24" 
      store: tsdb
      object_store: filesystem
      schema: v13 
      index:
        prefix: index_
        period: 24h
