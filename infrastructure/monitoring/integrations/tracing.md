# Distributed Tracing Integration

This document outlines how to integrate distributed tracing into our microservices architecture using OpenTelemetry and Tempo.

## Overview

Distributed tracing enables us to trace request flows across multiple services, making debugging and performance analysis much easier. We use OpenTelemetry for instrumentation and Tempo for trace storage and analysis, accessed through Grafana.

## Architecture

```
Service → OpenTelemetry SDK → OTLP HTTP → OpenTelemetry Collector → Tempo → Grafana
```

## Implementation Steps

### 1. Install Required Packages

```bash
npm install @opentelemetry/sdk-node @opentelemetry/auto-instrumentations-node @opentelemetry/exporter-trace-otlp-http @opentelemetry/resources @opentelemetry/semantic-conventions @opentelemetry/sdk-trace-base
```

### 2. Create a Tracing Service

Our observability library already provides a comprehensive TracingService:

```typescript
// Example usage in service module
import { TracingService, TracingConfig } from '@libs/observability';

const tracingConfig: TracingConfig = {
  serviceName: 'auth-service',
  environment: process.env.NODE_ENV || 'development',
  tempoEndpoint: process.env.TEMPO_ENDPOINT || 'http://localhost:4318/v1/traces',
};

const tracingService = new TracingService(tracingConfig);
```

### 3. OTLP Configuration

The tracing service automatically configures OTLP HTTP export to Tempo:

```typescript
// Automatic OTLP configuration in TracingService
const otlpExporter = new OTLPTraceExporter({
  url: tempoEndpoint, // e.g., http://localhost:4318/v1/traces
  headers: {},
});
```

### 4. Service Integration

Each service integrates tracing through the observability module:

```typescript
// services/auth-service/src/observability/observability.module.ts
BaseObservabilityModule.forRootAsync({
  useFactory: (configService: ConfigService) => ({
    tracing: {
      serviceName: 'auth-service',
      environment: configService.get<string>('NODE_ENV') || 'development',
      tempoEndpoint: configService.get<string>('TEMPO_ENDPOINT') || 'http://localhost:4318/v1/traces',
    },
  }),
})
```

### 5. Manual Instrumentation for Business Logic

Use the TracingService for custom business logic tracing:

```typescript
import { TracingService } from '@libs/observability';

@Injectable()
export class PaymentService {
  constructor(private tracingService: TracingService) {}

  async processPayment(userId: string, amount: number) {
    return this.tracingService.traceAsyncFunction(
      'process-payment',
      async (span) => {
        // Set attributes for better context
        span.setAttribute('user.id', userId);
        span.setAttribute('payment.amount', amount);
        span.setAttribute('service.operation', 'payment-processing');
        
        // Your business logic here
        const result = await this.executePayment(userId, amount);
        
        // Add result attributes
        span.setAttribute('payment.status', result.status);
        span.setAttribute('payment.transaction_id', result.transactionId);
        
        return result;
      },
      {
        'business.domain': 'payments',
        'operation.type': 'financial-transaction'
      }
    );
  }
}
```

### 6. Cross-Service Correlation

The HTTP library automatically propagates tracing context across service calls:

```typescript
// Automatic context propagation in @libs/http
@Injectable()
export class UserServiceClient extends BaseServiceClient {
  async getUser(userId: string) {
    // Tracing context automatically propagated in headers
    return this.httpClient.get<User>(`/users/${userId}`);
  }
}
```

## Environment Configuration

Configure tracing with these environment variables:

```bash
# Enable tracing (default: false)
ENABLE_TRACING=true

# Tempo endpoint for OTLP HTTP export
TEMPO_ENDPOINT=http://localhost:4318/v1/traces

# Alternative: OpenTelemetry standard environment variable
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://localhost:4318/v1/traces

# Service identification
SERVICE_NAME=auth-service
NODE_ENV=development

# Sampling configuration (handled by OpenTelemetry SDK)
OTEL_TRACES_SAMPLER=traceidratio
OTEL_TRACES_SAMPLER_ARG=1.0
```

## Viewing Traces

### 1. Access Grafana
Navigate to http://localhost:3200 (admin/admin)

### 2. Explore Traces
- Use the **Explore** tab
- Select **Tempo** as the data source
- Use the **Search** tab to find traces by:
  - Service name
  - Operation name
  - Duration
  - Tags

### 3. Service Map
- View service dependencies and request flows
- Identify performance bottlenecks
- Analyze error patterns

## Best Practices

### 1. Span Naming Convention
```typescript
// Good: Use consistent, hierarchical naming
'auth-service.user.login'
'payment-service.transaction.process'
'user-service.profile.update'

// Avoid: Generic or variable names
'operation'
`request-${userId}`
```

### 2. Meaningful Attributes
```typescript
span.setAttribute('http.method', 'POST');
span.setAttribute('http.route', '/api/users/{id}');
span.setAttribute('user.id', userId);
span.setAttribute('business.operation', 'user-registration');
span.setAttribute('database.table', 'users');
```

### 3. Error Handling
```typescript
try {
  const result = await riskyOperation();
  span.setStatus({ code: SpanStatusCode.OK });
  return result;
} catch (error) {
  span.setStatus({
    code: SpanStatusCode.ERROR,
    message: error.message
  });
  span.recordException(error);
  throw error;
}
```

### 4. Sampling Strategy
- **Development**: 100% sampling for complete visibility
- **Production**: Configure based on traffic volume (typically 1-10%)
- **High-value operations**: Always sample critical business transactions

### 5. Correlation with Logs
```typescript
// Automatically handled by our observability integration
const traceId = this.tracingService.getCurrentSpan()?.spanContext().traceId;
this.logger.info('Processing payment', { traceId, userId, amount });
```

## Integration with Other Observability Tools

### Correlation with Logs (Loki)
- Trace IDs automatically included in structured logs
- Search logs by trace ID in Grafana

### Metrics Correlation (Prometheus)
- Service metrics tagged with operation names
- Correlate performance metrics with trace data

### Continuous Profiling (Pyroscope)
- Profile code execution within traced operations
- Identify performance bottlenecks at the function level

## Troubleshooting

### Common Issues

1. **Traces not appearing in Tempo**
   - Verify OTLP endpoint configuration
   - Check OpenTelemetry Collector logs: `docker-compose logs otel-collector`
   - Ensure ENABLE_TRACING=true

2. **Missing context propagation**
   - Verify HTTP client uses @libs/http (auto-propagation enabled)
   - Check manual context passing for non-HTTP operations

3. **Performance impact**
   - Adjust sampling rate for high-traffic services
   - Monitor overhead with Pyroscope profiling

### Validation Commands

```bash
# Check if traces are being exported
curl http://localhost:4318/v1/traces -X POST \
  -H "Content-Type: application/json" \
  -d '{"resourceSpans":[{"resource":{"attributes":[{"key":"service.name","value":{"stringValue":"test"}}]},"scopeSpans":[{"spans":[{"traceId":"12345678901234567890123456789012","spanId":"1234567890123456","name":"test","startTimeUnixNano":"1640995200000000000","endTimeUnixNano":"1640995201000000000"}]}]}]}'

# Verify Tempo health
curl http://localhost:3201/ready

# Check OpenTelemetry Collector metrics
curl http://localhost:8888/metrics
```

## Migration from Jaeger

If migrating from Jaeger:

1. **Update Dependencies**: Replace `@opentelemetry/exporter-jaeger` with `@opentelemetry/exporter-trace-otlp-http`
2. **Update Configuration**: Change `JAEGER_ENDPOINT` to `TEMPO_ENDPOINT`
3. **Update Code**: Replace `JaegerExporter` with `OTLPTraceExporter`
4. **Grafana Integration**: Add Tempo as a data source instead of Jaeger

Traces from both systems can coexist during migration, with Tempo providing better performance and native Grafana integration.