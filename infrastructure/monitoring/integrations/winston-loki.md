# Winston Loki Integration

To integrate our custom logger with <PERSON>, we need to install the winston-loki transport:

```bash
npm install winston-loki --save
```

## Implementation Strategy

We'll extend the existing `CustomLogger` in `libs/nestjs-common/src/logging/logger.service.ts` with a Loki transport. 

## Configuration

The Loki transport should be configured based on the environment:

```typescript
// Add to imports
import LokiTransport from 'winston-loki';

// Inside the createWinstonLogger method
if (process.env.ENABLE_LOKI === 'true') {
  transports.push(
    new LokiTransport({
      host: process.env.LOKI_HOST || 'http://loki:3100',
      labels: { 
        app: process.env.APP_NAME || 'polyrepo',
        environment: process.env.NODE_ENV || 'development',
        service: process.env.SERVICE_NAME || 'unknown'
      },
      json: true,
      batching: true,
      interval: 5, // Send logs every 5 seconds
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    })
  );
}
```

## Environment Variables

Add the following environment variables to service configurations:

- `ENABLE_LOKI`: Set to 'true' to enable Loki logging
- `LOKI_HOST`: URL of the Loki server
- `APP_NAME`: Name of the application
- `SERVICE_NAME`: Name of the specific service
