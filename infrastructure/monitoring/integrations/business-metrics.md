# Business Metrics Integration

This document outlines the approach for tracking and logging business metrics in the polyrepo application.

## What to Track

The system should track user actions and business-relevant metrics, including:

1. **User Authentication Events**
   - Registration attempts (success/failure)
   - Login attempts (success/failure)
   - Password reset requests
   - Account verification events

2. **User Activity Metrics**
   - Session duration
   - Feature usage frequency
   - Conversion funnels
   - User engagement patterns

3. **Business Performance Metrics**
   - API response times
   - Error rates by endpoint
   - Database query performance
   - Resource utilization

## Implementation Approaches

### 1. Prometheus Metrics (for Real-time Operational Metrics)

For technical and aggregated metrics that require real-time monitoring:

```typescript
// Example using prom-client in NestJS
import { Injectable } from '@nestjs/common';
import { Registry, Counter, Histogram } from 'prom-client';

@Injectable()
export class MetricsService {
  private readonly registry: Registry;
  private readonly loginCounter: Counter;
  private readonly apiLatency: Histogram;

  constructor() {
    this.registry = new Registry();
    
    this.loginCounter = new Counter({
      name: 'user_login_total',
      help: 'Total number of user logins',
      labelNames: ['status', 'provider']
    });
    
    this.apiLatency = new Histogram({
      name: 'api_request_duration_seconds',
      help: 'API endpoint latency in seconds',
      labelNames: ['method', 'endpoint', 'status_code']
    });
    
    this.registry.registerMetric(this.loginCounter);
    this.registry.registerMetric(this.apiLatency);
  }

  incrementLogin(status: 'success' | 'failure', provider: string): void {
    this.loginCounter.inc({ status, provider });
  }

  recordApiLatency(method: string, endpoint: string, statusCode: number, durationSec: number): void {
    this.apiLatency.observe({ method, endpoint, statusCode }, durationSec);
  }

  getMetrics(): Promise<string> {
    return this.registry.metrics();
  }
}
```

### 2. Structured Logging (for Business Events and Analytics)

For business events that can be analyzed later through log aggregation:

```typescript
// Example using enhanced structured logging
import { Injectable } from '@nestjs/common';
import { CustomLogger } from '@libs/nestjs-common';

@Injectable()
export class BusinessEventLogger {
  constructor(private readonly logger: CustomLogger) {
    this.logger.setContext('BusinessEvents');
  }

  logUserActivity(
    userId: string, 
    action: string, 
    details: Record<string, any> = {}
  ): void {
    this.logger.log({
      event_type: 'user_activity',
      user_id: userId,
      action,
      timestamp: new Date().toISOString(),
      ...details
    });
  }

  logBusinessEvent(
    eventType: string,
    payload: Record<string, any> = {}
  ): void {
    this.logger.log({
      event_type: 'business_event',
      event_name: eventType,
      timestamp: new Date().toISOString(),
      ...payload
    });
  }
}
```

## Centralized Analytics Integration

To support centralized analytics and the future central logging system:

1. Ensure all business logs include standardized fields (timestamp, event_type, service)
2. Consider implementing a secondary log stream specifically for business events
3. Tag logs appropriately to allow filtering/routing to analytics systems

## Best Practices

1. **Consistent Schema**: Use standard field names across all services
2. **Sampling**: For high-volume events, consider sampling to reduce log volume
3. **PII Protection**: Never log sensitive personal information
4. **Context Preservation**: Include correlation IDs to trace user journeys
5. **Normalization**: Use consistent data formats (ISO dates, lowercase status values)
