{"dashboard": {"id": null, "title": "Business Impact - Error Monitoring", "tags": ["business", "impact", "auth", "users"], "timezone": "browser", "schemaVersion": 16, "version": 1, "refresh": "1m", "time": {"from": "now-4h", "to": "now"}, "panels": [{"id": 1, "title": "Authentication Failures (Critical Impact)", "type": "stat", "targets": [{"expr": "sum(count_over_time({service=\"auth-service\", level=\"error\"} |= \"INVALID_CREDENTIALS\" | json [1h]))", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "orange", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "User Registration Failures", "type": "stat", "targets": [{"expr": "sum(count_over_time({service=\"auth-service\", level=\"error\"} |= \"EMAIL_ALREADY_EXISTS\" | json [1h]))", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 25}]}}}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "User Data Access Errors", "type": "stat", "targets": [{"expr": "sum(count_over_time({service=\"user-service\", level=\"error\"} |= \"USER_NOT_FOUND\" | json [1h]))", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 20}]}}}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Keycloak Integration Issues", "type": "stat", "targets": [{"expr": "sum(count_over_time({service=\"auth-service\", level=\"error\"} |= \"KEYCLOAK_CONNECTION_ERROR\" | json [1h]))", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "orange", "value": 2}, {"color": "red", "value": 10}]}}}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "Error Rate Trends (Last 4 Hours)", "type": "timeseries", "targets": [{"expr": "sum by (service) (rate({level=\"error\"} |= \"correlationId\" | json [5m]))", "refId": "A", "datasource": "<PERSON>", "legendFormat": "{{service}}"}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 6}}, {"id": 6, "title": "Top Error Codes (Last Hour)", "type": "table", "targets": [{"expr": "topk(10, sum by (error_code) (count_over_time({level=\"error\"} |= \"correlationId\" | json | error_code != \"\" [1h])))", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}}, {"id": 7, "title": "Recent Critical Errors", "type": "logs", "targets": [{"expr": "{level=\"error\"} |= \"correlationId\" |= \"INVALID_CREDENTIALS\" or |= \"KEYCLOAK_CONNECTION_ERROR\" or |= \"USER_NOT_FOUND\" | json", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}}]}, "overwrite": true}