{"dashboard": {"id": null, "title": "Correlation ID Investigation Dashboard", "tags": ["correlation", "debugging", "error-tracking"], "timezone": "browser", "schemaVersion": 16, "version": 1, "refresh": "5s", "time": {"from": "now-15m", "to": "now"}, "templating": {"list": [{"name": "correlation_id", "type": "textbox", "label": "Correlation ID", "description": "Enter correlation ID to investigate (e.g., req-1749578400-abc123)"}]}, "panels": [{"id": 1, "title": "Request Timeline for Correlation ID: $correlation_id", "type": "logs", "targets": [{"expr": "{correlationId=\"$correlation_id\"} | json", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Service Flow for Request", "type": "table", "targets": [{"expr": "{correlationId=\"$correlation_id\"} | json | line_format \"{{.timestamp}} | {{.service}} | {{.level}} | {{.message}}\"", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 3, "title": "Error <PERSON>ails (if any)", "type": "logs", "targets": [{"expr": "{correlationId=\"$correlation_id\", level=\"error\"} | json", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}}, {"id": 4, "title": "Request Duration Analysis", "type": "stat", "targets": [{"expr": "max_over_time({correlationId=\"$correlation_id\"} | json | unwrap duration [15m]) - min_over_time({correlationId=\"$correlation_id\"} | json | unwrap timestamp [15m])", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"unit": "ms", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 2000}]}}}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 20}}]}, "overwrite": true}