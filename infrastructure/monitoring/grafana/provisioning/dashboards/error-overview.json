{"dashboard": {"id": null, "title": "Error Monitoring - Production Overview", "tags": ["error-handling", "correlation", "production"], "timezone": "browser", "schemaVersion": 16, "version": 1, "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Error Rate by Service (Last Hour)", "type": "stat", "targets": [{"expr": "rate({level=\"error\"} |= \"correlationId\" | json [5m])", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Error Types Distribution", "type": "piechart", "targets": [{"expr": "sum by (error_code) (count_over_time({level=\"error\"} |= \"correlationId\" | json | error_code != \"\" [1h]))", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 6, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Correlation ID Timeline (Errors)", "type": "logs", "targets": [{"expr": "{level=\"error\"} |= \"correlationId\" | json", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}}, {"id": 4, "title": "Service Error Rates", "type": "table", "targets": [{"expr": "sum by (service) (count_over_time({level=\"error\"} |= \"correlationId\" | json [1h]))", "refId": "A", "datasource": "<PERSON>"}], "gridPos": {"h": 6, "w": 12, "x": 0, "y": 14}}, {"id": 5, "title": "Critical Business Flow Errors", "type": "stat", "targets": [{"expr": "sum(count_over_time({service=~\"auth-service|user-service\"} |= \"correlationId\" |= \"error\" | json [1h]))", "refId": "A", "datasource": "<PERSON>"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "orange", "value": 5}, {"color": "red", "value": 20}]}}}, "gridPos": {"h": 4, "w": 12, "x": 0, "y": 20}}]}, "overwrite": true}