server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: varlogs
          __path__: /var/log/*log

  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          __path__: /var/lib/docker/containers/*/*log

  # Service-specific logs - these will be expanded as we add more services
  - job_name: auth-service
    static_configs:
      - targets:
          - localhost
        labels:
          job: auth-service
          service: auth
          __path__: /var/log/auth-service/*.log
