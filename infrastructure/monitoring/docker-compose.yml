version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - monitoring-network
      - local_network # Connect to the same network as other services
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"
      - "traefik.http.routers.prometheus.entrypoints=websecure"
      - "traefik.http.routers.prometheus.tls=true"

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3200:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin  # Change this in production!
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_ROOT_URL=http://grafana.localhost
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    networks:
      - monitoring-network
      - local_network
    restart: unless-stopped
    depends_on:
      - prometheus
      - loki
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
      - "traefik.http.routers.grafana.entrypoints=websecure"
      - "traefik.http.routers.grafana.tls=true"

  loki:
    image: grafana/loki:latest
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./loki/loki-config.yml:/etc/loki/loki-config.yml
      - loki_data:/loki
    command: -config.file=/etc/loki/loki-config.yml
    networks:
      - monitoring-network
      - local_network
    restart: unless-stopped

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    volumes:
      - ./promtail/promtail-config.yml:/etc/promtail/config.yml
      - /var/log:/var/log  # Mount host logs (adjust as needed)
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring-network
      - local_network
    restart: unless-stopped
    depends_on:
      - loki

  tempo:
    image: grafana/tempo:2.3.1
    container_name: tempo
    command: [ "-config.file=/etc/tempo.yaml" ]
    volumes:
      - ./tempo/tempo.yaml:/etc/tempo.yaml
      - tempo_data:/var/tempo
    ports:
      - "3201:3200"    # Tempo HTTP (avoiding conflict with Grafana on 3200)
      - "9095:9095"    # Tempo gRPC
    networks:
      - monitoring-network
      - local_network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.tempo.rule=Host(`tempo.localhost`)"
      - "traefik.http.routers.tempo.entrypoints=websecure"
      - "traefik.http.routers.tempo.tls=true"
      - "traefik.http.services.tempo.loadbalancer.server.port=3200"

  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.91.0
    container_name: otel-collector
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./otel/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"   # OTLP gRPC receiver
      - "4318:4318"   # OTLP HTTP receiver
      - "8889:8889"   # Prometheus metrics from collector
    depends_on:
      - tempo
    networks:
      - monitoring-network
      - local_network
    restart: unless-stopped

  pyroscope:
    image: grafana/pyroscope:1.5.0
    container_name: pyroscope
    ports:
      - "4040:4040"
    command:
      - "server"
    environment:
      - PYROSCOPE_LOG_LEVEL=info
      - PYROSCOPE_STORAGE_PATH=/var/lib/pyroscope
    volumes:
      - pyroscope_data:/var/lib/pyroscope
    networks:
      - monitoring-network
      - local_network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.pyroscope.rule=Host(`pyroscope.localhost`)"
      - "traefik.http.routers.pyroscope.entrypoints=websecure"
      - "traefik.http.routers.pyroscope.tls=true"
      - "traefik.http.services.pyroscope.loadbalancer.server.port=4040"

volumes:
  prometheus_data:
  grafana_data:
  loki_data:
  tempo_data:
  pyroscope_data:

networks:
  monitoring-network:
    driver: bridge
  # Use the same network defined in your main docker-compose file
  local_network:
    external: true
    name: polyrepo_local_network
