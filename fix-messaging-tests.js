const fs = require('fs');
const path = require('path');

// Fix in-memory-event-bus.unit.spec.ts
const inMemoryTestPath = '/root/code/polyrepo/libs/messaging/test/unit/in-memory-event-bus.unit.spec.ts';
let inMemoryContent = fs.readFileSync(inMemoryTestPath, 'utf8');

// Fix DomainEvent structure - remove metadata wrapper, move source to top level
inMemoryContent = inMemoryContent.replace(
  /metadata: { source: '([^']+)'(, correlationId: '[^']*')? }/g, 
  (match, source, correlationPart) => {
    if (correlationPart) {
      const correlationId = correlationPart.replace(", correlationId: '", '').replace("'", '');
      return `source: '${source}',\n        correlationId: '${correlationId}'`;
    }
    return `source: '${source}'`;
  }
);

// Fix handler definitions - replace jest.fn() with proper EventHandler interface
inMemoryContent = inMemoryContent.replace(
  /const (\w+) = jest\.fn\(\);/g,
  'const $1 = {\n        handle: jest.fn(),\n        supportedEvents: [\'test.event\']\n      };'
);

// Fix handler expectations
inMemoryContent = inMemoryContent.replace(/expect\((\w+)\)\.toHaveBeenCalled/g, 'expect($1.handle).toHaveBeenCalled');
inMemoryContent = inMemoryContent.replace(/expect\((\w+)\)\.not\.toHaveBeenCalled/g, 'expect($1.handle).not.toHaveBeenCalled');

// Fix some specific handler definitions that need custom event types
inMemoryContent = inMemoryContent.replace(
  /const wildcardHandler = {\s+handle: jest\.fn\(\),\s+supportedEvents: \['test\.event'\]\s+};/g,
  'const wildcardHandler = {\n        handle: jest.fn(),\n        supportedEvents: [\'*\']\n      };'
);

inMemoryContent = inMemoryContent.replace(
  /const specificHandler = {\s+handle: jest\.fn\(\),\s+supportedEvents: \['test\.event'\]\s+};/g,
  'const specificHandler = {\n        handle: jest.fn(),\n        supportedEvents: [\'user.created\']\n      };'
);

fs.writeFileSync(inMemoryTestPath, inMemoryContent);

// Fix event-factory.unit.spec.ts
const eventFactoryTestPath = '/root/code/polyrepo/libs/messaging/test/unit/event-factory.unit.spec.ts';
let eventFactoryContent = fs.readFileSync(eventFactoryTestPath, 'utf8');

// Fix EventFactory.cacheOperation calls - remove second parameter
eventFactoryContent = eventFactoryContent.replace(
  /EventFactory\.cacheOperation\(([^,]+), { source: '[^']*' }\)/g,
  'EventFactory.cacheOperation($1)'
);

// Fix userCreated call - change from object to string for userId
eventFactoryContent = eventFactoryContent.replace(
  /EventFactory\.userCreated\({ id: 1, email: 'test@test\.com' }, {\s*source: 'test', correlationId\s*}\)/g,
  'EventFactory.userCreated(\'user-123\', { id: 1, email: \'<EMAIL>\' }, correlationId)'
);

// Fix metadata access - change from optional to direct access for known fields
eventFactoryContent = eventFactoryContent.replace(/event\.metadata\?\.operationType/g, 'event.data.operation');
eventFactoryContent = eventFactoryContent.replace(/event\.metadata\?\.cacheResult/g, 'event.metadata?.cacheEfficiency');
eventFactoryContent = eventFactoryContent.replace(/event\.metadata\?\.cacheKey/g, 'event.data.key');

fs.writeFileSync(eventFactoryTestPath, eventFactoryContent);

console.log('Fixed messaging test files');