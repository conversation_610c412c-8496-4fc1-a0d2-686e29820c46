const fs = require('fs');

// Fix event factory test type references
const eventFactoryPath = '/root/code/polyrepo/libs/messaging/test/unit/event-factory.unit.spec.ts';
let factoryContent = fs.readFileSync(eventFactoryPath, 'utf8');

// Remove unused import
factoryContent = factoryContent.replace(
  /import { HttpRequestEvent, HttpResponseEvent, HttpErrorEvent } from '..\/..\/src\/events\/http-events\.types';/,
  '// Types are inferred from EventFactory methods'
);

// Replace type annotations with proper data structures
factoryContent = factoryContent.replace(/const requestData: HttpRequestEventData = \{/g, 'const requestData = {');
factoryContent = factoryContent.replace(/const responseData: HttpResponseEventData = \{/g, 'const responseData = {');
factoryContent = factoryContent.replace(/const errorData: HttpErrorEventData = \{/g, 'const errorData = {');

// Fix messaging module tests
const messagingModulePath = '/root/code/polyrepo/libs/messaging/test/unit/messaging.module.unit.spec.ts';
let moduleContent = fs.readFileSync(messagingModulePath, 'utf8');

// Update Redis configuration structure to match RedisStreamsConfig interface
moduleContent = moduleContent.replace(
  /redis: \{\s*host: '([^']+)',\s*port: (\d+),\s*streamName: '([^']+)'\s*\}/g,
  `redis: {
        redis: { host: '$1', port: $2 },
        defaultStream: '$3'
      }`
);

// Fix comprehensive Redis config
moduleContent = moduleContent.replace(
  /redis: \{\s*host: '([^']+)',\s*port: (\d+),\s*db: (\d+),\s*streamName: '([^']+)',\s*maxLength: (\d+),\s*batchSize: (\d+),\s*connectTimeout: (\d+),\s*commandTimeout: (\d+)\s*\}/g,
  `redis: {
        redis: { 
          host: '$1', 
          port: $2, 
          db: $3 
        },
        defaultStream: '$4',
        retention: { maxLength: $5 }
      }`
);

fs.writeFileSync(eventFactoryPath, factoryContent);
fs.writeFileSync(messagingModulePath, moduleContent);

console.log('Fixed remaining TypeScript type issues');