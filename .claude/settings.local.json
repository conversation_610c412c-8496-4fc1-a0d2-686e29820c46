{"permissions": {"allow": ["mcp__notion__API-post-search", "mcp__notion__API-get-self", "Bash(find:*)", "Bash(ls:*)", "mcp__notion__API-patch-page", "mcp__notion__API-patch-block-children", "mcp__notion__API-post-database-query", "mcp__notion__API-post-page", "mcp__notion__API-retrieve-a-database", "mcp__notion__API-update-a-database", "mcp__notion__API-create-a-comment", "mcp__notion__API-create-a-database", "mcp__notion__API-update-a-block", "<PERSON><PERSON>(docker:*)", "Bash(yarn install)", "Bash(yarn build:libs)", "Bash(grep:*)", "Bash(yarn build:webpack)", "mcp__grafana__list_datasources", "mcp__grafana__query_prometheus", "mcp__grafana__query_loki_logs", "mcp__grafana__list_loki_label_values", "<PERSON><PERSON>(curl:*)", "Bash(rg:*)", "Bash(yarn build)", "Bash(yarn start:dev:bundled:watch:*)", "mcp__notion__API-retrieve-a-page", "mcp__dart__get_config", "mcp__dart__list_tasks", "mcp__dart__create_task", "mcp__notion__API-delete-a-block", "mcp__dart__add_task_comment", "mcp__dart__get_task", "Bash(yarn bundle:rebuild:*)", "Bash(yarn test:all)", "mcp__dart__update_task", "Bash(yarn test)", "<PERSON><PERSON>(mkdir:*)", "Bash(yarn test:*)", "Bash(yarn jest test:*)", "Bash(yarn jest:*)", "Bash(./infrastructure/local-dev/build-libs-order.sh:*)", "Bash(./ultimate-dev-workflow.sh)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git add:*)", "Bash(redis-cli:*)", "Bash(yarn run webpack:*)", "Bash(yarn dev:health:*)", "Bash(yarn dev:stop:*)", "<PERSON><PERSON>(cat:*)", "Bash(npx webpack:*)", "Bash(yarn webpack:*)", "Bash(yarn dev:cli start:*)", "Bash(node:*)", "Bash(yarn --version)", "Bash(yarn dev:cli:*)", "Bash(yarn prisma generate:*)", "Bash(npx prisma generate:*)", "Bash(yarn prisma:generate:*)", "Bash(yarn prisma:migrate:dev:*)", "Bash(yarn prisma:setup:*)", "Bash(yarn run prisma:setup:*)", "Bash(yarn add:*)", "Bash(echo)", "Bash(npm test)", "Bash(npm run test:unit:*)", "Bash(npm run test:integration:*)"], "deny": []}}